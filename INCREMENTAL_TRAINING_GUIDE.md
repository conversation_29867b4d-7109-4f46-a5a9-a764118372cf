# 🚀 Incremental Training Guide: Adding Your 30 New Labels

## 📋 **Overview**

You've labeled 30 new transactions and want to train your ML model with this new data while keeping your existing trained data intact. This guide shows you exactly how to do **incremental training** that adds only the new data to your existing model.

## 🎯 **Step-by-Step Process**

### **Step 1: Combine All Labeled Data** 
*(This preserves your old training data and adds the new 30 labels)*

1. **Open ML Labeling Window**
   - Run: `python bank_statement_analyzer.py`
   - Click **"ML Labeling & Training"** button

2. **Access Session Training Management**
   - Go to **File → Manage Session Training** (or **Data → Manage Session Training**)
   - This opens the Session-Based Training Management dialog

3. **Combine All Labeled Data**
   - Click **"🔗 Combine All Labeled Data for Training"** button
   - The system will:
     - Find all your labeled transactions across all sessions
     - Combine your old labeled data + new 30 labels
     - Update the main training data file
   - Click **"Yes"** to confirm

### **Step 2: Train the ML Model**
*(This trains the model with all your data - old + new)*

1. **Access Model Training**
   - In the ML Labeling Window, go to **Tools → Train ML Model** (or similar menu option)
   - Or look for a **"Train Model"** button in the interface

2. **Start Training**
   - The system will automatically use all your combined labeled data
   - Training will include:
     - Your existing labeled transactions (200+)
     - Your new 30 labeled transactions
     - Total: ~230+ labeled transactions for training

3. **Monitor Training Progress**
   - The system will show training progress
   - Wait for training to complete
   - You'll see accuracy metrics and training results

## 🔧 **Alternative Method: Using Model Trainer Directly**

If the UI method doesn't work, you can use the programmatic approach:

```python
# Create a simple training script
from bank_analyzer.ml.integrated_categorizer import IntegratedCategorizer

# Initialize the categorizer
categorizer = IntegratedCategorizer()

# Trigger model training (this will use all available labeled data)
job_id = categorizer.trigger_model_training()

if job_id:
    print(f"Training started with job ID: {job_id}")
    print("Check the logs for training progress")
else:
    print("Training could not be started - check if you have enough labeled data")
```

## 📊 **What Happens During Incremental Training**

### **Data Combination:**
- ✅ **Existing labeled data**: Preserved and included
- ✅ **New 30 labels**: Added to training dataset  
- ✅ **No data loss**: All previous training is maintained
- ✅ **Incremental**: Only new data is added, not replaced

### **Model Training:**
- 🔄 **Retrains the entire model** with the combined dataset
- 📈 **Improves accuracy** with additional training examples
- 🎯 **Learns new patterns** from your 30 new labels
- 💾 **Saves new model version** with timestamp

### **Training Data Sources:**
1. **Labeling History** (`labeling_history.csv`) - Your manual labels
2. **Session Data** - Labels from current session
3. **Combined Training Data** - Merged dataset for model training

## ✅ **Verification Steps**

### **Before Training:**
1. Check that your 30 new labels are saved:
   - Look at the labeling statistics in the ML window
   - Should show increased "Labeled" count

### **During Training:**
1. Monitor training logs for:
   - Total training samples (should be ~230+)
   - Training progress messages
   - No error messages

### **After Training:**
1. **Test the model** on some unlabeled transactions
2. **Check accuracy metrics** - should be improved
3. **Verify new categories** are being predicted correctly

## 🚨 **Important Notes**

### **✅ DO:**
- Use **"Combine All Labeled Data"** to preserve existing training
- Wait for training to complete before using the model
- Test the model after training to verify improvements

### **❌ DON'T:**
- Use **"Replace Training Data"** - this will lose your old data
- Use **"NUCLEAR option"** - this deletes everything
- Train multiple times simultaneously

## 🔍 **Troubleshooting**

### **Issue: "Insufficient training data"**
**Solution**: Make sure you have at least 10 labeled transactions total

### **Issue: "Training failed"**
**Solution**: Check the logs for specific error messages

### **Issue: "No new data found"**
**Solution**: Verify your 30 new labels were actually saved to the system

### **Issue: Model accuracy didn't improve**
**Solution**: 
- Check if your new labels are diverse enough
- Ensure new labels are for different transaction types
- Consider labeling more transactions for better improvement

## 🎉 **Expected Results**

After successful incremental training:

- ✅ **Model accuracy improved** with additional training data
- ✅ **New transaction patterns recognized** from your 30 labels  
- ✅ **All previous training preserved** - no data loss
- ✅ **Better categorization** for similar transactions
- ✅ **Model version updated** with timestamp

## 📝 **Quick Reference Commands**

1. **Combine Data**: File → Manage Session Training → Combine All Labeled Data
2. **Train Model**: Tools → Train ML Model (or similar)
3. **Check Status**: View training logs and accuracy metrics
4. **Test Model**: Try categorizing some unlabeled transactions

---

🎯 **The key is using "Combine All Labeled Data" which does incremental addition rather than replacement, preserving all your existing training while adding the new 30 labels!**
