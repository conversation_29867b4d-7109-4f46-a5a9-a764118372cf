# Quick Start Guide: Three-Way Transaction Categorization

## 🚀 How to Use the New Categorization System

### Step 1: Open ML Labeling Window
1. Run the bank statement analyzer: `python bank_statement_analyzer.py`
2. Click **"ML Labeling & Training"** button or go to **Tools → ML Labeling & Training**

### Step 2: Load Session Data
1. In the ML Labeling Window, go to **File → Manage Sessions...** (Ctrl+M)
2. Select a session from the list
3. Click **"Load Session"** or **"Load & Close"**

### Step 3: Review Categorization Results
The **Transaction Categorization Dialog** will automatically appear showing:

#### 📊 **Already Labeled Tab**
- Transactions that have been previously labeled and categorized
- Shows existing category and sub-category
- **For reference only** - won't be retrained

#### ❓ **Unlabeled Tab** 
- Transactions that exist in the system but haven't been labeled yet
- **Available for training** - need your attention
- Shows frequency and when first/last seen

#### ✨ **New Tab**
- Completely new transactions not seen before
- **Available for training** - ready for labeling
- Fresh data for expanding your training set

### Step 4: Select Categories to Include
Choose which categories you want to work with:

- ✅ **Include Already Labeled** (Optional): For reference and consistency checking
- ✅ **Include Unlabeled** (Recommended): For training on existing but unlabeled data  
- ✅ **Include New** (Recommended): For training on completely new transactions

### Step 5: Proceed with Training
1. Click **"Proceed with Selected Categories"**
2. The selected transactions will load into the ML labeling interface
3. Start labeling and training as usual!

## 🎯 What Each Category Means

| Category | Description | Available for Training | Use Case |
|----------|-------------|----------------------|----------|
| **Already Labeled** | Previously categorized transactions | ❌ No (Reference only) | Check consistency, review past decisions |
| **Unlabeled** | Exist in system but not labeled | ✅ Yes | Label existing transactions to improve coverage |
| **New** | Completely new transactions | ✅ Yes | Expand training data with fresh transactions |

## 💡 Best Practices

### For New Users (No Training Data Yet)
- All transactions will appear as "New" 
- This is normal and expected
- Start labeling to build your training dataset

### For Existing Users (Have Training Data)
- **Include Unlabeled + New**: Focus on transactions that need attention
- **Include Already Labeled**: Only if you want to review past decisions
- **Recommended**: Unlabeled ✅ + New ✅, Already Labeled ❌

### For Quality Control
- **Include Already Labeled**: Review previously labeled transactions
- Check for consistency in categorization
- Verify that similar transactions have the same labels

## 🔧 Troubleshooting

### "All transactions showing as New"
- **Normal for new installations** - no training data exists yet
- Start labeling transactions to build your dataset

### "Categorization dialog not appearing"
- Make sure you're loading sessions from **ML Labeling Window**, not main UI
- Use **File → Manage Sessions...** within the ML Labeling Window

### "No Already Labeled transactions"
- Check that you have actually labeled transactions in previous sessions
- Verify `bank_analyzer_config/ml_data/labeling_history.csv` exists

### "Performance is slow"
- Normal with large datasets (thousands of transactions)
- Consider archiving old training data if needed

## 🎉 Benefits You'll See

✅ **No More Duplicate Training**: Already labeled transactions are clearly separated  
✅ **Better Focus**: Work only on transactions that need attention  
✅ **Quality Control**: Easy access to previously labeled data for consistency  
✅ **Efficient Workflow**: Automatic categorization saves time  
✅ **Data Integrity**: Clean separation prevents training data contamination  

## 📝 Quick Reference

**To access the new system:**
1. ML Labeling Window → File → Manage Sessions...
2. Select session → Load Session
3. Review categorization → Select categories → Proceed

**Default recommendation:**
- ✅ Include Unlabeled (for training)
- ✅ Include New (for training)  
- ❌ Include Already Labeled (unless reviewing)

**Remember:** Only unlabeled and new transactions will be available for actual ML training. Already labeled transactions are shown for reference only and won't be retrained.

---

🎯 **The system is now ready to use! Start by loading a session in the ML Labeling Window to see the new categorization in action.**
