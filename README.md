# Standalone Bank Statement Analyzer

A comprehensive, standalone Python application for analyzing bank statements with AI-powered categorization, machine learning capabilities, and modern GUI interface.

## 🚀 Quick Start

### Option 1: Simple Launch
```bash
python run_analyzer.py
```

### Option 2: Direct Launch
```bash
python bank_statement_analyzer.py
```

### Option 3: Advanced Launch (with dependency checking)
```bash
python launch_bank_analyzer.py
```

## 📋 Requirements

- **Python 3.8+**
- **Operating System**: Windows, macOS, or Linux
- **Memory**: 4GB RAM recommended
- **Storage**: 500MB free space

## 🛠️ Installation

1. **Download/Clone** this standalone folder
2. **Navigate** to the folder:
   ```bash
   cd bank_statement_analyzer_standalone
   ```
3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```
4. **Run the application**:
   ```bash
   python run_analyzer.py
   ```

## 📁 Folder Structure

```
bank_statement_analyzer_standalone/
├── run_analyzer.py              # Simple launcher
├── bank_statement_analyzer.py   # Main application
├── launch_bank_analyzer.py      # Advanced launcher
├── requirements.txt             # Dependencies
├── README.md                    # This file
├── README_BANK_ANALYZER.md      # Detailed documentation
├── bank_analyzer/               # Core application modules
│   ├── core/                    # Core functionality
│   ├── ml/                      # Machine learning components
│   ├── parsers/                 # File format parsers
│   ├── ui/                      # User interface
│   └── models/                  # Data models
├── bank_analyzer_config/        # Configuration and data
│   ├── ml_models/               # Trained ML models
│   ├── ml_data/                 # Training data
│   ├── sambanova/               # AI API configuration
│   └── merchant_cache/          # Merchant mappings
├── statements/                  # Place your bank statements here
├── logs/                        # Application logs
└── data/                        # Processed data
```

## 🎯 Features

### 📄 Multi-Format Support
- **PDF Statements**: Automatic text extraction and table parsing
- **CSV Files**: Intelligent column detection and mapping
- **Excel Files**: Support for .xlsx and .xls with sheet detection

### 🤖 AI-Powered Categorization
- **SambaNova AI**: Advanced transaction categorization
- **Machine Learning**: Local ML models with training capabilities
- **Rule-Based**: Customizable categorization rules
- **Hybrid Workflows**: Combine AI, ML, and manual review

### 🎛️ Advanced Features
- **Training Data Management**: Build and manage ML training datasets
- **Performance Analytics**: Track categorization accuracy
- **Cost Optimization**: Smart AI usage with budget controls
- **Merchant Mapping**: Intelligent merchant pattern recognition

### 📊 User Interface
- **Modern GUI**: Built with PySide6
- **Transaction Preview**: Review and edit before processing
- **Confidence Scoring**: Visual indicators for categorization quality
- **Bulk Operations**: Process multiple statements efficiently

## 🔧 Configuration

### AI Integration (Optional)
To use SambaNova AI features:
1. Get API key from SambaNova
2. Configure in `bank_analyzer_config/sambanova/`
3. Set budget limits in the application

### ML Training
1. Use "ML Menu" → "Manual Labeling with AI Assistant"
2. Label transactions to build training data
3. Train models using "ML Menu" → "Train Models"

## 📤 Export Options

- **CSV Export**: Categorized transactions ready for import
- **Excel Export**: Formatted spreadsheets
- **JSON Export**: Structured data for developers

## 🛠️ Troubleshooting

### Common Issues

**"Module not found" errors:**
```bash
pip install -r requirements.txt
```

**PDF parsing issues:**
```bash
pip install pymupdf  # Alternative PDF parser
```

**GUI not starting:**
- Ensure PySide6 is installed
- Check Python version (3.8+ required)

### Logs
Check `logs/` directory for detailed error information.

## 🔗 Additional Tools

### Diagnostic Scripts
- `diagnose_ml_model.py` - Check ML model status
- `fix_ml_categorization.py` - Fix ML issues
- `verify_session_features.py` - Verify functionality

### Hybrid Categorization
```bash
python launch_hybrid_categorization.py
```

## 📚 Documentation

See `README_BANK_ANALYZER.md` for detailed documentation including:
- Advanced configuration options
- API integration guides
- Development information
- Troubleshooting guides

## 🆘 Support

1. **Check logs** in the `logs/` directory
2. **Run diagnostics**: `python diagnose_ml_model.py`
3. **Reset configuration**: Delete `bank_analyzer_config/` (will recreate defaults)

## 🔄 Updates

This is a standalone version. For updates:
1. Download the latest standalone package
2. Copy your `bank_analyzer_config/` and `statements/` folders
3. Replace the application files

---

**Version**: Standalone 1.0  
**Last Updated**: 2025-06-29  
**Python**: 3.8+ Required
