#!/usr/bin/env python3
"""
Analyze what's actually in the training data - old vs new labels
"""

import sys
import pandas as pd
from pathlib import Path
from datetime import datetime

# Add the bank_analyzer module to the path
sys.path.insert(0, str(Path(__file__).parent))


def analyze_labeling_history():
    """Analyze the labeling history to see old vs new data"""
    print("🔍 Analyzing Labeling History (Old vs New)")
    print("=" * 60)
    
    try:
        labeling_history_path = Path("bank_analyzer_config/ml_data/labeling_history.csv")
        if not labeling_history_path.exists():
            print("❌ No labeling history found")
            return
        
        df = pd.read_csv(labeling_history_path)
        print(f"📊 Total labeling entries: {len(df)}")
        print(f"📊 Unique transactions: {df['hash_id'].nunique()}")
        
        # Convert timestamp to datetime for analysis
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        # Sort by timestamp
        df_sorted = df.sort_values('timestamp')
        
        # Show date range
        earliest = df_sorted['timestamp'].min()
        latest = df_sorted['timestamp'].max()
        
        print(f"📅 Date range: {earliest.strftime('%Y-%m-%d %H:%M')} to {latest.strftime('%Y-%m-%d %H:%M')}")
        
        # Group by date to see labeling activity
        df['date'] = df['timestamp'].dt.date
        daily_counts = df.groupby('date').size().sort_index()
        
        print(f"\n📈 Daily labeling activity:")
        for date, count in daily_counts.items():
            print(f"   {date}: {count} labels")
        
        # Show recent vs older labels
        cutoff_date = datetime.now() - pd.Timedelta(days=7)  # Last 7 days
        recent_labels = df[df['timestamp'] > cutoff_date]
        older_labels = df[df['timestamp'] <= cutoff_date]
        
        print(f"\n🕒 Recent vs Older Labels:")
        print(f"   Recent (last 7 days): {len(recent_labels)} entries, {recent_labels['hash_id'].nunique()} unique")
        print(f"   Older (before 7 days): {len(older_labels)} entries, {older_labels['hash_id'].nunique()} unique")
        
        # Show categories breakdown
        print(f"\n📋 Categories in your data:")
        category_counts = df['category'].value_counts()
        for category, count in category_counts.items():
            print(f"   {category}: {count} transactions")
        
        return len(recent_labels), len(older_labels), df['hash_id'].nunique()
        
    except Exception as e:
        print(f"❌ Error analyzing labeling history: {str(e)}")
        return 0, 0, 0


def analyze_current_training_data():
    """Analyze what's currently in the training data file"""
    print(f"\n🔍 Analyzing Current Training Data File")
    print("=" * 60)
    
    try:
        unique_transactions_path = Path("bank_analyzer_config/ml_data/unique_transactions.csv")
        if not unique_transactions_path.exists():
            print("❌ No unique_transactions.csv found")
            return
        
        df = pd.read_csv(unique_transactions_path)
        print(f"📊 Total transactions in training file: {len(df)}")
        
        # Check labeled vs unlabeled
        labeled_df = df[df['is_manually_labeled'] == True]
        unlabeled_df = df[df['is_manually_labeled'] != True]
        
        print(f"📊 Labeled transactions: {len(labeled_df)}")
        print(f"📊 Unlabeled transactions: {len(unlabeled_df)}")
        
        if len(labeled_df) > 0:
            print(f"\n📋 Categories in training data:")
            category_counts = labeled_df['category'].value_counts()
            for category, count in category_counts.items():
                print(f"   {category}: {count} transactions")
            
            # Show sample labeled transactions
            print(f"\n📋 Sample labeled transactions:")
            for i, (_, row) in enumerate(labeled_df.head(5).iterrows()):
                print(f"   {i+1}. {row['category']}/{row['sub_category']} (Hash: {row['hash_id'][:8]}...)")
        
        return len(labeled_df)
        
    except Exception as e:
        print(f"❌ Error analyzing training data: {str(e)}")
        return 0


def check_data_consistency():
    """Check if labeling history matches training data"""
    print(f"\n🔍 Checking Data Consistency")
    print("=" * 60)
    
    try:
        # Load both files
        labeling_history_path = Path("bank_analyzer_config/ml_data/labeling_history.csv")
        unique_transactions_path = Path("bank_analyzer_config/ml_data/unique_transactions.csv")
        
        if not labeling_history_path.exists() or not unique_transactions_path.exists():
            print("❌ Missing required files")
            return
        
        history_df = pd.read_csv(labeling_history_path)
        training_df = pd.read_csv(unique_transactions_path)
        
        # Get unique hash IDs from each
        history_hashes = set(history_df['hash_id'].unique())
        training_hashes = set(training_df[training_df['is_manually_labeled'] == True]['hash_id'].unique())
        
        print(f"📊 Hash IDs in labeling history: {len(history_hashes)}")
        print(f"📊 Hash IDs in training data: {len(training_hashes)}")
        
        # Check overlap
        overlap = history_hashes.intersection(training_hashes)
        missing_from_training = history_hashes - training_hashes
        extra_in_training = training_hashes - history_hashes
        
        print(f"📊 Overlap: {len(overlap)}")
        print(f"📊 Missing from training: {len(missing_from_training)}")
        print(f"📊 Extra in training: {len(extra_in_training)}")
        
        if len(missing_from_training) > 0:
            print(f"\n⚠️ Some labeled transactions are missing from training data!")
            print(f"   Missing: {len(missing_from_training)} transactions")
        
        if len(overlap) == len(history_hashes):
            print(f"\n✅ All labeled transactions are in training data!")
        
        return len(overlap) == len(history_hashes)
        
    except Exception as e:
        print(f"❌ Error checking consistency: {str(e)}")
        return False


def main():
    """Main analysis function"""
    print("🔍 Training Data Analysis: Old vs New Labels")
    print("=" * 70)
    print("Analyzing your training data to see if it includes both old and new labels")
    print("")
    
    # Analyze labeling history
    recent_count, older_count, unique_count = analyze_labeling_history()
    
    # Analyze current training data
    training_labeled_count = analyze_current_training_data()
    
    # Check consistency
    is_consistent = check_data_consistency()
    
    # Summary
    print("\n" + "=" * 70)
    print("🎯 SUMMARY: Does your training data include old + new labels?")
    print("=" * 70)
    
    print(f"📊 Labeling History Analysis:")
    print(f"   Recent labels (last 7 days): {recent_count}")
    print(f"   Older labels: {older_count}")
    print(f"   Total unique labeled: {unique_count}")
    
    print(f"\n📊 Training Data Analysis:")
    print(f"   Labeled transactions in training: {training_labeled_count}")
    print(f"   Data consistency: {'✅ Good' if is_consistent else '❌ Issues found'}")
    
    # Answer the user's question
    print(f"\n🎯 ANSWER TO YOUR QUESTION:")
    
    if training_labeled_count >= 200 and recent_count >= 20:
        print("✅ YES - Your training data appears to include both:")
        print(f"   • Old labeled data: ~{older_count} transactions")
        print(f"   • New labeled data: ~{recent_count} transactions")
        print(f"   • Total in training: {training_labeled_count} transactions")
    elif training_labeled_count >= 200:
        print("⚠️ MOSTLY YES - You have substantial training data:")
        print(f"   • Total in training: {training_labeled_count} transactions")
        print(f"   • But recent labeling activity: {recent_count} (may be low)")
    elif recent_count >= 20:
        print("⚠️ PARTIALLY - You have recent labels but may be missing old data:")
        print(f"   • Recent labels: {recent_count}")
        print(f"   • Training data: {training_labeled_count}")
    else:
        print("❌ UNCLEAR - Limited data found:")
        print(f"   • Training data: {training_labeled_count}")
        print(f"   • Recent labels: {recent_count}")
    
    # Training failure explanation
    print(f"\n💡 Why training failed:")
    print(f"   • Model accuracy too low (26%) - need more diverse training data")
    print(f"   • Many categories have insufficient examples")
    print(f"   • Subcategory models failed due to limited text variety")
    
    print(f"\n🔧 Recommendations:")
    print(f"   1. Add more diverse transaction descriptions for each category")
    print(f"   2. Ensure each category has at least 10-15 examples")
    print(f"   3. Try training with fewer, more populated categories first")


if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ Analysis failed: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
