# Personal Finance Dashboard

A comprehensive desktop application for personal finance management built with PySide6 (Qt6).

## Features

### Core Modules
1. **Expense Tracker** - Track daily expenses with categories and subcategories
2. **Goal Income Tracker** - Set and monitor income goals with progress tracking
3. **Habit Tracker** - Monitor daily habits with streak counting
4. **Attendance Tracker** - ✅ **FULLY FUNCTIONAL** - Track attendance with 8-period system + Holiday Management
5. **Advanced To-Do List** - Manage tasks with priorities and deadlines
6. **Investment Tracker** - Monitor investment portfolio performance
7. **Budget Planning** - Plan and track monthly budgets

### Key Features
- **Modern UI** with collapsible sidebar navigation
- **Dark/Light themes** for comfortable viewing
- **CSV data storage** with planned SQL migration
- **Auto-save functionality** to prevent data loss
- **Global search** across all modules
- **Multi-column sorting** for data tables
- **Backup and restore** functionality
- **Responsive design** for different screen sizes

## Installation

### Prerequisites
- Python 3.8 or higher
- pip package manager

### Setup
1. Clone or download the project
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

### Running the Application
```bash
python main.py
```

## Project Structure

```
personal-finance-dashboard/
├── main.py                 # Application entry point
├── requirements.txt        # Python dependencies
├── config.json            # Application configuration
├── src/                   # Source code
│   ├── core/              # Core functionality
│   │   ├── config.py      # Configuration management
│   │   └── data_manager.py # Data persistence layer
│   ├── ui/                # User interface
│   │   ├── main_window.py # Main application window
│   │   ├── sidebar.py     # Navigation sidebar
│   │   ├── dashboard.py   # Dashboard widget
│   │   └── styles.py      # Theme and styling
│   └── modules/           # Feature modules
├── data/                  # CSV data files
├── assets/               # Icons and images
└── backups/              # Data backups
```

## 🎉 Recent Major Update: Attendance Module Fixed!

The attendance module has been **completely rewritten** and is now **fully functional**!

### 🔧 What Was Fixed
- **❌ Before**: Pandas recursion errors causing application crashes
- **✅ After**: Stable, efficient CSV-based implementation
- **🚀 Result**: Attendance module now works perfectly without any crashes

### 🌟 New Features
- **📅 Calendar Integration**: Easy date selection with visual calendar
- **⚡ Quick Actions**: One-click buttons for marking all present/absent/holiday
- **📊 Real-time Statistics**: Live attendance percentage calculations
- **🛡️ Error Handling**: Comprehensive validation and error recovery
- **💾 Data Persistence**: Reliable CSV storage with automatic backups
- **🎨 Modern UI**: Clean, intuitive interface with responsive design

### 🧪 Thoroughly Tested
- ✅ All functionality tests pass
- ✅ CSV operations verified
- ✅ Error handling comprehensive
- ✅ UI components working perfectly
- ✅ Integration with dashboard confirmed

**The attendance module is now ready for production use!**

## 🎉 Latest Update: Holiday Management System!

The application now includes a comprehensive **Holiday Management System**:

### 🌟 **New Features Added**
- **🏖️ Automatic Holiday Integration**: Fetch holidays from Calendarific API (200+ countries)
- **⚙️ Settings Tab**: Complete settings dialog with holiday management
- **🔄 API Integration**: Real-time holiday data from official sources
- **📅 Smart Application**: Holidays automatically applied to attendance system
- **🌍 Multi-Country Support**: India, US, UK, Canada, Australia, and more
- **📊 Data Editing**: Edit/delete functionality for all modules (expenses, income, habits, attendance)

### 🔧 **How It Works**
1. **Access Settings**: Go to Tools → Settings → Holiday Management
2. **Select Country & Year**: Choose your location and year
3. **Fetch Holidays**: One-click holiday retrieval from API
4. **Apply to Attendance**: Select and apply holidays to your attendance records
5. **Automatic Integration**: Holidays marked in attendance with proper notes

### 🎯 **Benefits**
- **No Manual Entry**: Automatic holiday detection and application
- **Always Up-to-Date**: Latest holiday data from official sources
- **Flexible Selection**: Choose which holidays to apply
- **Seamless Integration**: Works perfectly with existing attendance system

**The holiday management system is now ready for production use!**

## 🎓 Latest Update: B.Tech Semester Management System!

The attendance system now includes a comprehensive **B.Tech Semester Management System**:

### 🌟 **New Semester Features**
- **📚 8-Semester System**: Complete B.Tech program structure (4 years, 2 semesters each)
- **📅 Smart Date Management**: Automatic semester detection based on dates
- **📊 Semester-Specific Statistics**: Attendance calculations per semester
- **🔄 Active Semester Tracking**: Easy switching between semesters
- **📈 Working Days Calculation**: Excludes weekends and holidays automatically
- **💾 Persistent Storage**: Semester data saved and restored

### 🎯 **How It Works**
1. **Automatic Setup**: 8 semesters created with proper date ranges
2. **Smart Detection**: Attendance records automatically assigned to correct semester
3. **Semester Management**: Easy editing of semester dates and academic years
4. **Real-time Statistics**: Attendance percentage calculated per semester
5. **Active Semester**: Set current semester for focused tracking

### 📋 **Semester Structure**
- **Semester 1-2**: First Year (Odd: July-Dec, Even: Jan-June)
- **Semester 3-4**: Second Year (Odd: July-Dec, Even: Jan-June)
- **Semester 5-6**: Third Year (Odd: July-Dec, Even: Jan-June)
- **Semester 7-8**: Fourth Year (Odd: July-Dec, Even: Jan-June)

### 🔧 **Benefits**
- **Accurate Tracking**: Semester-specific attendance percentages
- **Easy Management**: Visual semester management interface
- **Automatic Assignment**: Records automatically assigned to correct semester
- **Flexible Dates**: Customizable semester start and end dates
- **Holiday Integration**: Works seamlessly with holiday management

**The B.Tech semester management system is now ready for production use!**

## 💰 Latest Update: Enhanced Income Tracker with Time Periods!

The income tracker now includes comprehensive **time period analysis** capabilities:

### 🌟 **New Time Period Features**
- **📅 Weekly View**: Existing detailed weekly breakdown with daily progress
- **📊 Monthly View**: NEW - Complete monthly overview with weekly breakdown
- **📈 Yearly View**: NEW - Annual summary with monthly breakdown
- **🔄 Easy Navigation**: Previous/Next buttons for all time periods
- **📋 All Records**: Comprehensive table view for editing historical data
- **📊 Smart Statistics**: Time-period specific calculations and goals

### 🎯 **Monthly View Features**
- **💰 Monthly Summary Cards**: Total earned, average per day, goal progress
- **📊 Progress Tracking**: Visual progress bars and percentage completion
- **📅 Weekly Breakdown**: See performance week by week within the month
- **🎯 Working Days Calculation**: Excludes weekends for accurate averages
- **🔄 Month Navigation**: Easy browsing through different months

### 📈 **Yearly View Features**
- **💎 Annual Overview**: Total earnings, best month, annual goal progress
- **📊 Monthly Breakdown**: Performance comparison across all 12 months
- **📈 Trend Analysis**: Visual representation of monthly performance
- **🏆 Best Performance**: Highlights your most successful months
- **🎯 Goal Tracking**: Annual goal setting and progress monitoring

### 🔧 **Technical Improvements**
- **📊 Enhanced Data Models**: New methods for monthly and yearly summaries
- **🎨 Responsive UI**: Clean, modern interface for all time periods
- **⚡ Efficient Calculations**: Optimized data processing for large datasets
- **🔄 Real-time Updates**: All views update automatically when data changes
- **💾 Data Persistence**: All time period data saved and restored correctly

### 🎯 **Benefits**
- **📊 Better Analysis**: Understand income patterns across different time periods
- **🎯 Goal Setting**: Set and track daily, monthly, and annual income goals
- **📈 Trend Identification**: Spot seasonal patterns and growth trends
- **💡 Informed Decisions**: Make better financial decisions with comprehensive data
- **⏰ Time Management**: Optimize work schedule based on performance data

**The enhanced income tracker with time periods is now ready for production use!**

## 🎨 Latest Update: UI/UX Improvements for Better Information Density!

The entire application now features **optimized UI/UX design** for improved usability:

### 🌟 **UI/UX Enhancements**
- **📏 Optimized Spacing**: Reduced excessive margins and spacing throughout the application
- **⚡ Enhanced Quick Actions**: Improved visibility and styling of Quick Action buttons
- **📊 Better Information Density**: More content visible without scrolling
- **🎯 Compact Layouts**: Streamlined design for efficient space utilization
- **🎨 Consistent Styling**: Unified design language across all modules

### 🔧 **Specific Improvements**
- **📐 Module Layouts**: Reduced margins from 20px to 10px, spacing from 15px to 8px
- **🔘 Quick Action Buttons**: Enhanced styling with better contrast and hover effects
- **📱 Sidebar Optimization**: More compact navigation with reduced spacing
- **📋 GroupBox Styling**: Optimized title positioning and padding
- **🎯 Header Heights**: Reduced from 60px to 50px for better space efficiency

### 🎯 **Benefits**
- **📊 More Information**: See more data without scrolling
- **⚡ Better Usability**: Quick Actions are more prominent and accessible
- **🎨 Modern Design**: Clean, professional appearance
- **📱 Responsive Layout**: Better use of available screen space
- **👁️ Reduced Eye Strain**: Optimized spacing for comfortable viewing

### 🧪 **Quality Assurance**
- **✅ All Modules Tested**: Expenses, Income, Habits, Attendance
- **✅ Cross-Theme Compatibility**: Works with both dark and light themes
- **✅ Widget Creation**: All widgets create successfully with optimized layouts
- **✅ Style Consistency**: Unified styling across the entire application

**The UI/UX improvements are now ready for production use!**

## Development Status

### ✅ Completed
- [x] Project setup and structure
- [x] Main application framework
- [x] Collapsible sidebar navigation
- [x] Dashboard with overview cards
- [x] Dark/Light theme support
- [x] CSV data management layer
- [x] Configuration system
- [x] Auto-save functionality

### ✅ Recently Completed
- [x] **Attendance Tracker module** - Fully functional with comprehensive features
  - ✅ Period-wise attendance tracking (8 periods per day)
  - ✅ Calendar-based date selection
  - ✅ Holiday management and statistics
  - ✅ CSV-based data storage with backups
  - ✅ Comprehensive error handling and validation
  - ✅ Real-time attendance percentage calculations
  - ✅ Fixed pandas recursion issues (complete rewrite)

### 🚧 In Progress
- [ ] Expense Tracker module
- [ ] Goal Income Tracker module
- [ ] Habit Tracker module
- [ ] Advanced To-Do List module
- [ ] Investment Tracker module
- [ ] Budget Planning module

### 📋 Planned
- [ ] Global search functionality
- [ ] Data import/export
- [ ] Advanced reporting
- [ ] SQL database migration
- [ ] API integrations for investment data
- [ ] Mobile companion app

## Configuration

The application uses a `config.json` file for settings. Key configurations include:

- **Theme**: "dark" or "light"
- **Data Directory**: Location for CSV files
- **Auto-save Interval**: Frequency of automatic saves
- **Window Settings**: Size and position preferences
- **Module Settings**: Default categories, goals, etc.

## Data Storage

Currently uses CSV files for data storage with the following benefits:
- **Human-readable** format
- **Easy backup** and migration
- **Excel compatibility** for data analysis
- **Simple structure** for rapid development

Future migration to SQL databases (SQLite/PostgreSQL) is planned for:
- Better performance with large datasets
- Advanced querying capabilities
- Data integrity constraints
- Concurrent access support

## Contributing

This is a personal project, but suggestions and feedback are welcome!

### Development Guidelines
- Follow PEP 8 style guidelines
- Use type hints where appropriate
- Write docstrings for all classes and methods
- Test new features thoroughly
- Maintain backward compatibility with existing data

## License

This project is for personal use. Feel free to adapt for your own needs.

## Support

For issues or questions, please check the documentation or create an issue in the project repository.
