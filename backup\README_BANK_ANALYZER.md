# Bank Statement Analyzer

A comprehensive Python application for analyzing bank statements and importing transactions into your Personal Finance Dashboard. Built with PySide6 for a modern, user-friendly interface.

## Features

### 📄 Multi-Format Support
- **PDF Statements**: Automatic text extraction and parsing
- **CSV Files**: Intelligent column detection and mapping
- **Excel Files**: Support for .xlsx and .xls formats with automatic sheet detection

### 🤖 Intelligent Categorization
- Automatic transaction categorization using keyword matching and pattern recognition
- Integration with existing expense categories from your main application
- Confidence scoring for categorization accuracy
- Support for adding new categories when needed

### 🔍 Preview & Review Interface
- Comprehensive transaction preview with editing capabilities
- Filter and search functionality
- Confidence-based highlighting (green/yellow/red)
- Bulk editing and validation tools
- Category suggestion system

### 📊 Data Management
- Automatic data validation against main application structure
- Backup creation before import
- Duplicate detection and handling
- Missing category creation
- Import preview with statistics

### 🛡️ Robust Error Handling
- Comprehensive logging throughout the application
- Detailed error reporting and recovery
- File format validation
- Transaction validation with detailed error messages

## Installation

### Prerequisites
Ensure you have the main Personal Finance Dashboard application set up first.

### Install Additional Dependencies
```bash
pip install -r bank_analyzer_requirements.txt
```

### Required Libraries
- **PyPDF2**: PDF text extraction
- **pdfplumber**: Advanced PDF table extraction
- **openpyxl**: Excel file processing
- **xlrd**: Legacy Excel file support
- **pandas**: Data processing (should already be installed)
- **PySide6**: GUI framework (should already be installed)

## Usage

### 1. Launch the Application
```bash
python bank_statement_analyzer.py
```

### 2. Select Bank Statement Files
- Click "Select Files..." or use Ctrl+O
- Choose one or more bank statement files
- Supported formats: PDF, CSV, Excel (.xlsx, .xls)
- The application defaults to the `statements` folder

### 3. Process Files
- Click "Process Files" to start analysis
- The application will:
  - Parse all selected files
  - Extract transaction data
  - Automatically categorize transactions
  - Show progress in real-time

### 4. Preview & Edit Transactions
- Review all parsed transactions in the preview table
- Edit individual transactions by double-clicking or using "Edit Selected"
- Use filters to focus on specific categories, types, or confidence levels
- Search through transaction descriptions
- Validate all transactions before import

### 5. Manage Categories
- Add new categories and subcategories as needed
- View existing category structure
- Delete or modify categories
- Categories are automatically synchronized with the main application

### 6. Import to Main Application
- Review import summary with statistics
- Choose import options (backup, category creation)
- Click "Import to Main Application"
- Automatic backup creation and data validation

## File Structure

```
bank_analyzer/
├── __init__.py
├── core/
│   ├── __init__.py
│   ├── logger.py          # Comprehensive logging setup
│   ├── categorizer.py     # Transaction categorization engine
│   └── data_importer.py   # Data import and validation
├── models/
│   ├── __init__.py
│   └── transaction.py     # Transaction data models
├── parsers/
│   ├── __init__.py
│   ├── base_parser.py     # Base parser class
│   ├── pdf_parser.py      # PDF statement parser
│   ├── csv_parser.py      # CSV statement parser
│   ├── excel_parser.py    # Excel statement parser
│   └── parser_factory.py  # Parser factory and format detection
└── ui/
    ├── __init__.py
    ├── main_window.py      # Main application window
    ├── transaction_preview.py  # Transaction preview and editing
    └── category_manager.py # Category management interface
```

## Supported Bank Formats

### PDF Statements
- State Bank of India (SBI)
- Indian Bank
- Generic bank statement formats
- Automatic format detection based on content

### CSV Statements
- Automatic column detection for:
  - Date columns (various formats)
  - Description/Particulars
  - Amount (single column or separate debit/credit)
  - Balance information
  - Reference numbers

### Excel Statements
- Automatic sheet detection
- Header row identification
- Data cleaning and preprocessing
- Support for both .xlsx and .xls formats

## Categorization Rules

The application uses intelligent categorization based on:

### Keyword Matching
- Food & Dining: restaurant, cafe, grocery, zomato, swiggy
- Transportation: fuel, uber, ola, metro, parking
- Shopping: amazon, flipkart, clothing, electronics
- Bills & Utilities: electricity, internet, mobile, recharge
- Healthcare: doctor, pharmacy, hospital, medical

### Pattern Recognition
- Regular expressions for complex patterns
- Transaction amount analysis
- Date-based categorization
- Merchant name recognition

### Confidence Scoring
- **High (>80%)**: Green highlighting, automatic acceptance
- **Medium (50-80%)**: Yellow highlighting, review recommended
- **Low (<50%)**: Red highlighting, manual review required

## Data Validation

### Transaction Validation
- Required fields: date, category, subcategory, amount, transaction mode
- Date format validation
- Amount range validation
- Category existence verification

### Import Validation
- Duplicate ID detection
- Main application structure verification
- Category consistency checking
- Data integrity validation

## Logging

Comprehensive logging is implemented throughout the application:

### Log Levels
- **INFO**: Normal operation events
- **WARNING**: Potential issues that don't stop processing
- **ERROR**: Errors that affect functionality
- **DEBUG**: Detailed debugging information

### Log Files
- Location: `logs/bank_analyzer_YYYYMMDD.log`
- Automatic rotation (10MB max, 5 backup files)
- Console output for warnings and errors

## Troubleshooting

### Common Issues

#### PDF Parsing Problems
- **Issue**: PDF text extraction fails
- **Solution**: Install additional PDF libraries or try converting to CSV/Excel

#### Column Detection Issues
- **Issue**: CSV columns not detected correctly
- **Solution**: Check column headers, ensure standard naming conventions

#### Category Mapping Problems
- **Issue**: Poor categorization accuracy
- **Solution**: Add custom rules, review and edit transactions manually

#### Import Failures
- **Issue**: Import to main application fails
- **Solution**: Validate data structure, check file permissions, review error logs

### Error Recovery
- All operations include comprehensive error handling
- Automatic backup creation before imports
- Detailed error messages with suggested solutions
- Log files contain full error traces for debugging

## Integration with Main Application

The Bank Statement Analyzer is designed to seamlessly integrate with your Personal Finance Dashboard:

### Data Structure Compatibility
- Uses the same CSV structure as the main application
- Maintains category consistency
- Preserves data relationships

### Category Synchronization
- Reads existing categories from main application
- Adds new categories automatically
- Maintains category hierarchy

### Import Process
- Validates against existing data
- Prevents duplicate imports
- Creates backups automatically
- Updates main application files directly

## Advanced Features

### Batch Processing
- Process multiple files simultaneously
- Progress tracking for large files
- Background processing to maintain UI responsiveness

### Custom Rules
- Add custom categorization rules
- Pattern-based matching
- Keyword-based classification
- Confidence weighting

### Data Export
- Export processed transactions before import
- Generate reports and statistics
- Backup and restore functionality

## Support

For issues, questions, or feature requests:
1. Check the log files in the `logs/` directory
2. Review the troubleshooting section above
3. Ensure all dependencies are properly installed
4. Verify main application integration

## Version History

### v1.0.0
- Initial release
- Multi-format parsing (PDF, CSV, Excel)
- Automatic categorization
- Preview and editing interface
- Category management
- Data import and validation
- Comprehensive logging
