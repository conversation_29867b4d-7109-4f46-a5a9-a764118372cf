# Personal Finance Dashboard - User Guide

## 🚀 Getting Started

### First Launch
1. **Run the application**: `python main.py`
2. **Explore the dashboard**: Overview of your financial status
3. **Navigate modules**: Use the sidebar to switch between features
4. **Add your first data**: Start with expenses or income

### Interface Overview
- **Sidebar**: Navigate between modules (collapsible with Ctrl+B)
- **Main Content**: Current module's interface
- **Menu Bar**: File operations, tools, and settings
- **Status Bar**: Application status and record counts

## 📊 Module Guide

### 1. Dashboard
**Purpose**: Central overview of your financial status

**Features**:
- Financial summary cards (income, expenses, savings)
- Recent activity feed
- Quick action buttons
- Module status overview

**How to Use**:
- View at-a-glance financial metrics
- Click "Refresh" to update with latest data
- Use quick action buttons to jump to specific modules

### 2. Expense Tracker 💸
**Purpose**: Track and categorize your expenses

**Features**:
- Add expenses with amount, category, and description
- Filter by date range and category
- View expense analytics and trends
- Category management

**How to Use**:
1. Click "➕ Add Expense" to record new expenses
2. Fill in amount, select category, add description
3. Use filters to view specific time periods or categories
4. Review spending patterns in the summary

**Tips**:
- Use descriptive names for better tracking
- Categorize consistently for accurate analytics
- Review expenses regularly to identify spending patterns

### 3. Income Tracker 💰
**Purpose**: Set income goals and track progress

**Features**:
- Set monthly/yearly income goals
- Track multiple income sources
- Progress visualization
- Goal achievement tracking

**How to Use**:
1. Click "➕ Add Income Goal" to set targets
2. Record income as it comes in
3. Monitor progress toward goals
4. Adjust goals as needed

**Tips**:
- Set realistic, achievable goals
- Track all income sources separately
- Review and adjust goals quarterly

### 4. Habit Tracker 🎯
**Purpose**: Build and maintain positive daily habits

**Features**:
- Predefined habit templates
- Daily completion tracking
- Streak counting
- Progress visualization

**How to Use**:
1. Select from predefined habits or create custom ones
2. Mark habits as complete each day
3. Build streaks for motivation
4. Review progress over time

**Tips**:
- Start with 2-3 habits maximum
- Be consistent with daily tracking
- Celebrate streak milestones

### 5. To-Do List 📝
**Purpose**: Manage tasks and priorities effectively

**Features**:
- Task creation with priorities
- Category organization
- Status tracking (Not Started, In Progress, Completed)
- Due date management

**How to Use**:
1. Click "➕ Add Task" to create new tasks
2. Set priority levels (Low, Medium, High)
3. Organize with categories
4. Update status as you progress

**Tips**:
- Break large tasks into smaller ones
- Use priorities to focus on important items
- Review and update regularly

### 6. Investment Tracker 📈
**Purpose**: Monitor your investment portfolio

**Features**:
- Track stocks, mutual funds, and other investments
- Automatic profit/loss calculations
- Portfolio performance metrics
- Investment type categorization

**How to Use**:
1. Click "➕ Add Investment" to record holdings
2. Enter purchase details and current values
3. Update current prices regularly
4. Review portfolio performance

**Tips**:
- Update prices regularly for accurate tracking
- Diversify across investment types
- Monitor long-term performance trends

### 7. Budget Planner 💰
**Purpose**: Plan and track your monthly budgets

**Features**:
- Create monthly/yearly budget plans
- Track planned vs actual spending
- Budget health scoring
- Variance analysis

**How to Use**:
1. Click "➕ New Budget Plan" to create budgets
2. Set planned amounts for income and expenses
3. Update with actual amounts as they occur
4. Monitor budget health score

**Tips**:
- Be realistic with budget amounts
- Review and adjust monthly
- Use health score to gauge performance

## 🔧 Global Features

### Global Search (Ctrl+F)
**Purpose**: Search across all modules simultaneously

**How to Use**:
1. Press Ctrl+F or use Tools → Global Search
2. Enter search term (minimum 2 characters)
3. Filter by specific modules if needed
4. Click results to view details

**Search Tips**:
- Use specific terms for better results
- Search works across descriptions, categories, and names
- Filter by module to narrow results

### Themes
**Purpose**: Customize the application appearance

**How to Use**:
1. Go to View → Theme
2. Select Light or Dark theme
3. Theme applies immediately

### Backup & Restore
**Purpose**: Protect your data

**Features**:
- Automatic backups created regularly
- Manual backup creation
- Data stored in CSV format for portability

**How to Use**:
1. Use File → Create Backup for manual backups
2. Backups stored in `data/.backups/` directory
3. CSV files can be opened in Excel or other programs

## ⌨️ Keyboard Shortcuts

- `Ctrl+N` - New entry in current module
- `Ctrl+F` - Global search
- `Ctrl+B` - Toggle sidebar
- `Ctrl+Q` - Quit application

## 📁 Data Management

### Data Location
- **Main Data**: `data/` directory
- **Backups**: `data/.backups/` directory
- **Logs**: `logs/app_debug.log`

### Data Format
- **CSV Files**: Human-readable, Excel-compatible
- **Automatic Saving**: Changes saved immediately
- **Backup System**: Regular automatic backups

### Data Portability
- Export data by copying CSV files
- Import by placing CSV files in appropriate directories
- Compatible with Excel and other spreadsheet programs

## 🐛 Troubleshooting

### Common Issues

**Application won't start**:
- Check Python version (3.8+ required)
- Verify all dependencies installed: `pip install -r requirements.txt`
- Check logs in `logs/app_debug.log`

**Data not saving**:
- Check file permissions in data directory
- Ensure sufficient disk space
- Verify data directory is writable

**Module not loading**:
- Check error messages in status bar
- Review logs for detailed error information
- Restart application

**Performance issues**:
- Close and restart the application
- Check available system memory
- Clear old log files if disk space is low

### Getting Help
- Check the status bar for error messages
- Review logs in `logs/app_debug.log`
- Use Help → About for version information

## 💡 Best Practices

### Data Entry
- **Be Consistent**: Use consistent naming and categorization
- **Be Descriptive**: Add meaningful descriptions for better tracking
- **Be Regular**: Update data regularly for accuracy

### Organization
- **Use Categories**: Organize data with meaningful categories
- **Set Goals**: Use goal-setting features for motivation
- **Review Regularly**: Check progress and adjust as needed

### Backup
- **Regular Backups**: Create manual backups before major changes
- **Multiple Locations**: Store backups in multiple locations
- **Test Restores**: Verify backups work by testing restoration

---

**Need more help?** Check the logs in `logs/app_debug.log` for detailed information about any issues.
