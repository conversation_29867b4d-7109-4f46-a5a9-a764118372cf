"""
Data import and validation module
Handles importing processed transactions into the main application
"""

import pandas as pd
import shutil
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
import logging

from ..models.transaction import ProcessedTransaction
from ..core.logger import get_logger


class DataImporter:
    """
    Handles importing processed transactions into the main application's data structure
    """
    
    def __init__(self, main_app_data_dir: str = "data"):
        self.logger = get_logger(__name__)
        self.main_app_data_dir = Path(main_app_data_dir)
        
        # Main application file paths
        self.expenses_file = self.main_app_data_dir / "expenses" / "expenses.csv"
        self.categories_file = self.main_app_data_dir / "expenses" / "categories.csv"
        
        # Expected column structure for main application
        self.expense_columns = [
            'id', 'date', 'type', 'category', 'sub_category',
            'transaction_mode', 'amount', 'notes', 'created_at', 'updated_at'
        ]
        
        self.category_columns = [
            'id', 'category', 'sub_category', 'is_active', 'created_at'
        ]
    
    def validate_main_app_structure(self) -> Dict[str, Any]:
        """
        Validate that the main application data structure exists and is accessible
        
        Returns:
            Dictionary with validation results
        """
        result = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'expenses_file_exists': False,
            'categories_file_exists': False,
            'data_dir_exists': False,
            'expenses_dir_exists': False
        }
        
        try:
            # Check data directory
            if not self.main_app_data_dir.exists():
                result['errors'].append(f"Main application data directory not found: {self.main_app_data_dir}")
                result['is_valid'] = False
                return result
            
            result['data_dir_exists'] = True
            
            # Check expenses directory
            expenses_dir = self.main_app_data_dir / "expenses"
            if not expenses_dir.exists():
                result['warnings'].append(f"Expenses directory not found: {expenses_dir}")
                # Try to create it
                try:
                    expenses_dir.mkdir(parents=True, exist_ok=True)
                    result['warnings'].append("Created expenses directory")
                except Exception as e:
                    result['errors'].append(f"Could not create expenses directory: {str(e)}")
                    result['is_valid'] = False
                    return result
            
            result['expenses_dir_exists'] = True
            
            # Check expenses file
            if self.expenses_file.exists():
                result['expenses_file_exists'] = True
                # Validate structure
                try:
                    df = pd.read_csv(self.expenses_file)
                    missing_columns = set(self.expense_columns) - set(df.columns)
                    if missing_columns:
                        result['errors'].append(f"Expenses file missing columns: {missing_columns}")
                        result['is_valid'] = False
                except Exception as e:
                    result['errors'].append(f"Could not read expenses file: {str(e)}")
                    result['is_valid'] = False
            else:
                result['warnings'].append("Expenses file does not exist, will be created")
            
            # Check categories file
            if self.categories_file.exists():
                result['categories_file_exists'] = True
                # Validate structure
                try:
                    df = pd.read_csv(self.categories_file)
                    missing_columns = set(self.category_columns) - set(df.columns)
                    if missing_columns:
                        result['errors'].append(f"Categories file missing columns: {missing_columns}")
                        result['is_valid'] = False
                except Exception as e:
                    result['errors'].append(f"Could not read categories file: {str(e)}")
                    result['is_valid'] = False
            else:
                result['warnings'].append("Categories file does not exist, will be created")
            
        except Exception as e:
            result['errors'].append(f"Validation error: {str(e)}")
            result['is_valid'] = False
        
        return result
    
    def backup_existing_data(self) -> Optional[str]:
        """
        Create backup of existing data files before import
        
        Returns:
            Backup directory path or None if backup failed
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_dir = self.main_app_data_dir.parent / "backups" / f"bank_import_{timestamp}"
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            # Backup expenses file if it exists
            if self.expenses_file.exists():
                backup_expenses = backup_dir / "expenses.csv"
                shutil.copy2(self.expenses_file, backup_expenses)
                self.logger.info(f"Backed up expenses file to {backup_expenses}")
            
            # Backup categories file if it exists
            if self.categories_file.exists():
                backup_categories = backup_dir / "categories.csv"
                shutil.copy2(self.categories_file, backup_categories)
                self.logger.info(f"Backed up categories file to {backup_categories}")
            
            self.logger.info(f"Backup created at {backup_dir}")
            return str(backup_dir)
            
        except Exception as e:
            self.logger.error(f"Failed to create backup: {str(e)}")
            return None
    
    def validate_transactions(self, transactions: List[ProcessedTransaction]) -> Dict[str, Any]:
        """
        Validate transactions before import
        
        Args:
            transactions: List of processed transactions to validate
            
        Returns:
            Dictionary with validation results
        """
        result = {
            'is_valid': True,
            'total_transactions': len(transactions),
            'valid_transactions': 0,
            'invalid_transactions': 0,
            'errors': [],
            'warnings': [],
            'duplicate_ids': [],
            'missing_categories': set(),
            'validation_details': []
        }
        
        if not transactions:
            result['errors'].append("No transactions to validate")
            result['is_valid'] = False
            return result
        
        # Load existing data to check for duplicates
        existing_ids = set()
        if self.expenses_file.exists():
            try:
                existing_df = pd.read_csv(self.expenses_file)
                existing_ids = set(existing_df['id'].astype(str))
            except Exception as e:
                result['warnings'].append(f"Could not load existing expenses for duplicate check: {str(e)}")
        
        # Load existing categories
        existing_categories = set()
        if self.categories_file.exists():
            try:
                categories_df = pd.read_csv(self.categories_file)
                for _, row in categories_df.iterrows():
                    existing_categories.add((row['category'], row['sub_category']))
            except Exception as e:
                result['warnings'].append(f"Could not load existing categories: {str(e)}")
        
        # Validate each transaction
        transaction_ids = set()
        
        for i, transaction in enumerate(transactions):
            transaction_errors = []
            
            # Basic validation
            validation_errors = transaction.validate()
            if validation_errors:
                transaction_errors.extend(validation_errors)
            
            # Check for duplicate IDs within the import batch
            if transaction.id in transaction_ids:
                transaction_errors.append(f"Duplicate ID within import batch: {transaction.id}")
            else:
                transaction_ids.add(transaction.id)
            
            # Check for duplicate IDs with existing data
            if transaction.id in existing_ids:
                result['duplicate_ids'].append(transaction.id)
                transaction_errors.append(f"ID already exists in main application: {transaction.id}")
            
            # Check if category exists
            category_pair = (transaction.category, transaction.sub_category)
            if category_pair not in existing_categories:
                result['missing_categories'].add(category_pair)
            
            # Record validation result
            if transaction_errors:
                result['invalid_transactions'] += 1
                result['validation_details'].append({
                    'index': i,
                    'id': transaction.id,
                    'errors': transaction_errors
                })
            else:
                result['valid_transactions'] += 1
        
        # Overall validation result
        if result['invalid_transactions'] > 0:
            result['is_valid'] = False
            result['errors'].append(f"{result['invalid_transactions']} transactions have validation errors")
        
        if result['duplicate_ids']:
            result['errors'].append(f"{len(result['duplicate_ids'])} transactions have duplicate IDs")
        
        if result['missing_categories']:
            result['warnings'].append(f"{len(result['missing_categories'])} new categories will be created")
        
        return result
    
    def create_missing_categories(self, missing_categories: set) -> bool:
        """
        Create missing categories in the main application
        
        Args:
            missing_categories: Set of (category, sub_category) tuples to create
            
        Returns:
            True if successful, False otherwise
        """
        if not missing_categories:
            return True
        
        try:
            # Load existing categories or create new dataframe
            if self.categories_file.exists():
                categories_df = pd.read_csv(self.categories_file)
                next_id = categories_df['id'].max() + 1 if not categories_df.empty else 1
            else:
                categories_df = pd.DataFrame(columns=self.category_columns)
                next_id = 1
            
            # Create new category records
            new_categories = []
            for category, sub_category in missing_categories:
                new_categories.append({
                    'id': next_id,
                    'category': category,
                    'sub_category': sub_category,
                    'is_active': True,
                    'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                })
                next_id += 1
            
            # Append new categories
            new_df = pd.DataFrame(new_categories)
            updated_df = pd.concat([categories_df, new_df], ignore_index=True)
            
            # Ensure directory exists
            self.categories_file.parent.mkdir(parents=True, exist_ok=True)
            
            # Save updated categories
            updated_df.to_csv(self.categories_file, index=False)
            
            self.logger.info(f"Created {len(missing_categories)} new categories")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to create missing categories: {str(e)}")
            return False

    def import_transactions(self, transactions: List[ProcessedTransaction],
                          create_backup: bool = True,
                          create_categories: bool = True) -> Dict[str, Any]:
        """
        Import processed transactions into the main application

        Args:
            transactions: List of processed transactions to import
            create_backup: Whether to create backup before import
            create_categories: Whether to create missing categories

        Returns:
            Dictionary with import results
        """
        result = {
            'success': False,
            'imported_count': 0,
            'skipped_count': 0,
            'error_count': 0,
            'backup_path': None,
            'errors': [],
            'warnings': []
        }

        try:
            # Validate main application structure
            validation = self.validate_main_app_structure()
            if not validation['is_valid']:
                result['errors'].extend(validation['errors'])
                return result

            # Validate transactions
            transaction_validation = self.validate_transactions(transactions)
            if not transaction_validation['is_valid']:
                result['errors'].extend(transaction_validation['errors'])
                # Continue with valid transactions only if there are any
                if transaction_validation['valid_transactions'] == 0:
                    return result

            # Create backup if requested
            if create_backup:
                backup_path = self.backup_existing_data()
                if backup_path:
                    result['backup_path'] = backup_path
                else:
                    result['warnings'].append("Failed to create backup")

            # Create missing categories if requested
            if create_categories and transaction_validation['missing_categories']:
                if not self.create_missing_categories(transaction_validation['missing_categories']):
                    result['warnings'].append("Failed to create some missing categories")

            # Load existing expenses data
            if self.expenses_file.exists():
                existing_df = pd.read_csv(self.expenses_file)
            else:
                existing_df = pd.DataFrame(columns=self.expense_columns)

            # Convert transactions to dataframe format
            import_data = []
            for transaction in transactions:
                # Skip invalid transactions
                if transaction.validate():
                    result['skipped_count'] += 1
                    continue

                # Skip duplicates
                if not existing_df.empty and transaction.id in existing_df['id'].values:
                    result['skipped_count'] += 1
                    continue

                import_data.append(transaction.to_expense_dict())

            if not import_data:
                result['warnings'].append("No valid transactions to import")
                result['success'] = True
                return result

            # Create dataframe from import data
            import_df = pd.DataFrame(import_data)

            # Combine with existing data
            combined_df = pd.concat([existing_df, import_df], ignore_index=True)

            # Ensure directory exists
            self.expenses_file.parent.mkdir(parents=True, exist_ok=True)

            # Save to file
            combined_df.to_csv(self.expenses_file, index=False)

            result['success'] = True
            result['imported_count'] = len(import_data)

            self.logger.info(f"Successfully imported {result['imported_count']} transactions")

        except Exception as e:
            self.logger.error(f"Import failed: {str(e)}")
            result['errors'].append(f"Import failed: {str(e)}")

        return result

    def get_import_preview(self, transactions: List[ProcessedTransaction]) -> Dict[str, Any]:
        """
        Get preview of what would be imported without actually importing

        Args:
            transactions: List of processed transactions

        Returns:
            Dictionary with preview information
        """
        preview = {
            'total_transactions': len(transactions),
            'valid_transactions': 0,
            'invalid_transactions': 0,
            'new_categories': set(),
            'duplicate_ids': [],
            'total_amount': 0,
            'amount_by_type': {},
            'amount_by_category': {},
            'date_range': {'start': None, 'end': None}
        }

        # Load existing data for duplicate checking
        existing_ids = set()
        if self.expenses_file.exists():
            try:
                existing_df = pd.read_csv(self.expenses_file)
                existing_ids = set(existing_df['id'].astype(str))
            except Exception:
                pass

        # Load existing categories
        existing_categories = set()
        if self.categories_file.exists():
            try:
                categories_df = pd.read_csv(self.categories_file)
                for _, row in categories_df.iterrows():
                    existing_categories.add((row['category'], row['sub_category']))
            except Exception:
                pass

        # Analyze transactions
        valid_transactions = []
        for transaction in transactions:
            if transaction.validate():
                preview['invalid_transactions'] += 1
                continue

            if transaction.id in existing_ids:
                preview['duplicate_ids'].append(transaction.id)
                continue

            valid_transactions.append(transaction)
            preview['valid_transactions'] += 1

            # Check for new categories
            category_pair = (transaction.category, transaction.sub_category)
            if category_pair not in existing_categories:
                preview['new_categories'].add(category_pair)

            # Calculate amounts
            amount = float(transaction.amount)
            preview['total_amount'] += amount

            # Amount by type
            if transaction.type not in preview['amount_by_type']:
                preview['amount_by_type'][transaction.type] = 0
            preview['amount_by_type'][transaction.type] += amount

            # Amount by category
            if transaction.category not in preview['amount_by_category']:
                preview['amount_by_category'][transaction.category] = 0
            preview['amount_by_category'][transaction.category] += amount

            # Date range
            if transaction.date:
                if preview['date_range']['start'] is None or transaction.date < preview['date_range']['start']:
                    preview['date_range']['start'] = transaction.date
                if preview['date_range']['end'] is None or transaction.date > preview['date_range']['end']:
                    preview['date_range']['end'] = transaction.date

        return preview
