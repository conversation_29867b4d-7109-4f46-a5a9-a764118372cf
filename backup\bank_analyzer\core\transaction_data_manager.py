"""
Transaction Data Management System
Comprehensive data management for AI-assisted manual labeling with backup, clear, and restore capabilities
"""

import json
import csv
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, date
from dataclasses import dataclass, asdict
from enum import Enum
import hashlib
import shutil

from .logger import get_logger
from ..models.transaction import RawTransaction, ProcessedTransaction


class SessionStatus(Enum):
    """Status of a transaction session"""
    ACTIVE = "active"
    COMPLETED = "completed"
    ARCHIVED = "archived"
    CLEARED = "cleared"


class DataMergeStrategy(Enum):
    """Strategy for merging transaction data"""
    REPLACE_OLD = "replace_old"
    KEEP_OLD = "keep_old"
    MERGE_ALL = "merge_all"
    ARCHIVE_OLD = "archive_old"


@dataclass
class TransactionSession:
    """Represents a transaction data session"""
    session_id: str
    created_at: datetime
    updated_at: datetime
    status: SessionStatus
    transaction_count: int
    raw_transactions_file: str
    processed_transactions_file: str
    categorization_data_file: str
    description: str = ""
    source_files: List[str] = None
    ml_training_completed: bool = False
    ai_processing_completed: bool = False
    manual_labeling_completed: bool = False
    backup_created: bool = False
    
    def __post_init__(self):
        if self.source_files is None:
            self.source_files = []


@dataclass
class DataMergeOptions:
    """Options for merging transaction data"""
    strategy: DataMergeStrategy
    preserve_categorization: bool = True
    preserve_training_data: bool = True
    create_backup: bool = True
    merge_description: str = ""


class TransactionDataManager:
    """
    Comprehensive transaction data management system
    Handles clearing, backing up, and restoring transaction data with session management
    """
    
    def __init__(self, data_dir: str = "bank_analyzer_config/transaction_sessions"):
        self.logger = get_logger(__name__)
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # Directory structure
        self.sessions_dir = self.data_dir / "sessions"
        self.backups_dir = self.data_dir / "backups"
        self.archives_dir = self.data_dir / "archives"
        
        for dir_path in [self.sessions_dir, self.backups_dir, self.archives_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
        
        # Registry files
        self.sessions_registry_file = self.data_dir / "sessions_registry.json"
        self.active_session_file = self.data_dir / "active_session.json"
        
        # Current session
        self.current_session: Optional[TransactionSession] = None
        self.sessions_registry: Dict[str, TransactionSession] = {}
        
        # Load existing data
        self._load_sessions_registry()
        self._load_active_session()
        
        self.logger.info("Transaction Data Manager initialized")
    
    def create_session(self, raw_transactions: List[RawTransaction], 
                      processed_transactions: List[ProcessedTransaction] = None,
                      description: str = "", source_files: List[str] = None) -> str:
        """
        Create a new transaction session
        
        Args:
            raw_transactions: List of raw transactions
            processed_transactions: List of processed transactions (optional)
            description: Description of the session
            source_files: List of source file names
            
        Returns:
            Session ID
        """
        try:
            # Generate session ID
            session_id = self._generate_session_id(raw_transactions)
            
            # Create session directory
            session_dir = self.sessions_dir / session_id
            session_dir.mkdir(parents=True, exist_ok=True)
            
            # File paths
            raw_file = session_dir / "raw_transactions.json"
            processed_file = session_dir / "processed_transactions.json"
            categorization_file = session_dir / "categorization_data.json"
            
            # Save transaction data
            self._save_raw_transactions(raw_transactions, raw_file)
            if processed_transactions:
                self._save_processed_transactions(processed_transactions, processed_file)
            
            # Create session object
            session = TransactionSession(
                session_id=session_id,
                created_at=datetime.now(),
                updated_at=datetime.now(),
                status=SessionStatus.ACTIVE,
                transaction_count=len(raw_transactions),
                raw_transactions_file=str(raw_file),
                processed_transactions_file=str(processed_file),
                categorization_data_file=str(categorization_file),
                description=description or f"Session created on {datetime.now().strftime('%Y-%m-%d %H:%M')}",
                source_files=source_files or []
            )
            
            # Update registry
            self.sessions_registry[session_id] = session
            self.current_session = session
            
            # Save registry and active session
            self._save_sessions_registry()
            self._save_active_session()
            
            self.logger.info(f"Created transaction session: {session_id} ({len(raw_transactions)} transactions)")
            return session_id
            
        except Exception as e:
            self.logger.error(f"Error creating session: {str(e)}", exc_info=True)
            raise
    
    def backup_current_session(self, backup_description: str = "") -> str:
        """
        Create a backup of the current session
        
        Args:
            backup_description: Description for the backup
            
        Returns:
            Backup ID
        """
        try:
            if not self.current_session:
                raise ValueError("No active session to backup")
            
            # Generate backup ID
            backup_id = f"backup_{self.current_session.session_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            backup_dir = self.backups_dir / backup_id
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            # Copy session files
            session_dir = Path(self.current_session.raw_transactions_file).parent
            
            # Copy all files from session directory
            for file_path in session_dir.glob("*"):
                if file_path.is_file():
                    shutil.copy2(file_path, backup_dir / file_path.name)
            
            # Create backup metadata
            backup_metadata = {
                "backup_id": backup_id,
                "original_session_id": self.current_session.session_id,
                "created_at": datetime.now().isoformat(),
                "description": backup_description or f"Backup of session {self.current_session.session_id}",
                "transaction_count": self.current_session.transaction_count,
                "session_data": asdict(self.current_session)
            }
            
            # Save backup metadata
            with open(backup_dir / "backup_metadata.json", 'w', encoding='utf-8') as f:
                json.dump(backup_metadata, f, indent=2, ensure_ascii=False, default=str)
            
            # Update session to mark backup created
            self.current_session.backup_created = True
            self.current_session.updated_at = datetime.now()
            self._save_sessions_registry()
            
            self.logger.info(f"Created backup: {backup_id}")
            return backup_id
            
        except Exception as e:
            self.logger.error(f"Error creating backup: {str(e)}", exc_info=True)
            raise
    
    def clear_current_session(self, create_backup: bool = True, 
                            archive_session: bool = True) -> bool:
        """
        Clear the current session data
        
        Args:
            create_backup: Whether to create a backup before clearing
            archive_session: Whether to archive the session instead of deleting
            
        Returns:
            True if successful
        """
        try:
            if not self.current_session:
                self.logger.warning("No active session to clear")
                return True
            
            # Create backup if requested
            backup_id = None
            if create_backup:
                backup_id = self.backup_current_session("Pre-clear backup")
            
            # Archive or mark as cleared
            if archive_session:
                self.current_session.status = SessionStatus.ARCHIVED
                # Move session to archives
                archive_dir = self.archives_dir / self.current_session.session_id
                session_dir = Path(self.current_session.raw_transactions_file).parent
                
                if not archive_dir.exists():
                    shutil.move(str(session_dir), str(archive_dir))
                    
                    # Update file paths in session
                    self.current_session.raw_transactions_file = str(archive_dir / "raw_transactions.json")
                    self.current_session.processed_transactions_file = str(archive_dir / "processed_transactions.json")
                    self.current_session.categorization_data_file = str(archive_dir / "categorization_data.json")
            else:
                self.current_session.status = SessionStatus.CLEARED
            
            self.current_session.updated_at = datetime.now()
            
            # Clear current session reference
            cleared_session_id = self.current_session.session_id
            self.current_session = None
            
            # Save registry
            self._save_sessions_registry()
            self._save_active_session()
            
            self.logger.info(f"Cleared session: {cleared_session_id} (backup: {backup_id})")
            return True
            
        except Exception as e:
            self.logger.error(f"Error clearing session: {str(e)}", exc_info=True)
            return False
    
    def load_session(self, session_id: str) -> bool:
        """
        Load a specific session
        
        Args:
            session_id: ID of the session to load
            
        Returns:
            True if successful
        """
        try:
            if session_id not in self.sessions_registry:
                self.logger.error(f"Session not found: {session_id}")
                return False
            
            session = self.sessions_registry[session_id]
            
            # Verify session files exist
            if not Path(session.raw_transactions_file).exists():
                self.logger.error(f"Session data files not found for: {session_id}")
                return False
            
            # Set as current session
            self.current_session = session
            session.status = SessionStatus.ACTIVE
            session.updated_at = datetime.now()
            
            # Save active session
            self._save_sessions_registry()
            self._save_active_session()
            
            self.logger.info(f"Loaded session: {session_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error loading session: {str(e)}", exc_info=True)
            return False

    def get_session_transactions(self, session_id: str = None) -> Tuple[List[RawTransaction], List[ProcessedTransaction]]:
        """
        Get transactions from a session

        Args:
            session_id: Session ID (uses current session if None)

        Returns:
            Tuple of (raw_transactions, processed_transactions)
        """
        try:
            session = self.current_session if session_id is None else self.sessions_registry.get(session_id)

            if not session:
                return [], []

            # Load raw transactions
            raw_transactions = self._load_raw_transactions(Path(session.raw_transactions_file))

            # Load processed transactions if available
            processed_transactions = []
            if Path(session.processed_transactions_file).exists():
                processed_transactions = self._load_processed_transactions(Path(session.processed_transactions_file))

            return raw_transactions, processed_transactions

        except Exception as e:
            self.logger.error(f"Error getting session transactions: {str(e)}", exc_info=True)
            return [], []

    def detect_similar_sessions(self, new_transactions: List[RawTransaction]) -> List[Dict[str, Any]]:
        """
        Detect sessions with similar transaction data

        Args:
            new_transactions: New transactions to compare

        Returns:
            List of similar sessions with similarity scores
        """
        try:
            similar_sessions = []
            new_session_hash = self._generate_transaction_hash(new_transactions)

            for session_id, session in self.sessions_registry.items():
                if session.status in [SessionStatus.ARCHIVED, SessionStatus.CLEARED]:
                    continue

                # Load session transactions for comparison
                session_transactions, _ = self.get_session_transactions(session_id)
                if not session_transactions:
                    continue

                # Calculate similarity
                similarity_score = self._calculate_session_similarity(new_transactions, session_transactions)

                if similarity_score > 0.3:  # 30% similarity threshold
                    similar_sessions.append({
                        "session_id": session_id,
                        "session": session,
                        "similarity_score": similarity_score,
                        "transaction_count": len(session_transactions),
                        "overlap_count": int(similarity_score * min(len(new_transactions), len(session_transactions)))
                    })

            # Sort by similarity score
            similar_sessions.sort(key=lambda x: x["similarity_score"], reverse=True)

            return similar_sessions

        except Exception as e:
            self.logger.error(f"Error detecting similar sessions: {str(e)}", exc_info=True)
            return []

    def merge_sessions(self, target_session_id: str, source_transactions: List[RawTransaction],
                      merge_options: DataMergeOptions) -> bool:
        """
        Merge transactions into an existing session

        Args:
            target_session_id: ID of the target session
            source_transactions: Transactions to merge
            merge_options: Options for merging

        Returns:
            True if successful
        """
        try:
            if target_session_id not in self.sessions_registry:
                self.logger.error(f"Target session not found: {target_session_id}")
                return False

            target_session = self.sessions_registry[target_session_id]

            # Create backup if requested
            if merge_options.create_backup:
                self.backup_current_session(f"Pre-merge backup: {merge_options.merge_description}")

            # Load existing transactions
            existing_raw, existing_processed = self.get_session_transactions(target_session_id)

            # Merge based on strategy
            if merge_options.strategy == DataMergeStrategy.REPLACE_OLD:
                merged_raw = source_transactions
                merged_processed = []
            elif merge_options.strategy == DataMergeStrategy.KEEP_OLD:
                merged_raw = existing_raw
                merged_processed = existing_processed
            elif merge_options.strategy == DataMergeStrategy.MERGE_ALL:
                merged_raw = self._merge_transaction_lists(existing_raw, source_transactions)
                merged_processed = existing_processed  # Keep existing processed data
            elif merge_options.strategy == DataMergeStrategy.ARCHIVE_OLD:
                # Archive old session first
                self._archive_session(target_session_id)
                merged_raw = source_transactions
                merged_processed = []
            else:
                raise ValueError(f"Unknown merge strategy: {merge_options.strategy}")

            # Update session files
            session_dir = Path(target_session.raw_transactions_file).parent
            self._save_raw_transactions(merged_raw, session_dir / "raw_transactions.json")
            if merged_processed:
                self._save_processed_transactions(merged_processed, session_dir / "processed_transactions.json")

            # Update session metadata
            target_session.transaction_count = len(merged_raw)
            target_session.updated_at = datetime.now()
            target_session.description += f" | Merged: {merge_options.merge_description}"

            self._save_sessions_registry()

            self.logger.info(f"Merged sessions: {len(source_transactions)} transactions into {target_session_id}")
            return True

        except Exception as e:
            self.logger.error(f"Error merging sessions: {str(e)}", exc_info=True)
            return False

    def get_available_sessions(self, include_archived: bool = False) -> List[TransactionSession]:
        """
        Get list of available sessions

        Args:
            include_archived: Whether to include archived sessions

        Returns:
            List of available sessions
        """
        sessions = []
        for session in self.sessions_registry.values():
            if not include_archived and session.status == SessionStatus.ARCHIVED:
                continue
            sessions.append(session)

        # Sort by creation date (newest first)
        sessions.sort(key=lambda x: x.created_at, reverse=True)
        return sessions

    def delete_session(self, session_id: str, delete_files: bool = True) -> bool:
        """
        Delete a session

        Args:
            session_id: ID of the session to delete
            delete_files: Whether to delete associated files

        Returns:
            True if successful
        """
        try:
            if session_id not in self.sessions_registry:
                self.logger.error(f"Session not found: {session_id}")
                return False

            session = self.sessions_registry[session_id]

            # Delete files if requested
            if delete_files:
                session_dir = Path(session.raw_transactions_file).parent
                if session_dir.exists():
                    shutil.rmtree(session_dir)

            # Remove from registry
            del self.sessions_registry[session_id]

            # Clear current session if it was the deleted one
            if self.current_session and self.current_session.session_id == session_id:
                self.current_session = None

            self._save_sessions_registry()
            self._save_active_session()

            self.logger.info(f"Deleted session: {session_id}")
            return True

        except Exception as e:
            self.logger.error(f"Error deleting session: {str(e)}", exc_info=True)
            return False

    def _generate_session_id(self, transactions: List[RawTransaction]) -> str:
        """Generate a unique session ID based on transaction data"""
        transaction_hash = self._generate_transaction_hash(transactions)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"txn_session_{timestamp}_{transaction_hash[:8]}"

    def _generate_transaction_hash(self, transactions: List[RawTransaction]) -> str:
        """Generate a hash for a list of transactions"""
        transaction_data = []
        for txn in transactions:
            txn_str = f"{txn.description}_{txn.amount}_{txn.date}"
            transaction_data.append(txn_str)

        combined_data = "|".join(sorted(transaction_data))
        return hashlib.md5(combined_data.encode()).hexdigest()

    def _calculate_session_similarity(self, transactions1: List[RawTransaction],
                                    transactions2: List[RawTransaction]) -> float:
        """Calculate similarity between two transaction lists"""
        if not transactions1 or not transactions2:
            return 0.0

        # Create sets of transaction signatures
        set1 = set()
        set2 = set()

        for txn in transactions1:
            signature = f"{txn.description.lower().strip()}_{abs(float(txn.amount))}"
            set1.add(signature)

        for txn in transactions2:
            signature = f"{txn.description.lower().strip()}_{abs(float(txn.amount))}"
            set2.add(signature)

        # Calculate Jaccard similarity
        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))

        return intersection / union if union > 0 else 0.0

    def _merge_transaction_lists(self, list1: List[RawTransaction],
                               list2: List[RawTransaction]) -> List[RawTransaction]:
        """Merge two transaction lists, removing duplicates"""
        seen_signatures = set()
        merged = []

        # Add transactions from both lists, avoiding duplicates
        for txn_list in [list1, list2]:
            for txn in txn_list:
                signature = f"{txn.description}_{txn.amount}_{txn.date}"
                if signature not in seen_signatures:
                    seen_signatures.add(signature)
                    merged.append(txn)

        return merged

    def _archive_session(self, session_id: str) -> bool:
        """Archive a session"""
        try:
            if session_id not in self.sessions_registry:
                return False

            session = self.sessions_registry[session_id]
            session.status = SessionStatus.ARCHIVED
            session.updated_at = datetime.now()

            # Move to archives directory
            session_dir = Path(session.raw_transactions_file).parent
            archive_dir = self.archives_dir / session_id

            if session_dir.exists() and not archive_dir.exists():
                shutil.move(str(session_dir), str(archive_dir))

                # Update file paths
                session.raw_transactions_file = str(archive_dir / "raw_transactions.json")
                session.processed_transactions_file = str(archive_dir / "processed_transactions.json")
                session.categorization_data_file = str(archive_dir / "categorization_data.json")

            return True

        except Exception as e:
            self.logger.error(f"Error archiving session: {str(e)}", exc_info=True)
            return False

    def _save_raw_transactions(self, transactions: List[RawTransaction], file_path: Path):
        """Save raw transactions to JSON file"""
        try:
            data = []
            for txn in transactions:
                txn_dict = {
                    "date": txn.date.isoformat(),
                    "description": txn.description,
                    "amount": str(txn.amount),
                    "balance": str(txn.balance) if txn.balance else None,
                    "transaction_type": txn.transaction_type,
                    "reference_number": txn.reference_number,
                    "cheque_number": txn.cheque_number,
                    "branch_code": txn.branch_code,
                    "source_file": txn.source_file,
                    "source_line": txn.source_line,
                    "bank_name": txn.bank_name,
                    "account_number": txn.account_number,
                    "is_processed": txn.is_processed,
                    "processing_errors": txn.processing_errors
                }
                data.append(txn_dict)

            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)

        except Exception as e:
            self.logger.error(f"Error saving raw transactions: {str(e)}", exc_info=True)
            raise

    def _load_raw_transactions(self, file_path: Path) -> List[RawTransaction]:
        """Load raw transactions from JSON file"""
        try:
            if not file_path.exists():
                return []

            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            transactions = []
            for txn_dict in data:
                # Convert date string back to date object
                txn_date = datetime.fromisoformat(txn_dict["date"]).date()

                # Create RawTransaction object
                txn = RawTransaction(
                    date=txn_date,
                    description=txn_dict["description"],
                    amount=float(txn_dict["amount"]),
                    balance=float(txn_dict["balance"]) if txn_dict.get("balance") else None,
                    transaction_type=txn_dict.get("transaction_type", ""),
                    reference_number=txn_dict.get("reference_number"),
                    cheque_number=txn_dict.get("cheque_number"),
                    branch_code=txn_dict.get("branch_code"),
                    source_file=txn_dict.get("source_file"),
                    source_line=txn_dict.get("source_line"),
                    bank_name=txn_dict.get("bank_name"),
                    account_number=txn_dict.get("account_number"),
                    is_processed=txn_dict.get("is_processed", False),
                    processing_errors=txn_dict.get("processing_errors", [])
                )
                transactions.append(txn)

            return transactions

        except Exception as e:
            self.logger.error(f"Error loading raw transactions: {str(e)}", exc_info=True)
            return []

    def _save_processed_transactions(self, transactions: List[ProcessedTransaction], file_path: Path):
        """Save processed transactions to JSON file"""
        try:
            data = []
            for txn in transactions:
                txn_dict = {
                    "id": txn.id,
                    "date": txn.date.isoformat() if txn.date else None,
                    "type": txn.type,
                    "category": txn.category,
                    "sub_category": txn.sub_category,
                    "transaction_mode": txn.transaction_mode,
                    "amount": str(txn.amount),
                    "notes": txn.notes,
                    "created_at": txn.created_at.isoformat() if txn.created_at else None,
                    "updated_at": txn.updated_at.isoformat() if txn.updated_at else None,
                    "original_description": txn.original_description,
                    "confidence_score": txn.confidence_score,
                    "is_manually_reviewed": txn.is_manually_reviewed,
                    "suggested_categories": txn.suggested_categories
                }
                data.append(txn_dict)

            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)

        except Exception as e:
            self.logger.error(f"Error saving processed transactions: {str(e)}", exc_info=True)
            raise

    def _load_processed_transactions(self, file_path: Path) -> List[ProcessedTransaction]:
        """Load processed transactions from JSON file"""
        try:
            if not file_path.exists():
                return []

            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            transactions = []
            for txn_dict in data:
                # Convert date strings back to date/datetime objects
                txn_date = datetime.fromisoformat(txn_dict["date"]).date() if txn_dict.get("date") else None
                created_at = datetime.fromisoformat(txn_dict["created_at"]) if txn_dict.get("created_at") else None
                updated_at = datetime.fromisoformat(txn_dict["updated_at"]) if txn_dict.get("updated_at") else None

                # Create ProcessedTransaction object
                txn = ProcessedTransaction(
                    id=txn_dict.get("id"),
                    date=txn_date,
                    type=txn_dict.get("type", "Expense"),
                    category=txn_dict.get("category", ""),
                    sub_category=txn_dict.get("sub_category", ""),
                    transaction_mode=txn_dict.get("transaction_mode", ""),
                    amount=float(txn_dict.get("amount", 0)),
                    notes=txn_dict.get("notes", ""),
                    created_at=created_at,
                    updated_at=updated_at,
                    original_description=txn_dict.get("original_description", ""),
                    confidence_score=txn_dict.get("confidence_score", 0.0),
                    is_manually_reviewed=txn_dict.get("is_manually_reviewed", False),
                    suggested_categories=txn_dict.get("suggested_categories", [])
                )
                transactions.append(txn)

            return transactions

        except Exception as e:
            self.logger.error(f"Error loading processed transactions: {str(e)}", exc_info=True)
            return []

    def _load_sessions_registry(self):
        """Load sessions registry from file"""
        try:
            if not self.sessions_registry_file.exists():
                return

            with open(self.sessions_registry_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            for session_id, session_data in data.items():
                # Convert datetime strings back to datetime objects
                session_data["created_at"] = datetime.fromisoformat(session_data["created_at"])
                session_data["updated_at"] = datetime.fromisoformat(session_data["updated_at"])
                session_data["status"] = SessionStatus(session_data["status"])

                self.sessions_registry[session_id] = TransactionSession(**session_data)

        except Exception as e:
            self.logger.error(f"Error loading sessions registry: {str(e)}", exc_info=True)

    def _save_sessions_registry(self):
        """Save sessions registry to file"""
        try:
            data = {}
            for session_id, session in self.sessions_registry.items():
                session_dict = asdict(session)
                session_dict["created_at"] = session.created_at.isoformat()
                session_dict["updated_at"] = session.updated_at.isoformat()
                session_dict["status"] = session.status.value
                data[session_id] = session_dict

            with open(self.sessions_registry_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)

        except Exception as e:
            self.logger.error(f"Error saving sessions registry: {str(e)}", exc_info=True)

    def _load_active_session(self):
        """Load active session from file"""
        try:
            if not self.active_session_file.exists():
                return

            with open(self.active_session_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            session_id = data.get("active_session_id")
            if session_id and session_id in self.sessions_registry:
                self.current_session = self.sessions_registry[session_id]

        except Exception as e:
            self.logger.error(f"Error loading active session: {str(e)}", exc_info=True)

    def _save_active_session(self):
        """Save active session to file"""
        try:
            data = {
                "active_session_id": self.current_session.session_id if self.current_session else None,
                "updated_at": datetime.now().isoformat()
            }

            with open(self.active_session_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)

        except Exception as e:
            self.logger.error(f"Error saving active session: {str(e)}", exc_info=True)
