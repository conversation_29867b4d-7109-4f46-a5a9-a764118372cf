"""
Enhanced Integrated Categorizer that replaces the existing IntegratedCategorizer
with all the new intelligent filtering, batch processing, and budget protection features
"""

from typing import List, Dict, Optional, Any, Tuple
from datetime import datetime

from ..models.transaction import RawTransaction, ProcessedTransaction
from ..core.logger import get_logger
from .integrated_categorizer import IntegratedCategorizer
from .enhanced_sambanova_categorizer import EnhancedSambaNovaCategorizor, EnhancedConfig
from .hybrid_ml_categorizer import HybridMLCategorizer, HybridConfig, OperationMode, DataSource
from .ai_categorization_coordinator import AICategorizationCoordinator


class EnhancedIntegratedCategorizer(IntegratedCategorizer):
    """
    Enhanced version of IntegratedCategorizer with hybrid ML capabilities,
    intelligent filtering, batch processing, dynamic categories, and budget protection.
    Integrates SambaNova AI with existing ML workflow rather than replacing it.
    """

    def __init__(self, data_dir: str = "bank_analyzer_config"):
        # Initialize the base integrated categorizer
        super().__init__()

        # Initialize AI categorization coordinator (new enhanced system)
        self.ai_coordinator = AICategorizationCoordinator(data_dir)

        # Initialize hybrid ML categorizer (legacy support)
        hybrid_config = HybridConfig(
            operation_mode=OperationMode.HYBRID,
            ml_confidence_threshold=0.7,
            ai_confidence_threshold=0.8,
            auto_add_ai_labels=True,
            use_ai_for_uncertain_only=True,
            max_ai_transactions_per_session=50
        )

        self.hybrid_categorizer = HybridMLCategorizer(hybrid_config)

        # Configuration for enhanced features
        self.use_enhanced_ai_system = True  # Flag to enable new system

        self.logger.info("Enhanced integrated categorizer with AI coordinator initialized")
    
    def categorize_batch(self, raw_transactions: List[RawTransaction],
                        progress_callback=None) -> List[ProcessedTransaction]:
        """
        Enhanced batch categorization using the new AI coordinator system

        Args:
            raw_transactions: List of raw transactions to categorize
            progress_callback: Optional callback function for progress updates
                             Should accept (step_name, progress_percent, details)

        Returns:
            List of processed transactions
        """
        if not raw_transactions:
            return []

        self.logger.info(f"Starting enhanced AI categorization of {len(raw_transactions)} transactions")

        # Progress callback helper
        def update_progress(step, percent, details=""):
            if progress_callback:
                progress_callback(step, percent, details)

        try:
            update_progress("Initializing AI categorization system", 5)

            if self.use_enhanced_ai_system:
                update_progress("Analyzing transactions with AI coordinator", 10)

                # Use the new AI categorization coordinator with progress tracking
                result = self.ai_coordinator.categorize_transactions(
                    raw_transactions,
                    progress_callback=update_progress
                )

                # Log comprehensive results
                self.logger.info(f"Enhanced AI categorization completed: "
                               f"{result.total_transactions} total, "
                               f"{result.merchant_cache_used} cached, "
                               f"{result.ai_categorized} AI, "
                               f"{result.manual_required} manual, "
                               f"${result.total_cost:.2f} cost, "
                               f"${result.cost_saved:.2f} saved, "
                               f"{result.learned_patterns} patterns learned")

                if result.errors:
                    self.logger.warning(f"Categorization errors: {len(result.errors)}")

                return result.processed_transactions
            else:
                # Fallback to hybrid categorizer
                processed_transactions = self.hybrid_categorizer.categorize_batch(raw_transactions)

                # Log results
                stats = self.hybrid_categorizer.stats
                self.logger.info(f"Hybrid categorization completed: "
                               f"{stats['total_categorized']} total, "
                               f"{stats['ml_used']} ML, "
                               f"{stats['ai_used']} AI, "
                               f"{stats['hybrid_used']} hybrid, "
                               f"{stats['added_to_training']} added to training")

                return processed_transactions

        except Exception as e:
            self.logger.error(f"Error in enhanced categorization: {str(e)}")
            # Fallback to base categorizer
            return super().categorize_batch(raw_transactions)
    
    def _update_enhanced_stats(self, stats):
        """Update statistics with hybrid results"""
        # Add hybrid stats to existing stats
        if not hasattr(self, 'enhanced_stats'):
            self.enhanced_stats = {
                'total_enhanced_sessions': 0,
                'total_ml_processed': 0,
                'total_ai_processed': 0,
                'total_hybrid_processed': 0,
                'total_added_to_training': 0,
                'uncertain_cases_handled': 0
            }

        self.enhanced_stats['total_enhanced_sessions'] += 1
        self.enhanced_stats['total_ml_processed'] += stats.get('ml_used', 0)
        self.enhanced_stats['total_ai_processed'] += stats.get('ai_used', 0)
        self.enhanced_stats['total_hybrid_processed'] += stats.get('hybrid_used', 0)
        self.enhanced_stats['total_added_to_training'] += stats.get('added_to_training', 0)
        self.enhanced_stats['uncertain_cases_handled'] += stats.get('uncertain_cases', 0)
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get enhanced system status including AI coordinator features"""
        # Get base status
        base_status = super().get_system_status()

        # Add enhanced AI system status
        enhanced_status = {
            'enhanced_features': {
                'ai_categorization_coordinator': self.use_enhanced_ai_system,
                'merchant_pattern_learning': True,
                'smart_transaction_routing': True,
                'cost_optimization': True,
                'intelligent_caching': True,
                'hybrid_ml_integration': True,
                'ai_assisted_labeling': True,
                'smart_filtering': True,
                'batch_processing': True,
                'dynamic_categories': True,
                'transaction_prioritization': True,
                'budget_protection': True,
                'ml_training_integration': True
            }
        }

        # Get AI coordinator status if available
        if self.use_enhanced_ai_system and hasattr(self, 'ai_coordinator'):
            try:
                coordinator_status = self.ai_coordinator.get_system_status()
                enhanced_status['ai_coordinator_status'] = coordinator_status
            except Exception as e:
                enhanced_status['ai_coordinator_error'] = str(e)

        # Get hybrid categorizer stats (legacy support)
        if hasattr(self, 'hybrid_categorizer'):
            try:
                hybrid_stats = self.hybrid_categorizer.get_system_status()
                enhanced_status['hybrid_ml_stats'] = hybrid_stats
            except Exception as e:
                enhanced_status['hybrid_ml_error'] = str(e)

        # Add our enhanced stats
        if hasattr(self, 'enhanced_stats'):
            enhanced_status['session_stats'] = self.enhanced_stats

        # Merge with base status
        base_status.update(enhanced_status)
        return base_status
    
    def get_budget_status(self) -> Dict[str, Any]:
        """Get current budget status"""
        if hasattr(self, 'hybrid_categorizer'):
            return self.hybrid_categorizer._get_ai_budget_status()
        return {"error": "Hybrid categorizer not available"}

    def get_processing_recommendation(self, transactions: List[RawTransaction]) -> Dict[str, Any]:
        """Get recommendation for processing transactions"""
        if self.use_enhanced_ai_system and hasattr(self, 'ai_coordinator'):
            try:
                cost_estimate = self.ai_coordinator.estimate_categorization_cost(transactions)
                return {
                    "approved": cost_estimate.budget_sufficient,
                    "reason": cost_estimate.recommendation,
                    "cost_estimate": {
                        "total_transactions": cost_estimate.total_transactions,
                        "merchant_cache_available": cost_estimate.merchant_cache_available,
                        "ai_suitable": cost_estimate.ai_suitable,
                        "manual_required": cost_estimate.manual_required,
                        "estimated_cost": cost_estimate.estimated_ai_cost,
                        "cost_savings": cost_estimate.cost_savings_from_cache,
                        "transaction_count": cost_estimate.ai_suitable
                    }
                }
            except Exception as e:
                return {"approved": True, "reason": f"Enhanced cost estimation not available: {str(e)}"}
        elif hasattr(self, 'hybrid_categorizer'):
            try:
                return self.hybrid_categorizer.ai_categorizer.estimate_processing_cost(transactions)
            except Exception as e:
                return {"approved": True, "reason": f"Cost estimation not available: {str(e)}"}
        return {"approved": True, "reason": "No categorizer available"}

    def get_filtering_stats(self) -> Dict[str, Any]:
        """Get transaction filtering statistics"""
        if hasattr(self, 'hybrid_categorizer'):
            try:
                return self.hybrid_categorizer.ai_categorizer.transaction_filter.get_stats()
            except Exception as e:
                return {"error": f"Filtering stats not available: {str(e)}"}
        return {}

    def get_category_creation_summary(self) -> Dict[str, Any]:
        """Get summary of automatically created categories"""
        if hasattr(self, 'hybrid_categorizer'):
            try:
                return self.hybrid_categorizer.ai_categorizer.category_manager.get_new_categories_summary()
            except Exception as e:
                return {"error": f"Category summary not available: {str(e)}"}
        return {}

    def get_hybrid_ml_stats(self) -> Dict[str, Any]:
        """Get hybrid ML specific statistics"""
        if hasattr(self, 'hybrid_categorizer'):
            return self.hybrid_categorizer.stats
        return {}

    def get_uncertain_transactions(self, min_uncertainty: float = 0.5) -> List[Dict[str, Any]]:
        """Get transactions where local ML has low confidence"""
        if hasattr(self, 'hybrid_categorizer'):
            return self.hybrid_categorizer.get_uncertain_transactions(min_uncertainty)
        return []

    def get_training_data_quality_report(self) -> Dict[str, Any]:
        """Get training data quality report"""
        if hasattr(self, 'hybrid_categorizer'):
            return self.hybrid_categorizer.get_training_data_quality_report()
        return {}

    def switch_operation_mode(self, mode: str):
        """Switch hybrid operation mode"""
        if hasattr(self, 'hybrid_categorizer'):
            operation_mode = OperationMode(mode)
            self.hybrid_categorizer.switch_operation_mode(operation_mode)
            self.logger.info(f"Switched to {mode} operation mode")

    def train_model_with_hybrid_data(self, include_sources: List[str] = None) -> Dict[str, Any]:
        """Train model using hybrid data sources"""
        if hasattr(self, 'hybrid_categorizer'):
            if include_sources:
                data_sources = [DataSource(source) for source in include_sources]
            else:
                data_sources = [DataSource.MANUAL, DataSource.SAMBANOVA]

            return self.hybrid_categorizer.train_model_with_hybrid_data(data_sources)
        return {"success": False, "error": "Hybrid categorizer not available"}

    def get_ai_suggestions_for_labeling(self, transactions: List[RawTransaction]) -> Dict[str, Dict[str, Any]]:
        """Get AI suggestions for manual labeling"""
        if hasattr(self, 'hybrid_categorizer'):
            return self.hybrid_categorizer.get_ai_suggestions_for_manual_labeling(transactions)
        return {}
    
    def reset_enhanced_stats(self):
        """Reset all enhanced statistics"""
        if hasattr(self, 'hybrid_categorizer'):
            self.hybrid_categorizer.reset_stats()

        if hasattr(self, 'enhanced_stats'):
            self.enhanced_stats = {
                'total_enhanced_sessions': 0,
                'total_ml_processed': 0,
                'total_ai_processed': 0,
                'total_hybrid_processed': 0,
                'total_added_to_training': 0,
                'uncertain_cases_handled': 0
            }

        self.logger.info("Enhanced hybrid ML statistics reset")

    def get_merchant_mapping_stats(self) -> Dict[str, Any]:
        """Get merchant mapping statistics from the enhanced system"""
        if self.use_enhanced_ai_system and hasattr(self, 'ai_coordinator'):
            try:
                return self.ai_coordinator.merchant_mapper.get_merchant_statistics()
            except Exception as e:
                return {"error": f"Merchant mapping stats not available: {str(e)}"}
        return {}

    def get_ai_suggestions_for_manual_review(self, transactions: List[RawTransaction]) -> Dict[str, Dict[str, Any]]:
        """Get AI suggestions for transactions pending manual review"""
        if self.use_enhanced_ai_system and hasattr(self, 'ai_coordinator'):
            try:
                return self.ai_coordinator.get_ai_suggestions_for_manual_review(transactions)
            except Exception as e:
                self.logger.error(f"Error getting AI suggestions: {str(e)}")
                return {}
        elif hasattr(self, 'hybrid_categorizer'):
            return self.hybrid_categorizer.get_ai_suggestions_for_manual_labeling(transactions)
        return {}

    def cleanup_expired_merchant_patterns(self) -> Dict[str, int]:
        """Cleanup expired merchant patterns"""
        if self.use_enhanced_ai_system and hasattr(self, 'ai_coordinator'):
            try:
                return self.ai_coordinator.cleanup_expired_patterns()
            except Exception as e:
                return {"error": f"Cleanup failed: {str(e)}"}
        return {"removed_patterns": 0}

    def export_merchant_patterns(self, file_path: str) -> bool:
        """Export merchant patterns to file"""
        if self.use_enhanced_ai_system and hasattr(self, 'ai_coordinator'):
            try:
                return self.ai_coordinator.export_merchant_patterns(file_path)
            except Exception as e:
                self.logger.error(f"Export failed: {str(e)}")
                return False
        return False

    def import_merchant_patterns(self, file_path: str, merge: bool = True) -> Tuple[bool, str, Dict[str, int]]:
        """Import merchant patterns from file"""
        if self.use_enhanced_ai_system and hasattr(self, 'ai_coordinator'):
            try:
                return self.ai_coordinator.import_merchant_patterns(file_path, merge)
            except Exception as e:
                error_msg = f"Import failed: {str(e)}"
                return False, error_msg, {}
        return False, "Enhanced AI system not available", {}

    def switch_to_enhanced_system(self, enable: bool = True):
        """Switch between enhanced AI system and legacy hybrid system"""
        self.use_enhanced_ai_system = enable
        system_type = "enhanced AI coordinator" if enable else "legacy hybrid ML"
        self.logger.info(f"Switched to {system_type} system")

    def get_cost_estimate_for_transactions(self, transactions: List[RawTransaction]) -> Dict[str, Any]:
        """Get detailed cost estimate for processing transactions"""
        if self.use_enhanced_ai_system and hasattr(self, 'ai_coordinator'):
            try:
                cost_estimate = self.ai_coordinator.estimate_categorization_cost(transactions)
                return {
                    'total_transactions': cost_estimate.total_transactions,
                    'merchant_cache_available': cost_estimate.merchant_cache_available,
                    'ai_suitable': cost_estimate.ai_suitable,
                    'manual_required': cost_estimate.manual_required,
                    'estimated_ai_cost': cost_estimate.estimated_ai_cost,
                    'cost_savings_from_cache': cost_estimate.cost_savings_from_cache,
                    'budget_sufficient': cost_estimate.budget_sufficient,
                    'recommendation': cost_estimate.recommendation
                }
            except Exception as e:
                return {"error": f"Cost estimation failed: {str(e)}"}
        return {"error": "Enhanced AI system not available"}

    def update_ai_system_config(self, config_updates: Dict[str, Any]):
        """Update AI system configuration"""
        if self.use_enhanced_ai_system and hasattr(self, 'ai_coordinator'):
            try:
                self.ai_coordinator.update_configuration(config_updates)
                self.logger.info(f"AI system configuration updated: {config_updates}")
            except Exception as e:
                self.logger.error(f"Configuration update failed: {str(e)}")

    def reset_ai_system(self, confirm: bool = False) -> Dict[str, bool]:
        """Reset the AI system (requires confirmation)"""
        if not confirm:
            return {'reset': False, 'reason': 'Confirmation required'}

        if self.use_enhanced_ai_system and hasattr(self, 'ai_coordinator'):
            try:
                return self.ai_coordinator.reset_system(confirm=True)
            except Exception as e:
                self.logger.error(f"AI system reset failed: {str(e)}")
                return {'reset': False, 'error': str(e)}

        # Reset legacy system
        if hasattr(self, 'hybrid_categorizer'):
            self.hybrid_categorizer.reset_stats()

        if hasattr(self, 'enhanced_stats'):
            self.enhanced_stats = {
                'total_enhanced_sessions': 0,
                'total_ml_processed': 0,
                'total_ai_processed': 0,
                'total_hybrid_processed': 0,
                'total_added_to_training': 0,
                'uncertain_cases_handled': 0
            }

        return {'reset': True}


# Backward compatibility - replace the original IntegratedCategorizer
def create_enhanced_categorizer():
    """Factory function to create enhanced categorizer"""
    return EnhancedIntegratedCategorizer()
