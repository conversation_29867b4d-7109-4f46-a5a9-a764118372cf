"""
SambaNova-powered transaction categorizer
Uses SambaNova's cost-effective models with extensive caching for fast, accurate categorization
"""

import json
import time
from typing import List, Dict, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path

from ..core.logger import get_logger
from ..models.transaction import RawTransaction, ProcessedTransaction
from .sambanova_client import SambaNovaClient, SambaNovaConfig, SambaNovaClassificationResult
from .training_data_manager import TrainingDataManager
from .data_preparation import TransactionDataPreparator


@dataclass
class SambaNovaPrediction:
    """SambaNova prediction result"""
    category: str
    sub_category: str
    confidence: float
    model_version: str
    predicted_at: datetime
    processing_time: float
    tokens_used: int
    estimated_cost: float
    from_cache: bool = False
    examples_used: int = 0


class SambaNovaTransactionCategorizer:
    """
    SambaNova-powered transaction categorizer with cost optimization
    Provides fast, accurate categorization using cost-effective models
    """
    
    def __init__(self, ml_data_dir: str = "bank_analyzer_config/ml_data"):
        self.logger = get_logger(__name__)
        self.ml_data_dir = Path(ml_data_dir)
        self.ml_data_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize SambaNova client with cost optimization
        self.config = SambaNovaConfig(
            api_key="fc10c11a-7490-41c7-96c6-f0d6ab4761cf",
            model_name="Meta-Llama-3.1-8B-Instruct",  # Most cost-effective
            enabled=True,
            fallback_on_error=True,
            max_tokens=100,  # Keep low for cost efficiency
            temperature=0.1,
            max_daily_cost=1.0,  # $1 per day limit
            cache_enabled=True,
            cache_ttl_hours=24
        )
        
        self.sambanova_client = SambaNovaClient(self.config)
        
        # Initialize data components
        self.training_manager = TrainingDataManager(ml_data_dir)
        self.data_preparator = TransactionDataPreparator()
        
        # Cache for few-shot examples
        self._examples_cache = {}
        self._cache_timestamp = None
        self._cache_ttl = 1800  # 30 minutes for examples cache
        
        # Statistics
        self.stats = {
            "total_predictions": 0,
            "successful_predictions": 0,
            "failed_predictions": 0,
            "cache_hits": 0,
            "avg_processing_time": 0.0,
            "total_cost": 0.0,
            "examples_used": 0
        }
        
        self.logger.info(f"SambaNova categorizer initialized (available: {self.sambanova_client.is_available()})")
    
    def is_available(self) -> bool:
        """Check if SambaNova categorizer is available"""
        return self.sambanova_client.is_available()
    
    def predict_category(self, description: str, use_few_shot: bool = True) -> Optional[SambaNovaPrediction]:
        """
        Predict category for a transaction description using SambaNova
        
        Args:
            description: Transaction description
            use_few_shot: Whether to use few-shot examples (costs more tokens)
            
        Returns:
            SambaNova prediction or None if failed
        """
        if not self.is_available():
            self.logger.warning("SambaNova categorizer not available")
            return None
        
        start_time = time.time()
        self.stats["total_predictions"] += 1
        
        try:
            # Get few-shot examples if requested and budget allows
            examples = []
            if use_few_shot and self._should_use_few_shot():
                examples = self._get_few_shot_examples(description)
                self.stats["examples_used"] += len(examples)
            
            # Make prediction using SambaNova
            result = self.sambanova_client.classify_transaction(description, examples)
            
            if result:
                processing_time = time.time() - start_time
                
                # Create prediction object
                prediction = SambaNovaPrediction(
                    category=result.category,
                    sub_category=result.sub_category,
                    confidence=result.confidence,
                    model_version=result.model_used,
                    predicted_at=datetime.now(),
                    processing_time=processing_time,
                    tokens_used=result.tokens_used,
                    estimated_cost=result.estimated_cost,
                    from_cache=result.from_cache,
                    examples_used=len(examples)
                )
                
                # Update statistics
                self.stats["successful_predictions"] += 1
                self.stats["total_cost"] += result.estimated_cost
                if result.from_cache:
                    self.stats["cache_hits"] += 1
                self._update_avg_processing_time(processing_time)
                
                self.logger.debug(f"SambaNova prediction: {result.category}/{result.sub_category} "
                                f"(confidence: {result.confidence:.3f}, cost: ${result.estimated_cost:.6f})")
                
                return prediction
            else:
                self.stats["failed_predictions"] += 1
                return None
                
        except Exception as e:
            self.logger.error(f"Error in SambaNova prediction: {str(e)}")
            self.stats["failed_predictions"] += 1
            return None
    
    def _should_use_few_shot(self) -> bool:
        """Determine if we should use few-shot learning based on budget"""
        cost_stats = self.sambanova_client.get_cost_stats()
        
        # Don't use few-shot if we're close to daily limit
        if cost_stats["remaining_budget"] < 0.20:  # Less than $0.20 remaining
            return False
        
        # Don't use few-shot if we've made many requests today
        if cost_stats["daily_requests"] > 50:
            return False
        
        return True
    
    def categorize_transaction(self, raw_transaction: RawTransaction) -> Optional[ProcessedTransaction]:
        """
        Categorize a raw transaction using SambaNova
        
        Args:
            raw_transaction: Raw transaction to categorize
            
        Returns:
            Processed transaction or None if failed
        """
        try:
            # Get prediction
            prediction = self.predict_category(raw_transaction.description)
            
            if not prediction:
                return None
            
            # Create processed transaction
            processed = ProcessedTransaction()
            processed.update_from_raw(raw_transaction)
            processed.category = prediction.category
            processed.sub_category = prediction.sub_category
            processed.confidence_score = prediction.confidence
            
            # Add cost information to notes
            cost_info = f"SambaNova (${prediction.estimated_cost:.6f})"
            if prediction.from_cache:
                cost_info += " [cached]"
            processed.notes = cost_info
            
            return processed
            
        except Exception as e:
            self.logger.error(f"Error categorizing transaction: {str(e)}")
            return None
    
    def categorize_batch(self, raw_transactions: List[RawTransaction]) -> List[ProcessedTransaction]:
        """
        Categorize a batch of transactions with cost optimization
        
        Args:
            raw_transactions: List of raw transactions
            
        Returns:
            List of processed transactions
        """
        processed_transactions = []
        
        # Check budget before processing batch
        cost_stats = self.sambanova_client.get_cost_stats()
        if cost_stats["remaining_budget"] < 0.10:  # Less than $0.10 remaining
            self.logger.warning("Insufficient budget for batch processing")
            # Return fallback processed transactions
            for raw_transaction in raw_transactions:
                fallback = ProcessedTransaction()
                fallback.update_from_raw(raw_transaction)
                fallback.category = "Other"
                fallback.sub_category = "Miscellaneous"
                fallback.confidence_score = 0.1
                fallback.notes = "Budget limit reached"
                processed_transactions.append(fallback)
            return processed_transactions
        
        for i, raw_transaction in enumerate(raw_transactions):
            try:
                # Use few-shot only for first few transactions to save costs
                use_few_shot = i < 3 and self._should_use_few_shot()
                
                processed = self.categorize_transaction(raw_transaction)
                if processed:
                    processed_transactions.append(processed)
                else:
                    # Create fallback processed transaction
                    fallback = ProcessedTransaction()
                    fallback.update_from_raw(raw_transaction)
                    fallback.category = "Other"
                    fallback.sub_category = "Miscellaneous"
                    fallback.confidence_score = 0.1
                    fallback.notes = "SambaNova categorization failed"
                    processed_transactions.append(fallback)
                    
            except Exception as e:
                self.logger.error(f"Error in batch categorization: {str(e)}")
                # Create error fallback
                fallback = ProcessedTransaction()
                fallback.update_from_raw(raw_transaction)
                fallback.category = "Other"
                fallback.sub_category = "Miscellaneous"
                fallback.confidence_score = 0.1
                fallback.notes = f"Categorization error: {str(e)}"
                processed_transactions.append(fallback)
        
        return processed_transactions
    
    def _get_few_shot_examples(self, description: str, max_examples: int = 3) -> List[Dict[str, str]]:
        """
        Get few-shot examples for improved classification (limited for cost efficiency)
        
        Args:
            description: Transaction description to find examples for
            max_examples: Maximum number of examples (reduced for cost efficiency)
            
        Returns:
            List of example dictionaries
        """
        try:
            # Check cache first
            if self._is_examples_cache_valid():
                cache_key = self._get_examples_cache_key(description)
                if cache_key in self._examples_cache:
                    self.stats["cache_hits"] += 1
                    return self._examples_cache[cache_key]
            
            # Get labeled transactions from training manager
            labeled_transactions = self._get_labeled_transactions()
            
            if not labeled_transactions:
                return []
            
            # Find similar transactions for few-shot examples
            examples = self._find_similar_examples(description, labeled_transactions, max_examples)
            
            # Cache the results
            self._cache_examples(description, examples)
            
            return examples
            
        except Exception as e:
            self.logger.error(f"Error getting few-shot examples: {str(e)}")
            return []

    def _get_labeled_transactions(self) -> List[Dict[str, str]]:
        """Get labeled transactions for few-shot examples"""
        try:
            # Load unique transactions with labels
            unique_transactions = self.training_manager.data_preparator.load_unique_transactions()

            labeled = []
            for txn in unique_transactions.values():
                if txn.category and txn.sub_category and txn.is_manually_labeled:
                    labeled.append({
                        'description': txn.description,
                        'category': txn.category,
                        'sub_category': txn.sub_category,
                        'frequency': txn.frequency
                    })

            return labeled

        except Exception as e:
            self.logger.error(f"Error loading labeled transactions: {str(e)}")
            return []

    def _find_similar_examples(self, description: str, labeled_transactions: List[Dict[str, str]],
                             max_examples: int) -> List[Dict[str, str]]:
        """Find similar transactions for few-shot examples"""
        try:
            # Simple similarity based on word overlap
            description_words = set(description.lower().split())
            similarities = []

            for txn in labeled_transactions:
                txn_words = set(txn['description'].lower().split())

                # Calculate Jaccard similarity
                intersection = len(description_words.intersection(txn_words))
                union = len(description_words.union(txn_words))
                similarity = intersection / union if union > 0 else 0

                if similarity > 0.1:  # Minimum similarity threshold
                    similarities.append((similarity, txn))

            # Sort by similarity and frequency
            similarities.sort(key=lambda x: (x[0], x[1]['frequency']), reverse=True)

            # Return top examples
            return [txn for _, txn in similarities[:max_examples]]

        except Exception as e:
            self.logger.error(f"Error finding similar examples: {str(e)}")
            return []

    def _is_examples_cache_valid(self) -> bool:
        """Check if examples cache is still valid"""
        if not self._cache_timestamp:
            return False

        return (time.time() - self._cache_timestamp) < self._cache_ttl

    def _get_examples_cache_key(self, description: str) -> str:
        """Generate cache key for examples"""
        # Use first few words as cache key
        words = description.lower().split()[:3]
        return "_".join(words)

    def _cache_examples(self, description: str, examples: List[Dict[str, str]]):
        """Cache examples for future use"""
        cache_key = self._get_examples_cache_key(description)

        if not self._cache_timestamp:
            self._examples_cache = {}

        self._examples_cache[cache_key] = examples
        self._cache_timestamp = time.time()

    def _update_avg_processing_time(self, processing_time: float):
        """Update average processing time"""
        current_avg = self.stats["avg_processing_time"]
        successful_count = self.stats["successful_predictions"]

        if successful_count == 1:
            self.stats["avg_processing_time"] = processing_time
        else:
            self.stats["avg_processing_time"] = (current_avg * (successful_count - 1) + processing_time) / successful_count

    def get_stats(self) -> Dict[str, Any]:
        """Get categorizer statistics"""
        sambanova_stats = self.sambanova_client.get_stats()

        return {
            **self.stats,
            "sambanova_available": sambanova_stats["available"],
            "sambanova_enabled": sambanova_stats["enabled"],
            "model_name": sambanova_stats["model_name"],
            "daily_cost": sambanova_stats.get("daily_cost", 0.0),
            "total_cost": sambanova_stats.get("total_cost", 0.0),
            "remaining_budget": sambanova_stats.get("remaining_budget", 0.0),
            "cache_size": sambanova_stats.get("cache_size", 0),
            "success_rate": (self.stats["successful_predictions"] / max(1, self.stats["total_predictions"])) * 100
        }

    def get_cost_summary(self) -> Dict[str, Any]:
        """Get detailed cost summary"""
        return self.sambanova_client.get_cost_stats()

    def clear_cache(self):
        """Clear all caches"""
        self._examples_cache = {}
        self._cache_timestamp = None
        self.sambanova_client.clear_cache()
        self.logger.info("All caches cleared")
