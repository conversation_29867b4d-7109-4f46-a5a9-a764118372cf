"""
Smart Transaction Routing System for AI-Assisted Categorization

This module provides intelligent routing that determines the best categorization method
for each transaction: AI categorization, manual labeling, or cached merchant mappings.

Key Features:
- Intelligent decision making based on transaction characteristics
- Cost optimization through smart routing
- Integration with merchant mapping cache
- Transaction type and amount context consideration
- Confidence-based routing decisions
"""

from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
from datetime import datetime

from ..models.transaction import RawTransaction, ProcessedTransaction
from ..core.logger import get_logger
from .enhanced_merchant_mapper import Enhanced<PERSON>erchantMapper, MerchantMatchResult
from .transaction_filter import SmartTransactionFilter, FilterResult


class RoutingDecision(Enum):
    """Possible routing decisions for transactions"""
    USE_MERCHANT_CACHE = "merchant_cache"
    USE_AI_CATEGORIZATION = "ai_categorization"
    USE_MANUAL_LABELING = "manual_labeling"
    SKIP_PROCESSING = "skip_processing"


@dataclass
class RoutingResult:
    """Result of transaction routing decision"""
    decision: RoutingDecision
    confidence: float
    reason: str
    estimated_cost: float  # Estimated cost for this routing decision
    processing_priority: int  # 1-10, higher = more priority
    merchant_suggestion: Optional[Dict[str, Any]] = None
    ai_suitable: bool = False
    manual_required: bool = False


@dataclass
class RoutingStats:
    """Statistics for transaction routing"""
    total_routed: int = 0
    merchant_cache_used: int = 0
    ai_categorization_used: int = 0
    manual_labeling_used: int = 0
    skipped: int = 0
    cost_saved: float = 0.0
    total_estimated_cost: float = 0.0


class SmartTransactionRouter:
    """
    Smart transaction routing system that determines the optimal categorization method
    for each transaction based on various factors including cost, accuracy, and availability
    """
    
    def __init__(self, merchant_mapper: EnhancedMerchantMapper = None,
                 transaction_filter: SmartTransactionFilter = None,
                 config_dir: str = "bank_analyzer_config"):
        self.logger = get_logger(__name__)

        # Initialize components
        self.merchant_mapper = merchant_mapper or EnhancedMerchantMapper()
        self.transaction_filter = transaction_filter or SmartTransactionFilter()

        # Load routing configuration
        self.config = self._load_config(config_dir)

        # Statistics
        self.stats = RoutingStats()

        self.logger.info(f"Router initialized: cache_threshold={self.config['merchant_cache_confidence_threshold']}, "
                        f"max_ai_batch={self.config['max_ai_transactions_per_batch']}")

    def _load_config(self, config_dir: str):
        """Load router configuration from file or use defaults"""
        import json
        from pathlib import Path

        config_file = Path(config_dir) / "router_config.json"

        # Default configuration
        default_config = {
            'merchant_cache_confidence_threshold': 0.7,
            'ai_cost_per_transaction': 0.02,  # Estimated cost per AI transaction
            'manual_cost_per_transaction': 0.0,  # No direct cost for manual
            'merchant_cache_cost': 0.0,  # No cost for cached results
            'max_ai_transactions_per_batch': 50,
            'prioritize_high_amounts': True,
            'high_amount_threshold': 1000.0,
            'enable_cost_optimization': True
        }

        if config_file.exists():
            try:
                with open(config_file, 'r') as f:
                    loaded_config = json.load(f)

                # Update defaults with loaded config
                default_config.update(loaded_config)
                self.logger.info(f"Loaded router config from {config_file}")

            except Exception as e:
                self.logger.error(f"Error loading router config: {str(e)}")
        else:
            self.logger.info("Using default router configuration")

        return default_config
    
    def route_transaction(self, transaction: RawTransaction, 
                         available_budget: float = 1.0) -> RoutingResult:
        """
        Route a single transaction to the best categorization method
        
        Args:
            transaction: Transaction to route
            available_budget: Available budget for AI processing
            
        Returns:
            RoutingResult with routing decision and details
        """
        self.stats.total_routed += 1
        
        # Step 1: Check merchant cache first (fastest and free)
        merchant_suggestion = self.merchant_mapper.get_categorization_suggestion(transaction)
        if merchant_suggestion and merchant_suggestion['confidence'] >= self.config['merchant_cache_confidence_threshold']:
            self.stats.merchant_cache_used += 1
            self.stats.cost_saved += self.config['ai_cost_per_transaction']
            
            return RoutingResult(
                decision=RoutingDecision.USE_MERCHANT_CACHE,
                confidence=merchant_suggestion['confidence'],
                reason=f"High-confidence merchant cache match: {merchant_suggestion['pattern']}",
                estimated_cost=self.config['merchant_cache_cost'],
                processing_priority=8,  # High priority for cached results
                merchant_suggestion=merchant_suggestion,
                ai_suitable=False,
                manual_required=False
            )
        
        # Step 2: Check if transaction is suitable for AI processing
        filter_result = self.transaction_filter.filter_transaction(transaction)
        
        if filter_result.should_use_ai:
            # Check budget availability
            if available_budget >= self.config['ai_cost_per_transaction']:
                self.stats.ai_categorization_used += 1
                self.stats.total_estimated_cost += self.config['ai_cost_per_transaction']
                
                # Calculate priority based on amount and complexity
                priority = self._calculate_ai_priority(transaction, filter_result)
                
                return RoutingResult(
                    decision=RoutingDecision.USE_AI_CATEGORIZATION,
                    confidence=filter_result.confidence,
                    reason=f"AI suitable: {filter_result.reason}",
                    estimated_cost=self.config['ai_cost_per_transaction'],
                    processing_priority=priority,
                    merchant_suggestion=merchant_suggestion,  # May be low confidence
                    ai_suitable=True,
                    manual_required=False
                )
            else:
                # Budget exhausted, route to manual
                self.stats.manual_labeling_used += 1
                
                return RoutingResult(
                    decision=RoutingDecision.USE_MANUAL_LABELING,
                    confidence=0.5,
                    reason="Budget exhausted, routing to manual labeling",
                    estimated_cost=self.config['manual_cost_per_transaction'],
                    processing_priority=6,
                    merchant_suggestion=merchant_suggestion,
                    ai_suitable=True,  # Would be suitable if budget allowed
                    manual_required=True
                )
        
        # Step 3: Route to manual labeling
        self.stats.manual_labeling_used += 1
        
        # Determine priority for manual processing
        priority = self._calculate_manual_priority(transaction, filter_result)
        
        return RoutingResult(
            decision=RoutingDecision.USE_MANUAL_LABELING,
            confidence=0.6,
            reason=f"Manual required: {filter_result.reason}",
            estimated_cost=self.config['manual_cost_per_transaction'],
            processing_priority=priority,
            merchant_suggestion=merchant_suggestion,
            ai_suitable=False,
            manual_required=True
        )
    
    def route_batch(self, transactions: List[RawTransaction], 
                   available_budget: float = 5.0) -> Dict[str, List[Tuple[RawTransaction, RoutingResult]]]:
        """
        Route a batch of transactions with budget optimization
        
        Args:
            transactions: List of transactions to route
            available_budget: Total available budget for AI processing
            
        Returns:
            Dictionary with routing decisions grouped by method
        """
        self.logger.info(f"Routing batch of {len(transactions)} transactions with ${available_budget:.2f} budget")
        
        # Route all transactions first
        routing_results = []
        for transaction in transactions:
            result = self.route_transaction(transaction, available_budget)
            routing_results.append((transaction, result))
            
            # Update available budget
            if result.decision == RoutingDecision.USE_AI_CATEGORIZATION:
                available_budget -= result.estimated_cost
        
        # Group by routing decision
        grouped_results = {
            'merchant_cache': [],
            'ai_categorization': [],
            'manual_labeling': [],
            'skipped': []
        }
        
        for transaction, result in routing_results:
            if result.decision == RoutingDecision.USE_MERCHANT_CACHE:
                grouped_results['merchant_cache'].append((transaction, result))
            elif result.decision == RoutingDecision.USE_AI_CATEGORIZATION:
                grouped_results['ai_categorization'].append((transaction, result))
            elif result.decision == RoutingDecision.USE_MANUAL_LABELING:
                grouped_results['manual_labeling'].append((transaction, result))
            else:
                grouped_results['skipped'].append((transaction, result))
        
        # Sort AI categorization by priority (highest first)
        grouped_results['ai_categorization'].sort(
            key=lambda x: x[1].processing_priority, reverse=True
        )
        
        # Apply budget constraints to AI categorization
        if self.config['enable_cost_optimization']:
            grouped_results = self._apply_budget_constraints(grouped_results, available_budget)
        
        self.logger.info(f"Routing completed: "
                        f"{len(grouped_results['merchant_cache'])} cached, "
                        f"{len(grouped_results['ai_categorization'])} AI, "
                        f"{len(grouped_results['manual_labeling'])} manual, "
                        f"{len(grouped_results['skipped'])} skipped")
        
        return grouped_results
    
    def _calculate_ai_priority(self, transaction: RawTransaction, filter_result: FilterResult) -> int:
        """Calculate priority for AI processing (1-10, higher = more priority)"""
        priority = 5  # Base priority
        
        # Amount-based priority
        amount = float(abs(transaction.amount))
        if amount >= self.config['high_amount_threshold']:
            priority += 2
        elif amount >= 500:
            priority += 1
        
        # Filter confidence priority
        if filter_result.confidence >= 0.8:
            priority += 1
        
        # Filter priority score
        priority += min(2, int(filter_result.priority_score / 5))
        
        return min(10, max(1, priority))
    
    def _calculate_manual_priority(self, transaction: RawTransaction, filter_result: FilterResult) -> int:
        """Calculate priority for manual processing (1-10, higher = more priority)"""
        priority = 4  # Base priority for manual
        
        # Amount-based priority
        amount = float(abs(transaction.amount))
        if amount >= self.config['high_amount_threshold']:
            priority += 3  # High amounts get higher priority for manual review
        elif amount >= 500:
            priority += 1
        
        # Complexity-based priority (more complex = higher priority)
        if filter_result.filter_category == "unique":
            priority += 2
        
        return min(10, max(1, priority))
    
    def _apply_budget_constraints(self, grouped_results: Dict[str, List], 
                                available_budget: float) -> Dict[str, List]:
        """Apply budget constraints to AI categorization list"""
        ai_transactions = grouped_results['ai_categorization']
        
        # Calculate how many AI transactions we can afford
        max_ai_transactions = int(available_budget / self.config['ai_cost_per_transaction'])
        max_ai_transactions = min(max_ai_transactions, self.config['max_ai_transactions_per_batch'])
        
        if len(ai_transactions) > max_ai_transactions:
            # Move excess transactions to manual labeling
            excess_transactions = ai_transactions[max_ai_transactions:]
            grouped_results['ai_categorization'] = ai_transactions[:max_ai_transactions]
            
            # Update routing results for moved transactions
            for transaction, result in excess_transactions:
                result.decision = RoutingDecision.USE_MANUAL_LABELING
                result.reason = f"Budget constraint: {result.reason}"
                result.estimated_cost = self.config['manual_cost_per_transaction']
                result.manual_required = True
                
                grouped_results['manual_labeling'].append((transaction, result))
            
            self.logger.info(f"Budget constraint: moved {len(excess_transactions)} transactions to manual")
        
        return grouped_results
    
    def get_routing_statistics(self) -> Dict[str, Any]:
        """Get comprehensive routing statistics"""
        total_processed = (self.stats.merchant_cache_used + 
                          self.stats.ai_categorization_used + 
                          self.stats.manual_labeling_used)
        
        return {
            'total_routed': self.stats.total_routed,
            'total_processed': total_processed,
            'merchant_cache_used': self.stats.merchant_cache_used,
            'ai_categorization_used': self.stats.ai_categorization_used,
            'manual_labeling_used': self.stats.manual_labeling_used,
            'skipped': self.stats.skipped,
            'merchant_cache_percentage': (self.stats.merchant_cache_used / max(1, total_processed)) * 100,
            'ai_percentage': (self.stats.ai_categorization_used / max(1, total_processed)) * 100,
            'manual_percentage': (self.stats.manual_labeling_used / max(1, total_processed)) * 100,
            'cost_saved': self.stats.cost_saved,
            'total_estimated_cost': self.stats.total_estimated_cost,
            'cost_efficiency': (self.stats.cost_saved / max(0.01, self.stats.cost_saved + self.stats.total_estimated_cost)) * 100
        }
    
    def reset_statistics(self):
        """Reset routing statistics"""
        self.stats = RoutingStats()
        self.logger.info("Routing statistics reset")
    
    def update_config(self, config_updates: Dict[str, Any]):
        """Update routing configuration"""
        self.config.update(config_updates)
        self.logger.info(f"Routing configuration updated: {config_updates}")
