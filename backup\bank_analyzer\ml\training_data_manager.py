"""
Training data management for ML categorization system
Handles storage, versioning, and management of labeled training data
"""

import pandas as pd
import json
import re
from pathlib import Path
from typing import List, Dict, Optional, Any, Tuple
from datetime import datetime, date
from dataclasses import dataclass, asdict
from enum import Enum
import logging

from ..core.logger import get_logger
from ..models.transaction import RawTransaction
from .data_preparation import UniqueTransaction, TransactionDataPreparator
from .sambanova_client import SambaNovaClient, SambaNovaConfig


class FilterType(Enum):
    """Types of filters available for transaction filtering"""
    CONTAINS = "contains"
    STARTS_WITH = "starts_with"
    ENDS_WITH = "ends_with"
    REGEX = "regex"
    EXACT = "exact"
    MULTI_WORD_AND = "multi_word_and"  # All words must be present
    MULTI_WORD_OR = "multi_word_or"    # Any word must be present
    EXACT_PHRASE = "exact_phrase"      # Exact phrase match


class LabelingStatus(Enum):
    """Labeling status options"""
    ALL = "all"
    LABELED = "labeled"
    UNLABELED = "unlabeled"
    PENDING = "pending"


@dataclass
class TransactionFilter:
    """Filter criteria for transactions"""
    # Description filters
    description_text: Optional[str] = None
    description_filter_type: FilterType = FilterType.CONTAINS
    case_sensitive: bool = False

    # Amount filters
    min_amount: Optional[float] = None
    max_amount: Optional[float] = None

    # Frequency filters
    min_frequency: Optional[int] = None
    max_frequency: Optional[int] = None

    # Status filters
    labeling_status: LabelingStatus = LabelingStatus.ALL

    # Date filters
    start_date: Optional[date] = None
    end_date: Optional[date] = None

    # Category filters
    category: Optional[str] = None
    sub_category: Optional[str] = None

    # Similarity filters
    similar_to_hash_id: Optional[str] = None
    similarity_threshold: float = 0.3


@dataclass
class TrainingSession:
    """Represents a training session with metadata"""
    session_id: str
    started_at: datetime
    completed_at: Optional[datetime] = None
    labeled_count: int = 0
    user_name: str = "user"
    notes: str = ""


@dataclass
class LabelingStats:
    """Statistics about labeling progress"""
    total_unique_transactions: int
    labeled_transactions: int
    unlabeled_transactions: int
    labeling_progress: float
    categories_used: List[str]
    most_frequent_unlabeled: List[Dict[str, Any]]


class TrainingDataManager:
    """
    Manages training data for ML categorization
    Handles labeling sessions, data versioning, and quality control
    """
    
    def __init__(self, data_dir: str = "bank_analyzer_config/ml_data"):
        self.logger = get_logger(__name__)
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # File paths
        self.sessions_file = self.data_dir / "training_sessions.json"
        self.labeling_history_file = self.data_dir / "labeling_history.csv"
        self.quality_metrics_file = self.data_dir / "quality_metrics.json"
        
        # Data preparator
        self.data_preparator = TransactionDataPreparator(str(self.data_dir))

        # Initialize SambaNova client for embeddings
        self.sambanova_config = SambaNovaConfig(
            api_key="fc10c11a-7490-41c7-96c6-f0d6ab4761cf",
            enabled=True
        )
        self.sambanova_client = SambaNovaClient(self.sambanova_config)

        # Current session
        self.current_session: Optional[TrainingSession] = None

        # Cache for embeddings
        self._embedding_cache = {}
    
    def start_labeling_session(self, user_name: str = "user", notes: str = "") -> str:
        """
        Start a new labeling session
        
        Args:
            user_name: Name of the user doing the labeling
            notes: Optional notes about the session
            
        Returns:
            Session ID
        """
        session_id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        self.current_session = TrainingSession(
            session_id=session_id,
            started_at=datetime.now(),
            user_name=user_name,
            notes=notes
        )
        
        self.logger.info(f"Started labeling session: {session_id}")
        return session_id
    
    def end_labeling_session(self) -> bool:
        """
        End the current labeling session
        
        Returns:
            True if session ended successfully
        """
        if not self.current_session:
            self.logger.warning("No active labeling session to end")
            return False
        
        self.current_session.completed_at = datetime.now()
        
        # Save session to history
        self._save_session_to_history(self.current_session)
        
        self.logger.info(f"Ended labeling session: {self.current_session.session_id}")
        self.current_session = None
        return True
    
    def label_transaction(self, hash_id: str, category: str, sub_category: str, 
                         confidence: float = 1.0) -> bool:
        """
        Label a transaction and record the action
        
        Args:
            hash_id: Hash ID of the transaction
            category: Category label
            sub_category: Sub-category label
            confidence: Confidence in the label
            
        Returns:
            True if labeled successfully
        """
        # Update the transaction label
        success = self.data_preparator.update_transaction_label(
            hash_id, category, sub_category, confidence, 
            labeled_by=self.current_session.user_name if self.current_session else "user"
        )
        
        if success:
            # Record labeling action
            self._record_labeling_action(hash_id, category, sub_category, confidence)
            
            # Update session count
            if self.current_session:
                self.current_session.labeled_count += 1
            
            self.logger.info(f"Labeled transaction {hash_id} as {category}/{sub_category}")
        
        return success
    
    def batch_label_transactions(self, labels: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Label multiple transactions in batch with optimized file I/O

        Args:
            labels: List of label dictionaries with keys: hash_id, category, sub_category, confidence

        Returns:
            Results summary
        """
        result = {
            "success_count": 0,
            "error_count": 0,
            "errors": []
        }

        try:
            # Load unique transactions once at the beginning
            unique_transactions = self.data_preparator.load_unique_transactions()

            # Track which transactions were successfully updated
            updated_transactions = []

            # Process all labels in memory first
            for label_data in labels:
                try:
                    hash_id = label_data["hash_id"]
                    category = label_data["category"]
                    sub_category = label_data["sub_category"]
                    confidence = label_data.get("confidence", 1.0)

                    # Check if transaction exists
                    if hash_id not in unique_transactions:
                        result["error_count"] += 1
                        result["errors"].append(f"Transaction with hash_id {hash_id} not found")
                        continue

                    # Update transaction in memory
                    txn = unique_transactions[hash_id]
                    txn.category = category
                    txn.sub_category = sub_category
                    txn.confidence = confidence
                    txn.is_manually_labeled = True
                    txn.labeled_by = self.current_session.user_name if self.current_session else "user"
                    txn.labeled_at = datetime.now()

                    # Track successful update
                    updated_transactions.append({
                        "hash_id": hash_id,
                        "category": category,
                        "sub_category": sub_category,
                        "confidence": confidence
                    })

                    result["success_count"] += 1

                except Exception as e:
                    result["error_count"] += 1
                    result["errors"].append(f"Error processing {label_data.get('hash_id', 'unknown')}: {str(e)}")

            # Save all changes to disk once at the end
            if updated_transactions:
                save_success = self.data_preparator.save_unique_transactions(unique_transactions)
                if not save_success:
                    # If save failed, mark all as failed
                    result["error_count"] = len(labels)
                    result["success_count"] = 0
                    result["errors"] = ["Failed to save batch changes to disk"]
                else:
                    # Record labeling actions for successfully saved transactions
                    for update_data in updated_transactions:
                        self._record_labeling_action(
                            update_data["hash_id"],
                            update_data["category"],
                            update_data["sub_category"],
                            update_data["confidence"]
                        )

                        # Update session count
                        if self.current_session:
                            self.current_session.labeled_count += 1

        except Exception as e:
            self.logger.error(f"Critical error in batch labeling: {str(e)}")
            result["error_count"] = len(labels)
            result["success_count"] = 0
            result["errors"] = [f"Critical batch error: {str(e)}"]

        self.logger.info(f"Batch labeling completed: {result['success_count']} success, {result['error_count']} errors")
        return result
    
    def get_labeling_stats(self) -> LabelingStats:
        """
        Get statistics about labeling progress
        
        Returns:
            Labeling statistics
        """
        unique_transactions = self.data_preparator.load_unique_transactions()
        
        total_count = len(unique_transactions)
        labeled_count = sum(1 for txn in unique_transactions.values() if txn.is_manually_labeled)
        unlabeled_count = total_count - labeled_count
        
        progress = (labeled_count / total_count * 100) if total_count > 0 else 0
        
        # Get categories used
        categories_used = list(set(
            txn.category for txn in unique_transactions.values() 
            if txn.category and txn.is_manually_labeled
        ))
        
        # Get most frequent unlabeled transactions
        unlabeled_transactions = [
            txn for txn in unique_transactions.values() 
            if not txn.is_manually_labeled
        ]
        unlabeled_transactions.sort(key=lambda x: x.frequency, reverse=True)
        
        most_frequent_unlabeled = []
        for txn in unlabeled_transactions[:10]:
            most_frequent_unlabeled.append({
                "hash_id": txn.hash_id,
                "description": txn.description,
                "frequency": txn.frequency,
                "amount_range": txn.amount_range
            })
        
        return LabelingStats(
            total_unique_transactions=total_count,
            labeled_transactions=labeled_count,
            unlabeled_transactions=unlabeled_count,
            labeling_progress=progress,
            categories_used=categories_used,
            most_frequent_unlabeled=most_frequent_unlabeled
        )
    
    def get_unlabeled_batch(self, batch_size: int = 20, sort_by: str = "frequency",
                           offset: int = 0, min_frequency: int = None) -> List[UniqueTransaction]:
        """
        Get a batch of unlabeled transactions for labeling with pagination

        Args:
            batch_size: Number of transactions to return
            sort_by: Sort criteria ("frequency", "recent", "amount")
            offset: Number of results to skip (for pagination)
            min_frequency: Optional minimum frequency filter

        Returns:
            List of unlabeled transactions
        """
        # Use cached unlabeled transactions if available
        cache_key = f"unlabeled_{sort_by}_{min_frequency}"
        if hasattr(self, '_unlabeled_cache') and cache_key in self._unlabeled_cache:
            cached_unlabeled = self._unlabeled_cache[cache_key]
            return cached_unlabeled[offset:offset + batch_size]

        unlabeled = self.data_preparator.get_unlabeled_transactions()

        # Apply minimum frequency filter if specified
        if min_frequency is not None and min_frequency > 0:
            unlabeled = [txn for txn in unlabeled if txn.frequency >= min_frequency]
            self.logger.debug(f"Applied min_frequency filter ({min_frequency}): {len(unlabeled)} transactions remaining")

        if sort_by == "frequency":
            unlabeled.sort(key=lambda x: x.frequency, reverse=True)
        elif sort_by == "recent":
            unlabeled.sort(key=lambda x: x.last_seen or x.first_seen, reverse=True)
        elif sort_by == "amount":
            unlabeled.sort(key=lambda x: x.amount_range[1], reverse=True)

        # Cache the sorted results
        if not hasattr(self, '_unlabeled_cache'):
            self._unlabeled_cache = {}
        self._unlabeled_cache[cache_key] = unlabeled

        return unlabeled[offset:offset + batch_size]

    def get_filtered_transactions(self, filter_criteria: TransactionFilter,
                                 batch_size: Optional[int] = None,
                                 offset: int = 0) -> List[UniqueTransaction]:
        """
        Get transactions based on advanced filter criteria with pagination

        Args:
            filter_criteria: Filter criteria to apply
            batch_size: Optional limit on number of results
            offset: Number of results to skip (for pagination)

        Returns:
            List of filtered transactions
        """
        # Use cached results if available and filter hasn't changed
        cache_key = self._get_filter_cache_key(filter_criteria)
        if hasattr(self, '_filter_cache') and cache_key in self._filter_cache:
            cached_results = self._filter_cache[cache_key]
            if batch_size:
                return cached_results[offset:offset + batch_size]
            return cached_results[offset:]

        # Load and filter transactions
        all_transactions = list(self.data_preparator.load_unique_transactions().values())
        filtered_transactions = []

        # Early termination optimization - stop when we have enough results
        max_needed = (offset + batch_size) if batch_size else None

        for txn in all_transactions:
            if self._matches_filter(txn, filter_criteria):
                filtered_transactions.append(txn)

                # Early termination if we have enough results
                if max_needed and len(filtered_transactions) >= max_needed * 2:  # 2x buffer for sorting
                    break

        # Sort by frequency by default
        filtered_transactions.sort(key=lambda x: x.frequency, reverse=True)

        # Cache results for future use
        if not hasattr(self, '_filter_cache'):
            self._filter_cache = {}
        self._filter_cache[cache_key] = filtered_transactions

        # Apply pagination
        if batch_size:
            return filtered_transactions[offset:offset + batch_size]

        return filtered_transactions[offset:]

    def _get_filter_cache_key(self, filter_criteria: TransactionFilter) -> str:
        """Generate a cache key for filter criteria"""
        import hashlib

        # Create a string representation of the filter
        filter_str = f"{filter_criteria.description_text}_{filter_criteria.description_filter_type}_" \
                    f"{filter_criteria.case_sensitive}_{filter_criteria.min_amount}_{filter_criteria.max_amount}_" \
                    f"{filter_criteria.min_frequency}_{filter_criteria.max_frequency}_" \
                    f"{filter_criteria.labeling_status}_{filter_criteria.start_date}_{filter_criteria.end_date}_" \
                    f"{filter_criteria.category}_{filter_criteria.sub_category}_{filter_criteria.similar_to_hash_id}_" \
                    f"{filter_criteria.similarity_threshold}"

        # Generate hash for the filter string
        return hashlib.md5(filter_str.encode()).hexdigest()

    def clear_cache(self):
        """Clear all cached data to free memory"""
        if hasattr(self, '_filter_cache'):
            self._filter_cache.clear()
        if hasattr(self, '_unlabeled_cache'):
            self._unlabeled_cache.clear()
        if hasattr(self, '_similarity_cache'):
            self._similarity_cache.clear()

        self.logger.debug("Cleared all caches")

    def _matches_filter(self, transaction: UniqueTransaction, filter_criteria: TransactionFilter) -> bool:
        """
        Check if a transaction matches the filter criteria

        Args:
            transaction: Transaction to check
            filter_criteria: Filter criteria

        Returns:
            True if transaction matches all criteria
        """
        # Description filter
        if filter_criteria.description_text:
            if not self._matches_description_filter(transaction, filter_criteria):
                return False

        # Amount filter
        if filter_criteria.min_amount is not None or filter_criteria.max_amount is not None:
            if not self._matches_amount_filter(transaction, filter_criteria):
                return False

        # Frequency filter
        if filter_criteria.min_frequency is not None or filter_criteria.max_frequency is not None:
            if not self._matches_frequency_filter(transaction, filter_criteria):
                return False

        # Status filter
        if not self._matches_status_filter(transaction, filter_criteria):
            return False

        # Date filter
        if filter_criteria.start_date or filter_criteria.end_date:
            if not self._matches_date_filter(transaction, filter_criteria):
                return False

        # Category filter
        if filter_criteria.category or filter_criteria.sub_category:
            if not self._matches_category_filter(transaction, filter_criteria):
                return False

        # Similarity filter
        if filter_criteria.similar_to_hash_id:
            if not self._matches_similarity_filter(transaction, filter_criteria):
                return False

        return True

    def _matches_description_filter(self, transaction: UniqueTransaction,
                                   filter_criteria: TransactionFilter) -> bool:
        """Check if transaction matches description filter with enhanced multi-word support"""
        text = filter_criteria.description_text
        if not text:
            return True

        description = transaction.description if filter_criteria.case_sensitive else transaction.description.lower()

        if not filter_criteria.case_sensitive:
            text = text.lower()

        if filter_criteria.description_filter_type == FilterType.CONTAINS:
            return text in description
        elif filter_criteria.description_filter_type == FilterType.STARTS_WITH:
            return description.startswith(text)
        elif filter_criteria.description_filter_type == FilterType.ENDS_WITH:
            return description.endswith(text)
        elif filter_criteria.description_filter_type == FilterType.EXACT:
            return description == text
        elif filter_criteria.description_filter_type == FilterType.REGEX:
            try:
                flags = 0 if filter_criteria.case_sensitive else re.IGNORECASE
                return bool(re.search(text, description, flags))
            except re.error:
                return False
        elif filter_criteria.description_filter_type == FilterType.EXACT_PHRASE:
            # Exact phrase match - search for the exact phrase as a substring
            return text in description
        elif filter_criteria.description_filter_type == FilterType.MULTI_WORD_AND:
            # All words must be present (AND logic)
            words = self._parse_search_words(text)
            return all(word in description for word in words)
        elif filter_criteria.description_filter_type == FilterType.MULTI_WORD_OR:
            # Any word must be present (OR logic)
            words = self._parse_search_words(text)
            return any(word in description for word in words)

        return False

    def _parse_search_words(self, text: str) -> List[str]:
        """
        Parse search text into individual words, handling quoted phrases

        Args:
            text: Search text that may contain quoted phrases

        Returns:
            List of words/phrases to search for
        """
        words = []
        current_word = ""
        in_quotes = False

        i = 0
        while i < len(text):
            char = text[i]

            if char == '"':
                if in_quotes:
                    # End of quoted phrase
                    if current_word.strip():
                        words.append(current_word.strip())
                    current_word = ""
                    in_quotes = False
                else:
                    # Start of quoted phrase
                    if current_word.strip():
                        words.append(current_word.strip())
                    current_word = ""
                    in_quotes = True
            elif char == ' ' and not in_quotes:
                # Space outside quotes - end of word
                if current_word.strip():
                    words.append(current_word.strip())
                current_word = ""
            else:
                current_word += char

            i += 1

        # Add final word if any
        if current_word.strip():
            words.append(current_word.strip())

        # If no quoted phrases found, split by spaces
        if not words and text.strip():
            words = [word.strip() for word in text.split() if word.strip()]

        return words

    def _matches_amount_filter(self, transaction: UniqueTransaction,
                              filter_criteria: TransactionFilter) -> bool:
        """Check if transaction matches amount filter"""
        min_amount, max_amount = transaction.amount_range

        if filter_criteria.min_amount is not None:
            if max_amount < filter_criteria.min_amount:
                return False

        if filter_criteria.max_amount is not None:
            if min_amount > filter_criteria.max_amount:
                return False

        return True

    def _matches_frequency_filter(self, transaction: UniqueTransaction,
                                 filter_criteria: TransactionFilter) -> bool:
        """Check if transaction matches frequency filter"""
        if filter_criteria.min_frequency is not None:
            if transaction.frequency < filter_criteria.min_frequency:
                return False

        if filter_criteria.max_frequency is not None:
            if transaction.frequency > filter_criteria.max_frequency:
                return False

        return True

    def _matches_status_filter(self, transaction: UniqueTransaction,
                              filter_criteria: TransactionFilter) -> bool:
        """Check if transaction matches status filter"""
        if filter_criteria.labeling_status == LabelingStatus.ALL:
            return True
        elif filter_criteria.labeling_status == LabelingStatus.LABELED:
            return transaction.is_manually_labeled
        elif filter_criteria.labeling_status == LabelingStatus.UNLABELED:
            return not transaction.is_manually_labeled
        elif filter_criteria.labeling_status == LabelingStatus.PENDING:
            # Pending could mean has automatic label but not manually reviewed
            return bool(transaction.category) and not transaction.is_manually_labeled

        return True

    def _matches_date_filter(self, transaction: UniqueTransaction,
                            filter_criteria: TransactionFilter) -> bool:
        """Check if transaction matches date filter"""
        # Use last_seen date for filtering, fallback to first_seen
        txn_date = transaction.last_seen or transaction.first_seen

        if not txn_date:
            return False

        if filter_criteria.start_date and txn_date < filter_criteria.start_date:
            return False

        if filter_criteria.end_date and txn_date > filter_criteria.end_date:
            return False

        return True

    def _matches_category_filter(self, transaction: UniqueTransaction,
                                filter_criteria: TransactionFilter) -> bool:
        """Check if transaction matches category filter"""
        if filter_criteria.category and transaction.category != filter_criteria.category:
            return False

        if filter_criteria.sub_category and transaction.sub_category != filter_criteria.sub_category:
            return False

        return True

    def _matches_similarity_filter(self, transaction: UniqueTransaction,
                                  filter_criteria: TransactionFilter) -> bool:
        """Check if transaction matches similarity filter"""
        if not filter_criteria.similar_to_hash_id:
            return True

        # Get the reference transaction
        all_transactions = self.data_preparator.load_unique_transactions()
        reference_txn = all_transactions.get(filter_criteria.similar_to_hash_id)

        if not reference_txn:
            return False

        # Calculate similarity
        similarity = self._calculate_similarity(transaction.description, reference_txn.description)
        return similarity >= filter_criteria.similarity_threshold

    def _calculate_similarity(self, desc1: str, desc2: str) -> float:
        """Calculate similarity between two descriptions using enhanced similarity metrics"""
        # Normalize descriptions
        desc1_norm = desc1.lower().strip()
        desc2_norm = desc2.lower().strip()

        # Exact match
        if desc1_norm == desc2_norm:
            return 1.0

        # Apply semantic filtering first
        if self._are_semantically_different(desc1_norm, desc2_norm):
            return 0.0

        # Word-based Jaccard similarity
        words1 = set(desc1_norm.split())
        words2 = set(desc2_norm.split())

        if not words1 or not words2:
            return 0.0

        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))
        jaccard_similarity = intersection / union if union > 0 else 0

        # Early exit if Jaccard similarity is very low
        if jaccard_similarity < 0.1:
            return jaccard_similarity

        # Character-based similarity (for partial word matches)
        char_similarity = self._calculate_character_similarity(desc1_norm, desc2_norm)

        # Substring similarity (for common patterns)
        substring_similarity = self._calculate_substring_similarity(desc1_norm, desc2_norm)

        # Weighted combination - prioritize word-based similarity over character similarity
        final_similarity = (
            0.7 * jaccard_similarity +
            0.1 * char_similarity +
            0.2 * substring_similarity
        )

        return min(final_similarity, 1.0)

    def _calculate_similarity_optimized(self, desc1_lower: str, desc2_lower: str,
                                       words1: set, words2: set) -> float:
        """Optimized similarity calculation with pre-processed data"""
        # Exact match check
        if desc1_lower == desc2_lower:
            return 1.0

        # Quick word-based Jaccard similarity (already have word sets)
        if not words1 or not words2:
            return 0.0

        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))
        jaccard_similarity = intersection / union if union > 0 else 0

        # Early return if Jaccard similarity is very low
        if jaccard_similarity < 0.1:
            return jaccard_similarity

        # Only calculate expensive metrics for promising candidates
        char_similarity = self._calculate_character_similarity_fast(desc1_lower, desc2_lower)
        substring_similarity = self._calculate_substring_similarity_fast(desc1_lower, desc2_lower)

        # Weighted combination - prioritize word-based similarity over character similarity
        final_similarity = (
            0.7 * jaccard_similarity +
            0.1 * char_similarity +
            0.2 * substring_similarity
        )

        return min(final_similarity, 1.0)

    def _calculate_character_similarity_fast(self, desc1: str, desc2: str) -> float:
        """Fast character-level similarity calculation with improved discrimination"""
        if not desc1 or not desc2:
            return 0.0

        # Filter out common, non-discriminating characters
        def filter_chars(text):
            # Remove spaces, numbers, and common punctuation that don't add meaning
            filtered = ''.join(c.lower() for c in text if c.isalpha() and c.lower() not in 'aeiou')
            return filtered

        filtered1 = filter_chars(desc1)
        filtered2 = filter_chars(desc2)

        if not filtered1 or not filtered2:
            # Fallback to basic character overlap if filtering removes everything
            chars1 = set(c.lower() for c in desc1 if c.isalpha())
            chars2 = set(c.lower() for c in desc2 if c.isalpha())
            if not chars1 or not chars2:
                return 0.0
            intersection = len(chars1.intersection(chars2))
            union = len(chars1.union(chars2))
            return intersection / union if union > 0 else 0

        # Use character frequency for meaningful characters only
        chars1 = {}
        chars2 = {}

        for char in filtered1:
            chars1[char] = chars1.get(char, 0) + 1
        for char in filtered2:
            chars2[char] = chars2.get(char, 0) + 1

        # Calculate overlap with emphasis on unique characters
        common_chars = 0
        total_chars = 0

        all_chars = set(chars1.keys()) | set(chars2.keys())
        for char in all_chars:
            count1 = chars1.get(char, 0)
            count2 = chars2.get(char, 0)
            common_chars += min(count1, count2)
            total_chars += max(count1, count2)

        # Apply a penalty for very short filtered strings
        min_length = min(len(filtered1), len(filtered2))
        if min_length < 3:
            return (common_chars / total_chars) * 0.5 if total_chars > 0 else 0

        return common_chars / total_chars if total_chars > 0 else 0

    def _calculate_substring_similarity_fast(self, desc1: str, desc2: str) -> float:
        """Fast substring similarity with improved discrimination"""
        if not desc1 or not desc2:
            return 0.0

        # Filter out common, non-discriminating substrings
        common_patterns = {
            'upi', 'debit', 'credit', 'thru', 'xxxxx', 'seq', 'no', 'id', 'date', 'time',
            'mmdd', 'hhmmss', 'atm', 'pos', 'bna', 'dep', 'tran', 'prch', 'txn'
        }

        # Find meaningful substrings of length 4+ (increased from 3)
        min_length = 4
        max_common_length = 0
        meaningful_matches = 0

        # Only check substrings up to a reasonable length for performance
        max_check_length = min(len(desc1), len(desc2), 25)

        for i in range(len(desc1) - min_length + 1):
            for length in range(min_length, min(max_check_length, len(desc1) - i + 1)):
                substring = desc1[i:i + length].lower()

                # Skip if substring is just common patterns
                if substring in common_patterns or substring.isdigit():
                    continue

                # Skip if substring is mostly numbers or special characters
                alpha_count = sum(1 for c in substring if c.isalpha())
                if alpha_count < length * 0.6:  # At least 60% alphabetic
                    continue

                if substring in desc2.lower():
                    max_common_length = max(max_common_length, length)
                    meaningful_matches += 1

        # Penalize if no meaningful matches found
        if meaningful_matches == 0:
            return 0.0

        # Normalize by average length with additional penalty for short matches
        avg_length = (len(desc1) + len(desc2)) / 2
        base_similarity = max_common_length / avg_length if avg_length > 0 else 0

        # Apply penalty for very short meaningful substrings
        if max_common_length < 6:
            base_similarity *= 0.7

        return base_similarity

    def _calculate_character_similarity(self, desc1: str, desc2: str) -> float:
        """Calculate character-level similarity with improved discrimination"""
        if not desc1 or not desc2:
            return 0.0

        # Focus on meaningful alphabetic characters, excluding common ones
        chars1 = set(c.lower() for c in desc1 if c.isalpha() and c.lower() not in 'aeiou ')
        chars2 = set(c.lower() for c in desc2 if c.isalpha() and c.lower() not in 'aeiou ')

        if not chars1 or not chars2:
            return 0.0

        intersection = len(chars1.intersection(chars2))
        union = len(chars1.union(chars2))

        # Apply penalty for very small character sets
        min_chars = min(len(chars1), len(chars2))
        penalty = 1.0 if min_chars >= 3 else 0.5

        return (intersection / union) * penalty if union > 0 else 0

    def _calculate_substring_similarity(self, desc1: str, desc2: str) -> float:
        """Calculate similarity based on meaningful common substrings"""
        if not desc1 or not desc2:
            return 0.0

        desc1_lower = desc1.lower()
        desc2_lower = desc2.lower()

        # Find longest meaningful common substring
        max_length = 0
        meaningful_found = False

        for i in range(len(desc1_lower)):
            for j in range(len(desc2_lower)):
                length = 0
                while (i + length < len(desc1_lower) and
                       j + length < len(desc2_lower) and
                       desc1_lower[i + length] == desc2_lower[j + length]):
                    length += 1

                # Only consider substrings of length 4+ as meaningful
                if length >= 4:
                    substring = desc1_lower[i:i + length]

                    # Check if it's a meaningful substring (not just common patterns)
                    if self._is_meaningful_substring(substring):
                        max_length = max(max_length, length)
                        meaningful_found = True

        # Return 0 if no meaningful substrings found
        if not meaningful_found:
            return 0.0

        # Normalize by average length
        avg_length = (len(desc1) + len(desc2)) / 2
        return max_length / avg_length if avg_length > 0 else 0

    def _is_meaningful_substring(self, substring: str) -> bool:
        """Check if a substring is meaningful for similarity comparison"""
        # Skip common transaction patterns
        common_patterns = {
            'upi', 'debit', 'credit', 'thru', 'xxxxx', 'seq', 'no', 'id', 'date', 'time',
            'mmdd', 'hhmmss', 'atm', 'pos', 'bna', 'dep', 'tran', 'prch', 'txn', 'onus'
        }

        if substring in common_patterns:
            return False

        # Skip if mostly numbers
        if substring.isdigit():
            return False

        # Skip if mostly special characters
        alpha_count = sum(1 for c in substring if c.isalpha())
        if alpha_count < len(substring) * 0.5:  # At least 50% alphabetic
            return False

        return True

    def find_similar_transactions(self, reference_hash_id: str,
                                 similarity_threshold: float = 0.3,
                                 limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Find transactions similar to a reference transaction with caching and optimization

        Args:
            reference_hash_id: Hash ID of the reference transaction
            similarity_threshold: Minimum similarity score
            limit: Maximum number of results

        Returns:
            List of similar transactions with similarity scores
        """
        # Check cache first
        cache_key = f"{reference_hash_id}_{similarity_threshold}_{limit}"
        if hasattr(self, '_similarity_cache') and cache_key in self._similarity_cache:
            self.logger.debug(f"Using cached similarity results for {reference_hash_id}")
            return self._similarity_cache[cache_key]

        all_transactions = self.data_preparator.load_unique_transactions()
        reference_txn = all_transactions.get(reference_hash_id)

        if not reference_txn:
            return []

        # Pre-process reference description for faster comparison
        reference_words = set(reference_txn.normalized_description.lower().split())
        reference_desc_lower = reference_txn.description.lower()

        similar_transactions = []
        processed_count = 0

        for txn in all_transactions.values():
            if txn.hash_id == reference_hash_id:
                continue

            processed_count += 1

            # Quick pre-filter using word overlap for performance
            txn_words = set(txn.normalized_description.lower().split())
            word_overlap = len(reference_words.intersection(txn_words))

            # Skip if no word overlap (likely very low similarity)
            if word_overlap == 0:
                continue

            # Calculate full similarity only for promising candidates
            similarity = self._calculate_similarity_optimized(
                reference_desc_lower, txn.description.lower(),
                reference_words, txn_words
            )

            if similarity >= similarity_threshold:
                similar_transactions.append({
                    "transaction": txn,
                    "similarity": similarity,
                    "hash_id": txn.hash_id,
                    "description": txn.description,
                    "frequency": txn.frequency,
                    "amount_range": txn.amount_range,
                    "is_labeled": txn.is_manually_labeled,
                    "category": txn.category,
                    "sub_category": txn.sub_category
                })

                # Early termination if we have enough results
                if limit and len(similar_transactions) >= limit * 2:  # 2x buffer for sorting
                    break

        # Sort by similarity score
        similar_transactions.sort(key=lambda x: x["similarity"], reverse=True)

        # Apply limit
        if limit:
            similar_transactions = similar_transactions[:limit]

        # Cache results
        if not hasattr(self, '_similarity_cache'):
            self._similarity_cache = {}
        self._similarity_cache[cache_key] = similar_transactions

        self.logger.debug(f"Processed {processed_count} transactions, found {len(similar_transactions)} similar ones")
        return similar_transactions

    def get_batch_labeling_suggestions(self, hash_ids: List[str]) -> Dict[str, Any]:
        """
        Get labeling suggestions for a batch of transactions

        Args:
            hash_ids: List of transaction hash IDs

        Returns:
            Batch suggestions with common patterns and recommended labels
        """
        all_transactions = self.data_preparator.load_unique_transactions()
        batch_transactions = [all_transactions[hid] for hid in hash_ids if hid in all_transactions]

        if not batch_transactions:
            return {"suggestions": [], "common_patterns": []}

        # Find common words/patterns
        all_words = []
        for txn in batch_transactions:
            all_words.extend(txn.normalized_description.lower().split())

        # Count word frequency
        word_counts = {}
        for word in all_words:
            word_counts[word] = word_counts.get(word, 0) + 1

        # Find common patterns (words appearing in most transactions)
        common_patterns = []
        threshold = len(batch_transactions) * 0.5  # At least 50% of transactions

        for word, count in word_counts.items():
            if count >= threshold and len(word) > 2:  # Skip short words
                common_patterns.append({
                    "pattern": word,
                    "frequency": count,
                    "percentage": (count / len(batch_transactions)) * 100
                })

        common_patterns.sort(key=lambda x: x["frequency"], reverse=True)

        # Get label suggestions based on similar already-labeled transactions
        label_suggestions = {}

        for txn in batch_transactions:
            suggestions = self.suggest_similar_labels(txn.description, limit=3)
            for suggestion in suggestions:
                label_key = f"{suggestion['category']}/{suggestion['sub_category']}"
                if label_key not in label_suggestions:
                    label_suggestions[label_key] = {
                        "category": suggestion["category"],
                        "sub_category": suggestion["sub_category"],
                        "confidence": suggestion["similarity"],
                        "supporting_transactions": 0
                    }
                label_suggestions[label_key]["supporting_transactions"] += 1

        # Sort suggestions by support
        sorted_suggestions = sorted(
            label_suggestions.values(),
            key=lambda x: (x["supporting_transactions"], x["confidence"]),
            reverse=True
        )

        return {
            "suggestions": sorted_suggestions[:5],  # Top 5 suggestions
            "common_patterns": common_patterns[:10],  # Top 10 patterns
            "batch_size": len(batch_transactions),
            "unlabeled_count": sum(1 for txn in batch_transactions if not txn.is_manually_labeled)
        }

    def validate_batch_labeling(self, labels: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Validate a batch of labels before applying them

        Args:
            labels: List of label dictionaries

        Returns:
            Validation results with warnings and errors
        """
        validation_result = {
            "valid": True,
            "warnings": [],
            "errors": [],
            "summary": {
                "total_labels": len(labels),
                "valid_labels": 0,
                "invalid_labels": 0
            }
        }

        all_transactions = self.data_preparator.load_unique_transactions()

        for i, label_data in enumerate(labels):
            label_errors = []
            label_warnings = []

            # Check required fields
            required_fields = ["hash_id", "category", "sub_category"]
            for field in required_fields:
                if field not in label_data or not label_data[field]:
                    label_errors.append(f"Missing required field: {field}")

            # Check if transaction exists
            if "hash_id" in label_data:
                if label_data["hash_id"] not in all_transactions:
                    label_errors.append(f"Transaction not found: {label_data['hash_id']}")
                else:
                    txn = all_transactions[label_data["hash_id"]]

                    # Check if already labeled
                    if txn.is_manually_labeled:
                        label_warnings.append(f"Transaction already manually labeled")

                    # Check confidence value
                    confidence = label_data.get("confidence", 1.0)
                    if not 0 <= confidence <= 1:
                        label_errors.append(f"Invalid confidence value: {confidence}")

            if label_errors:
                validation_result["errors"].extend([f"Label {i+1}: {error}" for error in label_errors])
                validation_result["summary"]["invalid_labels"] += 1
                validation_result["valid"] = False
            else:
                validation_result["summary"]["valid_labels"] += 1

            if label_warnings:
                validation_result["warnings"].extend([f"Label {i+1}: {warning}" for warning in label_warnings])

        return validation_result

    def find_transaction_patterns(self, min_frequency: int = 3) -> List[Dict[str, Any]]:
        """
        Find common patterns in transaction descriptions

        Args:
            min_frequency: Minimum frequency for a pattern to be considered

        Returns:
            List of patterns with their frequencies and examples
        """
        all_transactions = list(self.data_preparator.load_unique_transactions().values())

        # Extract common word patterns
        word_patterns = {}
        phrase_patterns = {}

        for txn in all_transactions:
            words = txn.normalized_description.lower().split()

            # Single word patterns
            for word in words:
                if len(word) > 3:  # Skip short words
                    word_patterns[word] = word_patterns.get(word, 0) + txn.frequency

            # Two-word phrase patterns
            for i in range(len(words) - 1):
                phrase = f"{words[i]} {words[i+1]}"
                if len(phrase) > 6:  # Skip short phrases
                    phrase_patterns[phrase] = phrase_patterns.get(phrase, 0) + txn.frequency

        # Filter by minimum frequency and create pattern objects
        patterns = []

        # Add word patterns
        for word, freq in word_patterns.items():
            if freq >= min_frequency:
                # Find example transactions
                examples = [
                    txn for txn in all_transactions
                    if word in txn.normalized_description.lower()
                ][:3]

                patterns.append({
                    "type": "word",
                    "pattern": word,
                    "frequency": freq,
                    "transaction_count": len([txn for txn in all_transactions if word in txn.normalized_description.lower()]),
                    "examples": [{"description": txn.description, "frequency": txn.frequency} for txn in examples]
                })

        # Add phrase patterns
        for phrase, freq in phrase_patterns.items():
            if freq >= min_frequency:
                # Find example transactions
                examples = [
                    txn for txn in all_transactions
                    if phrase in txn.normalized_description.lower()
                ][:3]

                patterns.append({
                    "type": "phrase",
                    "pattern": phrase,
                    "frequency": freq,
                    "transaction_count": len([txn for txn in all_transactions if phrase in txn.normalized_description.lower()]),
                    "examples": [{"description": txn.description, "frequency": txn.frequency} for txn in examples]
                })

        # Sort by frequency
        patterns.sort(key=lambda x: x["frequency"], reverse=True)

        return patterns

    def suggest_auto_labeling_candidates(self, confidence_threshold: float = 0.8) -> List[Dict[str, Any]]:
        """
        Suggest transactions that could be automatically labeled based on patterns

        Args:
            confidence_threshold: Minimum confidence for auto-labeling suggestion

        Returns:
            List of auto-labeling candidates
        """
        unlabeled_transactions = self.data_preparator.get_unlabeled_transactions()
        labeled_transactions = [
            txn for txn in self.data_preparator.load_unique_transactions().values()
            if txn.is_manually_labeled
        ]

        if not labeled_transactions:
            return []

        candidates = []

        for unlabeled_txn in unlabeled_transactions:
            best_match = None
            best_similarity = 0

            # Find best matching labeled transaction
            for labeled_txn in labeled_transactions:
                similarity = self._calculate_similarity(
                    unlabeled_txn.description,
                    labeled_txn.description
                )

                if similarity > best_similarity:
                    best_similarity = similarity
                    best_match = labeled_txn

            # If similarity is high enough, suggest auto-labeling
            if best_similarity >= confidence_threshold and best_match:
                candidates.append({
                    "transaction": unlabeled_txn,
                    "suggested_category": best_match.category,
                    "suggested_sub_category": best_match.sub_category,
                    "confidence": best_similarity,
                    "similar_transaction": {
                        "description": best_match.description,
                        "frequency": best_match.frequency
                    },
                    "potential_impact": unlabeled_txn.frequency  # How many transactions this would label
                })

        # Sort by potential impact and confidence
        candidates.sort(key=lambda x: (x["potential_impact"], x["confidence"]), reverse=True)

        return candidates

    def get_labeling_recommendations(self) -> Dict[str, Any]:
        """
        Get comprehensive labeling recommendations

        Returns:
            Dictionary with various recommendations for improving labeling efficiency
        """
        stats = self.get_labeling_stats()
        patterns = self.find_transaction_patterns(min_frequency=5)
        auto_candidates = self.suggest_auto_labeling_candidates(confidence_threshold=0.8)

        recommendations = {
            "priority_transactions": [],
            "pattern_opportunities": [],
            "auto_labeling_candidates": auto_candidates[:10],  # Top 10
            "efficiency_tips": []
        }

        # Priority transactions (high frequency, unlabeled)
        unlabeled_transactions = self.data_preparator.get_unlabeled_transactions()
        high_frequency_unlabeled = [
            txn for txn in unlabeled_transactions
            if txn.frequency >= 5
        ][:10]

        recommendations["priority_transactions"] = [
            {
                "description": txn.description,
                "frequency": txn.frequency,
                "hash_id": txn.hash_id,
                "impact": f"Labeling this will categorize {txn.frequency} transactions"
            }
            for txn in high_frequency_unlabeled
        ]

        # Pattern opportunities
        unlabeled_patterns = [
            pattern for pattern in patterns
            if any(
                pattern["pattern"] in txn.normalized_description.lower()
                for txn in unlabeled_transactions
            )
        ][:5]

        recommendations["pattern_opportunities"] = [
            {
                "pattern": pattern["pattern"],
                "type": pattern["type"],
                "frequency": pattern["frequency"],
                "suggestion": f"Consider batch labeling transactions containing '{pattern['pattern']}'"
            }
            for pattern in unlabeled_patterns
        ]

        # Efficiency tips
        if stats.labeling_progress < 20:
            recommendations["efficiency_tips"].append(
                "Focus on high-frequency transactions first to maximize impact"
            )

        if len(auto_candidates) > 5:
            recommendations["efficiency_tips"].append(
                f"Consider auto-labeling {len(auto_candidates)} high-confidence matches"
            )

        if len(patterns) > 10:
            recommendations["efficiency_tips"].append(
                "Use pattern-based filtering to group similar transactions for batch labeling"
            )

        return recommendations


    
    def suggest_similar_labels(self, description: str, limit: int = 5) -> List[Dict[str, Any]]:
        """
        Suggest labels based on similar already-labeled transactions
        
        Args:
            description: Transaction description to find similar labels for
            limit: Maximum number of suggestions
            
        Returns:
            List of suggested labels with similarity scores
        """
        unique_transactions = self.data_preparator.load_unique_transactions()
        
        # Get labeled transactions
        labeled_transactions = [
            txn for txn in unique_transactions.values() 
            if txn.is_manually_labeled and txn.category
        ]
        
        if not labeled_transactions:
            return []
        
        # Simple similarity based on common words
        description_words = set(description.lower().split())
        suggestions = []
        
        for txn in labeled_transactions:
            txn_words = set(txn.normalized_description.lower().split())
            
            # Calculate Jaccard similarity
            intersection = len(description_words.intersection(txn_words))
            union = len(description_words.union(txn_words))
            similarity = intersection / union if union > 0 else 0
            
            if similarity > 0.1:  # Minimum similarity threshold
                suggestions.append({
                    "category": txn.category,
                    "sub_category": txn.sub_category,
                    "similarity": similarity,
                    "example_description": txn.description,
                    "frequency": txn.frequency
                })
        
        # Sort by similarity and frequency
        suggestions.sort(key=lambda x: (x["similarity"], x["frequency"]), reverse=True)
        
        # Remove duplicates and limit results
        seen_labels = set()
        unique_suggestions = []
        
        for suggestion in suggestions:
            label_key = f"{suggestion['category']}/{suggestion['sub_category']}"
            if label_key not in seen_labels:
                seen_labels.add(label_key)
                unique_suggestions.append(suggestion)
                
                if len(unique_suggestions) >= limit:
                    break
        
        return unique_suggestions
    
    def export_training_data(self, format: str = "csv") -> Optional[str]:
        """
        Export training data for external use
        
        Args:
            format: Export format ("csv", "json")
            
        Returns:
            Path to exported file or None if failed
        """
        try:
            training_data = self.data_preparator.get_training_data()
            
            if training_data.empty:
                self.logger.warning("No training data to export")
                return None
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            if format == "csv":
                export_file = self.data_dir / f"training_data_export_{timestamp}.csv"
                training_data.to_csv(export_file, index=False)
            elif format == "json":
                export_file = self.data_dir / f"training_data_export_{timestamp}.json"
                training_data.to_json(export_file, orient="records", indent=2)
            else:
                self.logger.error(f"Unsupported export format: {format}")
                return None
            
            self.logger.info(f"Training data exported to: {export_file}")
            return str(export_file)

        except Exception as e:
            self.logger.error(f"Error exporting training data: {str(e)}")
            return None

    def add_ai_categorization_to_training(self, transaction: RawTransaction,
                                        category: str, sub_category: str,
                                        confidence: float, source: str = "ai") -> bool:
        """
        Add AI categorization result to training data

        Args:
            transaction: The transaction that was categorized
            category: AI-determined category
            sub_category: AI-determined sub-category
            confidence: AI confidence score
            source: Source of categorization

        Returns:
            True if added successfully
        """
        try:
            # Extract unique transaction from raw transaction
            unique_transactions = self.data_preparator.extract_unique_transactions([transaction])

            if unique_transactions:
                # Get the first (and only) unique transaction
                unique_transaction = next(iter(unique_transactions.values()))

                # Add to training data with AI source
                success = self.data_preparator.update_transaction_label(
                    unique_transaction.hash_id,
                    category,
                    sub_category,
                    confidence,
                    labeled_by=f"ai_{source}"
                )

                if success:
                    self.logger.debug(f"Added AI categorization to training: {transaction.description[:50]}")
                    return True

            return False

        except Exception as e:
            self.logger.error(f"Error adding AI categorization to training: {str(e)}")
            return False
    
    def _record_labeling_action(self, hash_id: str, category: str, sub_category: str, confidence: float):
        """Record a labeling action to history"""
        try:
            action_record = {
                "timestamp": datetime.now().isoformat(),
                "session_id": self.current_session.session_id if self.current_session else "manual",
                "user_name": self.current_session.user_name if self.current_session else "user",
                "hash_id": hash_id,
                "category": category,
                "sub_category": sub_category,
                "confidence": confidence
            }
            
            # Append to history file
            history_df = pd.DataFrame([action_record])
            
            if self.labeling_history_file.exists():
                existing_df = pd.read_csv(self.labeling_history_file)
                history_df = pd.concat([existing_df, history_df], ignore_index=True)
            
            history_df.to_csv(self.labeling_history_file, index=False)
            
        except Exception as e:
            self.logger.error(f"Error recording labeling action: {str(e)}")
    
    def _save_session_to_history(self, session: TrainingSession):
        """Save session to history file"""
        try:
            sessions = []
            
            # Load existing sessions
            if self.sessions_file.exists():
                with open(self.sessions_file, 'r') as f:
                    sessions = json.load(f)
            
            # Add current session
            session_dict = asdict(session)
            session_dict["started_at"] = session.started_at.isoformat()
            if session.completed_at:
                session_dict["completed_at"] = session.completed_at.isoformat()
            
            sessions.append(session_dict)
            
            # Save back to file
            with open(self.sessions_file, 'w') as f:
                json.dump(sessions, f, indent=2)
                
        except Exception as e:
            self.logger.error(f"Error saving session to history: {str(e)}")
    
    def get_labeling_history(self, limit: int = 100) -> pd.DataFrame:
        """
        Get labeling history
        
        Args:
            limit: Maximum number of records to return
            
        Returns:
            DataFrame with labeling history
        """
        if not self.labeling_history_file.exists():
            return pd.DataFrame()
        
        try:
            df = pd.read_csv(self.labeling_history_file)
            return df.tail(limit)
        except Exception as e:
            self.logger.error(f"Error loading labeling history: {str(e)}")
            return pd.DataFrame()
    
    def validate_training_data_quality(self) -> Dict[str, Any]:
        """
        Validate the quality of training data
        
        Returns:
            Quality metrics and recommendations
        """
        training_data = self.data_preparator.get_training_data()
        
        if training_data.empty:
            return {"error": "No training data available"}
        
        # Calculate quality metrics
        metrics = {
            "total_samples": len(training_data),
            "unique_categories": training_data['category'].nunique(),
            "unique_subcategories": training_data['sub_category'].nunique(),
            "category_distribution": training_data['category'].value_counts().to_dict(),
            "min_samples_per_category": training_data['category'].value_counts().min(),
            "max_samples_per_category": training_data['category'].value_counts().max(),
            "manually_labeled_ratio": training_data['is_manually_labeled'].mean(),
            "recommendations": []
        }
        
        # Generate recommendations
        if metrics["total_samples"] < 50:
            metrics["recommendations"].append("Consider labeling more transactions (recommended: 50+)")
        
        if metrics["min_samples_per_category"] < 5:
            metrics["recommendations"].append("Some categories have very few samples (recommended: 5+ per category)")
        
        if metrics["manually_labeled_ratio"] < 0.8:
            metrics["recommendations"].append("Consider manually reviewing more automatic labels")
        
        return metrics

    def _are_semantically_different(self, desc1: str, desc2: str) -> bool:
        """Check if two descriptions are semantically different and should not be considered similar"""

        # Extract merchants from both descriptions
        merchants1 = self._extract_merchants(desc1)
        merchants2 = self._extract_merchants(desc2)

        # If both have merchants and they're different, they're semantically different
        if merchants1 and merchants2 and not merchants1.intersection(merchants2):
            return True

        # Extract transaction types
        types1 = self._extract_transaction_types(desc1)
        types2 = self._extract_transaction_types(desc2)

        # If they have different primary transaction types, they're different
        if types1 and types2:
            # Check for incompatible transaction types
            incompatible_pairs = [
                ({'atm'}, {'pos'}),
                ({'upi'}, {'neft'}),
                ({'upi'}, {'imps'}),
                ({'credit'}, {'debit'})
            ]

            for type_set1, type_set2 in incompatible_pairs:
                if (type_set1.issubset(types1) and type_set2.issubset(types2)) or \
                   (type_set2.issubset(types1) and type_set1.issubset(types2)):
                    return True

        # Check for different payment directions (credit vs debit)
        if self._has_different_payment_directions(desc1, desc2):
            return True

        return False

    def _extract_merchants(self, description: str) -> set:
        """Extract merchant names from transaction description"""
        merchants = set()

        # Common merchant patterns
        merchant_patterns = [
            'zomato', 'swiggy', 'amazon', 'flipkart', 'paytm', 'phonepe', 'googlepay',
            'meesho', 'myntra', 'bigbasket', 'grofers', 'dunzo', 'uber', 'ola',
            'netflix', 'spotify', 'hotstar', 'prime', 'jio', 'airtel', 'vodafone'
        ]

        for merchant in merchant_patterns:
            if merchant in description:
                merchants.add(merchant)

        return merchants

    def _extract_transaction_types(self, description: str) -> set:
        """Extract transaction types from description"""
        types = set()

        type_patterns = ['atm', 'pos', 'upi', 'neft', 'imps', 'credit', 'debit', 'transfer']

        for type_pattern in type_patterns:
            if type_pattern in description:
                types.add(type_pattern)

        return types

    def _has_different_payment_directions(self, desc1: str, desc2: str) -> bool:
        """Check if descriptions have different payment directions (credit vs debit)"""
        # Only consider this if one is clearly credit and other is clearly debit
        is_credit1 = 'credit' in desc1 and 'debit' not in desc1
        is_debit1 = 'debit' in desc1 and 'credit' not in desc1
        is_credit2 = 'credit' in desc2 and 'debit' not in desc2
        is_debit2 = 'debit' in desc2 and 'credit' not in desc2

        return (is_credit1 and is_debit2) or (is_debit1 and is_credit2)

    def find_similar_transactions_with_embeddings(self, reference_hash_id: str,
                                                 similarity_threshold: float = 0.7,
                                                 limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Find similar transactions using SambaNova embeddings for better semantic similarity

        Args:
            reference_hash_id: Hash ID of the reference transaction
            similarity_threshold: Minimum similarity score (0-1)
            limit: Maximum number of results

        Returns:
            List of similar transactions with similarity scores
        """
        if not self.sambanova_client.is_available():
            self.logger.warning("SambaNova client not available, falling back to traditional similarity")
            return self.find_similar_transactions(reference_hash_id, similarity_threshold * 0.5, limit)

        all_transactions = self.data_preparator.load_unique_transactions()
        reference_txn = all_transactions.get(reference_hash_id)

        if not reference_txn:
            return []

        try:
            # Get embedding for reference transaction
            reference_embedding = self._get_transaction_embedding(reference_txn.description)
            if not reference_embedding:
                # Fallback to traditional similarity
                return self.find_similar_transactions(reference_hash_id, similarity_threshold * 0.5, limit)

            similar_transactions = []

            # Compare with other transactions
            for txn in all_transactions.values():
                if txn.hash_id == reference_hash_id:
                    continue

                # Get embedding for comparison transaction
                txn_embedding = self._get_transaction_embedding(txn.description)
                if not txn_embedding:
                    continue

                # Calculate cosine similarity
                similarity = self._cosine_similarity(reference_embedding, txn_embedding)

                if similarity >= similarity_threshold:
                    similar_transactions.append({
                        "transaction": txn,
                        "similarity": similarity,
                        "hash_id": txn.hash_id,
                        "description": txn.description,
                        "frequency": txn.frequency,
                        "amount_range": txn.amount_range,
                        "is_labeled": txn.is_manually_labeled,
                        "category": txn.category,
                        "sub_category": txn.sub_category,
                        "similarity_method": "sambanova_embeddings"
                    })

            # Sort by similarity score
            similar_transactions.sort(key=lambda x: x["similarity"], reverse=True)

            # Apply limit
            if limit:
                similar_transactions = similar_transactions[:limit]

            self.logger.debug(f"Found {len(similar_transactions)} similar transactions using SambaNova embeddings")
            return similar_transactions

        except Exception as e:
            self.logger.error(f"Error in SambaNova embedding similarity: {str(e)}")
            # Fallback to traditional similarity
            return self.find_similar_transactions(reference_hash_id, similarity_threshold * 0.5, limit)

    def _get_transaction_embedding(self, description: str) -> Optional[List[float]]:
        """
        Get SambaNova embedding for a transaction description with caching
        Note: SambaNova doesn't provide embeddings, so this returns None for now

        Args:
            description: Transaction description

        Returns:
            Embedding vector or None (SambaNova doesn't provide embeddings)
        """
        # SambaNova doesn't provide embedding endpoints like Cohere
        # For now, return None to fall back to traditional similarity
        self.logger.debug("SambaNova embeddings not available, using traditional similarity")
        return None

    def _cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """
        Calculate cosine similarity between two vectors

        Args:
            vec1: First vector
            vec2: Second vector

        Returns:
            Cosine similarity score (0-1)
        """
        try:
            import numpy as np

            # Convert to numpy arrays
            a = np.array(vec1)
            b = np.array(vec2)

            # Calculate cosine similarity
            dot_product = np.dot(a, b)
            norm_a = np.linalg.norm(a)
            norm_b = np.linalg.norm(b)

            if norm_a == 0 or norm_b == 0:
                return 0.0

            similarity = dot_product / (norm_a * norm_b)

            # Ensure result is between 0 and 1
            return max(0.0, min(1.0, similarity))

        except Exception as e:
            self.logger.error(f"Error calculating cosine similarity: {str(e)}")
            return 0.0

    def clear_embedding_cache(self):
        """Clear the embedding cache"""
        self._embedding_cache = {}
        self.logger.info("Embedding cache cleared")

    def get_embedding_stats(self) -> Dict[str, Any]:
        """Get embedding cache statistics"""
        return {
            "cache_size": len(self._embedding_cache),
            "sambanova_available": self.sambanova_client.is_available(),
            "sambanova_enabled": self.sambanova_config.enabled
        }
