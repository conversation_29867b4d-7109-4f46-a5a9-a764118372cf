"""
Transaction Context Analyzer for Enhanced AI Categorization

This module provides advanced transaction context analysis that considers:
- Transaction type (debit/credit) patterns
- Amount ranges and patterns
- Temporal patterns (time of day, day of week, month)
- Merchant behavior patterns
- Account-specific patterns

The context information is used to improve AI categorization accuracy
and provide better routing decisions.
"""

import re
from typing import Dict, List, Optional, Tuple, Any, Set
from dataclasses import dataclass
from datetime import datetime, date, time
from decimal import Decimal
from collections import defaultdict
import statistics

from ..models.transaction import RawTransaction
from ..core.logger import get_logger


@dataclass
class TransactionContext:
    """Comprehensive context information for a transaction"""
    # Basic transaction info
    transaction_type: str  # DEBIT, CREDIT
    amount: float
    amount_category: str  # micro, small, medium, large, huge
    
    # Temporal context
    hour_of_day: int
    day_of_week: int  # 0=Monday, 6=Sunday
    day_of_month: int
    month: int
    is_weekend: bool
    is_business_hours: bool
    
    # Pattern context
    is_round_amount: bool  # ends in 00
    is_recurring_pattern: bool  # appears to be recurring
    merchant_type: str  # atm, pos, online, transfer, etc.
    
    # Amount patterns
    amount_percentile: float  # where this amount falls in user's spending
    is_unusual_amount: bool  # significantly different from typical amounts
    
    # Description patterns
    has_reference_number: bool
    has_merchant_code: bool
    description_complexity: float  # 0-1 score
    
    # Contextual flags
    likely_income: bool
    likely_expense: bool
    likely_transfer: bool
    likely_fee: bool
    likely_refund: bool


@dataclass
class AmountPattern:
    """Pattern analysis for transaction amounts"""
    min_amount: float
    max_amount: float
    avg_amount: float
    median_amount: float
    std_deviation: float
    common_amounts: List[float]  # Most frequent amounts
    percentiles: Dict[int, float]  # 25th, 50th, 75th, 90th percentiles


@dataclass
class MerchantBehavior:
    """Behavioral patterns for a specific merchant"""
    merchant_pattern: str
    typical_transaction_type: str
    typical_amount_range: Tuple[float, float]
    typical_times: List[int]  # Common hours
    typical_days: List[int]   # Common days of week
    frequency_pattern: str    # daily, weekly, monthly, irregular
    confidence_score: float


class TransactionContextAnalyzer:
    """
    Analyzes transaction context to provide enhanced categorization hints
    """
    
    def __init__(self):
        self.logger = get_logger(__name__)
        
        # Amount thresholds (can be customized per user)
        self.amount_thresholds = {
            'micro': 50.0,      # < ₹50
            'small': 500.0,     # ₹50-500
            'medium': 2000.0,   # ₹500-2000
            'large': 10000.0,   # ₹2000-10000
            'huge': float('inf') # > ₹10000
        }
        
        # Business hours (9 AM to 6 PM)
        self.business_start = 9
        self.business_end = 18
        
        # Pattern recognition
        self.merchant_patterns = {
            'atm': r'ATM|CASH\s*WITHDRAWAL',
            'pos': r'POS|PURCHASE',
            'online': r'ONLINE|INTERNET|WEB',
            'upi': r'UPI|UNIFIED\s*PAYMENT',
            'neft': r'NEFT|NATIONAL\s*ELECTRONIC',
            'imps': r'IMPS|IMMEDIATE\s*PAYMENT',
            'rtgs': r'RTGS|REAL\s*TIME',
            'cheque': r'CHQ|CHEQUE|CHECK',
            'salary': r'SALARY|SAL\s*CR|PAYROLL',
            'refund': r'REFUND|REVERSAL|RETURN',
            'fee': r'FEE|CHARGE|PENALTY',
            'interest': r'INTEREST|INT\s*CR'
        }
        
        # Historical data for pattern analysis
        self.transaction_history: List[RawTransaction] = []
        self.amount_patterns: Dict[str, AmountPattern] = {}
        self.merchant_behaviors: Dict[str, MerchantBehavior] = {}
        
        self.logger.info("Transaction Context Analyzer initialized")
    
    def analyze_transaction_context(self, transaction: RawTransaction, 
                                  transaction_history: List[RawTransaction] = None) -> TransactionContext:
        """
        Analyze comprehensive context for a transaction
        
        Args:
            transaction: Transaction to analyze
            transaction_history: Historical transactions for pattern analysis
            
        Returns:
            TransactionContext with detailed analysis
        """
        if transaction_history:
            self.transaction_history = transaction_history
            self._update_patterns()
        
        # Basic transaction info
        transaction_type = transaction.transaction_type.upper()
        amount = float(abs(transaction.amount))
        amount_category = self._categorize_amount(amount)
        
        # Temporal analysis
        transaction_date = transaction.date
        if isinstance(transaction_date, str):
            transaction_date = datetime.strptime(transaction_date, '%Y-%m-%d').date()
        
        # For time analysis, we'll use current time as we don't have transaction time
        # In a real implementation, this would come from the transaction timestamp
        now = datetime.now()
        hour_of_day = now.hour
        day_of_week = transaction_date.weekday()
        day_of_month = transaction_date.day
        month = transaction_date.month
        is_weekend = day_of_week >= 5  # Saturday=5, Sunday=6
        is_business_hours = self.business_start <= hour_of_day < self.business_end
        
        # Pattern analysis
        description = transaction.description.upper()
        is_round_amount = amount % 100 == 0 or amount % 50 == 0
        is_recurring_pattern = self._is_recurring_transaction(transaction)
        merchant_type = self._identify_merchant_type(description)
        
        # Amount pattern analysis
        amount_percentile = self._calculate_amount_percentile(amount, transaction_type)
        is_unusual_amount = self._is_unusual_amount(amount, transaction_type)
        
        # Description pattern analysis
        has_reference_number = bool(re.search(r'\d{6,}', description))
        has_merchant_code = bool(re.search(r'[A-Z]{3,}\d+', description))
        description_complexity = self._calculate_description_complexity(description)
        
        # Contextual classification
        likely_income = self._is_likely_income(transaction, description)
        likely_expense = self._is_likely_expense(transaction, description)
        likely_transfer = self._is_likely_transfer(transaction, description)
        likely_fee = self._is_likely_fee(transaction, description)
        likely_refund = self._is_likely_refund(transaction, description)
        
        return TransactionContext(
            transaction_type=transaction_type,
            amount=amount,
            amount_category=amount_category,
            hour_of_day=hour_of_day,
            day_of_week=day_of_week,
            day_of_month=day_of_month,
            month=month,
            is_weekend=is_weekend,
            is_business_hours=is_business_hours,
            is_round_amount=is_round_amount,
            is_recurring_pattern=is_recurring_pattern,
            merchant_type=merchant_type,
            amount_percentile=amount_percentile,
            is_unusual_amount=is_unusual_amount,
            has_reference_number=has_reference_number,
            has_merchant_code=has_merchant_code,
            description_complexity=description_complexity,
            likely_income=likely_income,
            likely_expense=likely_expense,
            likely_transfer=likely_transfer,
            likely_fee=likely_fee,
            likely_refund=likely_refund
        )
    
    def get_categorization_hints(self, context: TransactionContext) -> Dict[str, Any]:
        """
        Generate categorization hints based on transaction context
        
        Args:
            context: Transaction context analysis
            
        Returns:
            Dictionary with categorization hints and confidence scores
        """
        hints = {
            'suggested_categories': [],
            'confidence_modifiers': {},
            'routing_recommendations': {},
            'context_flags': []
        }
        
        # Category suggestions based on context
        if context.likely_income:
            if context.merchant_type == 'salary':
                hints['suggested_categories'].append(('Income', 'Salary', 0.9))
            elif context.merchant_type == 'interest':
                hints['suggested_categories'].append(('Income', 'Interest', 0.8))
            else:
                hints['suggested_categories'].append(('Income', 'Other', 0.6))
        
        if context.likely_expense:
            if context.merchant_type == 'atm':
                hints['suggested_categories'].append(('Cash', 'ATM Withdrawal', 0.9))
            elif context.merchant_type == 'pos':
                if context.amount_category in ['micro', 'small']:
                    hints['suggested_categories'].append(('Food & Dining', 'Restaurant', 0.7))
                else:
                    hints['suggested_categories'].append(('Shopping', 'General', 0.6))
            elif context.merchant_type == 'online':
                hints['suggested_categories'].append(('Shopping', 'Online', 0.8))
            elif context.merchant_type == 'upi':
                if context.amount_category == 'micro':
                    hints['suggested_categories'].append(('Food & Dining', 'Delivery', 0.7))
                else:
                    hints['suggested_categories'].append(('Transfer', 'UPI', 0.6))
        
        if context.likely_fee:
            hints['suggested_categories'].append(('Banking', 'Fees', 0.9))
        
        if context.likely_refund:
            hints['suggested_categories'].append(('Refund', 'General', 0.8))
        
        # Confidence modifiers
        if context.is_round_amount:
            hints['confidence_modifiers']['round_amount_bonus'] = 0.1
        
        if context.has_merchant_code:
            hints['confidence_modifiers']['merchant_code_bonus'] = 0.15
        
        if context.is_recurring_pattern:
            hints['confidence_modifiers']['recurring_bonus'] = 0.2
        
        if context.is_unusual_amount:
            hints['confidence_modifiers']['unusual_amount_penalty'] = -0.1
        
        # Routing recommendations
        if context.merchant_type in ['atm', 'pos', 'upi'] and not context.is_unusual_amount:
            hints['routing_recommendations']['ai_suitable'] = True
            hints['routing_recommendations']['priority'] = 'high' if context.amount_category in ['medium', 'large'] else 'normal'
        
        if context.description_complexity > 0.8:
            hints['routing_recommendations']['manual_review_recommended'] = True
        
        # Context flags for additional processing
        if context.is_weekend and context.merchant_type == 'pos':
            hints['context_flags'].append('weekend_shopping')
        
        if not context.is_business_hours and context.merchant_type == 'atm':
            hints['context_flags'].append('after_hours_atm')
        
        if context.amount_category == 'huge':
            hints['context_flags'].append('high_value_transaction')
        
        return hints

    def _categorize_amount(self, amount: float) -> str:
        """Categorize amount into size categories"""
        for category, threshold in self.amount_thresholds.items():
            if amount < threshold:
                return category
        return 'huge'

    def _identify_merchant_type(self, description: str) -> str:
        """Identify merchant type from description"""
        for merchant_type, pattern in self.merchant_patterns.items():
            if re.search(pattern, description, re.IGNORECASE):
                return merchant_type
        return 'unknown'

    def _is_recurring_transaction(self, transaction: RawTransaction) -> bool:
        """Check if transaction appears to be recurring"""
        if not self.transaction_history:
            return False

        # Look for similar transactions in history
        similar_count = 0
        for hist_txn in self.transaction_history:
            if (abs(float(hist_txn.amount) - float(transaction.amount)) < 10.0 and
                self._description_similarity(hist_txn.description, transaction.description) > 0.8):
                similar_count += 1

        return similar_count >= 2

    def _description_similarity(self, desc1: str, desc2: str) -> float:
        """Calculate similarity between two descriptions"""
        desc1_words = set(desc1.upper().split())
        desc2_words = set(desc2.upper().split())

        if not desc1_words or not desc2_words:
            return 0.0

        intersection = desc1_words.intersection(desc2_words)
        union = desc1_words.union(desc2_words)

        return len(intersection) / len(union) if union else 0.0

    def _calculate_amount_percentile(self, amount: float, transaction_type: str) -> float:
        """Calculate where this amount falls in historical amounts"""
        if not self.transaction_history:
            return 0.5  # Default to median

        # Get amounts for same transaction type
        same_type_amounts = [
            float(abs(txn.amount)) for txn in self.transaction_history
            if txn.transaction_type.upper() == transaction_type
        ]

        if not same_type_amounts:
            return 0.5

        same_type_amounts.sort()
        position = sum(1 for amt in same_type_amounts if amt <= amount)
        return position / len(same_type_amounts)

    def _is_unusual_amount(self, amount: float, transaction_type: str) -> bool:
        """Check if amount is unusual compared to historical patterns"""
        if not self.transaction_history:
            return False

        # Get amounts for same transaction type
        same_type_amounts = [
            float(abs(txn.amount)) for txn in self.transaction_history
            if txn.transaction_type.upper() == transaction_type
        ]

        if len(same_type_amounts) < 5:  # Need enough data
            return False

        try:
            mean_amount = statistics.mean(same_type_amounts)
            std_amount = statistics.stdev(same_type_amounts)

            # Consider unusual if more than 2 standard deviations from mean
            return abs(amount - mean_amount) > (2 * std_amount)
        except:
            return False

    def _calculate_description_complexity(self, description: str) -> float:
        """Calculate complexity score of description (0-1)"""
        # Factors that increase complexity:
        # - Length
        # - Number of special characters
        # - Number of numeric sequences
        # - Mixed case patterns

        length_score = min(len(description) / 100.0, 1.0)  # Normalize to 100 chars

        special_chars = len(re.findall(r'[^a-zA-Z0-9\s]', description))
        special_score = min(special_chars / 10.0, 1.0)  # Normalize to 10 special chars

        numeric_sequences = len(re.findall(r'\d+', description))
        numeric_score = min(numeric_sequences / 5.0, 1.0)  # Normalize to 5 sequences

        # Average the scores
        return (length_score + special_score + numeric_score) / 3.0

    def _is_likely_income(self, transaction: RawTransaction, description: str) -> bool:
        """Check if transaction is likely income"""
        if transaction.transaction_type.upper() != 'CREDIT':
            return False

        income_patterns = [
            r'SALARY|SAL\s*CR|PAYROLL',
            r'INTEREST|INT\s*CR',
            r'DIVIDEND|DIV\s*CR',
            r'BONUS|INCENTIVE',
            r'REFUND|REVERSAL',
            r'DEPOSIT|DEP\s*CR'
        ]

        return any(re.search(pattern, description, re.IGNORECASE) for pattern in income_patterns)

    def _is_likely_expense(self, transaction: RawTransaction, description: str) -> bool:
        """Check if transaction is likely an expense"""
        if transaction.transaction_type.upper() != 'DEBIT':
            return False

        # Most debits are expenses, but exclude transfers and fees
        if self._is_likely_transfer(transaction, description) or self._is_likely_fee(transaction, description):
            return False

        return True

    def _is_likely_transfer(self, transaction: RawTransaction, description: str) -> bool:
        """Check if transaction is likely a transfer"""
        transfer_patterns = [
            r'TRANSFER|TFRF|TRF',
            r'NEFT|IMPS|RTGS',
            r'UPI.*TO|UPI.*FROM',
            r'FUND\s*TRANSFER'
        ]

        return any(re.search(pattern, description, re.IGNORECASE) for pattern in transfer_patterns)

    def _is_likely_fee(self, transaction: RawTransaction, description: str) -> bool:
        """Check if transaction is likely a fee"""
        fee_patterns = [
            r'FEE|CHARGE|CHG',
            r'PENALTY|FINE',
            r'SERVICE\s*CHARGE',
            r'MAINTENANCE|MAINT',
            r'ANNUAL\s*FEE'
        ]

        return any(re.search(pattern, description, re.IGNORECASE) for pattern in fee_patterns)

    def _is_likely_refund(self, transaction: RawTransaction, description: str) -> bool:
        """Check if transaction is likely a refund"""
        refund_patterns = [
            r'REFUND|REVERSAL',
            r'RETURN|RTN',
            r'CREDIT\s*ADJUSTMENT',
            r'CHARGEBACK'
        ]

        return any(re.search(pattern, description, re.IGNORECASE) for pattern in refund_patterns)

    def _update_patterns(self):
        """Update amount and merchant patterns from transaction history"""
        if not self.transaction_history:
            return

        # Update amount patterns by transaction type
        type_amounts = defaultdict(list)
        for txn in self.transaction_history:
            type_amounts[txn.transaction_type.upper()].append(float(abs(txn.amount)))

        for txn_type, amounts in type_amounts.items():
            if len(amounts) >= 5:  # Need enough data
                try:
                    self.amount_patterns[txn_type] = AmountPattern(
                        min_amount=min(amounts),
                        max_amount=max(amounts),
                        avg_amount=statistics.mean(amounts),
                        median_amount=statistics.median(amounts),
                        std_deviation=statistics.stdev(amounts) if len(amounts) > 1 else 0,
                        common_amounts=self._find_common_amounts(amounts),
                        percentiles={
                            25: self._percentile(amounts, 0.25),
                            50: self._percentile(amounts, 0.50),
                            75: self._percentile(amounts, 0.75),
                            90: self._percentile(amounts, 0.90)
                        }
                    )
                except Exception as e:
                    self.logger.warning(f"Error updating amount patterns for {txn_type}: {str(e)}")

    def _find_common_amounts(self, amounts: List[float]) -> List[float]:
        """Find most common amounts in the list"""
        from collections import Counter
        amount_counts = Counter(amounts)
        # Return top 5 most common amounts
        return [amount for amount, count in amount_counts.most_common(5)]

    def _percentile(self, amounts: List[float], percentile: float) -> float:
        """Calculate percentile of amounts"""
        sorted_amounts = sorted(amounts)
        index = int(percentile * (len(sorted_amounts) - 1))
        return sorted_amounts[index]

    def get_context_summary(self, context: TransactionContext) -> str:
        """Get human-readable summary of transaction context"""
        summary_parts = []

        # Basic info
        summary_parts.append(f"{context.transaction_type} of ₹{context.amount:.2f} ({context.amount_category})")

        # Temporal info
        day_names = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
        summary_parts.append(f"on {day_names[context.day_of_week]}")

        if context.is_weekend:
            summary_parts.append("(weekend)")

        if not context.is_business_hours:
            summary_parts.append("(after hours)")

        # Pattern info
        if context.merchant_type != 'unknown':
            summary_parts.append(f"via {context.merchant_type.upper()}")

        if context.is_recurring_pattern:
            summary_parts.append("(recurring)")

        if context.is_round_amount:
            summary_parts.append("(round amount)")

        # Likelihood flags
        likely_flags = []
        if context.likely_income:
            likely_flags.append("income")
        if context.likely_expense:
            likely_flags.append("expense")
        if context.likely_transfer:
            likely_flags.append("transfer")
        if context.likely_fee:
            likely_flags.append("fee")
        if context.likely_refund:
            likely_flags.append("refund")

        if likely_flags:
            summary_parts.append(f"likely: {', '.join(likely_flags)}")

        return " ".join(summary_parts)
