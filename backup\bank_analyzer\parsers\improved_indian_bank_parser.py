"""
Improved Indian Bank PDF Parser
Optimized to capture all transactions from Indian Bank statements
"""

import re
import decimal
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from decimal import Decimal
from datetime import datetime, date

try:
    import PyPDF2
    import pdfplumber
except ImportError:
    PyPDF2 = None
    pdfplumber = None

from .base_parser import BaseStatementParser
from ..models.transaction import RawTransaction
from ..core.logger import get_logger


class ImprovedIndianBankParser:
    """
    Improved parser specifically designed for Indian Bank PDF statements
    Captures all transactions including those missed by generic parser
    """
    
    def __init__(self, bank_name: str = "Indian Bank"):
        self.bank_name = bank_name
        self.logger = get_logger(__name__)
        
        # Indian Bank specific patterns
        self.date_patterns = [
            r'\b(\d{1,2}/\d{1,2}/\d{4})\b',  # DD/MM/YYYY or D/M/YYYY
            r'\b(\d{2}-\d{2}-\d{4})\b',      # DD-MM-YYYY
        ]
        
        self.amount_patterns = [
            r'\b(\d{1,3}(?:,\d{3})*\.\d{2})\b',  # 1,234.56
            r'\b(\d+\.\d{2})\b',                  # 123.45
        ]
        
        # Transaction type indicators
        self.debit_indicators = ['THRU', 'DEBIT', 'WITHDRAWAL', 'WITHDRAWALS', 'ATM', 'CHARGES']
        self.credit_indicators = ['BY', 'CREDIT', 'DEPOSIT', 'DEPOSITS', 'TRANSFER IN']
    
    def validate_file_format(self, file_path: Path) -> Dict[str, Any]:
        """Validate if file is a supported Indian Bank PDF"""
        if not file_path.suffix.lower() == '.pdf':
            return {
                'is_valid': False,
                'errors': ['File is not a PDF'],
                'file_type': 'unknown'
            }
        
        try:
            # Quick check for Indian Bank content
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                if len(pdf_reader.pages) > 0:
                    first_page_text = pdf_reader.pages[0].extract_text().lower()
                    
                    if 'indian bank' in first_page_text:
                        return {
                            'is_valid': True,
                            'errors': [],
                            'file_type': 'indian_bank_pdf',
                            'pages': len(pdf_reader.pages)
                        }
            
            return {
                'is_valid': False,
                'errors': ['Not an Indian Bank statement'],
                'file_type': 'pdf'
            }
            
        except Exception as e:
            return {
                'is_valid': False,
                'errors': [f'Error reading PDF: {str(e)}'],
                'file_type': 'pdf'
            }
    
    def parse_file(self, file_path: Path) -> List[RawTransaction]:
        """Parse Indian Bank PDF statement"""
        if not pdfplumber:
            self.logger.error("pdfplumber not available for PDF parsing")
            return []
        
        transactions = []
        
        try:
            self.logger.info(f"Parsing Indian Bank PDF: {file_path}")
            
            with pdfplumber.open(file_path) as pdf:
                for page_num, page in enumerate(pdf.pages):
                    self.logger.info(f"Processing page {page_num + 1}/{len(pdf.pages)}")
                    
                    # Extract tables from page
                    tables = page.extract_tables()
                    
                    for table_num, table in enumerate(tables):
                        if table:
                            page_transactions = self._extract_transactions_from_table(
                                table, page_num + 1, table_num + 1
                            )
                            transactions.extend(page_transactions)
                            self.logger.info(f"Page {page_num + 1}, Table {table_num + 1}: {len(page_transactions)} transactions")
            
            self.logger.info(f"Total transactions extracted: {len(transactions)}")
            return transactions
            
        except Exception as e:
            self.logger.error(f"Error parsing PDF: {str(e)}")
            return []
    
    def _extract_transactions_from_table(self, table: List[List], page_num: int, table_num: int) -> List[RawTransaction]:
        """Extract transactions from a table"""
        transactions = []
        
        if not table or len(table) < 2:
            return transactions
        
        # Find header row and column indices
        header_info = self._find_table_structure(table)
        if not header_info:
            self.logger.warning(f"Could not identify table structure on page {page_num}")
            return transactions
        
        date_col = header_info.get('date_col')
        desc_col = header_info.get('desc_col')
        withdrawal_col = header_info.get('withdrawal_col')
        deposit_col = header_info.get('deposit_col')
        balance_col = header_info.get('balance_col')
        header_row = header_info.get('header_row', 0)
        
        # Process data rows
        for row_num, row in enumerate(table[header_row + 1:], start=header_row + 1):
            if not row or len(row) < 3:
                continue
            
            try:
                transaction = self._extract_transaction_from_table_row(
                    row, date_col, desc_col, withdrawal_col, deposit_col, balance_col, 
                    page_num, row_num
                )
                
                if transaction:
                    transactions.append(transaction)
                    
            except Exception as e:
                self.logger.debug(f"Error processing row {row_num} on page {page_num}: {str(e)}")
                continue
        
        return transactions
    
    def _find_table_structure(self, table: List[List]) -> Optional[Dict[str, int]]:
        """Find the structure of the transaction table"""
        for row_num, row in enumerate(table[:5]):  # Check first 5 rows for header
            if not row:
                continue
            
            # Convert row to lowercase for matching
            row_text = [str(cell).lower().strip() if cell else '' for cell in row]
            
            # Look for header patterns
            date_col = None
            desc_col = None
            withdrawal_col = None
            deposit_col = None
            balance_col = None
            
            for col_num, cell in enumerate(row_text):
                if 'date' in cell:
                    date_col = col_num
                elif 'particular' in cell or 'description' in cell:
                    desc_col = col_num
                elif 'withdrawal' in cell or 'debit' in cell:
                    withdrawal_col = col_num
                elif 'deposit' in cell or 'credit' in cell:
                    deposit_col = col_num
                elif 'balance' in cell:
                    balance_col = col_num
            
            # If we found key columns, this is likely the header
            if date_col is not None and desc_col is not None:
                return {
                    'header_row': row_num,
                    'date_col': date_col,
                    'desc_col': desc_col,
                    'withdrawal_col': withdrawal_col,
                    'deposit_col': deposit_col,
                    'balance_col': balance_col
                }
        
        # Fallback: assume standard Indian Bank format
        return {
            'header_row': 0,
            'date_col': 0,
            'desc_col': 1,
            'withdrawal_col': 2,
            'deposit_col': 3,
            'balance_col': 4
        }
    
    def _extract_transaction_from_table_row(self, row: List, date_col: int, desc_col: int,
                                          withdrawal_col: Optional[int], deposit_col: Optional[int],
                                          balance_col: Optional[int], page_num: int, row_num: int) -> Optional[RawTransaction]:
        """Extract transaction from a table row"""
        
        # Extract date
        if date_col is None or date_col >= len(row):
            return None
        
        date_str = str(row[date_col]).strip() if row[date_col] else ""
        transaction_date = self.parse_date_string(date_str)
        
        if not transaction_date:
            return None
        
        # Extract description
        description = ""
        if desc_col is not None and desc_col < len(row) and row[desc_col]:
            description = str(row[desc_col]).strip()
            # Clean up description
            description = re.sub(r'\s+', ' ', description)  # Normalize whitespace
            description = description.replace('\n', ' ')
        
        if not description or len(description) < 3:
            return None
        
        # Extract amount (try withdrawal first, then deposit)
        amount = None
        transaction_type = ""
        
        # Try withdrawal column
        if withdrawal_col is not None and withdrawal_col < len(row) and row[withdrawal_col]:
            withdrawal_str = str(row[withdrawal_col]).strip()
            if withdrawal_str and withdrawal_str != '-' and withdrawal_str.lower() != 'nil':
                amount_str = self.clean_amount_string(withdrawal_str)
                if amount_str and amount_str != "0":
                    try:
                        amount = Decimal(amount_str)
                        transaction_type = "DEBIT"
                    except (ValueError, decimal.InvalidOperation):
                        pass
        
        # Try deposit column if no withdrawal amount
        if amount is None and deposit_col is not None and deposit_col < len(row) and row[deposit_col]:
            deposit_str = str(row[deposit_col]).strip()
            if deposit_str and deposit_str != '-' and deposit_str.lower() != 'nil':
                amount_str = self.clean_amount_string(deposit_str)
                if amount_str and amount_str != "0":
                    try:
                        amount = Decimal(amount_str)
                        transaction_type = "CREDIT"
                    except (ValueError, decimal.InvalidOperation):
                        pass
        
        # If still no amount, try to extract from description
        if amount is None:
            amount_matches = re.findall(r'\b(\d{1,3}(?:,\d{3})*\.\d{2})\b', description)
            if amount_matches:
                amount_str = self.clean_amount_string(amount_matches[0])
                try:
                    amount = Decimal(amount_str)
                    # Determine type from description
                    desc_lower = description.lower()
                    if any(indicator in desc_lower for indicator in self.debit_indicators):
                        transaction_type = "DEBIT"
                    elif any(indicator in desc_lower for indicator in self.credit_indicators):
                        transaction_type = "CREDIT"
                    else:
                        transaction_type = "DEBIT"  # Default for Indian Bank
                except (ValueError, decimal.InvalidOperation):
                    pass
        
        if amount is None or amount == 0:
            return None
        
        # Extract balance (optional)
        balance = None
        if balance_col is not None and balance_col < len(row) and row[balance_col]:
            balance_str = str(row[balance_col]).strip()
            if balance_str and balance_str != '-':
                # Remove 'CR' or 'DR' suffix
                balance_str = re.sub(r'(CR|DR)$', '', balance_str).strip()
                balance_amount_str = self.clean_amount_string(balance_str)
                if balance_amount_str and balance_amount_str != "0":
                    try:
                        balance = Decimal(balance_amount_str)
                    except (ValueError, decimal.InvalidOperation):
                        pass
        
        # Create transaction
        transaction = RawTransaction(
            date=transaction_date.date(),
            description=description,
            amount=amount if transaction_type == "CREDIT" else -amount,
            transaction_type=transaction_type,
            balance=balance,
            reference_number=f"P{page_num}R{row_num}",
            raw_data={
                'page': page_num,
                'row': row_num,
                'original_row': row
            }
        )
        
        return transaction
    
    def parse_date_string(self, date_str: str) -> Optional[datetime]:
        """Parse date string with Indian Bank specific formats"""
        if not date_str or date_str.strip() == "":
            return None
        
        # Clean the date string
        date_str = date_str.strip()
        
        # Indian Bank date formats
        date_formats = [
            "%d/%m/%Y",    # 01/07/2024
            "%d/%m/%y",    # 01/07/24
            "%d-%m-%Y",    # 01-07-2024
            "%d-%m-%y",    # 01-07-24
            "%d.%m.%Y",    # 01.07.2024
            "%d.%m.%y",    # 01.07.24
        ]
        
        for fmt in date_formats:
            try:
                return datetime.strptime(date_str, fmt)
            except ValueError:
                continue
        
        # Try regex extraction
        for pattern in self.date_patterns:
            match = re.search(pattern, date_str)
            if match:
                date_part = match.group(1)
                for fmt in date_formats:
                    try:
                        return datetime.strptime(date_part, fmt)
                    except ValueError:
                        continue
        
        return None

    def clean_amount_string(self, amount_str: str) -> str:
        """Clean amount string for decimal conversion"""
        if not amount_str or amount_str.strip() == "":
            return "0"

        # Convert to string and strip whitespace
        amount_str = str(amount_str).strip()

        # Handle common non-numeric values
        if amount_str.lower() in ['', 'nil', 'null', 'none', 'n/a', '-', '--', 'na']:
            return "0"

        # Remove currency symbols
        currency_symbols = ['₹', '$', '€', '£', 'Rs.', 'INR', 'USD', 'EUR', 'GBP']
        for symbol in currency_symbols:
            amount_str = amount_str.replace(symbol, '')

        # Remove commas and spaces
        amount_str = amount_str.replace(',', '').replace(' ', '')

        # Handle parentheses (negative amounts)
        if amount_str.startswith('(') and amount_str.endswith(')'):
            amount_str = '-' + amount_str[1:-1]

        # Remove any remaining non-numeric characters except decimal point and minus
        amount_str = re.sub(r'[^\d.-]', '', amount_str)

        # Handle multiple decimal points (keep only the last one)
        if amount_str.count('.') > 1:
            parts = amount_str.split('.')
            amount_str = '.'.join(parts[:-1]).replace('.', '') + '.' + parts[-1]

        # Handle multiple minus signs (keep only the first one)
        if amount_str.count('-') > 1:
            is_negative = amount_str.startswith('-')
            amount_str = amount_str.replace('-', '')
            if is_negative:
                amount_str = '-' + amount_str

        # Ensure we have a valid number
        if not amount_str or amount_str in ['-', '.', '-.', '--']:
            return "0"

        # Handle edge cases
        if amount_str.startswith('.'):
            amount_str = '0' + amount_str
        if amount_str.endswith('.'):
            amount_str = amount_str[:-1]
        if amount_str == '-':
            amount_str = "0"

        # Validate the result
        try:
            float(amount_str)
            return amount_str
        except (ValueError, TypeError):
            self.logger.warning(f"Could not parse amount: '{amount_str}', returning 0")
            return "0"
