"""
Parser factory for automatic format detection and parser selection
"""

from pathlib import Path
from typing import List, Optional, Dict, Any
import logging

from .base_parser import BaseStatementParser
from .pdf_parser import PDFStatementParser
from .csv_parser import CSVStatementParser
from .excel_parser import ExcelStatementParser
from .production_indian_bank_parser import ProductionIndianBankParser
from .final_indian_bank_parser import FinalIndianBankParser
from .indian_bank_parser import IndianBankPDFParser
from ..models.transaction import RawTransaction
from ..core.logger import get_logger


class StatementParserFactory:
    """
    Factory class for creating appropriate parsers based on file format
    Automatically detects file format and returns the best parser
    """
    
    def __init__(self):
        self.logger = get_logger(__name__)
        
        # Initialize available parsers (use working rewritten parser)
        self.parsers = {
            'pdf': PDFStatementParser,
            'csv': CSVStatementParser,
            'excel': ExcelStatementParser
        }
        
        # File extension to parser type mapping
        self.extension_mapping = {
            '.pdf': 'pdf',
            '.csv': 'csv',
            '.xlsx': 'excel',
            '.xls': 'excel'
        }
    
    def get_parser(self, file_path: Path, bank_name: str = "Unknown") -> Optional[BaseStatementParser]:
        """
        Get the appropriate parser for the given file

        Args:
            file_path: Path to the statement file
            bank_name: Name of the bank (for parser customization)

        Returns:
            Parser instance or None if no suitable parser found
        """
        if not file_path.exists():
            self.logger.error(f"File does not exist: {file_path}")
            return None

        # Get file extension
        extension = file_path.suffix.lower()

        # Find parser type based on extension
        parser_type = self.extension_mapping.get(extension)

        if not parser_type:
            self.logger.error(f"Unsupported file format: {extension}")
            return None

        # Create parser instance
        parser_class = self.parsers.get(parser_type)
        if not parser_class:
            self.logger.error(f"Parser not available for type: {parser_type}")
            return None

        try:
            parser = parser_class(bank_name)

            # Verify parser can handle the file
            if not parser.can_parse(file_path):
                self.logger.error(f"Parser {parser_type} cannot handle file: {file_path}")
                return None

            self.logger.info(f"Selected {parser_type} parser for {file_path}")
            return parser

        except Exception as e:
            self.logger.error(f"Error creating parser {parser_type}: {str(e)}")
            return None
    
    def parse_file(self, file_path: Path, bank_name: str = "Unknown") -> List[RawTransaction]:
        """
        Parse a statement file using the appropriate parser
        
        Args:
            file_path: Path to the statement file
            bank_name: Name of the bank
            
        Returns:
            List of RawTransaction objects
        """
        parser = self.get_parser(file_path, bank_name)
        
        if not parser:
            return []
        
        try:
            transactions = parser.parse(file_path)
            
            # Log parsing results
            errors = parser.get_parsing_errors()
            if errors:
                self.logger.warning(f"Parsing completed with {len(errors)} errors:")
                for error in errors:
                    self.logger.warning(f"  - {error}")
            
            self.logger.info(f"Successfully parsed {len(transactions)} transactions from {file_path}")
            return transactions
            
        except Exception as e:
            self.logger.error(f"Error parsing file {file_path}: {str(e)}")
            return []
    
    def get_supported_formats(self) -> List[str]:
        """Get list of supported file formats"""
        return list(self.extension_mapping.keys())
    
    def get_parser_info(self) -> Dict[str, Any]:
        """Get information about available parsers"""
        info = {}
        
        for parser_type, parser_class in self.parsers.items():
            try:
                # Create temporary instance to get info
                temp_parser = parser_class()
                info[parser_type] = {
                    'class_name': parser_class.__name__,
                    'supported_formats': temp_parser.supported_formats,
                    'description': temp_parser.__class__.__doc__ or "No description available"
                }
            except Exception as e:
                info[parser_type] = {
                    'class_name': parser_class.__name__,
                    'error': str(e)
                }
        
        return info
    
    def validate_file_format(self, file_path: Path) -> Dict[str, Any]:
        """
        Validate file format and return detailed information
        
        Args:
            file_path: Path to the statement file
            
        Returns:
            Dictionary with validation results
        """
        result = {
            'is_valid': False,
            'file_exists': False,
            'supported_format': False,
            'parser_available': False,
            'file_info': {},
            'errors': []
        }
        
        try:
            # Check if file exists
            if not file_path.exists():
                result['errors'].append(f"File does not exist: {file_path}")
                return result
            
            result['file_exists'] = True
            
            # Get file info
            stat = file_path.stat()
            result['file_info'] = {
                'name': file_path.name,
                'size': stat.st_size,
                'extension': file_path.suffix.lower(),
                'size_mb': round(stat.st_size / (1024 * 1024), 2)
            }
            
            # Check if format is supported
            extension = file_path.suffix.lower()
            if extension not in self.extension_mapping:
                result['errors'].append(f"Unsupported file format: {extension}")
                return result
            
            result['supported_format'] = True
            
            # Check if parser is available
            parser_type = self.extension_mapping[extension]
            parser_class = self.parsers.get(parser_type)
            
            if not parser_class:
                result['errors'].append(f"Parser not available for format: {extension}")
                return result
            
            result['parser_available'] = True
            
            # Try to create parser and validate
            try:
                parser = parser_class()
                if parser.can_parse(file_path):
                    result['is_valid'] = True
                else:
                    result['errors'].append("Parser cannot handle this specific file")
            except Exception as e:
                result['errors'].append(f"Parser validation failed: {str(e)}")
            
        except Exception as e:
            result['errors'].append(f"Validation error: {str(e)}")
        
        return result


# Global factory instance
parser_factory = StatementParserFactory()
