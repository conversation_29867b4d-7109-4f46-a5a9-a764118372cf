"""
PDF parser for bank statements
Handles PDF format bank statements using PyPDF2 and pdfplumber
"""

import re
import decimal
from pathlib import Path
from typing import List, Dict, Any, Optional
from decimal import Decimal
from datetime import datetime, date

try:
    import PyPDF2
    import pdfplumber
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False

from .base_parser import BaseStatementParser
from ..models.transaction import RawTransaction


class PDFStatementParser(BaseStatementParser):
    """
    Parser for PDF bank statements
    Supports multiple Indian banks with different PDF formats
    """
    
    def __init__(self, bank_name: str = "Unknown"):
        super().__init__(bank_name)
        self.supported_formats = ['.pdf']
        
        # Bank-specific patterns for transaction extraction
        self.bank_patterns = {
            'sbi': {
                'transaction_pattern': r'(\d{2}/\d{2}/\d{4})\s+(.+?)\s+([\d,]+\.\d{2})\s*(Cr|Dr)?\s*([\d,]+\.\d{2})?',
                'date_format': '%d/%m/%Y',
                'description_cleanup': [r'\s+', ' ']
            },
            'indian_bank': {
                'transaction_pattern': r'(\d{2}-\d{2}-\d{4})\s+(.+?)\s+([\d,]+\.\d{2})\s*([\d,]+\.\d{2})',
                'date_format': '%d-%m-%Y',
                'description_cleanup': [r'\s+', ' ']
            },
            'generic': {
                'transaction_pattern': r'(\d{1,2}[/-]\d{1,2}[/-]\d{2,4})\s+(.+?)\s+([\d,]+\.?\d*)\s*([\d,]+\.?\d*)?',
                'date_format': '%d/%m/%Y',
                'description_cleanup': [r'\s+', ' ']
            }
        }
    
    def can_parse(self, file_path: Path) -> bool:
        """Check if this parser can handle the given PDF file"""
        if not PDF_AVAILABLE:
            self.logger.error("PDF parsing libraries not available. Install PyPDF2 and pdfplumber.")
            return False
        
        if file_path.suffix.lower() != '.pdf':
            return False
        
        return self.validate_file(file_path)
    
    def parse(self, file_path: Path) -> List[RawTransaction]:
        """Parse PDF bank statement and extract transactions"""
        if not self.can_parse(file_path):
            return []
        
        self.clear_parsing_errors()
        transactions = []
        
        try:
            # Try pdfplumber first (better for tables)
            transactions = self._parse_with_pdfplumber(file_path)
            
            # If pdfplumber fails or returns no results, try PyPDF2
            if not transactions:
                self.logger.info("pdfplumber returned no results, trying PyPDF2")
                transactions = self._parse_with_pypdf2(file_path)
            
        except Exception as e:
            self.logger.error(f"Error parsing PDF {file_path}: {str(e)}")
            self.add_parsing_error(f"Failed to parse PDF: {str(e)}")
        
        self.logger.info(f"Extracted {len(transactions)} transactions from {file_path}")
        return transactions
    
    def _parse_with_pdfplumber(self, file_path: Path) -> List[RawTransaction]:
        """Parse PDF using pdfplumber (better for structured data)"""
        transactions = []
        
        try:
            with pdfplumber.open(file_path) as pdf:
                all_text = ""
                
                for page_num, page in enumerate(pdf.pages):
                    # Try to extract tables first
                    tables = page.extract_tables()
                    if tables:
                        for table in tables:
                            table_transactions = self._parse_table_data(table, file_path, page_num)
                            transactions.extend(table_transactions)
                    
                    # If no tables or no transactions from tables, extract text
                    if not transactions:
                        page_text = page.extract_text()
                        if page_text:
                            all_text += page_text + "\n"
                
                # If no table data found, parse text
                if not transactions and all_text:
                    transactions = self._parse_text_data(all_text, file_path)
                    
        except Exception as e:
            self.logger.error(f"Error with pdfplumber: {str(e)}")
            raise
        
        return transactions
    
    def _parse_with_pypdf2(self, file_path: Path) -> List[RawTransaction]:
        """Parse PDF using PyPDF2 (fallback method)"""
        transactions = []
        
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                all_text = ""
                
                for page in pdf_reader.pages:
                    page_text = page.extract_text()
                    if page_text:
                        all_text += page_text + "\n"
                
                if all_text:
                    transactions = self._parse_text_data(all_text, file_path)
                    
        except Exception as e:
            self.logger.error(f"Error with PyPDF2: {str(e)}")
            raise
        
        return transactions
    
    def _parse_table_data(self, table: List[List[str]], file_path: Path, page_num: int) -> List[RawTransaction]:
        """Parse transaction data from extracted table"""
        transactions = []
        
        if not table or len(table) < 2:
            return transactions
        
        # Try to identify header row and data rows
        header_row = table[0] if table else []
        data_rows = table[1:] if len(table) > 1 else []
        
        # Look for common column patterns
        date_col = self._find_column_index(header_row, ['date', 'transaction date', 'txn date'])
        desc_col = self._find_column_index(header_row, ['description', 'particulars', 'details', 'narration'])
        amount_col = self._find_column_index(header_row, ['amount', 'debit', 'credit', 'withdrawal', 'deposit'])
        balance_col = self._find_column_index(header_row, ['balance', 'running balance', 'available balance'])
        
        for row_num, row in enumerate(data_rows):
            try:
                if not row or len(row) < 3:
                    continue
                
                # Extract data based on identified columns
                transaction = self._extract_transaction_from_row(
                    row, date_col, desc_col, amount_col, balance_col,
                    file_path, page_num, row_num + 2  # +2 for header and 0-based index
                )
                
                if transaction:
                    transactions.append(transaction)
                    
            except Exception as e:
                self.add_parsing_error(f"Error parsing table row {row_num + 2}: {str(e)}", row_num + 2)
        
        return transactions
    
    def _parse_text_data(self, text: str, file_path: Path) -> List[RawTransaction]:
        """Parse transaction data from extracted text using regex patterns"""
        transactions = []
        
        # Detect bank type based on text content
        bank_type = self._detect_bank_type(text)
        pattern_config = self.bank_patterns.get(bank_type, self.bank_patterns['generic'])
        
        # Split text into lines and process each line
        lines = text.split('\n')
        
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if not line:
                continue
            
            try:
                transaction = self._extract_transaction_from_text_line(
                    line, pattern_config, file_path, line_num
                )
                
                if transaction:
                    transactions.append(transaction)
                    
            except Exception as e:
                self.add_parsing_error(f"Error parsing text line {line_num}: {str(e)}", line_num)
        
        return transactions
    
    def _detect_bank_type(self, text: str) -> str:
        """Detect bank type from text content"""
        text_lower = text.lower()
        
        if 'state bank of india' in text_lower or 'sbi' in text_lower:
            return 'sbi'
        elif 'indian bank' in text_lower:
            return 'indian_bank'
        else:
            return 'generic'
    
    def _find_column_index(self, header_row: List[str], possible_names: List[str]) -> Optional[int]:
        """Find column index based on possible header names"""
        if not header_row:
            return None
        
        for i, header in enumerate(header_row):
            if header and isinstance(header, str):
                header_lower = header.lower().strip()
                for name in possible_names:
                    if name.lower() in header_lower:
                        return i
        return None
    
    def _extract_transaction_from_row(self, row: List[str], date_col: Optional[int],
                                    desc_col: Optional[int], amount_col: Optional[int],
                                    balance_col: Optional[int], file_path: Path,
                                    page_num: int, row_num: int) -> Optional[RawTransaction]:
        """Extract transaction from table row"""
        try:
            # Extract date
            date_str = row[date_col] if date_col is not None and date_col < len(row) else ""
            transaction_date = self.parse_date_string(date_str)
            
            if not transaction_date:
                return None
            
            # Extract description
            description = row[desc_col] if desc_col is not None and desc_col < len(row) else ""
            description = str(description).strip() if description else ""
            
            # Extract amount
            amount_str = row[amount_col] if amount_col is not None and amount_col < len(row) else ""
            amount_str = self.clean_amount_string(str(amount_str))

            if not amount_str or amount_str == "0":
                return None

            try:
                amount = Decimal(amount_str)
            except (ValueError, TypeError, decimal.InvalidOperation) as e:
                self.logger.warning(f"Could not convert amount '{amount_str}' to decimal: {e}")
                return None

            # Extract balance
            balance = None
            if balance_col is not None and balance_col < len(row):
                balance_str = self.clean_amount_string(str(row[balance_col]))
                if balance_str and balance_str != "0":
                    try:
                        balance = Decimal(balance_str)
                    except (ValueError, TypeError, decimal.InvalidOperation) as e:
                        self.logger.warning(f"Could not convert balance '{balance_str}' to decimal: {e}")
                        balance = None
            
            return RawTransaction(
                date=transaction_date.date(),
                description=description,
                amount=amount,
                balance=balance,
                source_file=str(file_path),
                source_line=row_num,
                bank_name=self.bank_name
            )
            
        except Exception as e:
            self.logger.error(f"Error extracting transaction from row: {str(e)}")
            return None
    
    def _extract_transaction_from_text_line(self, line: str, pattern_config: Dict[str, Any],
                                          file_path: Path, line_num: int) -> Optional[RawTransaction]:
        """Extract transaction from text line using regex pattern"""
        try:
            pattern = pattern_config['transaction_pattern']
            match = re.search(pattern, line)
            
            if not match:
                return None
            
            groups = match.groups()
            if len(groups) < 3:
                return None
            
            # Parse date
            date_str = groups[0]
            transaction_date = self.parse_date_string(date_str)
            
            if not transaction_date:
                return None
            
            # Parse description
            description = groups[1].strip() if len(groups) > 1 else ""
            
            # Clean up description using bank-specific rules
            cleanup_rules = pattern_config.get('description_cleanup', [])
            for i in range(0, len(cleanup_rules), 2):
                if i + 1 < len(cleanup_rules):
                    description = re.sub(cleanup_rules[i], cleanup_rules[i + 1], description)
            
            # Parse amount
            amount_str = self.clean_amount_string(groups[2]) if len(groups) > 2 else "0"
            if not amount_str or amount_str == "0":
                return None

            try:
                amount = Decimal(amount_str)
            except (ValueError, TypeError, decimal.InvalidOperation) as e:
                self.logger.warning(f"Could not convert amount '{amount_str}' to decimal: {e}")
                return None

            # Parse balance if available
            balance = None
            if len(groups) > 3 and groups[3]:
                balance_str = self.clean_amount_string(groups[3])
                if balance_str and balance_str != "0":
                    try:
                        balance = Decimal(balance_str)
                    except (ValueError, TypeError, decimal.InvalidOperation) as e:
                        self.logger.warning(f"Could not convert balance '{balance_str}' to decimal: {e}")
                        balance = None
            
            return RawTransaction(
                date=transaction_date.date(),
                description=description,
                amount=amount,
                balance=balance,
                source_file=str(file_path),
                source_line=line_num,
                bank_name=self.bank_name
            )
            
        except Exception as e:
            self.logger.error(f"Error extracting transaction from text line: {str(e)}")
            return None
