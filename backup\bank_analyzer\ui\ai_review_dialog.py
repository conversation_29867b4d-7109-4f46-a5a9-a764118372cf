"""
AI Categorization Review Dialog

This dialog provides a comprehensive interface for reviewing AI categorization suggestions,
managing merchant mappings, and handling batch approval workflows.

Features:
- Review AI categorization suggestions with confidence scores
- Batch approval and rejection workflows
- Merchant mapping management
- Error correction interfaces
- Cost tracking and budget monitoring
- Context information display
"""

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QTableWidget,
                           QTableWidgetItem, QPushButton, QLabel, QGroupBox,
                           QCheckBox, QComboBox, QLineEdit, QTextEdit, QTabWidget,
                           QWidget, QSplitter, QProgressBar, QMessageBox,
                           QHeaderView, QFrame, QScrollArea, QGridLayout, QApplication)
from PySide6.QtCore import Qt, QTimer, Signal
from PySide6.QtGui import QFont, QColor, QPalette

from typing import List, Dict, Any, Optional
from ..models.transaction import RawTransaction, ProcessedTransaction
from ..ml.enhanced_integrated_categorizer import EnhancedIntegratedCategorizer
from ..core.logger import get_logger


class AIReviewDialog(QDialog):
    """
    Dialog for reviewing AI categorization results and managing merchant mappings
    """
    
    # Signals
    transactions_updated = Signal(list)  # Emitted when transactions are updated
    merchant_patterns_updated = Signal()  # Emitted when merchant patterns change
    
    def __init__(self, parent=None, categorizer: EnhancedIntegratedCategorizer = None):
        super().__init__(parent)
        self.categorizer = categorizer
        self.logger = get_logger(__name__)
        
        # Data
        self.raw_transactions: List[RawTransaction] = []
        self.processed_transactions: List[ProcessedTransaction] = []
        self.ai_suggestions: Dict[str, Dict[str, Any]] = {}
        self.selected_transactions: List[int] = []
        
        # UI state
        self.current_filter = "all"
        self.show_only_ai_suggestions = False
        
        self.setup_ui()
        self.setup_connections()
        
        # Auto-refresh timer
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_statistics)
        self.refresh_timer.start(30000)  # Refresh every 30 seconds
    
    def setup_ui(self):
        """Setup the user interface"""
        self.setWindowTitle("AI Categorization Review & Management")
        self.setGeometry(100, 100, 1400, 900)
        
        # Main layout
        main_layout = QVBoxLayout(self)
        
        # Header with statistics
        self.create_header_section(main_layout)
        
        # Main content with tabs
        self.create_main_tabs(main_layout)
        
        # Footer with action buttons
        self.create_footer_section(main_layout)
    
    def create_header_section(self, layout):
        """Create header section with AI statistics"""
        header_frame = QFrame()
        header_frame.setFrameStyle(QFrame.StyledPanel)
        header_layout = QVBoxLayout(header_frame)
        
        # Title
        title_label = QLabel("🤖 AI Categorization Review & Management")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title_label)
        
        # Statistics dashboard
        stats_layout = QHBoxLayout()
        
        # AI Processing Stats
        ai_stats_group = QGroupBox("🧠 AI Processing")
        ai_stats_layout = QGridLayout(ai_stats_group)
        
        self.ai_processed_label = QLabel("Processed: 0")
        self.ai_confidence_label = QLabel("Avg Confidence: 0%")
        self.ai_cost_label = QLabel("Session Cost: $0.00")
        
        ai_stats_layout.addWidget(self.ai_processed_label, 0, 0)
        ai_stats_layout.addWidget(self.ai_confidence_label, 0, 1)
        ai_stats_layout.addWidget(self.ai_cost_label, 1, 0)
        
        stats_layout.addWidget(ai_stats_group)
        
        # Merchant Cache Stats
        cache_stats_group = QGroupBox("🏪 Merchant Cache")
        cache_stats_layout = QGridLayout(cache_stats_group)
        
        self.cache_patterns_label = QLabel("Patterns: 0")
        self.cache_hit_rate_label = QLabel("Hit Rate: 0%")
        self.cache_savings_label = QLabel("Savings: $0.00")
        
        cache_stats_layout.addWidget(self.cache_patterns_label, 0, 0)
        cache_stats_layout.addWidget(self.cache_hit_rate_label, 0, 1)
        cache_stats_layout.addWidget(self.cache_savings_label, 1, 0)
        
        stats_layout.addWidget(cache_stats_group)
        
        # Review Stats
        review_stats_group = QGroupBox("📋 Review Status")
        review_stats_layout = QGridLayout(review_stats_group)
        
        self.pending_review_label = QLabel("Pending: 0")
        self.approved_label = QLabel("Approved: 0")
        self.rejected_label = QLabel("Rejected: 0")
        
        review_stats_layout.addWidget(self.pending_review_label, 0, 0)
        review_stats_layout.addWidget(self.approved_label, 0, 1)
        review_stats_layout.addWidget(self.rejected_label, 1, 0)
        
        stats_layout.addWidget(review_stats_group)
        
        header_layout.addLayout(stats_layout)
        layout.addWidget(header_frame)
    
    def create_main_tabs(self, layout):
        """Create main tab widget"""
        self.tab_widget = QTabWidget()
        
        # AI Suggestions Review Tab
        self.create_ai_review_tab()
        
        # Merchant Mapping Management Tab
        self.create_merchant_management_tab()
        
        # Batch Operations Tab
        self.create_batch_operations_tab()
        
        # System Monitoring Tab
        self.create_monitoring_tab()
        
        layout.addWidget(self.tab_widget)
    
    def create_ai_review_tab(self):
        """Create AI suggestions review tab"""
        review_widget = QWidget()
        review_layout = QVBoxLayout(review_widget)
        
        # Filter controls
        filter_layout = QHBoxLayout()
        
        filter_layout.addWidget(QLabel("Filter:"))
        
        self.filter_combo = QComboBox()
        self.filter_combo.addItems(["All", "AI Suggestions", "High Confidence", "Low Confidence", "Needs Review"])
        filter_layout.addWidget(self.filter_combo)
        
        self.confidence_threshold_combo = QComboBox()
        self.confidence_threshold_combo.addItems(["All Confidence", "> 90%", "> 80%", "> 70%", "< 70%"])
        filter_layout.addWidget(self.confidence_threshold_combo)
        
        filter_layout.addStretch()
        
        self.refresh_btn = QPushButton("🔄 Refresh")
        filter_layout.addWidget(self.refresh_btn)
        
        review_layout.addLayout(filter_layout)
        
        # Main review table
        self.review_table = QTableWidget()
        self.review_table.setColumnCount(8)
        self.review_table.setHorizontalHeaderLabels([
            "Select", "Description", "Amount", "AI Category", "AI Sub-Category",
            "Confidence", "Context", "Actions"
        ])

        # Configure table
        header = self.review_table.horizontalHeader()
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # Description column
        header.setSectionResizeMode(6, QHeaderView.Stretch)  # Context column

        self.review_table.setAlternatingRowColors(True)
        self.review_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.review_table.setSelectionMode(QTableWidget.SelectionMode.MultiSelection)

        # Also create alias for compatibility with other methods
        self.transaction_table = self.review_table
        
        review_layout.addWidget(self.review_table)
        
        # Quick action buttons
        action_layout = QHBoxLayout()
        
        self.select_all_btn = QPushButton("Select All")
        self.select_none_btn = QPushButton("Select None")
        self.approve_selected_btn = QPushButton("✅ Approve Selected")
        self.reject_selected_btn = QPushButton("❌ Reject Selected")
        self.learn_patterns_btn = QPushButton("🧠 Learn Patterns")
        
        action_layout.addWidget(self.select_all_btn)
        action_layout.addWidget(self.select_none_btn)
        action_layout.addStretch()
        action_layout.addWidget(self.approve_selected_btn)
        action_layout.addWidget(self.reject_selected_btn)
        action_layout.addWidget(self.learn_patterns_btn)
        
        review_layout.addLayout(action_layout)
        
        self.tab_widget.addTab(review_widget, "🤖 AI Review")
    
    def create_merchant_management_tab(self):
        """Create merchant mapping management tab"""
        merchant_widget = QWidget()
        merchant_layout = QVBoxLayout(merchant_widget)
        
        # Merchant patterns table
        self.merchant_table = QTableWidget()
        self.merchant_table.setColumnCount(7)
        self.merchant_table.setHorizontalHeaderLabels([
            "Pattern", "Category", "Sub-Category", "Confidence", "Count", "Last Seen", "Actions"
        ])
        
        merchant_layout.addWidget(QLabel("Merchant Patterns:"))
        merchant_layout.addWidget(self.merchant_table)
        
        # Merchant management controls
        controls_layout = QHBoxLayout()
        
        self.export_patterns_btn = QPushButton("📤 Export Patterns")
        self.import_patterns_btn = QPushButton("📥 Import Patterns")
        self.backup_patterns_btn = QPushButton("💾 Create Backup")
        self.cleanup_patterns_btn = QPushButton("🧹 Cleanup Old")
        
        controls_layout.addWidget(self.export_patterns_btn)
        controls_layout.addWidget(self.import_patterns_btn)
        controls_layout.addWidget(self.backup_patterns_btn)
        controls_layout.addWidget(self.cleanup_patterns_btn)
        controls_layout.addStretch()
        
        merchant_layout.addLayout(controls_layout)
        
        self.tab_widget.addTab(merchant_widget, "🏪 Merchant Patterns")
    
    def create_batch_operations_tab(self):
        """Create batch operations tab"""
        batch_widget = QWidget()
        batch_layout = QVBoxLayout(batch_widget)
        
        # Batch processing controls
        batch_controls_group = QGroupBox("Batch Processing")
        batch_controls_layout = QGridLayout(batch_controls_group)
        
        batch_controls_layout.addWidget(QLabel("Confidence Threshold:"), 0, 0)
        self.batch_confidence_threshold = QComboBox()
        self.batch_confidence_threshold.addItems(["70%", "80%", "90%", "95%"])
        self.batch_confidence_threshold.setCurrentText("80%")
        batch_controls_layout.addWidget(self.batch_confidence_threshold, 0, 1)
        
        batch_controls_layout.addWidget(QLabel("Auto-approve:"), 1, 0)
        self.auto_approve_checkbox = QCheckBox("High confidence suggestions")
        batch_controls_layout.addWidget(self.auto_approve_checkbox, 1, 1)
        
        self.batch_process_btn = QPushButton("⚡ Process Batch")
        batch_controls_layout.addWidget(self.batch_process_btn, 2, 0, 1, 2)
        
        batch_layout.addWidget(batch_controls_group)
        
        # Progress tracking
        progress_group = QGroupBox("Progress")
        progress_layout = QVBoxLayout(progress_group)
        
        self.batch_progress_bar = QProgressBar()
        self.batch_status_label = QLabel("Ready")
        
        progress_layout.addWidget(self.batch_progress_bar)
        progress_layout.addWidget(self.batch_status_label)
        
        batch_layout.addWidget(progress_group)
        
        batch_layout.addStretch()
        
        self.tab_widget.addTab(batch_widget, "⚡ Batch Operations")
    
    def create_monitoring_tab(self):
        """Create system monitoring tab"""
        monitoring_widget = QWidget()
        monitoring_layout = QVBoxLayout(monitoring_widget)
        
        # System status
        status_group = QGroupBox("System Status")
        status_layout = QGridLayout(status_group)
        
        self.ai_system_status_label = QLabel("AI System: Checking...")
        self.budget_status_label = QLabel("Budget: Loading...")
        self.cache_status_label = QLabel("Cache: Loading...")
        
        status_layout.addWidget(self.ai_system_status_label, 0, 0)
        status_layout.addWidget(self.budget_status_label, 0, 1)
        status_layout.addWidget(self.cache_status_label, 1, 0)
        
        monitoring_layout.addWidget(status_group)
        
        # Performance metrics
        metrics_group = QGroupBox("Performance Metrics")
        metrics_layout = QVBoxLayout(metrics_group)
        
        self.metrics_text = QTextEdit()
        self.metrics_text.setReadOnly(True)
        self.metrics_text.setMaximumHeight(200)
        
        metrics_layout.addWidget(self.metrics_text)
        
        monitoring_layout.addWidget(metrics_group)
        
        monitoring_layout.addStretch()
        
        self.tab_widget.addTab(monitoring_widget, "📊 Monitoring")
    
    def create_footer_section(self, layout):
        """Create footer section with main action buttons"""
        footer_layout = QHBoxLayout()
        
        # Status label
        self.status_label = QLabel("Ready")
        footer_layout.addWidget(self.status_label)
        
        footer_layout.addStretch()
        
        # Main action buttons
        self.save_changes_btn = QPushButton("💾 Save Changes")
        self.save_changes_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        self.cancel_btn = QPushButton("❌ Cancel")
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        footer_layout.addWidget(self.save_changes_btn)
        footer_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(footer_layout)
    
    def setup_connections(self):
        """Setup signal connections"""
        # Filter controls
        self.filter_combo.currentTextChanged.connect(self.apply_filters)
        self.confidence_threshold_combo.currentTextChanged.connect(self.apply_filters)
        self.refresh_btn.clicked.connect(self.refresh_data)
        
        # Review table actions
        self.select_all_btn.clicked.connect(self.select_all_transactions)
        self.select_none_btn.clicked.connect(self.select_no_transactions)
        self.approve_selected_btn.clicked.connect(self.approve_selected_transactions)
        self.reject_selected_btn.clicked.connect(self.reject_selected_transactions)
        self.learn_patterns_btn.clicked.connect(self.learn_patterns_from_selected)
        
        # Merchant management
        self.export_patterns_btn.clicked.connect(self.export_merchant_patterns)
        self.import_patterns_btn.clicked.connect(self.import_merchant_patterns)
        self.backup_patterns_btn.clicked.connect(self.create_merchant_backup)
        self.cleanup_patterns_btn.clicked.connect(self.cleanup_old_patterns)
        
        # Batch operations
        self.batch_process_btn.clicked.connect(self.process_batch)
        
        # Footer buttons
        self.save_changes_btn.clicked.connect(self.save_changes)
        self.cancel_btn.clicked.connect(self.reject)

    def set_transactions(self, raw_transactions: List[RawTransaction],
                        processed_transactions: List[ProcessedTransaction]):
        """Set transactions for review"""
        self.raw_transactions = raw_transactions
        self.processed_transactions = processed_transactions

        # Get AI suggestions for manual review
        if self.categorizer:
            self.ai_suggestions = self.categorizer.get_ai_suggestions_for_manual_review(raw_transactions)

        self.refresh_data()
        self.refresh_statistics()

    def refresh_data(self):
        """Refresh all data displays"""
        self.populate_review_table()
        self.populate_merchant_table()
        self.update_monitoring_info()

    def populate_review_table(self):
        """Populate the AI review table"""
        # Filter transactions based on current filter
        filtered_transactions = self.get_filtered_transactions()

        self.review_table.setRowCount(len(filtered_transactions))

        for row, (raw_txn, processed_txn) in enumerate(filtered_transactions):
            # Checkbox for selection
            checkbox = QCheckBox()
            self.review_table.setCellWidget(row, 0, checkbox)

            # Transaction details
            self.review_table.setItem(row, 1, QTableWidgetItem(raw_txn.description[:50]))
            self.review_table.setItem(row, 2, QTableWidgetItem(f"₹{abs(raw_txn.amount):.2f}"))

            # AI suggestions
            suggestion = self.ai_suggestions.get(raw_txn.description, {})
            ai_category = suggestion.get('category', processed_txn.category if processed_txn else 'N/A')
            ai_sub_category = suggestion.get('sub_category', processed_txn.sub_category if processed_txn else 'N/A')
            confidence = suggestion.get('confidence', processed_txn.confidence_score if processed_txn else 0.0)

            self.review_table.setItem(row, 3, QTableWidgetItem(ai_category))
            self.review_table.setItem(row, 4, QTableWidgetItem(ai_sub_category))

            # Confidence with color coding
            confidence_item = QTableWidgetItem(f"{confidence:.1%}")
            if confidence >= 0.8:
                confidence_item.setBackground(QColor(200, 255, 200))  # Light green
            elif confidence >= 0.6:
                confidence_item.setBackground(QColor(255, 255, 200))  # Light yellow
            else:
                confidence_item.setBackground(QColor(255, 200, 200))  # Light red
            self.review_table.setItem(row, 5, confidence_item)

            # Context information
            context_info = "N/A"
            if processed_txn and hasattr(processed_txn, 'context_hints'):
                context_flags = processed_txn.context_hints.get('context_flags', [])
                context_info = ", ".join(context_flags[:2]) if context_flags else "Standard"

            self.review_table.setItem(row, 6, QTableWidgetItem(context_info))

            # Action buttons
            action_widget = QWidget()
            action_layout = QHBoxLayout(action_widget)
            action_layout.setContentsMargins(2, 2, 2, 2)

            approve_btn = QPushButton("✅")
            approve_btn.setMaximumWidth(30)
            approve_btn.clicked.connect(lambda checked, r=row: self.approve_transaction(r))

            reject_btn = QPushButton("❌")
            reject_btn.setMaximumWidth(30)
            reject_btn.clicked.connect(lambda checked, r=row: self.reject_transaction(r))

            edit_btn = QPushButton("✏️")
            edit_btn.setMaximumWidth(30)
            edit_btn.clicked.connect(lambda checked, r=row: self.edit_transaction(r))

            action_layout.addWidget(approve_btn)
            action_layout.addWidget(reject_btn)
            action_layout.addWidget(edit_btn)
            action_layout.addStretch()

            self.review_table.setCellWidget(row, 7, action_widget)

    def populate_merchant_table(self):
        """Populate the merchant patterns table"""
        if not self.categorizer:
            return

        try:
            merchant_stats = self.categorizer.get_merchant_mapping_stats()
            # This would need to be implemented to get individual patterns
            # For now, show summary information

            self.merchant_table.setRowCount(1)
            self.merchant_table.setItem(0, 0, QTableWidgetItem("Loading patterns..."))

        except Exception as e:
            self.logger.error(f"Error populating merchant table: {str(e)}")

    def get_filtered_transactions(self):
        """Get transactions based on current filter"""
        if not self.raw_transactions:
            return []

        # Combine raw and processed transactions
        combined = []
        for i, raw_txn in enumerate(self.raw_transactions):
            processed_txn = self.processed_transactions[i] if i < len(self.processed_transactions) else None
            combined.append((raw_txn, processed_txn))

        # Apply filters
        filtered = combined

        filter_text = self.filter_combo.currentText()
        if filter_text == "AI Suggestions":
            filtered = [(r, p) for r, p in filtered if r.description in self.ai_suggestions]
        elif filter_text == "High Confidence":
            filtered = [(r, p) for r, p in filtered if self._get_confidence(r, p) >= 0.8]
        elif filter_text == "Low Confidence":
            filtered = [(r, p) for r, p in filtered if self._get_confidence(r, p) < 0.6]
        elif filter_text == "Needs Review":
            filtered = [(r, p) for r, p in filtered if self._needs_review(r, p)]

        return filtered

    def _get_confidence(self, raw_txn, processed_txn):
        """Get confidence score for transaction"""
        if processed_txn:
            return processed_txn.confidence_score
        suggestion = self.ai_suggestions.get(raw_txn.description, {})
        return suggestion.get('confidence', 0.0)

    def _needs_review(self, raw_txn, processed_txn):
        """Check if transaction needs manual review"""
        confidence = self._get_confidence(raw_txn, processed_txn)
        return confidence < 0.7 or (processed_txn and processed_txn.category == "Other")

    def refresh_statistics(self):
        """Refresh statistics displays"""
        try:
            if not self.categorizer:
                return

            # Get system status
            system_status = self.categorizer.get_system_status()

            # Update AI processing stats
            ai_coordinator_status = system_status.get('ai_coordinator_status', {})
            coordinator_stats = ai_coordinator_status.get('coordinator_stats', {})

            total_processed = coordinator_stats.get('total_transactions_processed', 0)
            total_cost = coordinator_stats.get('total_cost', 0)

            self.ai_processed_label.setText(f"Processed: {total_processed}")
            self.ai_cost_label.setText(f"Session Cost: ${total_cost:.4f}")

            # Calculate average confidence
            if self.processed_transactions:
                avg_confidence = sum(t.confidence_score for t in self.processed_transactions) / len(self.processed_transactions)
                self.ai_confidence_label.setText(f"Avg Confidence: {avg_confidence:.1%}")

            # Update merchant cache stats
            merchant_stats = self.categorizer.get_merchant_mapping_stats()
            if merchant_stats:
                patterns = merchant_stats.get('total_patterns', 0)
                hit_rate = merchant_stats.get('cache_hit_rate', 0)
                savings = merchant_stats.get('cost_saved', 0)

                self.cache_patterns_label.setText(f"Patterns: {patterns}")
                self.cache_hit_rate_label.setText(f"Hit Rate: {hit_rate:.1%}")
                self.cache_savings_label.setText(f"Savings: ${savings:.4f}")

            # Update review stats
            total_transactions = len(self.raw_transactions)
            ai_suggestions_count = len(self.ai_suggestions)

            self.pending_review_label.setText(f"Pending: {total_transactions}")
            self.approved_label.setText(f"AI Suggestions: {ai_suggestions_count}")

        except Exception as e:
            self.logger.error(f"Error refreshing statistics: {str(e)}")

    def apply_filters(self):
        """Apply current filters to the review table"""
        self.populate_review_table()

    def select_all_transactions(self):
        """Select all visible transactions"""
        for row in range(self.review_table.rowCount()):
            checkbox = self.review_table.cellWidget(row, 0)
            if checkbox:
                checkbox.setChecked(True)

    def select_no_transactions(self):
        """Deselect all transactions"""
        for row in range(self.review_table.rowCount()):
            checkbox = self.review_table.cellWidget(row, 0)
            if checkbox:
                checkbox.setChecked(False)

    def get_selected_rows(self):
        """Get list of selected row indices"""
        selected = []
        for row in range(self.review_table.rowCount()):
            checkbox = self.review_table.cellWidget(row, 0)
            if checkbox and checkbox.isChecked():
                selected.append(row)
        return selected

    def approve_selected_transactions(self):
        """Approve selected transactions"""
        selected_rows = self.get_selected_rows()
        if not selected_rows:
            QMessageBox.information(self, "No Selection", "Please select transactions to approve.")
            return

        approved_count = 0
        for row in selected_rows:
            if self.approve_transaction(row):
                approved_count += 1

        QMessageBox.information(self, "Approval Complete",
                              f"Approved {approved_count} transactions.")
        self.refresh_data()

    def reject_selected_transactions(self):
        """Reject selected transactions"""
        selected_rows = self.get_selected_rows()
        if not selected_rows:
            QMessageBox.information(self, "No Selection", "Please select transactions to reject.")
            return

        rejected_count = 0
        for row in selected_rows:
            if self.reject_transaction(row):
                rejected_count += 1

        QMessageBox.information(self, "Rejection Complete",
                              f"Rejected {rejected_count} transactions.")
        self.refresh_data()

    def approve_transaction(self, row):
        """Approve a single transaction"""
        try:
            if row >= len(self.processed_transactions):
                return False

            transaction = self.processed_transactions[row]

            # Mark transaction as approved
            if not hasattr(transaction, 'approval_status'):
                transaction.approval_status = 'approved'
            else:
                transaction.approval_status = 'approved'

            # Learn pattern from approved transaction if it has AI categorization
            if (hasattr(transaction, 'confidence_score') and
                transaction.confidence_score > 0 and
                self.categorizer and
                hasattr(self.categorizer, 'merchant_mapper')):

                self.categorizer.merchant_mapper.learn_from_ai_categorization(
                    transaction,
                    transaction.category,
                    transaction.sub_category,
                    transaction.confidence_score,
                    "manual_approval"
                )

            # Update UI
            self.refresh_transaction_table()
            desc = getattr(transaction, 'description', getattr(transaction, 'original_description', 'Transaction'))
            self.status_label.setText(f"✅ Approved transaction: {desc[:30]}...")

            return True

        except Exception as e:
            self.logger.error(f"Error approving transaction: {str(e)}")
            return False

    def reject_transaction(self, row):
        """Reject a single transaction"""
        try:
            if row >= len(self.processed_transactions):
                return False

            transaction = self.processed_transactions[row]

            # Mark transaction as rejected
            if not hasattr(transaction, 'approval_status'):
                transaction.approval_status = 'rejected'
            else:
                transaction.approval_status = 'rejected'

            # Reset categorization for manual review
            transaction.category = "Other"
            transaction.sub_category = "Needs Review"
            transaction.confidence_score = 0.0
            transaction.notes = "Rejected by user - needs manual categorization"

            # Update UI
            self.refresh_transaction_table()
            desc = getattr(transaction, 'description', getattr(transaction, 'original_description', 'Transaction'))
            self.status_label.setText(f"❌ Rejected transaction: {desc[:30]}...")

            return True

        except Exception as e:
            self.logger.error(f"Error rejecting transaction: {str(e)}")
            return False

    def edit_transaction(self, row):
        """Edit a single transaction"""
        try:
            if row >= len(self.processed_transactions):
                return

            transaction = self.processed_transactions[row]

            # Create edit dialog
            from PySide6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QLineEdit, QPushButton, QComboBox

            edit_dialog = QDialog(self)
            edit_dialog.setWindowTitle("Edit Transaction")
            edit_dialog.setModal(True)
            edit_dialog.resize(400, 300)

            layout = QVBoxLayout(edit_dialog)

            # Form layout for editing
            form_layout = QFormLayout()

            # Description (read-only)
            desc_edit = QLineEdit(transaction.description)
            desc_edit.setReadOnly(True)
            form_layout.addRow("Description:", desc_edit)

            # Amount (read-only)
            amount_edit = QLineEdit(f"₹{transaction.amount}")
            amount_edit.setReadOnly(True)
            form_layout.addRow("Amount:", amount_edit)

            # Category (editable)
            category_combo = QComboBox()
            categories = ["Food & Dining", "Transportation", "Shopping", "Bills & Utilities",
                         "Entertainment", "Healthcare", "Education", "Travel", "Income",
                         "Transfer", "Investment", "Other"]
            category_combo.addItems(categories)
            if hasattr(transaction, 'category') and transaction.category:
                index = category_combo.findText(transaction.category)
                if index >= 0:
                    category_combo.setCurrentIndex(index)
            form_layout.addRow("Category:", category_combo)

            # Sub-category (editable)
            subcategory_edit = QLineEdit()
            if hasattr(transaction, 'sub_category') and transaction.sub_category:
                subcategory_edit.setText(transaction.sub_category)
            form_layout.addRow("Sub-Category:", subcategory_edit)

            # Notes (editable)
            notes_edit = QLineEdit()
            if hasattr(transaction, 'notes') and transaction.notes:
                notes_edit.setText(transaction.notes)
            form_layout.addRow("Notes:", notes_edit)

            layout.addLayout(form_layout)

            # Buttons
            button_layout = QHBoxLayout()
            save_btn = QPushButton("💾 Save")
            cancel_btn = QPushButton("❌ Cancel")

            button_layout.addWidget(save_btn)
            button_layout.addWidget(cancel_btn)
            layout.addLayout(button_layout)

            # Connect buttons
            def save_changes():
                transaction.category = category_combo.currentText()
                transaction.sub_category = subcategory_edit.text()
                transaction.notes = notes_edit.text()
                transaction.confidence_score = 1.0  # Manual edit = 100% confidence

                # Mark as manually edited
                if not hasattr(transaction, 'approval_status'):
                    transaction.approval_status = 'manually_edited'
                else:
                    transaction.approval_status = 'manually_edited'

                # Learn pattern if categorizer available
                if (self.categorizer and
                    hasattr(self.categorizer, 'merchant_mapper') and
                    transaction.category != "Other"):

                    self.categorizer.merchant_mapper.learn_from_ai_categorization(
                        transaction,
                        transaction.category,
                        transaction.sub_category,
                        1.0,  # Manual edit = high confidence
                        "manual_edit"
                    )

                self.refresh_transaction_table()
                desc = getattr(transaction, 'description', getattr(transaction, 'original_description', 'Transaction'))
                self.status_label.setText(f"✏️ Edited transaction: {desc[:30]}...")
                edit_dialog.accept()

            save_btn.clicked.connect(save_changes)
            cancel_btn.clicked.connect(edit_dialog.reject)

            # Show dialog
            edit_dialog.exec()

        except Exception as e:
            self.logger.error(f"Error editing transaction: {str(e)}")
            QMessageBox.critical(self, "Edit Error", f"Failed to edit transaction: {str(e)}")

    def learn_patterns_from_selected(self):
        """Learn merchant patterns from selected transactions"""
        try:
            selected_rows = self.get_selected_rows()
            if not selected_rows:
                QMessageBox.information(self, "No Selection", "Please select transactions to learn from.")
                return

            if not self.categorizer or not hasattr(self.categorizer, 'merchant_mapper'):
                QMessageBox.warning(self, "No Categorizer", "Merchant mapper not available.")
                return

            learned_count = 0
            skipped_count = 0

            for row in selected_rows:
                if row >= len(self.processed_transactions):
                    continue

                transaction = self.processed_transactions[row]

                # Only learn from transactions with valid categorization
                if (hasattr(transaction, 'category') and
                    transaction.category and
                    transaction.category != "Other" and
                    hasattr(transaction, 'confidence_score') and
                    transaction.confidence_score > 0.5):

                    success = self.categorizer.merchant_mapper.learn_from_ai_categorization(
                        transaction,
                        transaction.category,
                        transaction.sub_category or "Miscellaneous",
                        transaction.confidence_score,
                        "manual_selection"
                    )

                    if success:
                        learned_count += 1
                    else:
                        skipped_count += 1
                else:
                    skipped_count += 1

            # Show results
            if learned_count > 0:
                QMessageBox.information(
                    self, "Pattern Learning Complete",
                    f"Successfully learned patterns from {learned_count} transactions.\n"
                    f"Skipped {skipped_count} transactions (invalid categorization).\n\n"
                    f"These patterns will help categorize similar transactions automatically."
                )

                # Emit signal that patterns were updated
                self.merchant_patterns_updated.emit()

                # Refresh merchant patterns display
                self.refresh_merchant_patterns()
            else:
                QMessageBox.information(
                    self, "No Patterns Learned",
                    f"No valid patterns found in {len(selected_rows)} selected transactions.\n"
                    f"Make sure transactions have valid categories and confidence scores."
                )

        except Exception as e:
            self.logger.error(f"Error learning patterns: {str(e)}")
            QMessageBox.critical(self, "Pattern Learning Error", f"Failed to learn patterns: {str(e)}")

    def save_changes(self):
        """Save all changes and close dialog"""
        try:
            # Emit signal with updated transactions
            self.transactions_updated.emit(self.processed_transactions)
            self.accept()
        except Exception as e:
            QMessageBox.critical(self, "Save Error", f"Error saving changes: {str(e)}")

    def export_merchant_patterns(self):
        """Export merchant patterns to file"""
        if self.categorizer:
            success = self.categorizer.export_merchant_patterns("merchant_patterns_export.json")
            if success:
                QMessageBox.information(self, "Export Complete", "Merchant patterns exported successfully.")
            else:
                QMessageBox.warning(self, "Export Failed", "Failed to export merchant patterns.")

    def import_merchant_patterns(self):
        """Import merchant patterns from file"""
        try:
            if not self.categorizer or not hasattr(self.categorizer, 'merchant_mapper'):
                QMessageBox.warning(self, "No Categorizer", "Merchant mapper not available.")
                return

            # Open file dialog
            from PySide6.QtWidgets import QFileDialog
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "Import Merchant Patterns",
                "",
                "JSON Files (*.json);;All Files (*)"
            )

            if not file_path:
                return

            # Import patterns
            result = self.categorizer.merchant_mapper.restore_from_backup(file_path)

            if result.get('success', False):
                imported_count = result.get('patterns_loaded', 0)
                QMessageBox.information(
                    self, "Import Successful",
                    f"Successfully imported {imported_count} merchant patterns.\n\n"
                    f"These patterns are now available for transaction categorization."
                )

                # Refresh displays
                self.refresh_merchant_patterns()
                self.merchant_patterns_updated.emit()
            else:
                error_msg = result.get('error', 'Unknown error')
                QMessageBox.warning(
                    self, "Import Failed",
                    f"Failed to import merchant patterns:\n{error_msg}"
                )

        except Exception as e:
            self.logger.error(f"Error importing patterns: {str(e)}")
            QMessageBox.critical(self, "Import Error", f"Failed to import patterns: {str(e)}")

    def create_merchant_backup(self):
        """Create backup of merchant patterns"""
        if self.categorizer and hasattr(self.categorizer, 'merchant_mapper'):
            backup_file = self.categorizer.merchant_mapper.create_manual_backup("Manual backup from review dialog")
            if backup_file:
                QMessageBox.information(self, "Backup Created", f"Backup created: {backup_file}")
            else:
                QMessageBox.warning(self, "Backup Failed", "Failed to create backup.")

    def cleanup_old_patterns(self):
        """Cleanup old merchant patterns"""
        if self.categorizer:
            result = self.categorizer.cleanup_expired_merchant_patterns()
            removed = result.get('removed_patterns', 0)
            QMessageBox.information(self, "Cleanup Complete", f"Removed {removed} expired patterns.")

    def process_batch(self):
        """Process batch operations with AI categorization"""
        try:
            if not self.categorizer or not self.raw_transactions:
                QMessageBox.warning(self, "No Data", "No transactions available for batch processing.")
                return

            # Get batch processing settings
            confidence_threshold = float(self.batch_confidence_threshold.currentText().rstrip('%')) / 100
            auto_approve = self.auto_approve_checkbox.isChecked()

            # Show confirmation dialog
            reply = QMessageBox.question(
                self, "Batch Processing",
                f"Process {len(self.raw_transactions)} transactions with:\n"
                f"• Confidence threshold: {confidence_threshold:.0%}\n"
                f"• Auto-approve high confidence: {auto_approve}\n\n"
                f"This may incur AI processing costs. Continue?",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # Start batch processing
            self.batch_process_btn.setEnabled(False)
            self.batch_process_btn.setText("⏳ Processing...")

            # Update progress
            self.batch_progress_bar.setVisible(True)
            self.batch_progress_bar.setMaximum(len(self.raw_transactions))
            self.batch_progress_bar.setValue(0)

            processed_count = 0
            approved_count = 0
            rejected_count = 0

            # Process transactions in batches
            batch_size = 20  # Process 20 at a time for cost optimization
            for i in range(0, len(self.raw_transactions), batch_size):
                batch = self.raw_transactions[i:i + batch_size]

                # Process batch with AI
                processed_batch = self.categorizer.categorize_batch(batch)

                # Update processed transactions
                for j, processed_txn in enumerate(processed_batch):
                    original_index = i + j
                    if original_index < len(self.processed_transactions):
                        self.processed_transactions[original_index] = processed_txn

                        # Auto-approve if enabled and confidence is high
                        if auto_approve and processed_txn.confidence_score >= confidence_threshold:
                            # Mark as approved (you would implement actual approval logic here)
                            approved_count += 1
                        elif processed_txn.confidence_score < 0.6:  # Low confidence
                            rejected_count += 1

                        processed_count += 1
                        self.batch_progress_bar.setValue(processed_count)

                # Update UI periodically
                QApplication.processEvents()

            # Complete processing
            self.batch_progress_bar.setVisible(False)
            self.batch_process_btn.setEnabled(True)
            self.batch_process_btn.setText("⚡ Process Batch")

            # Refresh the transaction table
            self.refresh_transaction_table()

            # Show results
            QMessageBox.information(
                self, "Batch Processing Complete",
                f"Processed: {processed_count} transactions\n"
                f"Auto-approved: {approved_count} transactions\n"
                f"Needs review: {rejected_count} transactions\n\n"
                f"Check the AI Review tab for detailed results."
            )

            # Emit signal that transactions were updated
            self.transactions_updated.emit(self.processed_transactions)

        except Exception as e:
            self.logger.error(f"Error in batch processing: {str(e)}")
            QMessageBox.critical(self, "Batch Processing Error", f"Failed to process batch: {str(e)}")

            # Reset UI state
            self.batch_progress_bar.setVisible(False)
            self.batch_process_btn.setEnabled(True)
            self.batch_process_btn.setText("⚡ Process Batch")

    def update_monitoring_info(self):
        """Update monitoring information"""
        try:
            if not self.categorizer:
                return

            system_status = self.categorizer.get_system_status()

            # Update system status labels
            ai_features = system_status.get('enhanced_features', {})
            ai_available = ai_features.get('ai_categorization_coordinator', False)

            if ai_available:
                self.ai_system_status_label.setText("AI System: ✅ Enhanced Available")
            else:
                self.ai_system_status_label.setText("AI System: ⚠️ Basic Mode")

            # Update budget status
            budget_status = self.categorizer.get_budget_status()
            remaining = budget_status.get('total_remaining', 0)
            self.budget_status_label.setText(f"Budget: ${remaining:.4f} remaining")

            # Update cache status
            merchant_stats = self.categorizer.get_merchant_mapping_stats()
            if merchant_stats:
                patterns = merchant_stats.get('total_patterns', 0)
                self.cache_status_label.setText(f"Cache: {patterns} patterns")

            # Update metrics text
            metrics_text = f"""
Performance Metrics:
• Total Sessions: {system_status.get('ai_coordinator_status', {}).get('coordinator_stats', {}).get('total_sessions', 0)}
• Cache Hit Rate: {merchant_stats.get('cache_hit_rate', 0):.1%}
• Average Confidence: {sum(t.confidence_score for t in self.processed_transactions) / max(1, len(self.processed_transactions)):.1%}
• Cost Efficiency: {merchant_stats.get('cost_saved', 0) / max(0.01, merchant_stats.get('cost_saved', 0) + system_status.get('ai_coordinator_status', {}).get('coordinator_stats', {}).get('total_cost', 0)):.1%}
            """.strip()

            self.metrics_text.setText(metrics_text)

        except Exception as e:
            self.logger.error(f"Error updating monitoring info: {str(e)}")

    def refresh_transaction_table(self):
        """Refresh the transaction table with current data"""
        try:
            if not self.processed_transactions:
                return

            # Update the table with current transaction data
            self.populate_transaction_table(self.processed_transactions)

            # Update status counts
            total = len(self.processed_transactions)
            high_confidence = sum(1 for t in self.processed_transactions
                                if hasattr(t, 'confidence_score') and t.confidence_score >= 0.8)
            needs_review = sum(1 for t in self.processed_transactions
                             if hasattr(t, 'confidence_score') and t.confidence_score < 0.6)

            # Update status label
            self.status_label.setText(
                f"Total: {total} | High Confidence: {high_confidence} | Needs Review: {needs_review}"
            )

        except Exception as e:
            self.logger.error(f"Error refreshing transaction table: {str(e)}")

    def get_selected_rows(self):
        """Get list of selected row indices"""
        try:
            if not hasattr(self, 'review_table'):
                return []

            selected_rows = []

            # Method 1: Check checkboxes in first column
            for i in range(self.review_table.rowCount()):
                checkbox_widget = self.review_table.cellWidget(i, 0)
                if checkbox_widget and isinstance(checkbox_widget, QCheckBox) and checkbox_widget.isChecked():
                    selected_rows.append(i)

            # Method 2: If no checkboxes are selected, check for row selection
            if not selected_rows:
                selected_items = self.review_table.selectionModel().selectedRows()
                selected_rows = [item.row() for item in selected_items]

            return selected_rows

        except Exception as e:
            self.logger.error(f"Error getting selected rows: {str(e)}")
            return []

    def populate_transaction_table(self, transactions):
        """Populate the transaction table with data"""
        try:
            if not hasattr(self, 'review_table'):
                return

            # Clear existing data
            self.review_table.setRowCount(0)
            self.review_table.setRowCount(len(transactions))

            for row, transaction in enumerate(transactions):
                # Checkbox for selection
                checkbox = QCheckBox()
                self.review_table.setCellWidget(row, 0, checkbox)

                # Description
                desc = getattr(transaction, 'description', getattr(transaction, 'original_description', 'N/A'))
                desc_item = QTableWidgetItem(desc[:50])
                self.review_table.setItem(row, 1, desc_item)

                # Amount
                amount = getattr(transaction, 'amount', 0)
                amount_item = QTableWidgetItem(f"₹{abs(amount):.2f}")
                self.review_table.setItem(row, 2, amount_item)

                # AI Category
                category = getattr(transaction, 'category', 'Other')
                category_item = QTableWidgetItem(category)
                self.review_table.setItem(row, 3, category_item)

                # AI Sub-category
                sub_category = getattr(transaction, 'sub_category', 'Needs Review')
                sub_category_item = QTableWidgetItem(sub_category)
                self.review_table.setItem(row, 4, sub_category_item)

                # Confidence with color coding
                confidence = getattr(transaction, 'confidence_score', 0.0)
                confidence_item = QTableWidgetItem(f"{confidence:.1%}")

                if confidence >= 0.8:
                    confidence_item.setBackground(QColor(200, 255, 200))  # Light green
                elif confidence >= 0.6:
                    confidence_item.setBackground(QColor(255, 255, 200))  # Light yellow
                else:
                    confidence_item.setBackground(QColor(255, 200, 200))  # Light red

                self.review_table.setItem(row, 5, confidence_item)

                # Context info
                context_info = "Standard"
                if hasattr(transaction, 'context_hints'):
                    context_flags = transaction.context_hints.get('context_flags', [])
                    context_info = ", ".join(context_flags[:2]) if context_flags else "Standard"

                context_item = QTableWidgetItem(context_info)
                self.review_table.setItem(row, 6, context_item)

                # Action buttons
                action_widget = QWidget()
                action_layout = QHBoxLayout(action_widget)
                action_layout.setContentsMargins(2, 2, 2, 2)

                approve_btn = QPushButton("✅")
                approve_btn.setMaximumWidth(30)
                approve_btn.clicked.connect(lambda checked, r=row: self.approve_transaction(r))

                reject_btn = QPushButton("❌")
                reject_btn.setMaximumWidth(30)
                reject_btn.clicked.connect(lambda checked, r=row: self.reject_transaction(r))

                edit_btn = QPushButton("✏️")
                edit_btn.setMaximumWidth(30)
                edit_btn.clicked.connect(lambda checked, r=row: self.edit_transaction(r))

                action_layout.addWidget(approve_btn)
                action_layout.addWidget(reject_btn)
                action_layout.addWidget(edit_btn)

                self.review_table.setCellWidget(row, 7, action_widget)

        except Exception as e:
            self.logger.error(f"Error populating transaction table: {str(e)}")

    def select_all_transactions(self):
        """Select all transactions in the table"""
        try:
            for i in range(self.review_table.rowCount()):
                checkbox_widget = self.review_table.cellWidget(i, 0)
                if checkbox_widget and isinstance(checkbox_widget, QCheckBox):
                    checkbox_widget.setChecked(True)
        except Exception as e:
            self.logger.error(f"Error selecting all transactions: {str(e)}")

    def select_no_transactions(self):
        """Deselect all transactions in the table"""
        try:
            for i in range(self.review_table.rowCount()):
                checkbox_widget = self.review_table.cellWidget(i, 0)
                if checkbox_widget and isinstance(checkbox_widget, QCheckBox):
                    checkbox_widget.setChecked(False)
        except Exception as e:
            self.logger.error(f"Error deselecting all transactions: {str(e)}")

    def approve_selected_transactions(self):
        """Approve all selected transactions"""
        try:
            selected_rows = self.get_selected_rows()
            if not selected_rows:
                QMessageBox.information(self, "No Selection", "Please select transactions to approve.")
                return

            approved_count = 0
            for row in selected_rows:
                if self.approve_transaction(row):
                    approved_count += 1

            QMessageBox.information(
                self, "Batch Approval Complete",
                f"Successfully approved {approved_count} out of {len(selected_rows)} selected transactions."
            )

            # Clear selections after approval
            self.select_no_transactions()

        except Exception as e:
            self.logger.error(f"Error approving selected transactions: {str(e)}")
            QMessageBox.critical(self, "Approval Error", f"Failed to approve selected transactions: {str(e)}")

    def reject_selected_transactions(self):
        """Reject all selected transactions"""
        try:
            selected_rows = self.get_selected_rows()
            if not selected_rows:
                QMessageBox.information(self, "No Selection", "Please select transactions to reject.")
                return

            rejected_count = 0
            for row in selected_rows:
                if self.reject_transaction(row):
                    rejected_count += 1

            QMessageBox.information(
                self, "Batch Rejection Complete",
                f"Successfully rejected {rejected_count} out of {len(selected_rows)} selected transactions."
            )

            # Clear selections after rejection
            self.select_no_transactions()

        except Exception as e:
            self.logger.error(f"Error rejecting selected transactions: {str(e)}")
            QMessageBox.critical(self, "Rejection Error", f"Failed to reject selected transactions: {str(e)}")
