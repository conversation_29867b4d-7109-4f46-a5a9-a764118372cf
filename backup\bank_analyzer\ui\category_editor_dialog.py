"""
Category Editor Dialog for ML Transaction Labeling System
Allows users to add, edit, and delete categories and subcategories
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTreeWidget, QTreeWidgetItem,
    QPushButton, QLineEdit, QLabel, QMessageBox, QInputDialog,
    QGroupBox, QGridLayout, QTextEdit, QColorDialog, QComboBox,
    QSplitter, QFrame
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QColor, QBrush, QFont
from typing import Dict, List, Optional
import logging

from ..core.logger import get_logger


class CategoryEditorDialog(QDialog):
    """
    Dialog for editing categories and subcategories in the ML system
    """
    
    # Signals
    categories_changed = Signal()  # Emitted when categories are modified
    
    def __init__(self, category_manager, parent=None):
        super().__init__(parent)
        self.category_manager = category_manager
        self.logger = get_logger(__name__)
        
        self.setWindowTitle("Edit Categories & Subcategories")
        self.setModal(True)
        self.resize(800, 600)
        
        self.setup_ui()
        self.load_categories()
    
    def setup_ui(self):
        """Setup the user interface"""
        layout = QVBoxLayout(self)
        
        # Title
        title_label = QLabel("Category & Subcategory Editor")
        title_font = QFont()
        title_font.setBold(True)
        title_font.setPointSize(14)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # Main content area
        splitter = QSplitter(Qt.Horizontal)
        layout.addWidget(splitter)
        
        # Left panel - Category tree
        left_panel = self.create_category_tree_panel()
        splitter.addWidget(left_panel)
        
        # Right panel - Edit controls
        right_panel = self.create_edit_panel()
        splitter.addWidget(right_panel)
        
        # Set splitter proportions
        splitter.setSizes([400, 400])
        
        # Buttons
        button_layout = self.create_buttons()
        layout.addLayout(button_layout)
    
    def create_category_tree_panel(self):
        """Create the category tree panel"""
        panel = QFrame()
        layout = QVBoxLayout(panel)
        
        # Tree widget
        tree_label = QLabel("Categories & Subcategories")
        tree_label.setStyleSheet("font-weight: bold;")
        layout.addWidget(tree_label)
        
        self.category_tree = QTreeWidget()
        self.category_tree.setHeaderLabels(["Name", "Type", "Status"])
        self.category_tree.itemSelectionChanged.connect(self.on_category_selected)
        self.category_tree.itemDoubleClicked.connect(self.edit_selected_category)
        layout.addWidget(self.category_tree)
        
        # Tree action buttons
        tree_button_layout = QHBoxLayout()
        
        self.add_category_btn = QPushButton("Add Category")
        self.add_category_btn.clicked.connect(self.add_new_category)
        tree_button_layout.addWidget(self.add_category_btn)
        
        self.add_subcategory_btn = QPushButton("Add Subcategory")
        self.add_subcategory_btn.clicked.connect(self.add_new_subcategory)
        self.add_subcategory_btn.setEnabled(False)
        tree_button_layout.addWidget(self.add_subcategory_btn)
        
        self.delete_btn = QPushButton("Delete")
        self.delete_btn.clicked.connect(self.delete_selected_item)
        self.delete_btn.setEnabled(False)
        tree_button_layout.addWidget(self.delete_btn)
        
        layout.addLayout(tree_button_layout)
        
        return panel
    
    def create_edit_panel(self):
        """Create the edit panel"""
        panel = QFrame()
        layout = QVBoxLayout(panel)
        
        # Edit form
        edit_group = QGroupBox("Edit Selected Item")
        edit_layout = QGridLayout(edit_group)
        
        # Name
        edit_layout.addWidget(QLabel("Name:"), 0, 0)
        self.name_edit = QLineEdit()
        self.name_edit.textChanged.connect(self.on_name_changed)
        edit_layout.addWidget(self.name_edit, 0, 1)
        
        # Type (read-only)
        edit_layout.addWidget(QLabel("Type:"), 1, 0)
        self.type_label = QLabel("None")
        self.type_label.setStyleSheet("color: #666;")
        edit_layout.addWidget(self.type_label, 1, 1)
        
        # Parent (for subcategories)
        edit_layout.addWidget(QLabel("Parent:"), 2, 0)
        self.parent_combo = QComboBox()
        self.parent_combo.currentTextChanged.connect(self.on_parent_changed)
        edit_layout.addWidget(self.parent_combo, 2, 1)

        # Category Type (for main categories)
        edit_layout.addWidget(QLabel("Category Type:"), 3, 0)
        self.category_type_combo = QComboBox()
        self.category_type_combo.addItems(["Expense", "Income", "Both"])
        self.category_type_combo.currentTextChanged.connect(self.on_category_type_changed)
        edit_layout.addWidget(self.category_type_combo, 3, 1)

        # Description
        edit_layout.addWidget(QLabel("Description:"), 4, 0)
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(80)
        self.description_edit.textChanged.connect(self.on_description_changed)
        edit_layout.addWidget(self.description_edit, 4, 1)

        # Color
        edit_layout.addWidget(QLabel("Color:"), 5, 0)
        color_layout = QHBoxLayout()
        self.color_display = QLabel("      ")
        self.color_display.setStyleSheet("border: 1px solid #ccc; background-color: #ffffff;")
        self.color_button = QPushButton("Choose Color")
        self.color_button.clicked.connect(self.choose_color)
        color_layout.addWidget(self.color_display)
        color_layout.addWidget(self.color_button)
        color_layout.addStretch()
        edit_layout.addLayout(color_layout, 5, 1)

        # Save button
        self.save_btn = QPushButton("Save Changes")
        self.save_btn.clicked.connect(self.save_current_item)
        self.save_btn.setEnabled(False)
        edit_layout.addWidget(self.save_btn, 6, 0, 1, 2)
        
        layout.addWidget(edit_group)
        
        # Statistics
        stats_group = QGroupBox("Usage Statistics")
        stats_layout = QVBoxLayout(stats_group)
        
        self.stats_label = QLabel("Select a category to view statistics")
        self.stats_label.setWordWrap(True)
        stats_layout.addWidget(self.stats_label)
        
        layout.addWidget(stats_group)
        
        layout.addStretch()
        
        return panel
    
    def create_buttons(self):
        """Create dialog buttons"""
        layout = QHBoxLayout()
        
        # Help button
        help_btn = QPushButton("Help")
        help_btn.clicked.connect(self.show_help)
        layout.addWidget(help_btn)
        
        layout.addStretch()
        
        # Close button
        close_btn = QPushButton("Close")
        close_btn.clicked.connect(self.accept)
        layout.addWidget(close_btn)
        
        return layout
    
    def load_categories(self):
        """Load categories into the tree widget"""
        self.category_tree.clear()
        
        try:
            # Get main categories
            main_categories = self.category_manager.get_main_categories()
            
            for category in main_categories:
                # Create category item
                cat_item = QTreeWidgetItem([category.name, "Category", "Active" if category.is_active else "Inactive"])
                cat_item.setData(0, Qt.UserRole, {"type": "category", "id": category.id, "data": category})
                
                # Set color if available
                if category.color:
                    cat_item.setBackground(0, QBrush(QColor(category.color)))
                
                self.category_tree.addTopLevelItem(cat_item)
                
                # Add subcategories
                subcategories = self.category_manager.get_subcategories(category.name)
                for subcategory in subcategories:
                    subcat_item = QTreeWidgetItem([subcategory.name, "Subcategory", "Active" if subcategory.is_active else "Inactive"])
                    subcat_item.setData(0, Qt.UserRole, {"type": "subcategory", "id": subcategory.id, "data": subcategory})
                    
                    if subcategory.color:
                        subcat_item.setBackground(0, QBrush(QColor(subcategory.color)))
                    
                    cat_item.addChild(subcat_item)
                
                # Expand category
                cat_item.setExpanded(True)
            
            self.logger.info(f"Loaded {len(main_categories)} categories into editor")
            
        except Exception as e:
            self.logger.error(f"Error loading categories: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to load categories: {str(e)}")
    
    def on_category_selected(self):
        """Handle category selection"""
        selected_items = self.category_tree.selectedItems()
        if not selected_items:
            self.clear_edit_panel()
            return
        
        item = selected_items[0]
        item_data = item.data(0, Qt.UserRole)
        
        if not item_data:
            self.clear_edit_panel()
            return
        
        # Enable/disable buttons based on selection
        self.add_subcategory_btn.setEnabled(item_data["type"] == "category")
        self.delete_btn.setEnabled(True)
        
        # Populate edit panel
        self.populate_edit_panel(item_data)
        
        # Update statistics
        self.update_statistics(item_data)
    
    def populate_edit_panel(self, item_data):
        """Populate the edit panel with item data"""
        data = item_data["data"]
        
        # Block signals to prevent triggering change events
        self.name_edit.blockSignals(True)
        self.description_edit.blockSignals(True)
        self.parent_combo.blockSignals(True)
        self.category_type_combo.blockSignals(True)
        
        try:
            # Set basic info
            self.name_edit.setText(data.name)
            self.type_label.setText(item_data["type"].title())
            self.description_edit.setPlainText(data.description or "")
            
            # Set color
            if data.color:
                self.color_display.setStyleSheet(f"border: 1px solid #ccc; background-color: {data.color};")
            else:
                self.color_display.setStyleSheet("border: 1px solid #ccc; background-color: #ffffff;")
            
            # Handle category type for main categories
            if item_data["type"] == "category":
                self.category_type_combo.setEnabled(True)
                # Set category type
                category_type = getattr(data, 'category_type', 'expense')
                type_text = category_type.title()
                index = self.category_type_combo.findText(type_text)
                if index >= 0:
                    self.category_type_combo.setCurrentIndex(index)
            else:
                self.category_type_combo.setEnabled(False)

            # Handle parent for subcategories
            if item_data["type"] == "subcategory":
                self.parent_combo.setEnabled(True)
                self.populate_parent_combo()

                # Find and set current parent
                if data.parent_id:
                    parent_category = self.category_manager.hierarchy.categories.get(data.parent_id)
                    if parent_category:
                        index = self.parent_combo.findText(parent_category.name)
                        if index >= 0:
                            self.parent_combo.setCurrentIndex(index)
            else:
                self.parent_combo.setEnabled(False)
                self.parent_combo.clear()
            
            self.save_btn.setEnabled(False)
            
        finally:
            # Restore signals
            self.name_edit.blockSignals(False)
            self.description_edit.blockSignals(False)
            self.parent_combo.blockSignals(False)
            self.category_type_combo.blockSignals(False)
    
    def clear_edit_panel(self):
        """Clear the edit panel"""
        self.name_edit.clear()
        self.type_label.setText("None")
        self.description_edit.clear()
        self.parent_combo.clear()
        self.parent_combo.setEnabled(False)
        self.category_type_combo.setCurrentText("Expense")
        self.category_type_combo.setEnabled(False)
        self.color_display.setStyleSheet("border: 1px solid #ccc; background-color: #ffffff;")
        self.save_btn.setEnabled(False)
        self.add_subcategory_btn.setEnabled(False)
        self.delete_btn.setEnabled(False)
        self.stats_label.setText("Select a category to view statistics")
    
    def populate_parent_combo(self):
        """Populate the parent combo box with main categories"""
        self.parent_combo.clear()
        
        main_categories = self.category_manager.get_main_categories()
        for category in main_categories:
            self.parent_combo.addItem(category.name, category.id)
    
    def on_name_changed(self):
        """Handle name change"""
        self.save_btn.setEnabled(True)
    
    def on_description_changed(self):
        """Handle description change"""
        self.save_btn.setEnabled(True)
    
    def on_parent_changed(self):
        """Handle parent change"""
        self.save_btn.setEnabled(True)

    def on_category_type_changed(self):
        """Handle category type change"""
        self.save_btn.setEnabled(True)
    
    def choose_color(self):
        """Open color chooser dialog"""
        color = QColorDialog.getColor(Qt.white, self, "Choose Category Color")
        if color.isValid():
            self.color_display.setStyleSheet(f"border: 1px solid #ccc; background-color: {color.name()};")
            self.save_btn.setEnabled(True)
    
    def save_current_item(self):
        """Save changes to the current item"""
        selected_items = self.category_tree.selectedItems()
        if not selected_items:
            return
        
        item = selected_items[0]
        item_data = item.data(0, Qt.UserRole)
        
        if not item_data:
            return
        
        try:
            # Get updated values
            new_name = self.name_edit.text().strip()
            new_description = self.description_edit.toPlainText().strip()
            
            # Get color from display
            color_style = self.color_display.styleSheet()
            new_color = None
            if "background-color:" in color_style:
                color_part = color_style.split("background-color:")[1].split(";")[0].strip()
                if color_part != "#ffffff":
                    new_color = color_part
            
            # Validate name
            if not new_name:
                QMessageBox.warning(self, "Warning", "Name cannot be empty")
                return
            
            # Get parent for subcategories
            new_parent_id = None
            if item_data["type"] == "subcategory" and self.parent_combo.currentData():
                new_parent_id = self.parent_combo.currentData()

            # Get category type for main categories
            new_category_type = None
            if item_data["type"] == "category":
                category_type_text = self.category_type_combo.currentText().lower()
                new_category_type = category_type_text

            # Prepare update parameters
            update_params = {
                "name": new_name,
                "description": new_description,
                "color": new_color,
                "parent_id": new_parent_id
            }

            # Add category type if it's a main category
            if new_category_type is not None:
                update_params["category_type"] = new_category_type

            # Update the category/subcategory
            success = self.category_manager.update_category(
                item_data["id"],
                **update_params
            )
            
            if success:
                # Force refresh the category manager to ensure latest data
                self.category_manager.refresh_categories(force=True)

                # Reload the tree
                self.load_categories()

                # Emit signal
                self.categories_changed.emit()
                
                # Show success message
                QMessageBox.information(self, "Success", f"{item_data['type'].title()} updated successfully")
                
                self.save_btn.setEnabled(False)
            else:
                QMessageBox.critical(self, "Error", f"Failed to update {item_data['type']}")
                
        except Exception as e:
            self.logger.error(f"Error saving category: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to save changes: {str(e)}")
    
    def add_new_category(self):
        """Add a new main category"""
        name, ok = QInputDialog.getText(self, "Add Category", "Enter category name:")
        if ok and name.strip():
            try:
                category_id = self.category_manager.create_category(name.strip())
                if category_id:
                    self.load_categories()
                    self.categories_changed.emit()
                    QMessageBox.information(self, "Success", f"Category '{name}' created successfully")
                else:
                    QMessageBox.critical(self, "Error", "Failed to create category")
            except Exception as e:
                self.logger.error(f"Error creating category: {str(e)}")
                QMessageBox.critical(self, "Error", f"Failed to create category: {str(e)}")
    
    def add_new_subcategory(self):
        """Add a new subcategory to the selected category"""
        selected_items = self.category_tree.selectedItems()
        if not selected_items:
            return
        
        item = selected_items[0]
        item_data = item.data(0, Qt.UserRole)
        
        if item_data["type"] != "category":
            QMessageBox.warning(self, "Warning", "Please select a main category first")
            return
        
        name, ok = QInputDialog.getText(self, "Add Subcategory", "Enter subcategory name:")
        if ok and name.strip():
            try:
                parent_name = item_data["data"].name
                subcategory_id = self.category_manager.create_category(name.strip(), parent_name=parent_name)
                if subcategory_id:
                    self.load_categories()
                    self.categories_changed.emit()
                    QMessageBox.information(self, "Success", f"Subcategory '{name}' created successfully")
                else:
                    QMessageBox.critical(self, "Error", "Failed to create subcategory")
            except Exception as e:
                self.logger.error(f"Error creating subcategory: {str(e)}")
                QMessageBox.critical(self, "Error", f"Failed to create subcategory: {str(e)}")
    
    def delete_selected_item(self):
        """Delete the selected category or subcategory"""
        selected_items = self.category_tree.selectedItems()
        if not selected_items:
            return
        
        item = selected_items[0]
        item_data = item.data(0, Qt.UserRole)
        
        if not item_data:
            return
        
        # Confirm deletion
        item_type = item_data["type"]
        item_name = item_data["data"].name

        # Check if this category has subcategories
        warning_message = f"Are you sure you want to delete the {item_type} '{item_name}'?\n\n"

        if item_type == "category" and item_data["id"] in self.category_manager.hierarchy.hierarchy:
            subcategory_count = len(self.category_manager.hierarchy.hierarchy[item_data["id"]])
            warning_message += f"This category has {subcategory_count} subcategory(ies) that will also be deleted.\n\n"

        warning_message += "This action cannot be undone and may affect existing transaction labels."

        reply = QMessageBox.question(
            self, "Confirm Deletion",
            warning_message,
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                # Check if it's a system category first
                category = self.category_manager.hierarchy.categories.get(item_data["id"])
                if category and category.is_system:
                    # Ask for confirmation to delete system category
                    system_reply = QMessageBox.question(
                        self, "Delete System Category",
                        f"'{category.name}' is a system category. Deleting it may affect existing transaction labels.\n\n"
                        f"Are you absolutely sure you want to delete this system category?",
                        QMessageBox.Yes | QMessageBox.No,
                        QMessageBox.No
                    )

                    if system_reply == QMessageBox.Yes:
                        # Allow system category deletion with explicit permission
                        success = self.category_manager.delete_category(item_data["id"], force=True, allow_system=True)
                    else:
                        return  # User cancelled system category deletion
                else:
                    # Regular category deletion
                    success = self.category_manager.delete_category(item_data["id"], force=True)

                if success:
                    self.load_categories()
                    self.categories_changed.emit()
                    QMessageBox.information(self, "Success", f"{item_type.title()} deleted successfully")
                else:
                    # Get more specific error message
                    error_msg = f"Failed to delete {item_type}."
                    if category and category.is_system:
                        error_msg += "\n\nSystem categories are protected from deletion. "
                        error_msg += "They may have active transaction labels or be required for system operation."
                    QMessageBox.critical(self, "Deletion Failed", error_msg)

            except Exception as e:
                self.logger.error(f"Error deleting {item_type}: {str(e)}")
                error_msg = f"Failed to delete {item_type}: {str(e)}"
                if "system category" in str(e).lower():
                    error_msg += "\n\nSystem categories cannot be deleted as they are required for proper operation."
                QMessageBox.critical(self, "Error", error_msg)
    
    def edit_selected_category(self, item, column):
        """Handle double-click to edit category"""
        # Focus on name field for quick editing
        self.name_edit.setFocus()
        self.name_edit.selectAll()
    
    def update_statistics(self, item_data):
        """Update usage statistics for the selected item"""
        try:
            # This would need to be implemented based on your transaction data
            # For now, show basic info
            data = item_data["data"]
            stats_text = f"Name: {data.name}\n"
            stats_text += f"Type: {item_data['type'].title()}\n"
            stats_text += f"Status: {'Active' if data.is_active else 'Inactive'}\n"
            stats_text += f"Created: {data.created_at.strftime('%Y-%m-%d') if data.created_at else 'Unknown'}\n"
            
            if item_data["type"] == "category":
                subcategories = self.category_manager.get_subcategories(data.name)
                stats_text += f"Subcategories: {len(subcategories)}\n"
            
            self.stats_label.setText(stats_text)
            
        except Exception as e:
            self.logger.error(f"Error updating statistics: {str(e)}")
            self.stats_label.setText("Error loading statistics")
    
    def show_help(self):
        """Show help dialog"""
        help_text = """
Category Editor Help:

• Double-click any category or subcategory to edit it
• Use 'Add Category' to create new main categories
• Select a category and use 'Add Subcategory' to create subcategories
• Edit names, descriptions, and colors in the right panel
• Click 'Save Changes' to apply modifications
• Use 'Delete' to remove categories (be careful - this affects existing labels)

Tips:
• Categories organize your transaction types (e.g., "Food", "Transport")
• Subcategories provide more detail (e.g., "Restaurants", "Groceries" under "Food")
• Colors help visually distinguish categories in reports
• Deleting categories will affect existing transaction labels
        """
        QMessageBox.information(self, "Help", help_text.strip())
