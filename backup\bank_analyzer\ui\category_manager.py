"""
Category management interface
Allows users to add and manage expense categories and subcategories
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QFormLayout, QDialog,
    QDialogButtonBox, QLineEdit, QComboBox, QListWidget, QListWidgetItem,
    QPushButton, QLabel, QMessageBox, QGroupBox, QSplitter,
    QTreeWidget, QTreeWidgetItem, QHeaderView
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont

from typing import Dict, List, Optional
from ..core.logger import get_logger


class AddCategoryDialog(QDialog):
    """Dialog for adding new categories and subcategories"""
    
    category_added = Signal(str, str)  # category, subcategory
    
    def __init__(self, existing_categories: Dict[str, List[str]], parent=None):
        super().__init__(parent)
        self.existing_categories = existing_categories
        self.logger = get_logger(__name__)
        
        self.setWindowTitle("Add New Category")
        self.setModal(True)
        self.resize(400, 300)
        
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the dialog UI"""
        layout = QVBoxLayout(self)
        
        # Form layout
        form_layout = QFormLayout()
        
        # Category selection/input
        self.category_combo = QComboBox()
        self.category_combo.setEditable(True)
        self.category_combo.addItem("")  # Empty option for new category
        self.category_combo.addItems(sorted(self.existing_categories.keys()))
        self.category_combo.currentTextChanged.connect(self.on_category_changed)
        form_layout.addRow("Category:", self.category_combo)
        
        # Subcategory input
        self.subcategory_edit = QLineEdit()
        self.subcategory_edit.setPlaceholderText("Enter subcategory name")
        form_layout.addRow("Subcategory:", self.subcategory_edit)
        
        layout.addLayout(form_layout)
        
        # Existing subcategories display
        self.existing_group = QGroupBox("Existing Subcategories")
        existing_layout = QVBoxLayout(self.existing_group)
        
        self.existing_list = QListWidget()
        self.existing_list.setMaximumHeight(150)
        existing_layout.addWidget(self.existing_list)
        
        layout.addWidget(self.existing_group)
        
        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept_category)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
    
    def on_category_changed(self, category: str):
        """Handle category selection change"""
        self.existing_list.clear()
        
        if category in self.existing_categories:
            for subcategory in sorted(self.existing_categories[category]):
                self.existing_list.addItem(subcategory)
    
    def accept_category(self):
        """Accept and validate the new category"""
        category = self.category_combo.currentText().strip()
        subcategory = self.subcategory_edit.text().strip()
        
        if not category:
            QMessageBox.warning(self, "Invalid Input", "Please enter a category name.")
            return
        
        if not subcategory:
            QMessageBox.warning(self, "Invalid Input", "Please enter a subcategory name.")
            return
        
        # Check if combination already exists
        if category in self.existing_categories:
            if subcategory in self.existing_categories[category]:
                QMessageBox.warning(self, "Duplicate Category", 
                                  f"The combination '{category}' / '{subcategory}' already exists.")
                return
        
        self.category_added.emit(category, subcategory)
        self.accept()


class CategoryManagerWidget(QWidget):
    """
    Widget for managing expense categories and subcategories
    """
    
    categories_updated = Signal(dict)  # Emitted when categories are updated
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger(__name__)
        
        self.categories = {}
        
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the category manager UI"""
        layout = QVBoxLayout(self)
        
        # Header
        header_layout = QHBoxLayout()
        
        title_label = QLabel("Category Management")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # Add category button
        self.add_category_btn = QPushButton("Add Category")
        self.add_category_btn.clicked.connect(self.add_category)
        header_layout.addWidget(self.add_category_btn)
        
        layout.addLayout(header_layout)
        
        # Main content area
        splitter = QSplitter(Qt.Horizontal)
        
        # Left side - category tree
        self.create_category_tree(splitter)
        
        # Right side - category details
        self.create_category_details(splitter)
        
        splitter.setSizes([400, 300])
        layout.addWidget(splitter)
        
        # Bottom buttons
        self.create_action_buttons(layout)
    
    def create_category_tree(self, parent):
        """Create category tree widget"""
        tree_widget = QWidget()
        tree_layout = QVBoxLayout(tree_widget)
        
        # Tree widget
        self.category_tree = QTreeWidget()
        self.category_tree.setHeaderLabels(["Category", "Subcategories"])
        self.category_tree.itemSelectionChanged.connect(self.on_tree_selection_changed)
        
        # Configure tree
        header = self.category_tree.header()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        
        tree_layout.addWidget(self.category_tree)
        
        # Tree controls
        tree_controls = QHBoxLayout()
        
        self.expand_all_btn = QPushButton("Expand All")
        self.expand_all_btn.clicked.connect(self.category_tree.expandAll)
        tree_controls.addWidget(self.expand_all_btn)
        
        self.collapse_all_btn = QPushButton("Collapse All")
        self.collapse_all_btn.clicked.connect(self.category_tree.collapseAll)
        tree_controls.addWidget(self.collapse_all_btn)
        
        tree_controls.addStretch()
        tree_layout.addLayout(tree_controls)
        
        parent.addWidget(tree_widget)
    
    def create_category_details(self, parent):
        """Create category details panel"""
        details_widget = QWidget()
        details_layout = QVBoxLayout(details_widget)
        
        # Details group
        details_group = QGroupBox("Category Details")
        details_form = QFormLayout(details_group)
        
        self.selected_category_label = QLabel("None")
        self.selected_subcategory_label = QLabel("None")
        self.subcategory_count_label = QLabel("0")
        
        details_form.addRow("Selected Category:", self.selected_category_label)
        details_form.addRow("Selected Subcategory:", self.selected_subcategory_label)
        details_form.addRow("Subcategory Count:", self.subcategory_count_label)
        
        details_layout.addWidget(details_group)
        
        # Actions group
        actions_group = QGroupBox("Actions")
        actions_layout = QVBoxLayout(actions_group)
        
        self.add_subcategory_btn = QPushButton("Add Subcategory to Selected Category")
        self.add_subcategory_btn.clicked.connect(self.add_subcategory_to_selected)
        self.add_subcategory_btn.setEnabled(False)
        actions_layout.addWidget(self.add_subcategory_btn)
        
        self.edit_category_btn = QPushButton("Edit Selected Category")
        self.edit_category_btn.clicked.connect(self.edit_selected_category)
        self.edit_category_btn.setEnabled(False)
        actions_layout.addWidget(self.edit_category_btn)
        
        self.delete_category_btn = QPushButton("Delete Selected")
        self.delete_category_btn.clicked.connect(self.delete_selected)
        self.delete_category_btn.setEnabled(False)
        self.delete_category_btn.setStyleSheet("QPushButton { background-color: #f44336; color: white; }")
        actions_layout.addWidget(self.delete_category_btn)
        
        details_layout.addWidget(actions_group)
        details_layout.addStretch()
        
        parent.addWidget(details_widget)
    
    def create_action_buttons(self, layout):
        """Create action buttons"""
        button_layout = QHBoxLayout()
        
        self.refresh_btn = QPushButton("Refresh")
        self.refresh_btn.clicked.connect(self.refresh_categories)
        button_layout.addWidget(self.refresh_btn)
        
        button_layout.addStretch()
        
        self.save_btn = QPushButton("Save Changes")
        self.save_btn.clicked.connect(self.save_categories)
        self.save_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        button_layout.addWidget(self.save_btn)
        
        layout.addLayout(button_layout)
    
    def set_categories(self, categories: Dict[str, List[str]]):
        """Set categories to display"""
        self.categories = categories.copy()
        self.populate_tree()
    
    def populate_tree(self):
        """Populate the category tree"""
        self.category_tree.clear()
        
        for category, subcategories in sorted(self.categories.items()):
            # Create category item
            category_item = QTreeWidgetItem([category, f"{len(subcategories)} subcategories"])
            
            # Add subcategory items
            for subcategory in sorted(subcategories):
                subcategory_item = QTreeWidgetItem([subcategory, ""])
                category_item.addChild(subcategory_item)
            
            self.category_tree.addTopLevelItem(category_item)
        
        # Expand all by default
        self.category_tree.expandAll()
    
    def on_tree_selection_changed(self):
        """Handle tree selection change"""
        current_item = self.category_tree.currentItem()
        
        if not current_item:
            self.selected_category_label.setText("None")
            self.selected_subcategory_label.setText("None")
            self.subcategory_count_label.setText("0")
            self.add_subcategory_btn.setEnabled(False)
            self.edit_category_btn.setEnabled(False)
            self.delete_category_btn.setEnabled(False)
            return
        
        parent_item = current_item.parent()
        
        if parent_item is None:
            # Category selected
            category = current_item.text(0)
            self.selected_category_label.setText(category)
            self.selected_subcategory_label.setText("None")
            self.subcategory_count_label.setText(str(len(self.categories.get(category, []))))
            self.add_subcategory_btn.setEnabled(True)
            self.edit_category_btn.setEnabled(True)
            self.delete_category_btn.setEnabled(True)
        else:
            # Subcategory selected
            category = parent_item.text(0)
            subcategory = current_item.text(0)
            self.selected_category_label.setText(category)
            self.selected_subcategory_label.setText(subcategory)
            self.subcategory_count_label.setText(str(len(self.categories.get(category, []))))
            self.add_subcategory_btn.setEnabled(False)
            self.edit_category_btn.setEnabled(True)
            self.delete_category_btn.setEnabled(True)
    
    def add_category(self):
        """Add a new category"""
        dialog = AddCategoryDialog(self.categories, self)
        dialog.category_added.connect(self.on_category_added)
        dialog.exec()
    
    def add_subcategory_to_selected(self):
        """Add subcategory to selected category"""
        current_item = self.category_tree.currentItem()
        if not current_item or current_item.parent() is not None:
            return
        
        category = current_item.text(0)
        
        # Create a simplified dialog for just subcategory
        subcategory, ok = self.get_subcategory_input(category)
        if ok and subcategory:
            self.on_category_added(category, subcategory)
    
    def get_subcategory_input(self, category: str) -> tuple:
        """Get subcategory input from user"""
        from PySide6.QtWidgets import QInputDialog
        
        subcategory, ok = QInputDialog.getText(
            self, "Add Subcategory",
            f"Enter subcategory name for '{category}':",
            text=""
        )
        
        return subcategory.strip(), ok
    
    def on_category_added(self, category: str, subcategory: str):
        """Handle new category addition"""
        if category not in self.categories:
            self.categories[category] = []
        
        if subcategory not in self.categories[category]:
            self.categories[category].append(subcategory)
            self.populate_tree()
            self.categories_updated.emit(self.categories)
            
            self.logger.info(f"Added category: {category}/{subcategory}")
            QMessageBox.information(self, "Category Added", 
                                  f"Successfully added '{category}' / '{subcategory}'")
    
    def edit_selected_category(self):
        """Edit the selected category or subcategory"""
        # Implementation for editing would go here
        QMessageBox.information(self, "Edit Category", "Edit functionality would be implemented here.")
    
    def delete_selected(self):
        """Delete the selected category or subcategory"""
        current_item = self.category_tree.currentItem()
        if not current_item:
            return
        
        parent_item = current_item.parent()
        
        if parent_item is None:
            # Deleting category
            category = current_item.text(0)
            reply = QMessageBox.question(
                self, "Confirm Delete",
                f"Are you sure you want to delete the category '{category}' and all its subcategories?",
                QMessageBox.Yes | QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                if category in self.categories:
                    del self.categories[category]
                    self.populate_tree()
                    self.categories_updated.emit(self.categories)
                    self.logger.info(f"Deleted category: {category}")
        else:
            # Deleting subcategory
            category = parent_item.text(0)
            subcategory = current_item.text(0)
            
            reply = QMessageBox.question(
                self, "Confirm Delete",
                f"Are you sure you want to delete the subcategory '{subcategory}' from '{category}'?",
                QMessageBox.Yes | QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                if category in self.categories and subcategory in self.categories[category]:
                    self.categories[category].remove(subcategory)
                    self.populate_tree()
                    self.categories_updated.emit(self.categories)
                    self.logger.info(f"Deleted subcategory: {category}/{subcategory}")
    
    def refresh_categories(self):
        """Refresh categories display"""
        self.populate_tree()
    
    def save_categories(self):
        """Save categories (emit signal for parent to handle)"""
        self.categories_updated.emit(self.categories)
        QMessageBox.information(self, "Categories Saved", "Categories have been updated.")
    
    def get_categories(self) -> Dict[str, List[str]]:
        """Get current categories"""
        return self.categories.copy()
