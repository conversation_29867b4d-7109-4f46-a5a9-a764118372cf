"""
Enhanced Category Selector Widget
Provides searchable, filterable category selection with quick-add capabilities
"""

from PySide6.QtWidgets import (
    QComboBox, QLineEdit, QCompleter, QVBoxLayout, QHBoxLayout, 
    QWidget, QPushButton, QLabel, QFrame, QListWidget, QListWidgetItem,
    QDialog, QDialogButtonBox, QTextEdit, QColorDialog, QMessageBox
)
from PySide6.QtCore import Qt, Signal, QStringListModel, QTimer
from PySide6.QtGui import QColor, QPalette
from typing import List, Dict, Optional, Any
import logging

from ..ml.category_manager import CategoryManager, Category
from ..core.logger import get_logger


class SearchableComboBox(QComboBox):
    """
    Enhanced combo box with search functionality
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        # Make it editable for search functionality but with better behavior
        self.setEditable(True)
        self.setInsertPolicy(QComboBox.NoInsert)

        # Setup completer for search
        self.completer = QCompleter(self)
        self.completer.setCompletionMode(QCompleter.PopupCompletion)
        self.completer.setCaseSensitivity(Qt.CaseInsensitive)
        self.setCompleter(self.completer)

        # Connect signals
        self.activated.connect(self.on_item_selected)

        # Store original items for completer
        self.all_items = []

        # Ensure dropdown shows on click
        self.lineEdit().mousePressEvent = self.on_line_edit_click
        
    def addItems(self, items: List[str]):
        """Add items and store them for filtering"""
        super().addItems(items)
        self.all_items = items.copy()
        self.update_completer()
        
    def addItem(self, item: str):
        """Add single item"""
        super().addItem(item)
        self.all_items.append(item)
        self.update_completer()
        
    def clear(self):
        """Clear all items"""
        super().clear()
        self.all_items.clear()
        self.update_completer()
        
    def update_completer(self):
        """Update completer with current items"""
        model = QStringListModel(self.all_items)
        self.completer.setModel(model)
        

            
    def on_item_selected(self, index: int):
        """Handle item selection"""
        if index >= 0:
            text = self.itemText(index)
            self.lineEdit().setText(text)

    def on_line_edit_click(self, event):
        """Handle click on line edit to show dropdown"""
        # Show the popup when clicking on the line edit
        self.showPopup()
        # Call the original mouse press event
        QLineEdit.mousePressEvent(self.lineEdit(), event)


class QuickCategoryDialog(QDialog):
    """
    Quick dialog for creating categories during labeling
    """
    
    category_created = Signal(str, str, str)  # name, parent_name, category_id
    
    def __init__(self, category_manager: CategoryManager, parent_name: str = None, parent=None):
        super().__init__(parent)
        self.category_manager = category_manager
        self.parent_name = parent_name
        self.logger = get_logger(__name__)
        
        self.setWindowTitle("Quick Add Category" if not parent_name else f"Quick Add Subcategory to '{parent_name}'")
        self.setModal(True)
        self.resize(400, 300)
        
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the dialog UI"""
        layout = QVBoxLayout(self)
        
        # Title
        title = "Add New Category" if not self.parent_name else f"Add Subcategory to '{self.parent_name}'"
        title_label = QLabel(title)
        title_label.setStyleSheet("font-weight: bold; font-size: 14px; margin-bottom: 10px;")
        layout.addWidget(title_label)
        
        # Name input
        layout.addWidget(QLabel("Name:"))
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("Enter category name...")
        layout.addWidget(self.name_edit)
        
        # Description input
        layout.addWidget(QLabel("Description (optional):"))
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(80)
        self.description_edit.setPlaceholderText("Enter description...")
        layout.addWidget(self.description_edit)
        
        # Color selection (only for main categories)
        if not self.parent_name:
            color_layout = QHBoxLayout()
            color_layout.addWidget(QLabel("Color:"))
            
            self.color_display = QLabel("      ")
            self.color_display.setStyleSheet("border: 1px solid #ccc; background-color: #007ACC;")
            self.selected_color = "#007ACC"
            
            self.color_button = QPushButton("Choose Color")
            self.color_button.clicked.connect(self.choose_color)
            
            color_layout.addWidget(self.color_display)
            color_layout.addWidget(self.color_button)
            color_layout.addStretch()
            
            layout.addLayout(color_layout)

            # Category type selection (only for main categories)
            type_layout = QHBoxLayout()
            type_layout.addWidget(QLabel("Category Type:"))

            self.type_combo = QComboBox()
            self.type_combo.addItems(["Expense", "Income", "Both"])
            self.type_combo.setCurrentText("Expense")  # Default to expense
            self.type_combo.setToolTip("Select whether this category is for expenses, income, or both")

            type_layout.addWidget(self.type_combo)
            type_layout.addStretch()

            layout.addLayout(type_layout)

        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.create_category)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
        
        # Focus on name input
        self.name_edit.setFocus()
        
    def choose_color(self):
        """Open color chooser"""
        color = QColorDialog.getColor(QColor(self.selected_color), self, "Choose Category Color")
        if color.isValid():
            self.selected_color = color.name()
            self.color_display.setStyleSheet(f"border: 1px solid #ccc; background-color: {self.selected_color};")
            
    def create_category(self):
        """Create the category with validation"""
        name = self.name_edit.text().strip()

        # Validate name
        validation_result = self.validate_category_name(name)
        if not validation_result["valid"]:
            QMessageBox.warning(self, "Validation Error", validation_result["message"])
            return

        description = self.description_edit.toPlainText().strip()

        # Check for duplicates
        if self.check_duplicate_category(name):
            reply = QMessageBox.question(
                self,
                "Duplicate Category",
                f"A category named '{name}' already exists.\n\n"
                f"Would you like to:\n"
                f"• Use the existing category (Yes)\n"
                f"• Choose a different name (No)",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # Use existing category - find the existing category ID
                existing_id = None
                if self.parent_name:
                    subcategories = self.category_manager.get_subcategories(self.parent_name)
                    for sub in subcategories:
                        if sub.name.lower() == name.lower():
                            existing_id = sub.id
                            break
                else:
                    main_categories = self.category_manager.get_main_categories()
                    for cat in main_categories:
                        if cat.name.lower() == name.lower():
                            existing_id = cat.id
                            break

                self.category_created.emit(name, self.parent_name or "", existing_id or "")
                self.accept()
                return
            else:
                # Let user choose different name
                self.name_edit.setFocus()
                self.name_edit.selectAll()
                return

        try:
            kwargs = {
                "name": name,
                "description": description
            }

            if self.parent_name:
                kwargs["parent_name"] = self.parent_name
            else:
                kwargs["color"] = getattr(self, 'selected_color', '#007ACC')
                # Add category type for main categories
                if hasattr(self, 'type_combo'):
                    type_text = self.type_combo.currentText().lower()
                    kwargs["category_type"] = type_text

            category_id = self.category_manager.create_category(**kwargs)

            if category_id:
                # Force refresh of the category manager to ensure consistency
                self.category_manager.refresh_categories()

                self.category_created.emit(name, self.parent_name or "", category_id)
                self.accept()

                self.logger.info(f"Successfully created category: {name} (ID: {category_id})")
            else:
                QMessageBox.warning(self, "Warning", "Failed to create category. Please try again.")

        except Exception as e:
            self.logger.error(f"Error creating category: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to create category: {str(e)}")

    def validate_category_name(self, name: str) -> Dict[str, Any]:
        """Validate category name"""
        if not name:
            return {"valid": False, "message": "Category name is required"}

        if len(name) < 2:
            return {"valid": False, "message": "Category name must be at least 2 characters long"}

        if len(name) > 50:
            return {"valid": False, "message": "Category name must be less than 50 characters"}

        # Check for invalid characters
        invalid_chars = ['/', '\\', ':', '*', '?', '"', '<', '>', '|']
        for char in invalid_chars:
            if char in name:
                return {"valid": False, "message": f"Category name cannot contain '{char}'"}

        # Check for reserved names
        reserved_names = ['select category', 'none', 'null', 'undefined']
        if name.lower() in reserved_names:
            return {"valid": False, "message": f"'{name}' is a reserved name"}

        return {"valid": True, "message": ""}

    def check_duplicate_category(self, name: str) -> bool:
        """Check if category already exists"""
        try:
            if self.parent_name:
                # Check for duplicate subcategory
                subcategories = self.category_manager.get_subcategories(self.parent_name)
                return any(sub.name.lower() == name.lower() for sub in subcategories)
            else:
                # Check for duplicate main category
                main_categories = self.category_manager.get_main_categories()
                return any(cat.name.lower() == name.lower() for cat in main_categories)
        except Exception:
            return False


class CategoryEditDialog(QDialog):
    """
    Dialog for editing existing categories and subcategories
    """

    category_updated = Signal(str, str, str)  # category_id, old_name, new_name

    def __init__(self, category_manager: CategoryManager, category, parent=None):
        super().__init__(parent)
        self.category_manager = category_manager
        self.category = category
        self.logger = get_logger(__name__)

        self.setWindowTitle(f"Edit {'Category' if not hasattr(category, 'parent_id') or not category.parent_id else 'Subcategory'}")
        self.setModal(True)
        self.resize(400, 250)

        self.setup_ui()

    def setup_ui(self):
        """Setup the dialog UI"""
        layout = QVBoxLayout(self)

        # Title
        is_main_category = not hasattr(self.category, 'parent_id') or not self.category.parent_id
        title = f"Edit {'Category' if is_main_category else 'Subcategory'}: {self.category.name}"
        title_label = QLabel(title)
        title_label.setStyleSheet("font-weight: bold; font-size: 14px; margin-bottom: 10px;")
        layout.addWidget(title_label)

        # Name input
        layout.addWidget(QLabel("Name:"))
        self.name_edit = QLineEdit()
        self.name_edit.setText(self.category.name)
        self.name_edit.setPlaceholderText("Enter category name...")
        layout.addWidget(self.name_edit)

        # Description input
        layout.addWidget(QLabel("Description:"))
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(80)
        self.description_edit.setPlainText(getattr(self.category, 'description', '') or "")
        self.description_edit.setPlaceholderText("Enter description...")
        layout.addWidget(self.description_edit)

        # Color selection (only for main categories)
        if is_main_category:
            color_layout = QHBoxLayout()
            color_layout.addWidget(QLabel("Color:"))

            self.color_display = QLabel("      ")
            current_color = getattr(self.category, 'color', '#007ACC') or "#007ACC"
            self.color_display.setStyleSheet(f"border: 1px solid #ccc; background-color: {current_color};")
            self.selected_color = current_color

            self.color_button = QPushButton("Choose Color")
            self.color_button.clicked.connect(self.choose_color)

            color_layout.addWidget(self.color_display)
            color_layout.addWidget(self.color_button)
            color_layout.addStretch()

            layout.addLayout(color_layout)

        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.update_category)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

        # Focus on name input
        self.name_edit.setFocus()
        self.name_edit.selectAll()

    def choose_color(self):
        """Open color chooser"""
        color = QColorDialog.getColor(QColor(self.selected_color), self, "Choose Category Color")
        if color.isValid():
            self.selected_color = color.name()
            self.color_display.setStyleSheet(f"border: 1px solid #ccc; background-color: {self.selected_color};")

    def update_category(self):
        """Update the category"""
        new_name = self.name_edit.text().strip()
        if not new_name:
            QMessageBox.warning(self, "Warning", "Category name is required")
            return

        # Check for duplicates (excluding current category)
        if new_name != self.category.name:
            if self.check_duplicate_name(new_name):
                QMessageBox.warning(self, "Warning", f"A category named '{new_name}' already exists")
                return

        description = self.description_edit.toPlainText().strip()

        try:
            # Prepare update data
            update_data = {
                "name": new_name,
                "description": description
            }

            # Add color for main categories
            is_main_category = not hasattr(self.category, 'parent_id') or not self.category.parent_id
            if is_main_category:
                update_data["color"] = getattr(self, 'selected_color', getattr(self.category, 'color', '#007ACC'))

            # Update the category
            success = self.category_manager.update_category(self.category.id, **update_data)

            if success:
                old_name = self.category.name
                self.category_updated.emit(self.category.id, old_name, new_name)
                self.accept()
            else:
                QMessageBox.warning(self, "Warning", "Failed to update category")

        except Exception as e:
            self.logger.error(f"Error updating category: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to update category: {str(e)}")

    def check_duplicate_name(self, name: str) -> bool:
        """Check if the new name would create a duplicate"""
        try:
            has_parent = hasattr(self.category, 'parent_id') and self.category.parent_id
            if has_parent:
                # Check for duplicate subcategory - need to find parent category name
                # This is a simplified check - in a real implementation you'd need proper parent lookup
                return False  # For now, allow subcategory renames
            else:
                # Check for duplicate main category
                main_categories = self.category_manager.get_main_categories()
                return any(cat.name.lower() == name.lower() and cat.id != self.category.id for cat in main_categories)
        except Exception:
            return False


class EnhancedCategorySelector(QWidget):
    """
    Enhanced category selector with search, filtering, and quick-add capabilities
    """
    
    category_changed = Signal(str)  # category name
    subcategory_changed = Signal(str)  # subcategory name
    categories_updated = Signal()  # emitted when categories are modified
    
    def __init__(self, category_manager: CategoryManager, parent=None):
        super().__init__(parent)
        self.category_manager = category_manager
        self.logger = get_logger(__name__)

        # Transaction type filtering
        self._transaction_type = None  # "debit", "credit", or None for all
        self._category_type_filter = None  # "expense", "income", or None for all

        # Caching for performance
        self._categories_cache = None
        self._subcategories_cache = {}
        self._cache_timestamp = 0
        self._cache_ttl = 300  # 5 minutes cache TTL

        # Debouncing for search
        self._search_debounce_timer = QTimer()
        self._search_debounce_timer.setSingleShot(True)
        self._search_debounce_timer.timeout.connect(self._perform_search)

        self.setup_ui()
        self.load_categories()

    def set_transaction_type(self, transaction_type: Optional[str]):
        """
        Set the transaction type to filter categories

        Args:
            transaction_type: "debit", "credit", or None for all transactions
        """
        self._transaction_type = transaction_type

        # Determine category type filter based on transaction type
        if transaction_type == "debit":
            self._category_type_filter = "expense"
        elif transaction_type == "credit":
            self._category_type_filter = "income"
        else:
            self._category_type_filter = None

        # Reload categories with new filter
        self.load_categories()

        self.logger.debug(f"Transaction type set to: {transaction_type}, category filter: {self._category_type_filter}")

    def get_transaction_type(self) -> Optional[str]:
        """Get the current transaction type filter"""
        return self._transaction_type

    def get_category_type_filter(self) -> Optional[str]:
        """Get the current category type filter"""
        return self._category_type_filter
        
    def setup_ui(self):
        """Setup the widget UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # Category selection
        cat_frame = QFrame()
        cat_layout = QHBoxLayout(cat_frame)
        cat_layout.setContentsMargins(0, 0, 0, 0)
        
        cat_layout.addWidget(QLabel("Category:"))
        
        self.category_combo = QComboBox()
        self.category_combo.currentTextChanged.connect(self.on_category_changed)
        cat_layout.addWidget(self.category_combo)
        
        self.add_category_btn = QPushButton("+")
        self.add_category_btn.setMaximumWidth(30)
        self.add_category_btn.setToolTip("Add new category")
        self.add_category_btn.clicked.connect(self.quick_add_category)
        self.add_category_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        cat_layout.addWidget(self.add_category_btn)

        # Edit category button
        self.edit_category_btn = QPushButton("✏")
        self.edit_category_btn.setMaximumWidth(30)
        self.edit_category_btn.setToolTip("Edit selected category")
        self.edit_category_btn.clicked.connect(self.edit_selected_category)
        self.edit_category_btn.setEnabled(False)
        self.edit_category_btn.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        cat_layout.addWidget(self.edit_category_btn)
        
        layout.addWidget(cat_frame)
        
        # Subcategory selection
        subcat_frame = QFrame()
        subcat_layout = QHBoxLayout(subcat_frame)
        subcat_layout.setContentsMargins(0, 0, 0, 0)
        
        subcat_layout.addWidget(QLabel("Subcategory:"))
        
        self.subcategory_combo = QComboBox()
        self.subcategory_combo.currentTextChanged.connect(self.on_subcategory_changed)
        subcat_layout.addWidget(self.subcategory_combo)
        
        self.add_subcategory_btn = QPushButton("+")
        self.add_subcategory_btn.setMaximumWidth(30)
        self.add_subcategory_btn.setToolTip("Add new subcategory")
        self.add_subcategory_btn.clicked.connect(self.quick_add_subcategory)
        self.add_subcategory_btn.setEnabled(False)
        self.add_subcategory_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        subcat_layout.addWidget(self.add_subcategory_btn)

        # Edit subcategory button
        self.edit_subcategory_btn = QPushButton("✏")
        self.edit_subcategory_btn.setMaximumWidth(30)
        self.edit_subcategory_btn.setToolTip("Edit selected subcategory")
        self.edit_subcategory_btn.clicked.connect(self.edit_selected_subcategory)
        self.edit_subcategory_btn.setEnabled(False)
        self.edit_subcategory_btn.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        subcat_layout.addWidget(self.edit_subcategory_btn)
        
        layout.addWidget(subcat_frame)
        
    def load_categories(self):
        """Load categories into combo boxes with transaction type filtering and caching"""
        import time
        current_time = time.time()

        # Check if cache is still valid
        cache_key = f"categories_{self._category_type_filter}"
        if (self._categories_cache and
            cache_key in self._categories_cache and
            current_time - self._cache_timestamp < self._cache_ttl):

            # Use cached data
            main_categories = self._categories_cache[cache_key]
            self.logger.debug(f"Using cached categories for type: {self._category_type_filter}")
        else:
            # Load fresh data
            main_categories = self.category_manager.get_main_categories(self._category_type_filter)

            # Update cache
            if not self._categories_cache:
                self._categories_cache = {}
            self._categories_cache[cache_key] = main_categories
            self._cache_timestamp = current_time

            self.logger.debug(f"Loaded fresh {len(main_categories)} categories for type: {self._category_type_filter}")

        # Populate combo box
        self.category_combo.clear()
        self.category_combo.addItem("Select Category...")
        for category in main_categories:
            self.category_combo.addItem(category.name)
            
    def on_category_changed(self, category_name: str):
        """Handle category selection change with caching"""
        self.subcategory_combo.clear()
        self.subcategory_combo.addItem("Select Sub-category...")

        # Enable/disable buttons based on selection
        category_selected = bool(category_name and category_name != "Select Category...")
        self.add_subcategory_btn.setEnabled(category_selected)
        self.edit_category_btn.setEnabled(category_selected)
        self.edit_subcategory_btn.setEnabled(False)  # Will be enabled when subcategory is selected

        if category_selected:
            # Check cache first
            if category_name in self._subcategories_cache:
                subcategories = self._subcategories_cache[category_name]
                self.logger.debug(f"Using cached subcategories for: {category_name}")
            else:
                # Load fresh data and cache it
                subcategories = self.category_manager.get_subcategories(category_name)
                self._subcategories_cache[category_name] = subcategories
                self.logger.debug(f"Loaded fresh {len(subcategories)} subcategories for: {category_name}")

            for subcategory in subcategories:
                self.subcategory_combo.addItem(subcategory.name)

        self.category_changed.emit(category_name)

    def on_subcategory_changed(self, subcategory_name: str):
        """Handle subcategory selection change"""
        # Enable/disable edit subcategory button
        subcategory_selected = bool(subcategory_name and subcategory_name != "Select Sub-category...")
        self.edit_subcategory_btn.setEnabled(subcategory_selected)

        self.subcategory_changed.emit(subcategory_name)
        
    def quick_add_category(self):
        """Quick add a new category with validation"""
        # Check if we have too many categories
        main_categories = self.category_manager.get_main_categories()
        if len(main_categories) >= 50:  # Reasonable limit
            QMessageBox.warning(
                self,
                "Category Limit",
                "You have reached the maximum number of main categories (50).\n"
                "Consider using subcategories or deleting unused categories."
            )
            return

        dialog = QuickCategoryDialog(self.category_manager, parent=self)
        dialog.category_created.connect(self.on_category_created)
        dialog.exec()

    def quick_add_subcategory(self):
        """Quick add a new subcategory with validation"""
        current_category = self.category_combo.currentText()
        if not current_category or current_category == "Select Category...":
            QMessageBox.warning(self, "Warning", "Please select a category first")
            return

        # Check if we have too many subcategories
        subcategories = self.category_manager.get_subcategories(current_category)
        if len(subcategories) >= 20:  # Reasonable limit per category
            QMessageBox.warning(
                self,
                "Subcategory Limit",
                f"Category '{current_category}' has reached the maximum number of subcategories (20).\n"
                f"Consider reorganizing or deleting unused subcategories."
            )
            return

        dialog = QuickCategoryDialog(self.category_manager, current_category, self)
        dialog.category_created.connect(self.on_subcategory_created)
        dialog.exec()
        
    def on_category_created(self, name: str, parent_name: str, category_id: str = ""):
        """Handle new category creation"""
        # Invalidate cache
        self._invalidate_cache()

        # Force refresh of category manager to ensure consistency
        self.category_manager.refresh_categories()

        self.load_categories()

        # Select the new category
        index = self.category_combo.findText(name)
        if index >= 0:
            self.category_combo.setCurrentIndex(index)

        self.categories_updated.emit()

    def on_subcategory_created(self, name: str, parent_name: str, category_id: str = ""):
        """Handle new subcategory creation"""
        # Force refresh of category manager to ensure consistency
        self.category_manager.refresh_categories()

        # Reload subcategories for current category
        self.on_category_changed(parent_name)

        # Select the new subcategory
        index = self.subcategory_combo.findText(name)
        if index >= 0:
            self.subcategory_combo.setCurrentIndex(index)

        self.categories_updated.emit()
        
    def get_selected_category(self) -> str:
        """Get the selected category"""
        category = self.category_combo.currentText()
        return category if category != "Select Category..." else ""
        
    def get_selected_subcategory(self) -> str:
        """Get the selected subcategory"""
        subcategory = self.subcategory_combo.currentText()
        return subcategory if subcategory != "Select Sub-category..." else ""
        
    def set_category(self, category_name: str):
        """Set the selected category"""
        index = self.category_combo.findText(category_name)
        if index >= 0:
            self.category_combo.setCurrentIndex(index)
            
    def set_subcategory(self, subcategory_name: str):
        """Set the selected subcategory"""
        index = self.subcategory_combo.findText(subcategory_name)
        if index >= 0:
            self.subcategory_combo.setCurrentIndex(index)
            
    def refresh_categories(self):
        """Refresh categories from the manager"""
        self.logger.info("Refreshing categories in EnhancedCategorySelector")

        current_category = self.get_selected_category()
        current_subcategory = self.get_selected_subcategory()

        # Force refresh of category manager first
        if hasattr(self.category_manager, 'force_refresh_from_files'):
            self.category_manager.force_refresh_from_files()
        else:
            self.category_manager.refresh_categories()

        # Small delay to ensure file operations are complete
        from PySide6.QtCore import QTimer
        QTimer.singleShot(50, lambda: self._complete_refresh(current_category, current_subcategory))

    def _complete_refresh(self, current_category, current_subcategory):
        """Complete the refresh process after a small delay"""
        try:
            # Reload categories
            self.load_categories()

            # Try to restore selections
            if current_category:
                self.set_category(current_category)
                if current_subcategory:
                    self.set_subcategory(current_subcategory)

            self.logger.info(f"Categories refreshed. Current filter: {self._category_type_filter}")

            # Emit signal to notify other components
            self.categories_updated.emit()

        except Exception as e:
            self.logger.error(f"Error completing category refresh: {str(e)}")

    def edit_selected_category(self):
        """Edit the currently selected category"""
        current_category = self.get_selected_category()
        if not current_category:
            QMessageBox.warning(self, "Warning", "Please select a category first")
            return

        # Find the category object
        category_obj = None
        for cat in self.category_manager.get_main_categories():
            if cat.name == current_category:
                category_obj = cat
                break

        if not category_obj:
            QMessageBox.warning(self, "Warning", "Category not found")
            return

        # Create edit dialog
        dialog = CategoryEditDialog(self.category_manager, category_obj, self)
        dialog.category_updated.connect(self.on_category_updated)
        dialog.exec()

    def edit_selected_subcategory(self):
        """Edit the currently selected subcategory"""
        current_category = self.get_selected_category()
        current_subcategory = self.get_selected_subcategory()

        if not current_category or not current_subcategory:
            QMessageBox.warning(self, "Warning", "Please select both category and subcategory first")
            return

        # Find the subcategory object
        subcategory_obj = None
        subcategories = self.category_manager.get_subcategories(current_category)
        for subcat in subcategories:
            if subcat.name == current_subcategory:
                subcategory_obj = subcat
                break

        if not subcategory_obj:
            QMessageBox.warning(self, "Warning", "Subcategory not found")
            return

        # Create edit dialog
        dialog = CategoryEditDialog(self.category_manager, subcategory_obj, self)
        dialog.category_updated.connect(self.on_category_updated)
        dialog.exec()

    def on_category_updated(self, category_id: str, old_name: str, new_name: str):
        """Handle category update with optimized refresh"""
        # Check if we need to refresh (avoid redundant refreshes)
        if hasattr(self, '_last_refresh_time'):
            import time
            if time.time() - self._last_refresh_time < 0.5:  # Debounce refreshes
                self.logger.debug("Skipping redundant category refresh")
                return

        # Store current selections before refresh
        current_category = self.get_selected_category()
        current_subcategory = self.get_selected_subcategory()

        # Refresh the selector (this will also refresh the category manager)
        self.refresh_categories()

        # Try to select the updated category/subcategory
        if old_name == current_category:
            self.set_category(new_name)
        elif old_name == current_subcategory:
            self.set_subcategory(new_name)

        self.categories_updated.emit()

    def _invalidate_cache(self):
        """Invalidate all cached data"""
        self._categories_cache = None
        self._subcategories_cache.clear()
        self._cache_timestamp = 0
        self.logger.debug("Category cache invalidated")

    def _perform_search(self):
        """Perform debounced search operation"""
        # This method can be extended for search functionality
        pass

    def refresh_categories(self):
        """Force refresh categories by invalidating cache"""
        self._invalidate_cache()
        self.load_categories()

        # Update timestamp to prevent redundant refreshes
        import time
        self._last_refresh_time = time.time()

        # Update refresh timestamp
        import time
        self._last_refresh_time = time.time()
