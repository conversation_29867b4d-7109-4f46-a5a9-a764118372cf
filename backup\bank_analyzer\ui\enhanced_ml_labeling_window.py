"""
Enhanced ML labeling interface with AI suggestions integration
Provides AI-assisted manual labeling with accept/reject/modify workflow
"""

from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QPushButton, QLabel, QComboBox, QLineEdit, QProgressBar, QTextEdit,
    QSplitter, QGroupBox, QFormLayout, QMessageBox, QHeaderView,
    QCheckBox, QSpinBox, QTabWidget, QFrame, QScrollArea, QDialog,
    QInputDialog, QToolButton, QMenu, QButtonGroup, QRadioButton
)
from PySide6.QtCore import Qt, QThread, QTimer, Signal, QSize
from PySide6.QtGui import QFont, QColor, QPalette, QIcon

import pandas as pd
from typing import List, Dict, Optional, Any
from datetime import datetime
import logging
import time

from ..ml.hybrid_ml_categorizer import HybridMLCategorizer, OperationMode, DataSource
from ..ml.training_data_manager import TrainingDataManager
from ..ml.category_manager import CategoryManager
from ..ml.data_preparation import UniqueTransaction
from ..core.logger import get_logger
from .ml_labeling_window import MLLabelingWindow
from .enhanced_category_selector import EnhancedCategorySelector


class AISuggestionWidget(QWidget):
    """Widget for displaying and managing AI suggestions"""
    
    suggestion_accepted = Signal(dict)
    suggestion_rejected = Signal(dict)
    suggestion_modified = Signal(dict, dict)  # original, modified
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_suggestion = None
        self.setup_ui()
    
    def setup_ui(self):
        """Setup AI suggestion widget UI"""
        layout = QVBoxLayout(self)
        
        # Header
        header = QLabel("🤖 AI Suggestion")
        header.setFont(QFont("Arial", 12, QFont.Bold))
        layout.addWidget(header)
        
        # Suggestion display
        self.suggestion_frame = QFrame()
        self.suggestion_frame.setFrameStyle(QFrame.Box)
        self.suggestion_frame.setStyleSheet("QFrame { background-color: #f0f8ff; border: 1px solid #4a90e2; }")
        suggestion_layout = QVBoxLayout(self.suggestion_frame)
        
        # Category suggestion
        self.category_label = QLabel("Category: -")
        self.category_label.setFont(QFont("Arial", 10, QFont.Bold))
        suggestion_layout.addWidget(self.category_label)
        
        # Subcategory suggestion
        self.subcategory_label = QLabel("Subcategory: -")
        suggestion_layout.addWidget(self.subcategory_label)
        
        # Confidence
        self.confidence_label = QLabel("Confidence: -")
        suggestion_layout.addWidget(self.confidence_label)
        
        # Notes
        self.notes_label = QLabel("Notes: -")
        self.notes_label.setWordWrap(True)
        suggestion_layout.addWidget(self.notes_label)
        
        layout.addWidget(self.suggestion_frame)
        
        # Action buttons
        button_layout = QHBoxLayout()
        
        self.accept_btn = QPushButton("✅ Accept")
        self.accept_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; }")
        self.accept_btn.clicked.connect(self.accept_suggestion)
        button_layout.addWidget(self.accept_btn)
        
        self.modify_btn = QPushButton("✏️ Modify")
        self.modify_btn.setStyleSheet("QPushButton { background-color: #FF9800; color: white; }")
        self.modify_btn.clicked.connect(self.modify_suggestion)
        button_layout.addWidget(self.modify_btn)
        
        self.reject_btn = QPushButton("❌ Reject")
        self.reject_btn.setStyleSheet("QPushButton { background-color: #f44336; color: white; }")
        self.reject_btn.clicked.connect(self.reject_suggestion)
        button_layout.addWidget(self.reject_btn)
        
        layout.addWidget(QWidget())  # Spacer
        layout.addLayout(button_layout)
        
        # Initially hidden
        self.hide_suggestion()
    
    def show_suggestion(self, suggestion: Dict[str, Any]):
        """Display AI suggestion"""
        self.current_suggestion = suggestion
        
        self.category_label.setText(f"Category: {suggestion.get('category', 'Unknown')}")
        self.subcategory_label.setText(f"Subcategory: {suggestion.get('subcategory', 'Unknown')}")
        self.confidence_label.setText(f"Confidence: {suggestion.get('confidence', 0):.1%}")
        self.notes_label.setText(f"Notes: {suggestion.get('notes', 'No notes')}")
        
        # Color code by confidence
        confidence = suggestion.get('confidence', 0)
        if confidence >= 0.8:
            color = "#4CAF50"  # Green
        elif confidence >= 0.6:
            color = "#FF9800"  # Orange
        else:
            color = "#f44336"  # Red
        
        self.suggestion_frame.setStyleSheet(f"QFrame {{ background-color: #f0f8ff; border: 2px solid {color}; }}")
        
        self.suggestion_frame.show()
        self.accept_btn.setEnabled(True)
        self.modify_btn.setEnabled(True)
        self.reject_btn.setEnabled(True)
    
    def hide_suggestion(self):
        """Hide AI suggestion"""
        self.current_suggestion = None
        self.suggestion_frame.hide()
        self.accept_btn.setEnabled(False)
        self.modify_btn.setEnabled(False)
        self.reject_btn.setEnabled(False)
    
    def accept_suggestion(self):
        """Accept AI suggestion"""
        if self.current_suggestion:
            self.suggestion_accepted.emit(self.current_suggestion)
            self.hide_suggestion()
    
    def reject_suggestion(self):
        """Reject AI suggestion"""
        if self.current_suggestion:
            self.suggestion_rejected.emit(self.current_suggestion)
            self.hide_suggestion()
    
    def modify_suggestion(self):
        """Modify AI suggestion"""
        if self.current_suggestion:
            # Open category selector for modification
            dialog = EnhancedCategorySelector(self)
            dialog.set_current_category(
                self.current_suggestion.get('category', ''),
                self.current_suggestion.get('subcategory', '')
            )
            
            if dialog.exec() == QDialog.Accepted:
                modified = self.current_suggestion.copy()
                modified['category'] = dialog.get_selected_category()
                modified['subcategory'] = dialog.get_selected_subcategory()
                modified['confidence'] = 1.0  # User modified = high confidence
                modified['source'] = 'manual_modified_ai'
                
                self.suggestion_modified.emit(self.current_suggestion, modified)
                self.hide_suggestion()


class OperationModeSelector(QWidget):
    """Widget for selecting operation mode"""
    
    mode_changed = Signal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        """Setup operation mode selector UI"""
        layout = QVBoxLayout(self)
        
        # Header
        header = QLabel("🔧 Operation Mode")
        header.setFont(QFont("Arial", 12, QFont.Bold))
        layout.addWidget(header)
        
        # Mode selection
        self.mode_group = QButtonGroup(self)
        
        modes = [
            ("ai_first", "🚀 AI First", "Use SambaNova AI first, fall back to local ML"),
            ("ml_first", "🤖 ML First", "Use local ML first, AI for uncertain cases"),
            ("hybrid", "⚡ Hybrid", "Intelligently combine all approaches"),
            ("manual_only", "✋ Manual Only", "Disable AI, use only local ML and manual")
        ]
        
        for mode_value, mode_name, description in modes:
            radio = QRadioButton(mode_name)
            radio.setToolTip(description)
            radio.toggled.connect(lambda checked, mode=mode_value: self.on_mode_changed(mode, checked))
            self.mode_group.addButton(radio)
            layout.addWidget(radio)
            
            # Add description
            desc_label = QLabel(f"  {description}")
            desc_label.setStyleSheet("color: gray; font-size: 10px;")
            layout.addWidget(desc_label)
        
        # Set default to hybrid
        self.mode_group.buttons()[2].setChecked(True)  # Hybrid mode
    
    def on_mode_changed(self, mode: str, checked: bool):
        """Handle mode change"""
        if checked:
            self.mode_changed.emit(mode)
    
    def get_current_mode(self) -> str:
        """Get currently selected mode"""
        for i, button in enumerate(self.mode_group.buttons()):
            if button.isChecked():
                modes = ["ai_first", "ml_first", "hybrid", "manual_only"]
                return modes[i]
        return "hybrid"


class EnhancedMLLabelingWindow(MLLabelingWindow):
    """
    Enhanced ML labeling window with AI suggestions integration
    Extends the existing MLLabelingWindow with hybrid ML capabilities
    """
    
    def __init__(self, parent=None):
        # Initialize parent first
        super().__init__(parent)

        # Initialize hybrid categorizer
        self.hybrid_categorizer = HybridMLCategorizer()

        # Initialize AI suggestions
        self.ai_suggestions = {}

        # Add enhanced features
        self.setup_enhanced_ui()

        self.logger.info("Enhanced ML labeling window initialized")
    
    def setup_enhanced_ui(self):
        """Add enhanced UI components"""
        # Ensure parent UI is initialized
        if not hasattr(self, 'tab_widget'):
            self.logger.warning("Parent UI not fully initialized, skipping enhanced UI setup")
            return

        # Create enhanced tab
        self.enhanced_tab = QWidget()
        self.tab_widget.addTab(self.enhanced_tab, "🚀 AI-Assisted Labeling")
        
        # Enhanced tab layout
        enhanced_layout = QHBoxLayout(self.enhanced_tab)
        
        # Left panel - transaction list with AI suggestions
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        
        # AI suggestion controls
        controls_group = QGroupBox("AI Assistance Controls")
        controls_layout = QVBoxLayout(controls_group)
        
        # Operation mode selector
        self.mode_selector = OperationModeSelector()
        self.mode_selector.mode_changed.connect(self.on_operation_mode_changed)
        controls_layout.addWidget(self.mode_selector)
        
        # Get AI suggestions button
        self.get_suggestions_btn = QPushButton("🤖 Get AI Suggestions")
        self.get_suggestions_btn.clicked.connect(self.get_ai_suggestions)
        controls_layout.addWidget(self.get_suggestions_btn)
        
        # Budget status
        self.budget_label = QLabel("Budget: Loading...")
        controls_layout.addWidget(self.budget_label)
        
        left_layout.addWidget(controls_group)
        
        # Transaction table (reuse existing if available)
        if hasattr(self, 'transaction_table'):
            left_layout.addWidget(self.transaction_table)
        else:
            placeholder_label = QLabel("Transaction table not available")
            left_layout.addWidget(placeholder_label)
        
        enhanced_layout.addWidget(left_panel, 2)
        
        # Right panel - AI suggestion widget
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        
        # AI suggestion widget
        self.ai_suggestion_widget = AISuggestionWidget()
        self.ai_suggestion_widget.suggestion_accepted.connect(self.on_suggestion_accepted)
        self.ai_suggestion_widget.suggestion_rejected.connect(self.on_suggestion_rejected)
        self.ai_suggestion_widget.suggestion_modified.connect(self.on_suggestion_modified)
        right_layout.addWidget(self.ai_suggestion_widget)
        
        # Statistics
        stats_group = QGroupBox("Session Statistics")
        stats_layout = QVBoxLayout(stats_group)
        
        self.stats_label = QLabel("No statistics available")
        stats_layout.addWidget(self.stats_label)
        
        right_layout.addWidget(stats_group)
        
        enhanced_layout.addWidget(right_panel, 1)
        
        # Update budget status
        self.update_budget_status()
        
        # Update statistics timer
        self.stats_timer = QTimer()
        self.stats_timer.timeout.connect(self.update_statistics)
        self.stats_timer.start(10000)  # Update every 10 seconds

    def on_operation_mode_changed(self, mode: str):
        """Handle operation mode change"""
        try:
            operation_mode = OperationMode(mode)
            self.hybrid_categorizer.switch_operation_mode(operation_mode)
            self.logger.info(f"Switched to {mode} mode")

            # Update UI based on mode
            if mode == "manual_only":
                self.get_suggestions_btn.setEnabled(False)
                self.get_suggestions_btn.setText("🚫 AI Disabled")
            else:
                self.get_suggestions_btn.setEnabled(True)
                self.get_suggestions_btn.setText("🤖 Get AI Suggestions")

            self.update_budget_status()

        except Exception as e:
            self.logger.error(f"Error changing operation mode: {str(e)}")
            QMessageBox.warning(self, "Error", f"Failed to change operation mode: {str(e)}")

    def get_ai_suggestions(self):
        """Get AI suggestions for unlabeled transactions"""
        try:
            # Get unlabeled transactions
            unlabeled = self.training_manager.get_unique_transactions(labeled=False)

            if not unlabeled:
                QMessageBox.information(self, "No Data", "No unlabeled transactions found.")
                return

            # Limit to reasonable batch size
            batch_size = min(20, len(unlabeled))
            batch = unlabeled[:batch_size]

            # Convert to RawTransaction format
            from ..models.transaction import RawTransaction
            raw_transactions = []
            for txn in batch:
                raw_txn = RawTransaction(
                    description=txn.description,
                    amount=txn.amount,
                    date=datetime.now().strftime("%Y-%m-%d")
                )
                raw_transactions.append(raw_txn)

            # Get AI suggestions
            self.get_suggestions_btn.setEnabled(False)
            self.get_suggestions_btn.setText("🔄 Getting Suggestions...")

            suggestions = self.hybrid_categorizer.get_ai_suggestions_for_manual_labeling(raw_transactions)

            if suggestions:
                self.ai_suggestions = suggestions
                QMessageBox.information(
                    self,
                    "AI Suggestions Ready",
                    f"Got AI suggestions for {len(suggestions)} transactions.\n"
                    "Click on transactions in the table to see suggestions."
                )

                # Highlight transactions with suggestions
                self.highlight_transactions_with_suggestions()
            else:
                QMessageBox.warning(
                    self,
                    "No Suggestions",
                    "No AI suggestions available. This may be due to budget constraints."
                )

        except Exception as e:
            self.logger.error(f"Error getting AI suggestions: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to get AI suggestions: {str(e)}")

        finally:
            self.get_suggestions_btn.setEnabled(True)
            self.get_suggestions_btn.setText("🤖 Get AI Suggestions")
            self.update_budget_status()

    def highlight_transactions_with_suggestions(self):
        """Highlight transactions that have AI suggestions"""
        for row in range(self.transaction_table.rowCount()):
            description_item = self.transaction_table.item(row, 0)  # Assuming description is in column 0
            if description_item:
                description = description_item.text()
                if description in self.ai_suggestions:
                    # Highlight row with light blue background
                    for col in range(self.transaction_table.columnCount()):
                        item = self.transaction_table.item(row, col)
                        if item:
                            item.setBackground(QColor(173, 216, 230))  # Light blue

    def on_transaction_selected(self):
        """Handle transaction selection - show AI suggestion if available"""
        try:
            # Call parent method first
            super().on_transaction_selected()

            # Then handle AI suggestions if available
            if hasattr(self, 'ai_suggestions') and hasattr(self, 'ai_suggestion_widget'):
                current_row = self.transaction_table.currentRow()
                if current_row >= 0:
                    description_item = self.transaction_table.item(current_row, 0)
                    if description_item:
                        description = description_item.text()

                        if description in self.ai_suggestions:
                            suggestion = self.ai_suggestions[description]
                            self.ai_suggestion_widget.show_suggestion(suggestion)
                        else:
                            self.ai_suggestion_widget.hide_suggestion()

        except Exception as e:
            self.logger.error(f"Error handling enhanced transaction selection: {str(e)}")
            # Try to call parent method as fallback
            try:
                super().on_transaction_selected()
            except:
                pass  # Ignore if parent method also fails

    def on_suggestion_accepted(self, suggestion: Dict[str, Any]):
        """Handle AI suggestion acceptance"""
        try:
            current_row = self.transaction_table.currentRow()
            if current_row >= 0:
                # Apply suggestion to current transaction
                self.apply_category_to_transaction(
                    current_row,
                    suggestion['category'],
                    suggestion['subcategory'],
                    confidence=suggestion['confidence'],
                    source='sambanova_accepted'
                )

                # Remove from suggestions
                description_item = self.transaction_table.item(current_row, 0)
                if description_item:
                    description = description_item.text()
                    if description in self.ai_suggestions:
                        del self.ai_suggestions[description]

                # Move to next transaction
                self.move_to_next_transaction()

                self.logger.info(f"Accepted AI suggestion: {suggestion['category']}/{suggestion['subcategory']}")

        except Exception as e:
            self.logger.error(f"Error accepting suggestion: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to accept suggestion: {str(e)}")

    def on_suggestion_rejected(self, suggestion: Dict[str, Any]):
        """Handle AI suggestion rejection"""
        try:
            # Remove from suggestions
            current_row = self.transaction_table.currentRow()
            if current_row >= 0:
                description_item = self.transaction_table.item(current_row, 0)
                if description_item:
                    description = description_item.text()
                    if description in self.ai_suggestions:
                        del self.ai_suggestions[description]

                # Clear highlighting
                for col in range(self.transaction_table.columnCount()):
                    item = self.transaction_table.item(current_row, col)
                    if item:
                        item.setBackground(QColor(255, 255, 255))  # White

                self.logger.info(f"Rejected AI suggestion: {suggestion['category']}/{suggestion['subcategory']}")

        except Exception as e:
            self.logger.error(f"Error rejecting suggestion: {str(e)}")

    def on_suggestion_modified(self, original: Dict[str, Any], modified: Dict[str, Any]):
        """Handle AI suggestion modification"""
        try:
            current_row = self.transaction_table.currentRow()
            if current_row >= 0:
                # Apply modified suggestion
                self.apply_category_to_transaction(
                    current_row,
                    modified['category'],
                    modified['subcategory'],
                    confidence=modified['confidence'],
                    source='manual_modified_ai'
                )

                # Remove from suggestions
                description_item = self.transaction_table.item(current_row, 0)
                if description_item:
                    description = description_item.text()
                    if description in self.ai_suggestions:
                        del self.ai_suggestions[description]

                # Move to next transaction
                self.move_to_next_transaction()

                self.logger.info(f"Modified AI suggestion: {original['category']} -> {modified['category']}")

        except Exception as e:
            self.logger.error(f"Error modifying suggestion: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to modify suggestion: {str(e)}")

    def apply_category_to_transaction(self, row: int, category: str, subcategory: str,
                                    confidence: float = 1.0, source: str = 'manual'):
        """Apply category to transaction at specified row"""
        try:
            # Get transaction description
            description_item = self.transaction_table.item(row, 0)
            if not description_item:
                return

            description = description_item.text()

            # Update the transaction in training manager
            success = self.training_manager.label_transaction(
                description=description,
                category=category,
                sub_category=subcategory,
                confidence=confidence,
                source=source
            )

            if success:
                # Update table display
                category_item = self.transaction_table.item(row, 2)  # Assuming category is column 2
                subcategory_item = self.transaction_table.item(row, 3)  # Assuming subcategory is column 3

                if category_item:
                    category_item.setText(category)
                if subcategory_item:
                    subcategory_item.setText(subcategory)

                # Update row color to indicate labeled
                for col in range(self.transaction_table.columnCount()):
                    item = self.transaction_table.item(row, col)
                    if item:
                        item.setBackground(QColor(144, 238, 144))  # Light green

                self.update_statistics()

        except Exception as e:
            self.logger.error(f"Error applying category: {str(e)}")

    def move_to_next_transaction(self):
        """Move to next unlabeled transaction"""
        current_row = self.transaction_table.currentRow()

        # Find next unlabeled transaction
        for row in range(current_row + 1, self.transaction_table.rowCount()):
            category_item = self.transaction_table.item(row, 2)
            if category_item and not category_item.text():
                self.transaction_table.setCurrentRow(row)
                return

        # If no next unlabeled, go to first unlabeled
        for row in range(self.transaction_table.rowCount()):
            category_item = self.transaction_table.item(row, 2)
            if category_item and not category_item.text():
                self.transaction_table.setCurrentRow(row)
                return

    def update_budget_status(self):
        """Update budget status display"""
        try:
            budget_status = self.hybrid_categorizer.ai_categorizer.get_budget_status()
            remaining = budget_status.get('total_remaining', 0)
            usage_pct = budget_status.get('total_usage_pct', 0)
            warning_level = budget_status.get('warning_level', 'unknown')

            if warning_level == 'safe':
                color = "green"
                icon = "✅"
            elif warning_level == 'warning':
                color = "orange"
                icon = "⚠️"
            elif warning_level == 'critical':
                color = "red"
                icon = "🚨"
            else:
                color = "gray"
                icon = "❓"

            budget_text = f"{icon} Budget: ${remaining:.4f} ({usage_pct:.1f}% used)"
            self.budget_label.setText(f'<span style="color: {color};">{budget_text}</span>')

        except Exception as e:
            self.budget_label.setText("💰 Budget: Status unavailable")
            self.logger.debug(f"Budget status update failed: {str(e)}")

    def update_statistics(self):
        """Update session statistics"""
        try:
            stats = self.hybrid_categorizer.get_system_status()
            hybrid_stats = stats.get('hybrid_statistics', {})

            stats_text = f"""
            <b>Session Statistics:</b><br>
            • Total Categorized: {hybrid_stats.get('total_categorized', 0)}<br>
            • ML Used: {hybrid_stats.get('ml_used', 0)}<br>
            • AI Used: {hybrid_stats.get('ai_used', 0)}<br>
            • Rules Used: {hybrid_stats.get('rules_used', 0)}<br>
            • Hybrid Used: {hybrid_stats.get('hybrid_used', 0)}<br>
            • Added to Training: {hybrid_stats.get('added_to_training', 0)}<br>
            • Uncertain Cases: {hybrid_stats.get('uncertain_cases', 0)}<br>

            <b>AI Suggestions:</b><br>
            • Available: {len(self.ai_suggestions)}<br>
            """

            self.stats_label.setText(stats_text)

        except Exception as e:
            self.stats_label.setText("Statistics unavailable")
            self.logger.debug(f"Statistics update failed: {str(e)}")

    def closeEvent(self, event):
        """Handle window close event"""
        # Stop timers
        if hasattr(self, 'stats_timer'):
            self.stats_timer.stop()

        # Call parent close event
        super().closeEvent(event)
