"""
Advanced filtering panel for ML transaction labeling
Provides comprehensive filtering options for transaction data
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QGroupBox,
    QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox, QDateEdit,
    QCheckBox, QPushButton, QLabel, QSlider, QFrame
)
from PySide6.QtCore import Qt, Signal, QDate, QTimer
from PySide6.QtGui import QFont
from datetime import date, datetime
from typing import Optional

from ..ml.training_data_manager import TransactionFilter, FilterType, LabelingStatus
from ..core.logger import get_logger


class FilterPanel(QWidget):
    """
    Advanced filtering panel for transaction labeling
    Provides multiple filter criteria with real-time updates
    """
    
    # Signals
    filter_changed = Signal(object)  # Emits TransactionFilter object
    filter_reset = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger(__name__)

        # Current filter state
        self.current_filter = TransactionFilter()

        # Debouncing for filter inputs
        self.filter_debounce_timer = QTimer()
        self.filter_debounce_timer.setSingleShot(True)
        self.filter_debounce_timer.timeout.connect(self.apply_filters)
        self.debounce_delay = 500  # 500ms delay

        self.setup_ui()
        self.connect_signals()
    
    def setup_ui(self):
        """Setup the filter panel UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        
        # Title
        title_label = QLabel("Advanced Filters")
        title_font = QFont()
        title_font.setBold(True)
        title_font.setPointSize(12)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # Description Filter Group
        desc_group = self.create_description_filter_group()
        layout.addWidget(desc_group)
        
        # Amount Filter Group
        amount_group = self.create_amount_filter_group()
        layout.addWidget(amount_group)
        
        # Frequency Filter Group
        freq_group = self.create_frequency_filter_group()
        layout.addWidget(freq_group)
        
        # Status Filter Group
        status_group = self.create_status_filter_group()
        layout.addWidget(status_group)
        
        # Date Filter Group
        date_group = self.create_date_filter_group()
        layout.addWidget(date_group)
        
        # Category Filter Group
        category_group = self.create_category_filter_group()
        layout.addWidget(category_group)
        
        # Control Buttons
        button_layout = self.create_control_buttons()
        layout.addLayout(button_layout)
        
        # Add stretch to push everything to top
        layout.addStretch()
    
    def create_description_filter_group(self) -> QGroupBox:
        """Create description filter group"""
        group = QGroupBox("Description Filter")
        layout = QVBoxLayout(group)
        
        # Filter text input
        self.desc_text_edit = QLineEdit()
        self.desc_text_edit.setPlaceholderText("Enter text to search for (e.g., 'zomato credit', 'UPI PAYTM')...")
        self.desc_text_edit.textChanged.connect(self.on_search_text_changed)
        layout.addWidget(self.desc_text_edit)

        # Help text for multi-word searches
        self.help_label = QLabel("💡 Default: Multi-Word AND finds transactions with ALL words. Try 'zomato credit' to find Zomato credit transactions.")
        self.help_label.setStyleSheet("color: #666; font-size: 10px; font-style: italic;")
        self.help_label.setWordWrap(True)
        layout.addWidget(self.help_label)
        
        # Filter type and case sensitivity
        filter_layout = QHBoxLayout()
        
        self.desc_filter_type = QComboBox()
        self.desc_filter_type.addItems([
            "Multi-Word AND", "Multi-Word OR", "Contains", "Starts With",
            "Ends With", "Exact Match", "Exact Phrase", "Regex"
        ])
        # Set Multi-Word AND as default for better user experience
        self.desc_filter_type.setCurrentIndex(0)
        filter_layout.addWidget(QLabel("Type:"))
        filter_layout.addWidget(self.desc_filter_type)
        
        self.case_sensitive_check = QCheckBox("Case Sensitive")
        filter_layout.addWidget(self.case_sensitive_check)
        
        layout.addLayout(filter_layout)
        
        return group
    
    def create_amount_filter_group(self) -> QGroupBox:
        """Create amount filter group"""
        group = QGroupBox("Amount Range")
        layout = QGridLayout(group)
        
        # Min amount
        layout.addWidget(QLabel("Min Amount:"), 0, 0)
        self.min_amount_spin = QDoubleSpinBox()
        self.min_amount_spin.setRange(0, 999999)
        self.min_amount_spin.setPrefix("₹")
        self.min_amount_spin.setSpecialValueText("No Limit")
        self.min_amount_spin.setValue(0)
        layout.addWidget(self.min_amount_spin, 0, 1)
        
        # Max amount
        layout.addWidget(QLabel("Max Amount:"), 1, 0)
        self.max_amount_spin = QDoubleSpinBox()
        self.max_amount_spin.setRange(0, 999999)
        self.max_amount_spin.setPrefix("₹")
        self.max_amount_spin.setSpecialValueText("No Limit")
        self.max_amount_spin.setValue(0)
        layout.addWidget(self.max_amount_spin, 1, 1)
        
        return group
    
    def create_frequency_filter_group(self) -> QGroupBox:
        """Create frequency filter group"""
        group = QGroupBox("Frequency Range")
        layout = QGridLayout(group)
        
        # Min frequency
        layout.addWidget(QLabel("Min Frequency:"), 0, 0)
        self.min_freq_spin = QSpinBox()
        self.min_freq_spin.setRange(0, 9999)
        self.min_freq_spin.setSpecialValueText("No Limit")
        self.min_freq_spin.setValue(0)
        layout.addWidget(self.min_freq_spin, 0, 1)
        
        # Max frequency
        layout.addWidget(QLabel("Max Frequency:"), 1, 0)
        self.max_freq_spin = QSpinBox()
        self.max_freq_spin.setRange(0, 9999)
        self.max_freq_spin.setSpecialValueText("No Limit")
        self.max_freq_spin.setValue(0)
        layout.addWidget(self.max_freq_spin, 1, 1)
        
        return group
    
    def create_status_filter_group(self) -> QGroupBox:
        """Create status filter group"""
        group = QGroupBox("Labeling Status")
        layout = QVBoxLayout(group)
        
        self.status_combo = QComboBox()
        self.status_combo.addItems([
            "All Transactions", "Labeled Only", "Unlabeled Only", "Pending Review"
        ])
        layout.addWidget(self.status_combo)
        
        return group
    
    def create_date_filter_group(self) -> QGroupBox:
        """Create date filter group"""
        group = QGroupBox("Date Range")
        layout = QGridLayout(group)
        
        # Enable date filter checkbox
        self.date_filter_enabled = QCheckBox("Enable Date Filter")
        layout.addWidget(self.date_filter_enabled, 0, 0, 1, 2)
        
        # Start date
        layout.addWidget(QLabel("From:"), 1, 0)
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setDate(QDate.currentDate().addMonths(-6))
        self.start_date_edit.setCalendarPopup(True)
        self.start_date_edit.setEnabled(False)
        layout.addWidget(self.start_date_edit, 1, 1)
        
        # End date
        layout.addWidget(QLabel("To:"), 2, 0)
        self.end_date_edit = QDateEdit()
        self.end_date_edit.setDate(QDate.currentDate())
        self.end_date_edit.setCalendarPopup(True)
        self.end_date_edit.setEnabled(False)
        layout.addWidget(self.end_date_edit, 2, 1)
        
        # Connect checkbox to enable/disable date inputs
        self.date_filter_enabled.toggled.connect(self.start_date_edit.setEnabled)
        self.date_filter_enabled.toggled.connect(self.end_date_edit.setEnabled)
        
        return group
    
    def create_category_filter_group(self) -> QGroupBox:
        """Create category filter group"""
        group = QGroupBox("Category Filter")
        layout = QGridLayout(group)
        
        # Category filter
        layout.addWidget(QLabel("Category:"), 0, 0)
        self.category_combo = QComboBox()
        self.category_combo.addItem("All Categories")
        layout.addWidget(self.category_combo, 0, 1)
        
        # Sub-category filter
        layout.addWidget(QLabel("Sub-category:"), 1, 0)
        self.subcategory_combo = QComboBox()
        self.subcategory_combo.addItem("All Sub-categories")
        layout.addWidget(self.subcategory_combo, 1, 1)
        
        return group
    
    def create_control_buttons(self) -> QHBoxLayout:
        """Create control buttons"""
        layout = QHBoxLayout()
        
        # Apply filter button
        self.apply_button = QPushButton("Apply Filters")
        self.apply_button.setStyleSheet("""
            QPushButton {
                background-color: #007ACC;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #005a9e;
            }
        """)
        layout.addWidget(self.apply_button)
        
        # Reset filter button
        self.reset_button = QPushButton("Reset Filters")
        self.reset_button.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #545b62;
            }
        """)
        layout.addWidget(self.reset_button)
        
        layout.addStretch()
        
        return layout
    
    def connect_signals(self):
        """Connect UI signals with debouncing"""
        # Apply and reset buttons
        self.apply_button.clicked.connect(self.apply_filters)
        self.reset_button.clicked.connect(self.reset_filters)

        # Connect text inputs to debounced filter application
        self.desc_text_edit.textChanged.connect(self.on_filter_input_changed)
        self.status_combo.currentTextChanged.connect(self.on_filter_input_changed)
        self.category_combo.currentTextChanged.connect(self.on_filter_input_changed)
        self.subcategory_combo.currentTextChanged.connect(self.on_filter_input_changed)

        # Connect numeric inputs to debounced filter application
        self.min_amount_spin.valueChanged.connect(self.on_filter_input_changed)
        self.max_amount_spin.valueChanged.connect(self.on_filter_input_changed)
        self.min_freq_spin.valueChanged.connect(self.on_filter_input_changed)
        self.max_freq_spin.valueChanged.connect(self.on_filter_input_changed)

        # Connect date inputs to debounced filter application
        self.date_filter_enabled.toggled.connect(self.on_filter_input_changed)
        self.start_date_edit.dateChanged.connect(self.on_filter_input_changed)
        self.end_date_edit.dateChanged.connect(self.on_filter_input_changed)

    def on_filter_input_changed(self):
        """Handle filter input changes with debouncing"""
        # Reset the debounce timer
        self.filter_debounce_timer.stop()
        self.filter_debounce_timer.start(self.debounce_delay)

    def on_search_text_changed(self, text: str):
        """Handle search text changes and provide smart suggestions"""
        text = text.strip()
        if not text:
            return

        # Auto-suggest filter type based on input
        if '"' in text:
            # Has quotes - suggest exact phrase
            suggested_index = self.desc_filter_type.findText("Exact Phrase")
            if suggested_index >= 0:
                self.desc_filter_type.setCurrentIndex(suggested_index)
        elif len(text.split()) > 1:
            # Multiple words - ensure Multi-Word AND is selected (should be default)
            if self.desc_filter_type.currentText() not in ["Multi-Word AND", "Multi-Word OR", "Exact Phrase"]:
                suggested_index = self.desc_filter_type.findText("Multi-Word AND")
                if suggested_index >= 0:
                    self.desc_filter_type.setCurrentIndex(suggested_index)

        # Update help text based on current input
        self.update_help_text(text)

    def update_help_text(self, text: str):
        """Update help text based on current search input"""
        if not hasattr(self, 'help_label'):
            return

        if not text:
            self.help_label.setText("💡 Default: Multi-Word AND finds transactions with ALL words. Try 'zomato credit' to find Zomato credit transactions.")
        elif '"' in text:
            self.help_label.setText("💡 Quotes detected: Using Exact Phrase search for precise matching.")
        elif len(text.split()) > 1:
            words = text.split()
            if len(words) == 2:
                self.help_label.setText(f"💡 Multi-Word search: Finding transactions with both '{words[0]}' AND '{words[1]}'.")
            else:
                self.help_label.setText(f"💡 Multi-Word search: Finding transactions with all {len(words)} words.")
        else:
            self.help_label.setText("💡 Single word search: Use Multi-Word filters for better results with multiple words.")
    
    def apply_filters(self):
        """Apply current filter settings"""
        try:
            # Build filter object
            filter_obj = TransactionFilter()

            # Description filter
            desc_text = self.desc_text_edit.text().strip()
            if desc_text:
                filter_obj.description_text = desc_text
                filter_obj.case_sensitive = self.case_sensitive_check.isChecked()

                # Map combo box to filter type
                type_map = {
                    "Multi-Word AND": FilterType.MULTI_WORD_AND,
                    "Multi-Word OR": FilterType.MULTI_WORD_OR,
                    "Contains": FilterType.CONTAINS,
                    "Starts With": FilterType.STARTS_WITH,
                    "Ends With": FilterType.ENDS_WITH,
                    "Exact Match": FilterType.EXACT,
                    "Exact Phrase": FilterType.EXACT_PHRASE,
                    "Regex": FilterType.REGEX
                }
                filter_obj.description_filter_type = type_map[self.desc_filter_type.currentText()]
            
            # Amount filter
            if self.min_amount_spin.value() > 0:
                filter_obj.min_amount = self.min_amount_spin.value()
            if self.max_amount_spin.value() > 0:
                filter_obj.max_amount = self.max_amount_spin.value()
            
            # Frequency filter
            if self.min_freq_spin.value() > 0:
                filter_obj.min_frequency = self.min_freq_spin.value()
            if self.max_freq_spin.value() > 0:
                filter_obj.max_frequency = self.max_freq_spin.value()
            
            # Status filter
            status_map = {
                "All Transactions": LabelingStatus.ALL,
                "Labeled Only": LabelingStatus.LABELED,
                "Unlabeled Only": LabelingStatus.UNLABELED,
                "Pending Review": LabelingStatus.PENDING
            }
            current_status_text = self.status_combo.currentText()
            filter_obj.labeling_status = status_map[current_status_text]

            # Log filter application for debugging
            self.logger.debug(f"Applying filter - Status: {current_status_text} ({filter_obj.labeling_status})")

            # Date filter
            if self.date_filter_enabled.isChecked():
                filter_obj.start_date = self.start_date_edit.date().toPython()
                filter_obj.end_date = self.end_date_edit.date().toPython()

            # Category filter
            if self.category_combo.currentText() != "All Categories":
                filter_obj.category = self.category_combo.currentText()
            if self.subcategory_combo.currentText() != "All Sub-categories":
                filter_obj.sub_category = self.subcategory_combo.currentText()

            self.current_filter = filter_obj
            self.filter_changed.emit(filter_obj)
            
        except Exception as e:
            self.logger.error(f"Error applying filters: {str(e)}")
    
    def reset_filters(self):
        """Reset all filters to default values"""
        # Reset all controls
        self.desc_text_edit.clear()
        self.desc_filter_type.setCurrentIndex(0)
        self.case_sensitive_check.setChecked(False)
        
        self.min_amount_spin.setValue(0)
        self.max_amount_spin.setValue(0)
        
        self.min_freq_spin.setValue(0)
        self.max_freq_spin.setValue(0)
        
        self.status_combo.setCurrentIndex(0)
        
        self.date_filter_enabled.setChecked(False)
        self.start_date_edit.setDate(QDate.currentDate().addMonths(-6))
        self.end_date_edit.setDate(QDate.currentDate())
        
        self.category_combo.setCurrentIndex(0)
        self.subcategory_combo.setCurrentIndex(0)
        
        # Reset filter object and emit signal
        self.current_filter = TransactionFilter()
        self.filter_reset.emit()
    
    def get_current_filter(self) -> TransactionFilter:
        """Get the current filter configuration"""
        return self.current_filter

    def get_current_status_filter(self) -> str:
        """Get the current status filter text for debugging"""
        return self.status_combo.currentText()

    def set_status_filter(self, status_text: str):
        """Set the status filter programmatically"""
        index = self.status_combo.findText(status_text)
        if index >= 0:
            self.status_combo.setCurrentIndex(index)
            self.on_filter_input_changed()  # Trigger filter update
    
    def load_categories(self, categories: list, subcategories: dict):
        """Load available categories and subcategories"""
        # Clear existing items (except "All")
        self.category_combo.clear()
        self.category_combo.addItem("All Categories")
        
        self.subcategory_combo.clear()
        self.subcategory_combo.addItem("All Sub-categories")
        
        # Add categories
        for category in categories:
            self.category_combo.addItem(category)
        
        # Add subcategories (flattened for now)
        all_subcategories = set()
        for subcat_list in subcategories.values():
            all_subcategories.update(subcat_list)
        
        for subcategory in sorted(all_subcategories):
            self.subcategory_combo.addItem(subcategory)
