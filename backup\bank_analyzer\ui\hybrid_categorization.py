#!/usr/bin/env python3
"""
Hybrid Categorization System - Three-phase approach:
1. ML Model Categorization (trained model only)
2. AI-Assisted Categorization (SambaNova)
3. Manual Categorization
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import pandas as pd
from pathlib import Path
import json
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import threading

from ..ml.ml_categorizer import MLTransactionCategorizer
from ..ml.sambanova_client import SambaNovaClient, SambaNovaConfig
from ..core.logger import get_logger


class HybridCategorizationUI:
    """UI for hybrid categorization system"""
    
    def __init__(self, parent=None):
        self.parent = parent
        self.logger = get_logger(__name__)

        # Initialize components
        self.ml_categorizer = MLTransactionCategorizer()

        # Initialize SambaNova client with default config
        sambanova_config = SambaNovaConfig(
            api_key="your-api-key-here",  # Will be loaded from config
            model_name="Meta-Llama-3.1-8B-Instruct",
            max_tokens=150,
            temperature=0.1
        )
        self.sambanova_client = SambaNovaClient(sambanova_config)

        # Data
        self.transactions_df = None
        self.categorized_transactions = []
        self.uncategorized_transactions = []
        self.ai_categorized_transactions = []
        self.manual_categorized_transactions = []
        self.temp_csv_file = None  # Track temporary CSV file for cleanup

        # Configuration
        self.ml_confidence_threshold = 0.4  # Minimum confidence for ML categorization
        self.ai_batch_size = 20  # Batch size for AI processing

        # UI
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the main UI"""
        if self.parent:
            self.root = tk.Toplevel(self.parent)
        else:
            self.root = tk.Tk()
            
        self.root.title("Hybrid Transaction Categorization")
        self.root.geometry("1200x800")

        # Set up window close protocol
        self.root.protocol("WM_DELETE_WINDOW", self.on_window_close)
        
        # Create notebook for phases
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Phase 1: ML Categorization
        self.ml_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.ml_frame, text="Phase 1: ML Categorization")
        self.setup_ml_phase()
        
        # Phase 2: AI Categorization
        self.ai_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.ai_frame, text="Phase 2: AI Categorization")
        self.setup_ai_phase()
        
        # Phase 3: Manual Categorization
        self.manual_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.manual_frame, text="Phase 3: Manual Categorization")
        self.setup_manual_phase()
        
        # Phase 4: Final Review & Export
        self.export_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.export_frame, text="Phase 4: Export to Main App")
        self.setup_export_phase()
        
    def setup_ml_phase(self):
        """Setup ML categorization phase"""
        # Header
        header_frame = ttk.Frame(self.ml_frame)
        header_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(header_frame, text="Phase 1: ML Model Categorization", 
                 font=("Arial", 14, "bold")).pack(side=tk.LEFT)
        
        # Load file section
        load_frame = ttk.LabelFrame(self.ml_frame, text="Load Bank Statement")
        load_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(load_frame, text="Load CSV File", 
                  command=self.load_transactions_file).pack(side=tk.LEFT, padx=5, pady=5)
        
        self.file_label = ttk.Label(load_frame, text="No file loaded")
        self.file_label.pack(side=tk.LEFT, padx=10)
        
        # Configuration section
        config_frame = ttk.LabelFrame(self.ml_frame, text="ML Configuration")
        config_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(config_frame, text="Confidence Threshold:").pack(side=tk.LEFT, padx=5)
        self.confidence_var = tk.DoubleVar(value=self.ml_confidence_threshold)
        confidence_scale = ttk.Scale(config_frame, from_=0.1, to=0.9, 
                                   variable=self.confidence_var, orient=tk.HORIZONTAL)
        confidence_scale.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        
        self.confidence_label = ttk.Label(config_frame, text="0.4")
        self.confidence_label.pack(side=tk.LEFT, padx=5)
        confidence_scale.configure(command=self.update_confidence_label)
        
        # Process button
        process_frame = ttk.Frame(self.ml_frame)
        process_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.ml_process_btn = ttk.Button(process_frame, text="Process with ML Model", 
                                        command=self.process_ml_categorization, state=tk.DISABLED)
        self.ml_process_btn.pack(side=tk.LEFT, padx=5)
        
        # Progress
        self.ml_progress = ttk.Progressbar(process_frame, mode='determinate')
        self.ml_progress.pack(side=tk.LEFT, padx=10, fill=tk.X, expand=True)
        
        # Results section
        results_frame = ttk.LabelFrame(self.ml_frame, text="ML Categorization Results")
        results_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Results tree
        columns = ("Description", "Amount", "Date", "ML Category", "Confidence", "Status")
        self.ml_tree = ttk.Treeview(results_frame, columns=columns, show="headings", height=15)
        
        for col in columns:
            self.ml_tree.heading(col, text=col)
            self.ml_tree.column(col, width=150)
        
        # Scrollbars
        ml_v_scroll = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.ml_tree.yview)
        ml_h_scroll = ttk.Scrollbar(results_frame, orient=tk.HORIZONTAL, command=self.ml_tree.xview)
        self.ml_tree.configure(yscrollcommand=ml_v_scroll.set, xscrollcommand=ml_h_scroll.set)
        
        self.ml_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        ml_v_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        ml_h_scroll.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Summary
        self.ml_summary_label = ttk.Label(self.ml_frame, text="Ready to load transactions")
        self.ml_summary_label.pack(pady=5)
        
    def setup_ai_phase(self):
        """Setup AI categorization phase"""
        # Header
        header_frame = ttk.Frame(self.ai_frame)
        header_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(header_frame, text="Phase 2: AI-Assisted Categorization", 
                 font=("Arial", 14, "bold")).pack(side=tk.LEFT)
        
        # Configuration
        config_frame = ttk.LabelFrame(self.ai_frame, text="AI Configuration")
        config_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(config_frame, text="Batch Size:").pack(side=tk.LEFT, padx=5)
        self.batch_size_var = tk.IntVar(value=self.ai_batch_size)
        batch_spinbox = ttk.Spinbox(config_frame, from_=10, to=50, textvariable=self.batch_size_var, width=10)
        batch_spinbox.pack(side=tk.LEFT, padx=5)
        
        # Cost tracking
        self.cost_label = ttk.Label(config_frame, text="Estimated Cost: $0.00")
        self.cost_label.pack(side=tk.RIGHT, padx=10)
        
        # Process button
        process_frame = ttk.Frame(self.ai_frame)
        process_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.ai_process_btn = ttk.Button(process_frame, text="Process with AI", 
                                        command=self.process_ai_categorization, state=tk.DISABLED)
        self.ai_process_btn.pack(side=tk.LEFT, padx=5)
        
        # Progress
        self.ai_progress = ttk.Progressbar(process_frame, mode='determinate')
        self.ai_progress.pack(side=tk.LEFT, padx=10, fill=tk.X, expand=True)
        
        # Results section
        results_frame = ttk.LabelFrame(self.ai_frame, text="AI Categorization Results")
        results_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Results tree
        columns = ("Description", "Amount", "Date", "AI Category", "Confidence", "Status")
        self.ai_tree = ttk.Treeview(results_frame, columns=columns, show="headings", height=15)
        
        for col in columns:
            self.ai_tree.heading(col, text=col)
            self.ai_tree.column(col, width=150)
        
        # Scrollbars
        ai_v_scroll = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.ai_tree.yview)
        ai_h_scroll = ttk.Scrollbar(results_frame, orient=tk.HORIZONTAL, command=self.ai_tree.xview)
        self.ai_tree.configure(yscrollcommand=ai_v_scroll.set, xscrollcommand=ai_h_scroll.set)
        
        self.ai_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        ai_v_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        ai_h_scroll.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Summary
        self.ai_summary_label = ttk.Label(self.ai_frame, text="Complete ML categorization first")
        self.ai_summary_label.pack(pady=5)
        
    def setup_manual_phase(self):
        """Setup manual categorization phase"""
        # Header
        header_frame = ttk.Frame(self.manual_frame)
        header_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(header_frame, text="Phase 3: Manual Categorization",
                 font=("Arial", 14, "bold")).pack(side=tk.LEFT)

        # Load available categories
        self.load_categories()

        # Control frame
        control_frame = ttk.Frame(self.manual_frame)
        control_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(control_frame, text="Load Uncategorized",
                  command=self.load_manual_transactions).pack(side=tk.LEFT, padx=5)

        # Category selection
        ttk.Label(control_frame, text="Category:").pack(side=tk.LEFT, padx=(20, 5))
        self.manual_category_var = tk.StringVar()
        self.manual_category_combo = ttk.Combobox(control_frame, textvariable=self.manual_category_var,
                                                 values=self.available_categories, width=20)
        self.manual_category_combo.pack(side=tk.LEFT, padx=5)

        ttk.Button(control_frame, text="Apply Category",
                  command=self.apply_manual_category).pack(side=tk.LEFT, padx=5)

        # Transaction list
        list_frame = ttk.LabelFrame(self.manual_frame, text="Uncategorized Transactions")
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # Create treeview for manual categorization
        columns = ("Description", "Amount", "Date", "Current Category", "Action")
        self.manual_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=15)

        for col in columns:
            self.manual_tree.heading(col, text=col)
            self.manual_tree.column(col, width=150)

        # Scrollbars
        manual_v_scroll = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.manual_tree.yview)
        manual_h_scroll = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, command=self.manual_tree.xview)
        self.manual_tree.configure(yscrollcommand=manual_v_scroll.set, xscrollcommand=manual_h_scroll.set)

        self.manual_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        manual_v_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        manual_h_scroll.pack(side=tk.BOTTOM, fill=tk.X)

        # Summary
        self.manual_summary_label = ttk.Label(self.manual_frame, text="Complete AI processing first")
        self.manual_summary_label.pack(pady=5)
        
    def setup_export_phase(self):
        """Setup export phase"""
        # Header
        header_frame = ttk.Frame(self.export_frame)
        header_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(header_frame, text="Phase 4: Export to Main Application",
                 font=("Arial", 14, "bold")).pack(side=tk.LEFT)

        # Summary frame
        summary_frame = ttk.LabelFrame(self.export_frame, text="Categorization Summary")
        summary_frame.pack(fill=tk.X, padx=10, pady=5)

        self.export_summary_text = tk.Text(summary_frame, height=8, wrap=tk.WORD)
        self.export_summary_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Export options
        options_frame = ttk.LabelFrame(self.export_frame, text="Export Options")
        options_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(options_frame, text="Generate Summary",
                  command=self.generate_export_summary).pack(side=tk.LEFT, padx=5, pady=5)

        ttk.Button(options_frame, text="Export to CSV",
                  command=self.export_to_csv).pack(side=tk.LEFT, padx=5, pady=5)

        ttk.Button(options_frame, text="Load to Main App",
                  command=self.load_to_main_app).pack(side=tk.LEFT, padx=5, pady=5)

        # Status
        self.export_status_label = ttk.Label(self.export_frame, text="Complete all phases first")
        self.export_status_label.pack(pady=5)

    def update_confidence_label(self, value):
        """Update confidence threshold label"""
        self.confidence_label.config(text=f"{float(value):.2f}")
        self.ml_confidence_threshold = float(value)

    def load_transactions_file(self):
        """Load transactions from CSV file"""
        file_path = filedialog.askopenfilename(
            title="Select Bank Statement CSV",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )

        if not file_path:
            return

        self._load_csv_file(file_path)

    def auto_load_csv(self, file_path):
        """Automatically load CSV file (called from main app)"""
        # Track if this is a temporary file for cleanup
        if "temp_transactions_for_hybrid" in str(file_path):
            self.temp_csv_file = file_path
        self._load_csv_file(file_path)

    def _load_csv_file(self, file_path):
        """Internal method to load CSV file"""
        try:
            # Load CSV
            self.transactions_df = pd.read_csv(file_path)

            # Validate required columns
            required_columns = ['description', 'amount', 'date']
            missing_columns = [col for col in required_columns if col not in self.transactions_df.columns]

            if missing_columns:
                messagebox.showerror("Error", f"Missing required columns: {missing_columns}")
                return

            # Update UI
            filename = Path(file_path).name
            self.file_label.config(text=f"Loaded: {len(self.transactions_df)} transactions from {filename}")
            self.ml_process_btn.config(state=tk.NORMAL)
            self.ml_summary_label.config(text=f"Ready to process {len(self.transactions_df)} transactions")

            self.logger.info(f"Loaded {len(self.transactions_df)} transactions from {file_path}")

            # Auto-switch to ML processing tab and show ready message
            if hasattr(self, 'notebook'):
                self.notebook.select(0)  # Select first tab (ML Categorization)

            # Ask if user wants to start ML processing immediately
            if messagebox.askyesno("Auto-Start ML Processing",
                                 f"Successfully loaded {len(self.transactions_df)} transactions.\n\n"
                                 f"Would you like to start ML categorization immediately?"):
                # Start ML processing automatically
                self.root.after(100, self.process_ml_categorization)  # Small delay to ensure UI is ready

        except Exception as e:
            messagebox.showerror("Error", f"Failed to load file: {str(e)}")
            self.logger.error(f"Error loading file: {str(e)}")

    def process_ml_categorization(self):
        """Process transactions using ML model"""
        if self.transactions_df is None:
            messagebox.showerror("Error", "Please load a transactions file first")
            return

        if not self.ml_categorizer.is_trained:
            messagebox.showerror("Error", "ML model is not trained")
            return

        # Disable button and show progress
        self.ml_process_btn.config(state=tk.DISABLED)
        self.ml_progress.config(maximum=len(self.transactions_df))

        # Clear previous results
        for item in self.ml_tree.get_children():
            self.ml_tree.delete(item)

        # Reset data
        self.categorized_transactions = []
        self.uncategorized_transactions = []

        # Process in thread to avoid UI freezing
        thread = threading.Thread(target=self._process_ml_worker)
        thread.daemon = True
        thread.start()

    def _process_ml_worker(self):
        """Worker thread for ML processing"""
        try:
            categorized_count = 0
            uncategorized_count = 0

            for idx, row in self.transactions_df.iterrows():
                description = str(row['description'])
                amount = row['amount']
                date = row['date']

                # Get ML prediction
                prediction = self.ml_categorizer.predict_category(description)

                if prediction and prediction.confidence >= self.ml_confidence_threshold:
                    # Categorized by ML
                    transaction_data = {
                        'index': idx,
                        'description': description,
                        'amount': amount,
                        'date': date,
                        'category': prediction.category,
                        'sub_category': prediction.sub_category,
                        'confidence': prediction.confidence,
                        'method': 'ML',
                        'status': 'Categorized'
                    }
                    self.categorized_transactions.append(transaction_data)
                    categorized_count += 1

                    # Update UI in main thread
                    self.root.after(0, self._add_ml_result, transaction_data, "Categorized")

                else:
                    # Not categorized by ML
                    transaction_data = {
                        'index': idx,
                        'description': description,
                        'amount': amount,
                        'date': date,
                        'category': None,
                        'sub_category': None,
                        'confidence': prediction.confidence if prediction else 0.0,
                        'method': None,
                        'status': 'Uncategorized'
                    }
                    self.uncategorized_transactions.append(transaction_data)
                    uncategorized_count += 1

                    # Update UI in main thread
                    self.root.after(0, self._add_ml_result, transaction_data, "Needs AI/Manual")

                # Update progress
                self.root.after(0, lambda: self.ml_progress.config(value=idx + 1))

            # Update summary
            summary_text = f"ML Results: {categorized_count} categorized, {uncategorized_count} need further processing"
            self.root.after(0, lambda: self.ml_summary_label.config(text=summary_text))

            # Enable AI processing if there are uncategorized transactions
            if uncategorized_count > 0:
                self.root.after(0, lambda: self.ai_process_btn.config(state=tk.NORMAL))
                estimated_cost = self._estimate_ai_cost(uncategorized_count)
                self.root.after(0, lambda: self.cost_label.config(text=f"Estimated Cost: ${estimated_cost:.2f}"))

            # Re-enable ML button
            self.root.after(0, lambda: self.ml_process_btn.config(state=tk.NORMAL))

            self.logger.info(f"ML processing complete: {categorized_count} categorized, {uncategorized_count} uncategorized")

        except Exception as e:
            self.logger.error(f"Error in ML processing: {str(e)}")
            self.root.after(0, lambda: messagebox.showerror("Error", f"ML processing failed: {str(e)}"))
            self.root.after(0, lambda: self.ml_process_btn.config(state=tk.NORMAL))

    def _add_ml_result(self, transaction_data, status):
        """Add ML result to tree view"""
        values = (
            transaction_data['description'][:50] + "..." if len(transaction_data['description']) > 50 else transaction_data['description'],
            transaction_data['amount'],
            transaction_data['date'],
            transaction_data['category'] or "N/A",
            f"{transaction_data['confidence']:.3f}" if transaction_data['confidence'] else "N/A",
            status
        )
        self.ml_tree.insert("", tk.END, values=values)

    def _estimate_ai_cost(self, transaction_count):
        """Estimate AI processing cost"""
        # Rough estimate: $0.001 per transaction (adjust based on actual API costs)
        return transaction_count * 0.001

    def process_ai_categorization(self):
        """Process uncategorized transactions using AI"""
        if not self.uncategorized_transactions:
            messagebox.showinfo("Info", "No uncategorized transactions to process")
            return

        # Confirm cost
        estimated_cost = self._estimate_ai_cost(len(self.uncategorized_transactions))
        if not messagebox.askyesno("Confirm AI Processing",
                                  f"Process {len(self.uncategorized_transactions)} transactions with AI?\n"
                                  f"Estimated cost: ${estimated_cost:.2f}"):
            return

        # Disable button and show progress
        self.ai_process_btn.config(state=tk.DISABLED)
        self.ai_progress.config(maximum=len(self.uncategorized_transactions))

        # Clear previous AI results
        for item in self.ai_tree.get_children():
            self.ai_tree.delete(item)

        # Reset AI data
        self.ai_categorized_transactions = []

        # Process in thread
        thread = threading.Thread(target=self._process_ai_worker)
        thread.daemon = True
        thread.start()

    def _process_ai_worker(self):
        """Worker thread for AI processing"""
        try:
            batch_size = self.batch_size_var.get()
            ai_categorized_count = 0
            still_uncategorized_count = 0

            # Process in batches
            for i in range(0, len(self.uncategorized_transactions), batch_size):
                batch = self.uncategorized_transactions[i:i + batch_size]

                # Prepare batch for AI
                descriptions = [t['description'] for t in batch]

                try:
                    # Get AI categorizations
                    ai_results = self.sambanova_client.categorize_transactions_batch(descriptions)

                    # Process results
                    for j, transaction in enumerate(batch):
                        if j < len(ai_results) and ai_results[j]:
                            # AI categorized successfully
                            ai_result = ai_results[j]
                            transaction['category'] = ai_result.get('category', 'Other')
                            transaction['sub_category'] = ai_result.get('sub_category', 'Miscellaneous')
                            transaction['confidence'] = ai_result.get('confidence', 0.5)
                            transaction['method'] = 'AI'
                            transaction['status'] = 'AI Categorized'

                            self.ai_categorized_transactions.append(transaction)
                            ai_categorized_count += 1

                            # Update UI
                            self.root.after(0, self._add_ai_result, transaction, "AI Categorized")
                        else:
                            # AI failed to categorize
                            transaction['status'] = 'Needs Manual'
                            still_uncategorized_count += 1

                            # Update UI
                            self.root.after(0, self._add_ai_result, transaction, "Needs Manual")

                        # Update progress
                        progress_value = i + j + 1
                        self.root.after(0, lambda v=progress_value: self.ai_progress.config(value=v))

                except Exception as e:
                    self.logger.error(f"AI batch processing error: {str(e)}")
                    # Mark batch as needing manual categorization
                    for transaction in batch:
                        transaction['status'] = 'Needs Manual'
                        still_uncategorized_count += 1
                        self.root.after(0, self._add_ai_result, transaction, "AI Failed")

            # Update uncategorized list (remove AI categorized ones)
            self.uncategorized_transactions = [t for t in self.uncategorized_transactions
                                             if t['status'] != 'AI Categorized']

            # Update summary
            summary_text = f"AI Results: {ai_categorized_count} categorized, {still_uncategorized_count} need manual review"
            self.root.after(0, lambda: self.ai_summary_label.config(text=summary_text))

            # Re-enable AI button
            self.root.after(0, lambda: self.ai_process_btn.config(state=tk.NORMAL))

            self.logger.info(f"AI processing complete: {ai_categorized_count} categorized, {still_uncategorized_count} need manual")

        except Exception as e:
            self.logger.error(f"Error in AI processing: {str(e)}")
            self.root.after(0, lambda: messagebox.showerror("Error", f"AI processing failed: {str(e)}"))
            self.root.after(0, lambda: self.ai_process_btn.config(state=tk.NORMAL))

    def _add_ai_result(self, transaction_data, status):
        """Add AI result to tree view"""
        values = (
            transaction_data['description'][:50] + "..." if len(transaction_data['description']) > 50 else transaction_data['description'],
            transaction_data['amount'],
            transaction_data['date'],
            transaction_data['category'] or "N/A",
            f"{transaction_data['confidence']:.3f}" if transaction_data['confidence'] else "N/A",
            status
        )
        self.ai_tree.insert("", tk.END, values=values)

    def load_categories(self):
        """Load available categories from the system"""
        try:
            # Load from ML categorizer's known categories
            if hasattr(self.ml_categorizer, 'category_encoder') and self.ml_categorizer.category_encoder:
                if hasattr(self.ml_categorizer.category_encoder, 'classes_'):
                    self.available_categories = list(self.ml_categorizer.category_encoder.classes_)
                else:
                    self.available_categories = []
            else:
                self.available_categories = []

            # Add some common categories if none found
            if not self.available_categories:
                self.available_categories = [
                    "Food & Dining", "Transportation", "Shopping", "Bills & Utilities",
                    "Entertainment", "Income", "Other", "EMI", "Online Stores",
                    "Bank charges", "Investment", "Refunds"
                ]

            self.logger.info(f"Loaded {len(self.available_categories)} categories")

        except Exception as e:
            self.logger.error(f"Error loading categories: {str(e)}")
            self.available_categories = ["Other"]

    def load_manual_transactions(self):
        """Load uncategorized transactions for manual review"""
        # Clear manual tree
        for item in self.manual_tree.get_children():
            self.manual_tree.delete(item)

        # Load uncategorized transactions
        uncategorized = [t for t in self.uncategorized_transactions if t['status'] == 'Needs Manual']

        for transaction in uncategorized:
            values = (
                transaction['description'][:50] + "..." if len(transaction['description']) > 50 else transaction['description'],
                transaction['amount'],
                transaction['date'],
                transaction['category'] or "Uncategorized",
                "Select to categorize"
            )
            self.manual_tree.insert("", tk.END, values=values, tags=(transaction['index'],))

        self.manual_summary_label.config(text=f"Loaded {len(uncategorized)} transactions for manual review")

    def apply_manual_category(self):
        """Apply selected category to selected transactions"""
        selected_items = self.manual_tree.selection()
        if not selected_items:
            messagebox.showwarning("Warning", "Please select transactions to categorize")
            return

        category = self.manual_category_var.get()
        if not category:
            messagebox.showwarning("Warning", "Please select a category")
            return

        # Apply category to selected transactions
        for item in selected_items:
            # Get transaction index from tags
            tags = self.manual_tree.item(item, 'tags')
            if tags:
                transaction_index = int(tags[0])

                # Find and update transaction
                for transaction in self.uncategorized_transactions:
                    if transaction['index'] == transaction_index:
                        transaction['category'] = category
                        transaction['sub_category'] = 'Miscellaneous'
                        transaction['confidence'] = 1.0
                        transaction['method'] = 'Manual'
                        transaction['status'] = 'Manual Categorized'

                        # Move to manual categorized list
                        if transaction not in self.manual_categorized_transactions:
                            self.manual_categorized_transactions.append(transaction)
                        break

                # Update tree item
                values = list(self.manual_tree.item(item, 'values'))
                values[3] = category
                values[4] = "Categorized"
                self.manual_tree.item(item, values=values)

        messagebox.showinfo("Success", f"Applied category '{category}' to {len(selected_items)} transactions")

    def generate_export_summary(self):
        """Generate summary of all categorization results"""
        total_transactions = len(self.transactions_df) if self.transactions_df is not None else 0
        ml_categorized = len(self.categorized_transactions)
        ai_categorized = len(self.ai_categorized_transactions)
        manual_categorized = len(self.manual_categorized_transactions)
        still_uncategorized = len([t for t in self.uncategorized_transactions if t['status'] == 'Needs Manual'])

        summary = f"""HYBRID CATEGORIZATION SUMMARY
{'='*50}

Total Transactions: {total_transactions}

PHASE 1 - ML MODEL CATEGORIZATION:
- Successfully categorized: {ml_categorized}
- Confidence threshold: {self.ml_confidence_threshold:.2f}

PHASE 2 - AI CATEGORIZATION:
- Successfully categorized: {ai_categorized}
- Batch size used: {self.ai_batch_size}

PHASE 3 - MANUAL CATEGORIZATION:
- Manually categorized: {manual_categorized}

FINAL RESULTS:
- Total categorized: {ml_categorized + ai_categorized + manual_categorized}
- Still uncategorized: {still_uncategorized}
- Success rate: {((ml_categorized + ai_categorized + manual_categorized) / total_transactions * 100):.1f}%

CATEGORIZATION BREAKDOWN:
"""

        # Add category breakdown
        all_categorized = self.categorized_transactions + self.ai_categorized_transactions + self.manual_categorized_transactions
        category_counts = {}
        for transaction in all_categorized:
            category = transaction['category']
            category_counts[category] = category_counts.get(category, 0) + 1

        for category, count in sorted(category_counts.items()):
            summary += f"- {category}: {count} transactions\n"

        # Update summary text
        self.export_summary_text.delete(1.0, tk.END)
        self.export_summary_text.insert(1.0, summary)

        self.export_status_label.config(text="Summary generated - ready for export")

    def export_to_csv(self):
        """Export categorized transactions to CSV"""
        if not self.transactions_df is not None:
            messagebox.showerror("Error", "No transactions loaded")
            return

        # Combine all categorized transactions
        all_categorized = self.categorized_transactions + self.ai_categorized_transactions + self.manual_categorized_transactions

        if not all_categorized:
            messagebox.showwarning("Warning", "No categorized transactions to export")
            return

        # Create export dataframe
        export_data = []
        for transaction in all_categorized:
            export_data.append({
                'description': transaction['description'],
                'amount': transaction['amount'],
                'date': transaction['date'],
                'category': transaction['category'],
                'sub_category': transaction['sub_category'],
                'confidence': transaction['confidence'],
                'categorization_method': transaction['method']
            })

        export_df = pd.DataFrame(export_data)

        # Save to file
        file_path = filedialog.asksaveasfilename(
            title="Save Categorized Transactions",
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )

        if file_path:
            export_df.to_csv(file_path, index=False)
            messagebox.showinfo("Success", f"Exported {len(export_data)} categorized transactions to {file_path}")
            self.logger.info(f"Exported {len(export_data)} transactions to {file_path}")

    def load_to_main_app(self):
        """Load categorized transactions to main bank analyzer app"""
        messagebox.showinfo("Info", "Integration with main app will be implemented next")
        # This will integrate with the main bank statement analyzer

    def cleanup_temp_files(self):
        """Clean up temporary files"""
        if self.temp_csv_file:
            try:
                temp_path = Path(self.temp_csv_file)
                if temp_path.exists():
                    temp_path.unlink()
                    self.logger.info(f"Cleaned up temporary file: {self.temp_csv_file}")
            except Exception as e:
                self.logger.warning(f"Could not clean up temporary file {self.temp_csv_file}: {e}")

    def on_window_close(self):
        """Handle window close event"""
        self.cleanup_temp_files()
        self.root.destroy()


def main():
    """Main function to run the hybrid categorization system"""
    import tkinter as tk

    root = tk.Tk()
    root.withdraw()  # Hide root window

    app = HybridCategorizationUI()
    app.root.mainloop()


if __name__ == "__main__":
    main()
