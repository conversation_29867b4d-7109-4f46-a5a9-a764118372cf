"""
Main window for Bank Statement Analyzer
Provides the primary interface for analyzing and importing bank statements
"""

from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QTabWidget,
    QPushButton, QLabel, QFileDialog, QProgressBar, QTextEdit,
    QSplitter, QGroupBox, QFormLayout, QMessageBox, QStatusBar,
    QMenuBar, QMenu, QToolBar, QFrame, QDialog
)
from PySide6.QtCore import Qt, Signal, QThread, QTimer
from PySide6.QtGui import QAction, QIcon, QFont, QPixmap

import sys
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime

from ..core.logger import get_logger
from ..parsers.parser_factory import parser_factory
from ..core.categorizer import TransactionCategorizer
from ..core.data_importer import DataImporter
from ..models.transaction import RawTransaction, ProcessedTransaction
from ..ml.enhanced_integrated_categorizer import EnhancedIntegratedCategorizer as IntegratedCategorizer
from .transaction_preview import TransactionPreviewWidget
from .category_manager import CategoryManagerWidget
from .ml_labeling_window import MLLabelingWindow
from .ml_review_window import MLReviewWindow
from .ml_analytics_window import MLAnalyticsWindow
from .ml_category_dialog import MLCategoryDialog
from .ai_review_dialog import AIReviewDialog


class StatementProcessingThread(QThread):
    """Thread for processing bank statements in background"""
    
    progress_updated = Signal(int, str)  # progress percentage, status message
    processing_completed = Signal(list, list)  # raw_transactions, processed_transactions
    error_occurred = Signal(str)  # error message
    
    def __init__(self, file_paths: List[Path], bank_name: str = "Unknown"):
        super().__init__()
        self.file_paths = file_paths
        self.bank_name = bank_name
        self.logger = get_logger(__name__)
    
    def run(self):
        """Run the statement processing"""
        try:
            all_raw_transactions = []
            total_files = len(self.file_paths)
            
            # Parse all files
            for i, file_path in enumerate(self.file_paths):
                self.progress_updated.emit(
                    int((i / total_files) * 50),  # First 50% for parsing
                    f"Parsing {file_path.name}..."
                )
                
                transactions = parser_factory.parse_file(file_path, self.bank_name)
                all_raw_transactions.extend(transactions)
                
                self.logger.info(f"Parsed {len(transactions)} transactions from {file_path}")
            
            # Use basic categorization (no AI) for regular processing
            self.progress_updated.emit(40, "Starting basic categorization...")

            from ..core.categorizer import TransactionCategorizer
            basic_categorizer = TransactionCategorizer()

            # Categorize transactions using basic system (no AI)
            self.progress_updated.emit(50, f"Categorizing {len(all_raw_transactions)} transactions...")

            processed_transactions = []
            for i, raw_txn in enumerate(all_raw_transactions):
                if i % 10 == 0:  # Update progress every 10 transactions
                    progress = 50 + (i * 40) // len(all_raw_transactions)
                    self.progress_updated.emit(progress, f"Categorizing transaction {i+1}/{len(all_raw_transactions)}")

                processed_txn = basic_categorizer.categorize_transaction(raw_txn)
                processed_transactions.append(processed_txn)

            # No training data extraction for basic processing
            self.progress_updated.emit(90, "Finalizing basic categorization...")
            
            self.progress_updated.emit(100, "Processing completed")
            self.processing_completed.emit(all_raw_transactions, processed_transactions)
            
        except Exception as e:
            self.logger.error(f"Error processing statements: {str(e)}")
            self.error_occurred.emit(str(e))


class EnhancedStatementProcessingThread(QThread):
    """Enhanced thread for processing bank statements with AI categorization"""

    progress_updated = Signal(int, str)  # progress percentage, status message
    processing_completed = Signal(list, list)  # raw_transactions, processed_transactions
    error_occurred = Signal(str)  # error message

    def __init__(self, file_paths: List[Path], use_ai: bool = False):
        super().__init__()
        self.file_paths = file_paths
        self.use_ai = use_ai
        self.logger = get_logger(__name__)

    def run(self):
        """Run the enhanced processing with AI"""
        try:
            self.progress_updated.emit(0, "Initializing enhanced processing...")

            # Import required modules
            from ..parsers.parser_factory import parser_factory
            from ..ml.enhanced_integrated_categorizer import EnhancedIntegratedCategorizer

            # Initialize categorizer with config directory
            categorizer = EnhancedIntegratedCategorizer("bank_analyzer_config") if self.use_ai else None

            self.progress_updated.emit(10, "Loading bank statements...")

            # Parse all files using the same parser factory as the original thread
            all_raw_transactions = []
            total_files = len(self.file_paths)

            for i, file_path in enumerate(self.file_paths):
                self.progress_updated.emit(
                    10 + (i * 30) // total_files,
                    f"Parsing {file_path.name}..."
                )

                # Use parser factory with auto-detection (same as original thread)
                transactions = parser_factory.parse_file(file_path, "Unknown")
                all_raw_transactions.extend(transactions)

                self.logger.info(f"Parsed {len(transactions)} transactions from {file_path}")

            self.progress_updated.emit(40, f"Loaded {len(all_raw_transactions)} transactions")

            if self.use_ai and categorizer:
                # Enhanced AI categorization with detailed progress
                self.progress_updated.emit(50, "Initializing AI categorization...")

                # Get processing recommendation
                recommendation = categorizer.get_processing_recommendation(all_raw_transactions)
                self.progress_updated.emit(55, f"AI Analysis: {recommendation.get('reason', 'Ready')}")

                # Create progress tracking function
                def ai_progress_callback(step_name, progress_percent, details=""):
                    # Map AI progress (0-100) to overall progress (60-95)
                    overall_progress = 60 + (progress_percent * 35) // 100
                    message = f"AI: {step_name}"
                    if details:
                        message += f" - {details}"
                    self.progress_updated.emit(overall_progress, message)

                # Process with AI using progress callback
                self.progress_updated.emit(60, "Starting AI categorization...")
                processed_transactions = categorizer.categorize_batch(
                    all_raw_transactions,
                    progress_callback=ai_progress_callback
                )

                self.progress_updated.emit(95, "Finalizing AI results...")

                # Get final statistics
                stats = categorizer.get_system_status()
                ai_count = sum(1 for t in processed_transactions if hasattr(t, 'notes') and 'AI' in str(t.notes))
                cached_count = sum(1 for t in processed_transactions if hasattr(t, 'notes') and 'cache' in str(t.notes).lower())

                self.progress_updated.emit(
                    98,
                    f"✅ AI: {ai_count}, Cached: {cached_count}, Manual: {len(processed_transactions) - ai_count - cached_count}"
                )

            else:
                # Basic categorization
                self.progress_updated.emit(60, "Applying basic categorization...")
                from ..core.categorizer import TransactionCategorizer
                basic_categorizer = TransactionCategorizer()

                processed_transactions = []
                for i, raw_txn in enumerate(all_raw_transactions):
                    if i % 10 == 0:  # Update progress every 10 transactions
                        progress = 60 + (i * 35) // len(all_raw_transactions)
                        self.progress_updated.emit(progress, f"Categorizing transaction {i+1}/{len(all_raw_transactions)}")

                    processed_txn = basic_categorizer.categorize_transaction(raw_txn)
                    processed_transactions.append(processed_txn)

            self.progress_updated.emit(100, "Processing completed!")

            # Emit completion signal
            self.processing_completed.emit(all_raw_transactions, processed_transactions)

        except Exception as e:
            self.logger.error(f"Enhanced processing failed: {str(e)}", exc_info=True)
            self.error_occurred.emit(f"Enhanced processing failed: {str(e)}")


class BankAnalyzerMainWindow(QMainWindow):
    """
    Main window for the Bank Statement Analyzer application
    """
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger(__name__)
        
        # Initialize components
        self.categorizer = TransactionCategorizer()
        self.integrated_categorizer = IntegratedCategorizer()
        self.data_importer = DataImporter()
        self.processing_thread = None

        # Create status bar for budget monitoring
        self.create_status_bar()

        # ML windows
        self.ml_labeling_window = None
        self.ml_review_window = None
        self.ml_analytics_window = None
        
        # Data
        self.raw_transactions = []
        self.processed_transactions = []
        self.categories = {}
        
        self.setup_ui()
        self.setup_connections()
        self.load_initial_data()
        
        self.logger.info("Bank Analyzer main window initialized")
    
    def setup_ui(self):
        """Setup the main window UI"""
        self.setWindowTitle("🤖 Bank Statement Analyzer with Hybrid ML")
        self.setGeometry(100, 100, 1200, 800)
        
        # Create menu bar
        self.create_menu_bar()
        
        # Create toolbar
        self.create_toolbar()
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        
        # Create header section
        self.create_header_section(main_layout)
        
        # Create main content area with tabs
        self.create_main_content(main_layout)
        
        # Create status bar
        self.create_status_bar()
    
    def create_menu_bar(self):
        """Create menu bar"""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu("File")
        
        open_action = QAction("Open Statements...", self)
        open_action.setShortcut("Ctrl+O")
        open_action.triggered.connect(self.select_files)
        file_menu.addAction(open_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction("Exit", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # Tools menu
        tools_menu = menubar.addMenu("Tools")

        validate_action = QAction("Validate Data Structure", self)
        validate_action.triggered.connect(self.validate_data_structure)
        tools_menu.addAction(validate_action)



        # ML menu
        ml_menu = menubar.addMenu("Machine Learning")

        # Labeling workflows section
        labeling_menu = ml_menu.addMenu("📝 Transaction Labeling")

        manual_labeling_action = QAction("🤖 Manual Labeling with AI Assistant", self)
        manual_labeling_action.triggered.connect(self.open_ml_labeling)
        manual_labeling_action.setToolTip("Primary manual labeling workflow with integrated AI assistance")
        labeling_menu.addAction(manual_labeling_action)

        ai_labeling_action = QAction("🚀 Advanced AI-Assisted Labeling", self)
        ai_labeling_action.triggered.connect(self.open_enhanced_ml_labeling)
        ai_labeling_action.setToolTip("Advanced AI labeling interface with multiple operation modes")
        labeling_menu.addAction(ai_labeling_action)

        ml_menu.addSeparator()

        # AI Processing section
        ai_processing_menu = ml_menu.addMenu("🤖 AI Processing")

        process_ai_action = QAction("Process with AI", self)
        process_ai_action.triggered.connect(self.process_files_with_ai)
        process_ai_action.setToolTip("Process bank statements using AI categorization")
        ai_processing_menu.addAction(process_ai_action)

        ai_review_action = QAction("AI Review & Approval", self)
        ai_review_action.triggered.connect(self.show_ai_review)
        ai_review_action.setToolTip("Review and approve AI categorization suggestions")
        ai_processing_menu.addAction(ai_review_action)

        hybrid_categorization_action = QAction("🔄 Hybrid Categorization System", self)
        hybrid_categorization_action.triggered.connect(self.open_hybrid_categorization)
        hybrid_categorization_action.setToolTip("Three-phase categorization: ML Model → AI → Manual")
        ai_processing_menu.addAction(hybrid_categorization_action)

        ml_menu.addSeparator()

        # Model Management section
        model_menu = ml_menu.addMenu("🤖 Model Management")

        review_action = QAction("Review Predictions", self)
        review_action.triggered.connect(self.open_ml_review)
        model_menu.addAction(review_action)

        train_action = QAction("Train Model", self)
        train_action.triggered.connect(self.trigger_model_training)
        model_menu.addAction(train_action)

        model_menu.addSeparator()

        # Operation Mode submenu under Model Management
        operation_mode_menu = model_menu.addMenu("🔧 Operation Mode")

        ai_first_action = QAction("🚀 AI First", self)
        ai_first_action.triggered.connect(lambda: self.switch_operation_mode("ai_first"))
        operation_mode_menu.addAction(ai_first_action)

        ml_first_action = QAction("🤖 ML First", self)
        ml_first_action.triggered.connect(lambda: self.switch_operation_mode("ml_first"))
        operation_mode_menu.addAction(ml_first_action)

        hybrid_action = QAction("⚡ Hybrid", self)
        hybrid_action.triggered.connect(lambda: self.switch_operation_mode("hybrid"))
        operation_mode_menu.addAction(hybrid_action)

        manual_only_action = QAction("✋ Manual Only", self)
        manual_only_action.triggered.connect(lambda: self.switch_operation_mode("manual_only"))
        operation_mode_menu.addAction(manual_only_action)

        ml_menu.addSeparator()

        # Analytics and Monitoring section
        analytics_menu = ml_menu.addMenu("📊 Analytics & Monitoring")

        analytics_action = QAction("Analytics Dashboard", self)
        analytics_action.triggered.connect(self.open_ml_analytics)
        analytics_menu.addAction(analytics_action)

        system_status_action = QAction("System Status", self)
        system_status_action.triggered.connect(self.show_ml_status)
        analytics_menu.addAction(system_status_action)

        analytics_menu.addSeparator()

        # AI Statistics submenu under Analytics
        ai_stats_menu = analytics_menu.addMenu("🤖 AI Statistics")

        budget_status_action = QAction("Budget Status", self)
        budget_status_action.triggered.connect(self.show_budget_status)
        ai_stats_menu.addAction(budget_status_action)

        filtering_stats_action = QAction("Filtering Statistics", self)
        filtering_stats_action.triggered.connect(self.show_filtering_stats)
        ai_stats_menu.addAction(filtering_stats_action)

        category_stats_action = QAction("Category Creation Summary", self)
        category_stats_action.triggered.connect(self.show_category_stats)
        ai_stats_menu.addAction(category_stats_action)

        hybrid_stats_action = QAction("Hybrid ML Statistics", self)
        hybrid_stats_action.triggered.connect(self.show_hybrid_ml_stats)
        ai_stats_menu.addAction(hybrid_stats_action)

        uncertain_txns_action = QAction("Uncertain Transactions", self)
        uncertain_txns_action.triggered.connect(self.show_uncertain_transactions)
        ai_stats_menu.addAction(uncertain_txns_action)

        training_quality_action = QAction("Training Data Quality", self)
        training_quality_action.triggered.connect(self.show_training_quality)
        ai_stats_menu.addAction(training_quality_action)

        ml_menu.addSeparator()

        # Category management section
        category_mgmt_action = QAction("Manage Categories", self)
        category_mgmt_action.triggered.connect(self.open_category_manager)
        ml_menu.addAction(category_mgmt_action)
        
        # Help menu
        help_menu = menubar.addMenu("Help")
        
        about_action = QAction("About", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def create_toolbar(self):
        """Create toolbar"""
        toolbar = self.addToolBar("Main")
        
        # Open statements button
        open_btn = QPushButton("Open Statements")
        open_btn.clicked.connect(self.select_files)
        toolbar.addWidget(open_btn)
        
        toolbar.addSeparator()
        
        # Progress bar (initially hidden)
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumWidth(200)
        toolbar.addWidget(self.progress_bar)
        
        # Progress label
        self.progress_label = QLabel()
        self.progress_label.setVisible(False)
        toolbar.addWidget(self.progress_label)


    
    def create_header_section(self, layout):
        """Create enhanced header section with AI indicators"""
        header_frame = QFrame()
        header_frame.setFrameStyle(QFrame.StyledPanel)
        header_layout = QVBoxLayout(header_frame)

        # Title with AI status
        title_section = QHBoxLayout()
        title_label = QLabel("🤖 Bank Statement Analyzer with Hybrid ML")
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_section.addWidget(title_label)

        # AI System Status Indicator
        self.ai_status_indicator = QLabel("🤖 AI: Ready")
        self.ai_status_indicator.setStyleSheet("""
            QLabel {
                background-color: #e8f5e8;
                color: #27ae60;
                border: 1px solid #27ae60;
                border-radius: 4px;
                padding: 5px 10px;
                font-weight: bold;
            }
        """)
        title_section.addWidget(self.ai_status_indicator)
        title_section.addStretch()
        header_layout.addLayout(title_section)

        # Subtitle
        subtitle_label = QLabel("Hybrid AI-ML workflow for intelligent transaction categorization")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setStyleSheet("color: gray; font-style: italic;")
        header_layout.addWidget(subtitle_label)

        # AI Statistics Dashboard
        self.create_ai_dashboard(header_layout)

        layout.addWidget(header_frame)

    def create_ai_dashboard(self, layout):
        """Create AI statistics dashboard"""
        dashboard_frame = QFrame()
        dashboard_frame.setFrameStyle(QFrame.Box)
        dashboard_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                margin: 5px;
            }
        """)
        dashboard_layout = QHBoxLayout(dashboard_frame)

        # Merchant Cache Stats
        cache_group = QGroupBox("🏪 Merchant Cache")
        cache_layout = QVBoxLayout(cache_group)
        self.cache_patterns_label = QLabel("Patterns: Loading...")
        self.cache_hit_rate_label = QLabel("Hit Rate: Loading...")
        self.cache_savings_label = QLabel("Savings: Loading...")
        cache_layout.addWidget(self.cache_patterns_label)
        cache_layout.addWidget(self.cache_hit_rate_label)
        cache_layout.addWidget(self.cache_savings_label)
        dashboard_layout.addWidget(cache_group)

        # Cost Tracking
        cost_group = QGroupBox("💰 Cost Tracking")
        cost_layout = QVBoxLayout(cost_group)
        self.session_cost_label = QLabel("Session: $0.00")
        self.total_cost_label = QLabel("Total: Loading...")
        self.budget_remaining_label = QLabel("Budget: Loading...")
        cost_layout.addWidget(self.session_cost_label)
        cost_layout.addWidget(self.total_cost_label)
        cost_layout.addWidget(self.budget_remaining_label)
        dashboard_layout.addWidget(cost_group)

        # Processing Stats
        processing_group = QGroupBox("⚡ Processing")
        processing_layout = QVBoxLayout(processing_group)
        self.ai_processed_label = QLabel("AI: 0")
        self.cached_processed_label = QLabel("Cached: 0")
        self.manual_required_label = QLabel("Manual: 0")
        processing_layout.addWidget(self.ai_processed_label)
        processing_layout.addWidget(self.cached_processed_label)
        processing_layout.addWidget(self.manual_required_label)
        dashboard_layout.addWidget(processing_group)

        # System Health
        health_group = QGroupBox("🔧 System Health")
        health_layout = QVBoxLayout(health_group)
        self.ai_available_label = QLabel("AI: Checking...")
        self.patterns_learned_label = QLabel("Learned: 0")
        self.last_update_label = QLabel("Updated: Never")
        health_layout.addWidget(self.ai_available_label)
        health_layout.addWidget(self.patterns_learned_label)
        health_layout.addWidget(self.last_update_label)
        dashboard_layout.addWidget(health_group)

        layout.addWidget(dashboard_frame)

    def create_main_content(self, layout):
        """Create main content area with tabs"""
        self.tab_widget = QTabWidget()
        
        # File Selection Tab
        self.create_file_selection_tab()
        
        # Transaction Preview Tab
        self.create_preview_tab()
        
        # Category Management Tab
        self.create_category_tab()
        
        # Import Tab
        self.create_import_tab()
        
        layout.addWidget(self.tab_widget)
    
    def create_file_selection_tab(self):
        """Create file selection tab"""
        file_widget = QWidget()
        file_layout = QVBoxLayout(file_widget)
        
        # Instructions
        instructions = QLabel("""
        <h3>Select Bank Statement Files</h3>
        <p>Choose one or more bank statement files to analyze. Supported formats:</p>
        <ul>
        <li><b>PDF</b> - Bank statement PDFs (requires text extraction)</li>
        <li><b>CSV</b> - Comma-separated values files</li>
        <li><b>Excel</b> - .xlsx and .xls files</li>
        </ul>
        <p>The application will automatically detect the format and parse transactions.</p>
        """)
        instructions.setWordWrap(True)
        file_layout.addWidget(instructions)
        
        # File selection area
        file_selection_group = QGroupBox("File Selection")
        file_selection_layout = QVBoxLayout(file_selection_group)
        
        # Selected files list
        self.selected_files_list = QTextEdit()
        self.selected_files_list.setMaximumHeight(150)
        self.selected_files_list.setPlaceholderText("No files selected")
        file_selection_layout.addWidget(self.selected_files_list)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.select_files_btn = QPushButton("Select Files...")
        self.select_files_btn.clicked.connect(self.select_files)
        button_layout.addWidget(self.select_files_btn)
        
        self.clear_files_btn = QPushButton("Clear")
        self.clear_files_btn.clicked.connect(self.clear_files)
        button_layout.addWidget(self.clear_files_btn)
        
        button_layout.addStretch()
        
        self.process_files_btn = QPushButton("📄 Process Files")
        self.process_files_btn.clicked.connect(self.process_files)
        self.process_files_btn.setEnabled(False)
        self.process_files_btn.setToolTip("Process files with categorization")
        self.process_files_btn.setStyleSheet("QPushButton { background-color: #2196F3; color: white; font-weight: bold; }")
        button_layout.addWidget(self.process_files_btn)
        
        file_selection_layout.addLayout(button_layout)
        file_layout.addWidget(file_selection_group)
        
        file_layout.addStretch()
        
        self.tab_widget.addTab(file_widget, "1. Select Files")
        self.selected_file_paths = []
    
    def create_preview_tab(self):
        """Create transaction preview tab"""
        self.preview_widget = TransactionPreviewWidget()
        self.preview_widget.transactions_ready.connect(self.on_transactions_ready_for_import)
        
        self.tab_widget.addTab(self.preview_widget, "2. Preview & Edit")
        self.tab_widget.setTabEnabled(1, False)  # Initially disabled
    
    def create_category_tab(self):
        """Create category management tab"""
        self.category_widget = CategoryManagerWidget()
        self.category_widget.categories_updated.connect(self.on_categories_updated)
        
        self.tab_widget.addTab(self.category_widget, "3. Manage Categories")
    
    def create_import_tab(self):
        """Create import tab"""
        import_widget = QWidget()
        import_layout = QVBoxLayout(import_widget)
        
        # Import summary
        summary_group = QGroupBox("Import Summary")
        summary_layout = QFormLayout(summary_group)
        
        self.import_total_label = QLabel("0")
        self.import_amount_label = QLabel("₹0.00")
        self.import_date_range_label = QLabel("N/A")
        self.import_categories_label = QLabel("0")
        
        summary_layout.addRow("Total Transactions:", self.import_total_label)
        summary_layout.addRow("Total Amount:", self.import_amount_label)
        summary_layout.addRow("Date Range:", self.import_date_range_label)
        summary_layout.addRow("New Categories:", self.import_categories_label)
        
        import_layout.addWidget(summary_group)
        
        # Import options
        options_group = QGroupBox("Import Options")
        options_layout = QVBoxLayout(options_group)
        
        from PySide6.QtWidgets import QCheckBox
        self.create_backup_checkbox = QCheckBox("Create backup before import")
        self.create_backup_checkbox.setChecked(True)
        options_layout.addWidget(self.create_backup_checkbox)
        
        self.create_categories_checkbox = QCheckBox("Create missing categories automatically")
        self.create_categories_checkbox.setChecked(True)
        options_layout.addWidget(self.create_categories_checkbox)
        
        import_layout.addWidget(options_group)
        
        # Import button
        import_button_layout = QHBoxLayout()
        import_button_layout.addStretch()
        
        self.final_import_btn = QPushButton("Import to Main Application")
        self.final_import_btn.clicked.connect(self.perform_final_import)
        self.final_import_btn.setEnabled(False)
        self.final_import_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; padding: 10px; }")
        import_button_layout.addWidget(self.final_import_btn)
        
        import_layout.addLayout(import_button_layout)
        import_layout.addStretch()
        
        self.tab_widget.addTab(import_widget, "4. Import")
        self.tab_widget.setTabEnabled(3, False)  # Initially disabled

    def create_status_bar(self):
        """Create status bar"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Ready")

    def setup_connections(self):
        """Setup signal connections"""
        pass  # Connections are set up in individual methods

    def load_initial_data(self):
        """Load initial data"""
        try:
            # Load categories from main application
            self.categories = self.categorizer.get_categories()
            self.category_widget.set_categories(self.categories)

            self.logger.info("Initial data loaded successfully")
        except Exception as e:
            self.logger.error(f"Error loading initial data: {str(e)}")
            QMessageBox.warning(self, "Warning", f"Could not load initial data: {str(e)}")

    def select_files(self):
        """Select bank statement files"""
        file_dialog = QFileDialog(self)
        file_dialog.setFileMode(QFileDialog.ExistingFiles)
        file_dialog.setNameFilter("Bank Statements (*.pdf *.csv *.xlsx *.xls);;All Files (*)")
        file_dialog.setDirectory(str(Path.cwd() / "statements"))  # Default to statements folder

        if file_dialog.exec():
            self.selected_file_paths = [Path(path) for path in file_dialog.selectedFiles()]
            self.update_selected_files_display()
            self.process_files_btn.setEnabled(len(self.selected_file_paths) > 0)

    def clear_files(self):
        """Clear selected files"""
        self.selected_file_paths = []
        self.update_selected_files_display()
        self.process_files_btn.setEnabled(False)

    def update_selected_files_display(self):
        """Update the selected files display"""
        if not self.selected_file_paths:
            self.selected_files_list.setPlainText("No files selected")
        else:
            file_info = []
            for file_path in self.selected_file_paths:
                try:
                    size_mb = file_path.stat().st_size / (1024 * 1024)
                    file_info.append(f"{file_path.name} ({size_mb:.1f} MB)")
                except Exception:
                    file_info.append(f"{file_path.name} (size unknown)")

            self.selected_files_list.setPlainText("\n".join(file_info))

    def process_files(self):
        """Process selected files"""
        if not self.selected_file_paths:
            QMessageBox.warning(self, "No Files", "Please select files to process.")
            return

        # Validate files
        invalid_files = []
        for file_path in self.selected_file_paths:
            validation = parser_factory.validate_file_format(file_path)
            if not validation['is_valid']:
                invalid_files.append(f"{file_path.name}: {', '.join(validation['errors'])}")

        if invalid_files:
            QMessageBox.warning(self, "Invalid Files",
                              f"The following files cannot be processed:\n\n" + "\n".join(invalid_files))
            return

        # Start processing in background thread
        self.start_processing()

    def start_processing(self):
        """Start processing files in background thread"""
        self.processing_thread = StatementProcessingThread(self.selected_file_paths)
        self.processing_thread.progress_updated.connect(self.on_processing_progress)
        self.processing_thread.processing_completed.connect(self.on_processing_completed)
        self.processing_thread.error_occurred.connect(self.on_processing_error)

        # Update UI
        self.progress_bar.setVisible(True)
        self.progress_label.setVisible(True)
        self.process_files_btn.setEnabled(False)
        self.status_bar.showMessage("Processing files...")

        # Start thread
        self.processing_thread.start()

    def process_files_with_ai(self):
        """Process files with enhanced AI categorization (accessed via ML menu)"""
        if not self.selected_file_paths:
            QMessageBox.warning(self, "No Files", "Please select bank statement files first.")
            return

        # Create enhanced processing thread with AI
        self.processing_thread = EnhancedStatementProcessingThread(self.selected_file_paths, use_ai=True)

        # Connect signals for AI progress
        self.processing_thread.progress_updated.connect(self.on_processing_progress)
        self.processing_thread.processing_completed.connect(self.on_ai_processing_completed)
        self.processing_thread.error_occurred.connect(self.on_processing_error)

        # Update main UI
        self.progress_bar.setVisible(True)
        self.progress_label.setVisible(True)
        self.process_files_btn.setEnabled(False)
        self.status_bar.showMessage("💰 AI Processing...")

        # Start processing
        self.processing_thread.start()

    def on_ai_processing_completed(self, raw_transactions: List[RawTransaction],
                                  processed_transactions: List[ProcessedTransaction]):
        """Handle AI processing completion with enhanced features"""
        self.raw_transactions = raw_transactions
        self.processed_transactions = processed_transactions

        # Update UI
        self.progress_bar.setVisible(False)
        self.progress_label.setVisible(False)
        if hasattr(self, 'cost_indicator'):
            self.cost_indicator.setVisible(False)
        self.process_files_btn.setEnabled(True)

        # Enable preview tab and switch to it
        self.tab_widget.setTabEnabled(1, True)
        self.preview_widget.set_transactions(processed_transactions, self.categories)
        self.tab_widget.setCurrentIndex(1)

        # Enable import functionality
        self.tab_widget.setTabEnabled(3, True)
        self.final_import_btn.setEnabled(True)

        # Update AI dashboard
        self.update_status_bar()
        self.update_ai_dashboard()

        # Calculate final statistics for message box
        ai_count = sum(1 for t in processed_transactions if hasattr(t, 'notes') and 'AI' in str(t.notes))
        cached_count = sum(1 for t in processed_transactions if hasattr(t, 'notes') and 'cache' in str(t.notes).lower())
        manual_count = len(processed_transactions) - ai_count - cached_count

        # Show success message with AI statistics
        QMessageBox.information(
            self, "AI Processing Complete",
            f"Successfully processed {len(processed_transactions)} transactions!\n\n"
            f"📊 Processing Breakdown:\n"
            f"• AI Categorized: {ai_count}\n"
            f"• Cache Processed: {cached_count}\n"
            f"• Manual Required: {manual_count}\n\n"
            f"Use 'Machine Learning > 🤖 AI Review & Approval' to review AI suggestions."
        )

        self.status_bar.showMessage("AI processing completed successfully")



    def on_processing_progress(self, percentage: int, message: str):
        """Handle processing progress updates with AI indicators"""
        self.progress_bar.setValue(percentage)
        self.progress_label.setText(message)
        self.status_bar.showMessage(message)

    def on_ai_detailed_progress(self, percentage: int, message: str):
        """Handle detailed AI processing progress updates"""
        # Update main progress bar
        self.progress_bar.setValue(percentage)
        self.progress_label.setText(message)
        self.status_bar.showMessage(message)

        # Update AI progress dialog if it exists
        if hasattr(self, 'ai_progress_dialog') and self.ai_progress_dialog:
            # Parse AI-specific progress information
            if "AI:" in message:
                # Extract AI step information
                ai_part = message.split("AI:", 1)[1].strip()
                if " - " in ai_part:
                    step, details = ai_part.split(" - ", 1)
                    self.ai_progress_dialog.update_step(step.strip(), percentage)

                    # Update statistics if available
                    if "AI:" in details and "Cached:" in details and "Manual:" in details:
                        # Parse statistics from details
                        try:
                            parts = details.split(",")
                            ai_count = int(parts[0].split(":")[1].strip())
                            cache_count = int(parts[1].split(":")[1].strip())
                            manual_count = int(parts[2].split(":")[1].strip())

                            stats = {
                                'ai_categorized': ai_count,
                                'cache_used': cache_count,
                                'manual_required': manual_count
                            }
                            self.ai_progress_dialog.update_statistics(stats)
                        except:
                            pass
                else:
                    self.ai_progress_dialog.update_step(ai_part, percentage)
            else:
                self.ai_progress_dialog.update_step(message, percentage)



        # Update processing status in status bar
        self.processing_status_label.setText(f"⚡ Processing ({percentage}%)")
        self.processing_status_label.setStyleSheet("color: blue;")

        # Show cost indicator if processing with AI
        if "AI" in message or "Categorizing" in message:
            if hasattr(self, 'cost_indicator'):
                self.cost_indicator.setText("💰 AI Processing...")
                self.cost_indicator.setVisible(True)

        # Update AI dashboard during processing
        if percentage % 25 == 0:  # Update every 25%
            QTimer.singleShot(100, self.update_ai_dashboard)

    def on_processing_completed(self, raw_transactions: List[RawTransaction],
                              processed_transactions: List[ProcessedTransaction]):
        """Handle processing completion with enhanced AI statistics"""
        self.raw_transactions = raw_transactions
        self.processed_transactions = processed_transactions

        # Update UI
        self.progress_bar.setVisible(False)
        self.progress_label.setVisible(False)
        if hasattr(self, 'cost_indicator'):
            self.cost_indicator.setVisible(False)
        self.process_files_btn.setEnabled(True)

        # Enable preview tab and switch to it
        self.tab_widget.setTabEnabled(1, True)
        self.preview_widget.set_transactions(processed_transactions, self.categories)
        self.tab_widget.setCurrentIndex(1)

        # Get enhanced processing statistics
        try:
            system_status = self.integrated_categorizer.get_system_status()
            ai_coordinator_status = system_status.get('ai_coordinator_status', {})
            coordinator_stats = ai_coordinator_status.get('coordinator_stats', {})

            # Calculate processing breakdown
            ai_processed = 0
            cached_processed = 0
            manual_required = 0

            for txn in processed_transactions:
                if "AI" in txn.notes or "SambaNova" in txn.notes:
                    ai_processed += 1
                elif "cache" in txn.notes.lower() or "merchant" in txn.notes.lower():
                    cached_processed += 1
                else:
                    manual_required += 1

            # Update processing stats in dashboard
            self.ai_processed_label.setText(f"AI: {ai_processed}")
            self.cached_processed_label.setText(f"Cached: {cached_processed}")
            self.manual_required_label.setText(f"Manual: {manual_required}")

            # Update session cost
            session_cost = coordinator_stats.get('total_cost', 0)
            self.session_cost_label.setText(f"Session: ${session_cost:.4f}")
            if session_cost > 0:
                self.session_cost_label.setStyleSheet("color: red; font-weight: bold;")
            else:
                self.session_cost_label.setStyleSheet("color: green; font-weight: bold;")

            # Create enhanced completion message
            cost_info = f"Cost: ${session_cost:.4f}" if session_cost > 0 else "Cost: $0.00 (cached)"
            breakdown_info = f"AI: {ai_processed}, Cached: {cached_processed}, Manual: {manual_required}"

            completion_message = (
                f"✅ Successfully processed {len(processed_transactions)} transactions\n\n"
                f"Processing Breakdown:\n"
                f"• {breakdown_info}\n"
                f"• {cost_info}\n\n"
                f"Please review and edit them in the Preview tab."
            )

        except Exception as e:
            self.logger.error(f"Error getting processing statistics: {str(e)}")
            completion_message = (
                f"Successfully processed {len(processed_transactions)} transactions.\n"
                f"Please review and edit them in the Preview tab."
            )

        # Update status bar with processing results and budget info
        self.status_bar.showMessage(f"✅ Processed {len(processed_transactions)} transactions")
        self.processing_status_label.setText("⚡ Complete")
        self.processing_status_label.setStyleSheet("color: green;")

        # Refresh all status information
        self.update_status_bar()
        self.update_ai_dashboard()

        QMessageBox.information(self, "Processing Complete", completion_message)



    def on_processing_error(self, error_message: str):
        """Handle processing errors"""
        self.progress_bar.setVisible(False)
        self.progress_label.setVisible(False)
        self.process_files_btn.setEnabled(True)

        self.status_bar.showMessage("Processing failed")

        QMessageBox.critical(self, "Processing Error",
                           f"Failed to process files:\n\n{error_message}")

    def on_categories_updated(self, categories: Dict[str, List[str]]):
        """Handle category updates"""
        self.categories = categories
        self.categorizer.categories = categories

        # Update preview widget if it has transactions
        if self.processed_transactions:
            self.preview_widget.set_transactions(self.processed_transactions, self.categories)

    def on_transactions_ready_for_import(self, transactions: List[ProcessedTransaction]):
        """Handle transactions ready for import"""
        self.processed_transactions = transactions

        # Update import summary
        self.update_import_summary()

        # Enable import tab and switch to it
        self.tab_widget.setTabEnabled(3, True)
        self.final_import_btn.setEnabled(True)
        self.tab_widget.setCurrentIndex(3)

    def update_import_summary(self):
        """Update import summary display"""
        if not self.processed_transactions:
            return

        # Get import preview
        preview = self.data_importer.get_import_preview(self.processed_transactions)

        self.import_total_label.setText(str(preview['valid_transactions']))
        self.import_amount_label.setText(f"₹{preview['total_amount']:.2f}")

        if preview['date_range']['start'] and preview['date_range']['end']:
            date_range = f"{preview['date_range']['start'].strftime('%Y-%m-%d')} to {preview['date_range']['end'].strftime('%Y-%m-%d')}"
        else:
            date_range = "N/A"
        self.import_date_range_label.setText(date_range)

        self.import_categories_label.setText(str(len(preview['new_categories'])))

    def perform_final_import(self):
        """Perform the final import to main application"""
        if not self.processed_transactions:
            QMessageBox.warning(self, "No Data", "No transactions to import.")
            return

        # Confirm import
        reply = QMessageBox.question(
            self, "Confirm Import",
            f"Are you sure you want to import {len(self.processed_transactions)} transactions "
            f"into the main application?",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        # Perform import
        try:
            result = self.data_importer.import_transactions(
                self.processed_transactions,
                create_backup=self.create_backup_checkbox.isChecked(),
                create_categories=self.create_categories_checkbox.isChecked()
            )

            if result['success']:
                message = f"Successfully imported {result['imported_count']} transactions."
                if result['skipped_count'] > 0:
                    message += f"\n{result['skipped_count']} transactions were skipped."
                if result['backup_path']:
                    message += f"\nBackup created at: {result['backup_path']}"

                QMessageBox.information(self, "Import Successful", message)

                # Reset the application
                self.reset_application()

            else:
                error_msg = "Import failed:\n" + "\n".join(result['errors'])
                QMessageBox.critical(self, "Import Failed", error_msg)

        except Exception as e:
            self.logger.error(f"Import error: {str(e)}")
            QMessageBox.critical(self, "Import Error", f"An error occurred during import:\n{str(e)}")

    def reset_application(self):
        """Reset application to initial state"""
        self.selected_file_paths = []
        self.raw_transactions = []
        self.processed_transactions = []

        self.update_selected_files_display()
        self.process_files_btn.setEnabled(False)

        self.tab_widget.setTabEnabled(1, False)
        self.tab_widget.setTabEnabled(3, False)
        self.final_import_btn.setEnabled(False)

        self.tab_widget.setCurrentIndex(0)
        self.status_bar.showMessage("Ready")

    def validate_data_structure(self):
        """Validate main application data structure"""
        validation = self.data_importer.validate_main_app_structure()

        if validation['is_valid']:
            QMessageBox.information(self, "Validation Successful",
                                  "Main application data structure is valid.")
        else:
            error_msg = "Validation failed:\n" + "\n".join(validation['errors'])
            if validation['warnings']:
                error_msg += "\n\nWarnings:\n" + "\n".join(validation['warnings'])
            QMessageBox.warning(self, "Validation Failed", error_msg)

    def show_about(self):
        """Show about dialog"""
        QMessageBox.about(self, "About Bank Statement Analyzer",
                         """
                         <h3>Bank Statement Analyzer v1.0.0</h3>
                         <p>A comprehensive tool for analyzing and importing bank statements
                         into your Personal Finance Dashboard.</p>

                         <p><b>Features:</b></p>
                         <ul>
                         <li>Parse PDF, CSV, and Excel bank statements</li>
                         <li>Automatic transaction categorization</li>
                         <li>Preview and edit transactions before import</li>
                         <li>Category management</li>
                         <li>Data validation and backup</li>
                         </ul>

                         <p>Built with PySide6 and Python.</p>
                         """)

    def show_about(self):
        """Show about dialog"""
        QMessageBox.about(self, "About Bank Statement Analyzer",
                         """
                         <h3>Bank Statement Analyzer v1.0.0</h3>
                         <p>A comprehensive tool for analyzing and importing bank statements
                         into your Personal Finance Dashboard.</p>

                         <p><b>Features:</b></p>
                         <ul>
                         <li>Parse PDF, CSV, and Excel bank statements</li>
                         <li>Automatic transaction categorization</li>
                         <li>Preview and edit transactions before import</li>
                         <li>Category management</li>
                         <li>Data validation and backup</li>
                         </ul>

                         <p>Built with PySide6 and Python.</p>
                         """)

    def show_ml_status(self):
        """Show ML system status"""
        try:
            status = self.integrated_categorizer.get_system_status()

            # Get enhanced features status
            enhanced_features = status.get('enhanced_features', {})

            # Try to get budget status
            budget_text = ""
            try:
                budget_status = self.integrated_categorizer.get_budget_status()
                budget_text = f"""
            <b>💰 Budget Status:</b><br>
            • Status: {budget_status.get('warning_level', 'Unknown').title()}<br>
            • Total Budget: ${budget_status.get('total_budget', 0):.2f}<br>
            • Used: ${budget_status.get('total_used', 0):.6f}<br>
            • Remaining: ${budget_status.get('total_remaining', 0):.6f}<br>
            • Daily Remaining: ${budget_status.get('daily_remaining', 0):.6f}<br>
            • Usage: {budget_status.get('total_usage_pct', 0):.1f}%<br>
                """
            except:
                budget_text = """
            <b>💰 Budget Status:</b><br>
            • Budget protection not available<br>
                """

            status_text = f"""
            <h3>🚀 Enhanced AI Categorization System Status</h3>

            <b>✨ Enhanced Features:</b><br>
            • Smart Filtering: {'✅ Enabled' if enhanced_features.get('smart_filtering') else '❌ Disabled'}<br>
            • Batch Processing: {'✅ Enabled' if enhanced_features.get('batch_processing') else '❌ Disabled'}<br>
            • Budget Protection: {'✅ Enabled' if enhanced_features.get('budget_protection') else '❌ Disabled'}<br>
            • Dynamic Categories: {'✅ Enabled' if enhanced_features.get('dynamic_categories') else '❌ Disabled'}<br>
            • Transaction Prioritization: {'✅ Enabled' if enhanced_features.get('transaction_prioritization') else '❌ Disabled'}<br>
            {budget_text}
            <b>🤖 Model Status:</b><br>
            • ML Available: {'Yes' if status['ml_available'] else 'No'}<br>
            • Model Trained: {'Yes' if status['ml_trained'] else 'No'}<br>
            • Model Version: {status['model_version']}<br>

            <b>🔄 Training Status:</b><br>
            • Training in Progress: {'Yes' if status['training_in_progress'] else 'No'}<br>
            • Auto-retrain Enabled: {'Yes' if status['auto_retrain_enabled'] else 'No'}<br>
            • Can Auto-retrain: {'Yes' if status['can_auto_retrain'] else 'No'}<br>

            <b>📊 Data Status:</b><br>
            • Total Unique Transactions: {status['total_unique_transactions']}<br>
            • Labeled Transactions: {status['labeled_transactions']}<br>
            • Labeling Progress: {status['labeling_progress']:.1f}%<br>

            <b>📈 Categorization Statistics:</b><br>
            • Total Categorized: {status['categorization_stats']['total_categorized']}<br>
            • ML Used: {status['categorization_stats']['ml_used']}<br>
            • Rules Used: {status['categorization_stats']['rules_used']}<br>
            • Hybrid Used: {status['categorization_stats']['hybrid_used']}<br>
            • Auto-learned: {status['categorization_stats']['auto_learned']}<br>
            """

            msg = QMessageBox(self)
            msg.setWindowTitle("Enhanced AI System Status")
            msg.setTextFormat(Qt.RichText)
            msg.setText(status_text)
            msg.exec()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to get enhanced system status: {str(e)}")

    def show_budget_status(self):
        """Show budget status"""
        try:
            budget_status = self.integrated_categorizer.get_budget_status()

            status_text = f"""
            <h3>💰 Budget Protection Status</h3>

            <b>Current Status:</b><br>
            • Warning Level: {budget_status.get('warning_level', 'Unknown').title()}<br>
            • Can Continue Processing: {'✅ Yes' if budget_status.get('can_continue') else '❌ No'}<br>

            <b>Budget Limits:</b><br>
            • Total Budget: ${budget_status.get('total_budget', 0):.2f}<br>
            • Daily Budget: ${budget_status.get('daily_budget', 0):.2f}<br>

            <b>Usage:</b><br>
            • Total Used: ${budget_status.get('total_used', 0):.6f}<br>
            • Daily Used: ${budget_status.get('daily_used', 0):.6f}<br>
            • Total Remaining: ${budget_status.get('total_remaining', 0):.6f}<br>
            • Daily Remaining: ${budget_status.get('daily_remaining', 0):.6f}<br>

            <b>Usage Percentage:</b><br>
            • Total: {budget_status.get('total_usage_pct', 0):.1f}%<br>
            • Daily: {budget_status.get('daily_usage_pct', 0):.1f}%<br>
            """

            msg = QMessageBox(self)
            msg.setWindowTitle("Budget Status")
            msg.setTextFormat(Qt.RichText)
            msg.setText(status_text)
            msg.exec()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to get budget status: {str(e)}")

    def show_filtering_stats(self):
        """Show transaction filtering statistics"""
        try:
            filter_stats = self.integrated_categorizer.get_filtering_stats()

            status_text = f"""
            <h3>🔍 Smart Transaction Filtering Statistics</h3>

            <b>Processing Efficiency:</b><br>
            • AI Suitable: {filter_stats.get('ai_suitable', 0)} transactions ({filter_stats.get('ai_percentage', 0):.1f}%)<br>
            • Manual Required: {filter_stats.get('manual_required', 0)} transactions ({filter_stats.get('manual_percentage', 0):.1f}%)<br>
            • Excluded: {filter_stats.get('excluded', 0)} transactions ({filter_stats.get('excluded_percentage', 0):.1f}%)<br>
            • Total Processed: {filter_stats.get('total_transactions', 0)} transactions<br>

            <b>Pattern Matching:</b><br>
            • Generic Patterns Matched: {filter_stats.get('generic_patterns', 0)}<br>
            • Specific Patterns Matched: {filter_stats.get('specific_patterns', 0)}<br>

            <p><b>Interpretation:</b><br>
            • Higher AI percentage = More cost-efficient processing<br>
            • Manual transactions contain person names or unique identifiers<br>
            • Excluded transactions are test/error transactions</p>
            """

            msg = QMessageBox(self)
            msg.setWindowTitle("Filtering Statistics")
            msg.setTextFormat(Qt.RichText)
            msg.setText(status_text)
            msg.exec()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to get filtering statistics: {str(e)}")

    def show_category_stats(self):
        """Show category creation statistics"""
        try:
            category_stats = self.integrated_categorizer.get_category_creation_summary()

            status_text = f"""
            <h3>📁 Dynamic Category Management Summary</h3>

            <b>Category Creation:</b><br>
            • Total New Categories: {category_stats.get('total_new_categories', 0)}<br>
            • Main Categories Created: {category_stats.get('main_categories', 0)}<br>
            • Subcategories Created: {category_stats.get('subcategories', 0)}<br>

            <b>Recent Activity:</b><br>
            • Recent Creations: {len(category_stats.get('recent_creations', []))}<br>
            """

            # Add recent creations if available
            recent = category_stats.get('recent_creations', [])
            if recent:
                status_text += "<br><b>Recent Category Creations:</b><br>"
                for creation in recent[-5:]:  # Last 5
                    timestamp = creation.get('timestamp', 'Unknown')[:10]  # Date only
                    category = creation.get('category', 'Unknown')
                    subcategory = creation.get('subcategory', 'Unknown')
                    status_text += f"• {timestamp}: {category}/{subcategory}<br>"

            status_text += """
            <p><b>Note:</b><br>
            Categories are automatically created when the AI returns
            category names that don't exist in your current system.</p>
            """

            msg = QMessageBox(self)
            msg.setWindowTitle("Category Creation Summary")
            msg.setTextFormat(Qt.RichText)
            msg.setText(status_text)
            msg.exec()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to get category statistics: {str(e)}")

    def open_enhanced_ml_labeling(self):
        """Open enhanced ML labeling window with AI assistance"""
        try:
            from .simple_enhanced_ml_window import SimpleEnhancedMLWindow
            self.enhanced_labeling_window = SimpleEnhancedMLWindow(self)
            self.enhanced_labeling_window.show()
        except Exception as e:
            self.logger.error(f"Error opening enhanced ML labeling window: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to open enhanced labeling window: {str(e)}")

    def show_hybrid_ml_stats(self):
        """Show hybrid ML statistics"""
        try:
            hybrid_stats = self.integrated_categorizer.get_hybrid_ml_stats()

            status_text = f"""
            <h3>🔄 Hybrid ML Statistics</h3>

            <b>Categorization Methods Used:</b><br>
            • Total Categorized: {hybrid_stats.get('total_categorized', 0)}<br>
            • ML Used: {hybrid_stats.get('ml_used', 0)}<br>
            • AI Used: {hybrid_stats.get('ai_used', 0)}<br>
            • Rules Used: {hybrid_stats.get('rules_used', 0)}<br>
            • Hybrid Used: {hybrid_stats.get('hybrid_used', 0)}<br>

            <b>Training Data Integration:</b><br>
            • Added to Training: {hybrid_stats.get('added_to_training', 0)}<br>
            • Uncertain Cases: {hybrid_stats.get('uncertain_cases', 0)}<br>

            <p><b>Interpretation:</b><br>
            • Higher ML usage = Better local model performance<br>
            • AI usage for uncertain cases = Smart budget usage<br>
            • Hybrid usage = Intelligent combination of methods</p>
            """

            msg = QMessageBox(self)
            msg.setWindowTitle("Hybrid ML Statistics")
            msg.setTextFormat(Qt.RichText)
            msg.setText(status_text)
            msg.exec()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to get hybrid ML statistics: {str(e)}")

    def show_uncertain_transactions(self):
        """Show transactions where local ML has low confidence"""
        try:
            uncertain = self.integrated_categorizer.get_uncertain_transactions()

            if not uncertain:
                QMessageBox.information(self, "No Uncertain Transactions",
                                      "No transactions with low ML confidence found.")
                return

            status_text = f"""
            <h3>❓ Uncertain Transactions (Top 10)</h3>

            <p>These transactions have low local ML confidence and could benefit from AI categorization:</p>

            """

            for i, item in enumerate(uncertain[:10], 1):
                txn = item['transaction']
                ml_pred = item['ml_prediction']
                uncertainty = item['uncertainty_score']

                status_text += f"""
                <b>{i}. {txn.description[:50]}...</b><br>
                • Amount: ${abs(txn.amount):.2f}<br>
                • ML Prediction: {ml_pred['category']}/{ml_pred['subcategory']}<br>
                • ML Confidence: {ml_pred['confidence']:.1%}<br>
                • Uncertainty Score: {uncertainty:.1%}<br>
                • Recommendation: {item['recommendation']}<br><br>
                """

            status_text += """
            <p><b>Suggestion:</b> Use AI-assisted labeling to improve these predictions.</p>
            """

            msg = QMessageBox(self)
            msg.setWindowTitle("Uncertain Transactions")
            msg.setTextFormat(Qt.RichText)
            msg.setText(status_text)
            msg.exec()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to get uncertain transactions: {str(e)}")

    def show_training_quality(self):
        """Show training data quality report"""
        try:
            quality_report = self.integrated_categorizer.get_training_data_quality_report()

            if 'error' in quality_report:
                QMessageBox.warning(self, "Error", f"Failed to generate quality report: {quality_report['error']}")
                return

            source_breakdown = quality_report.get('source_breakdown', {})
            avg_confidence = quality_report.get('average_confidence_by_source', {})
            recommendations = quality_report.get('recommendations', [])

            status_text = f"""
            <h3>📊 Training Data Quality Report</h3>

            <b>Data Sources:</b><br>
            • Total Labeled Transactions: {quality_report.get('total_labeled_transactions', 0)}<br>
            """

            for source, count in source_breakdown.items():
                confidence = avg_confidence.get(source, 0)
                status_text += f"• {source.title()}: {count} transactions (avg confidence: {confidence:.1%})<br>"

            status_text += f"""

            <b>Overall Quality Score:</b> {quality_report.get('data_quality_score', 0):.1%}<br>

            <b>Recommendations:</b><br>
            """

            if recommendations:
                for rec in recommendations:
                    status_text += f"• {rec}<br>"
            else:
                status_text += "• Training data quality is good<br>"

            msg = QMessageBox(self)
            msg.setWindowTitle("Training Data Quality")
            msg.setTextFormat(Qt.RichText)
            msg.setText(status_text)
            msg.exec()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to get training quality report: {str(e)}")

    def switch_operation_mode(self, mode: str):
        """Switch hybrid operation mode"""
        try:
            self.integrated_categorizer.switch_operation_mode(mode)

            mode_names = {
                "ai_first": "🚀 AI First",
                "ml_first": "🤖 ML First",
                "hybrid": "⚡ Hybrid",
                "manual_only": "✋ Manual Only"
            }

            QMessageBox.information(
                self,
                "Operation Mode Changed",
                f"Switched to {mode_names.get(mode, mode)} mode.\n\n"
                "This affects how transactions are categorized:\n"
                "• AI First: Use SambaNova AI first, fall back to local ML\n"
                "• ML First: Use local ML first, AI for uncertain cases\n"
                "• Hybrid: Intelligently combine all approaches\n"
                "• Manual Only: Disable AI, use only local ML and manual labeling"
            )

            # Update status bar
            self.update_status_bar()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to switch operation mode: {str(e)}")

    def create_status_bar(self):
        """Create enhanced status bar with AI and budget information"""
        self.status_bar = self.statusBar()

        # AI System status
        self.ai_system_label = QLabel("🤖 AI: Initializing...")
        self.status_bar.addPermanentWidget(self.ai_system_label)

        # Merchant cache status
        self.cache_status_label = QLabel("🏪 Cache: Loading...")
        self.status_bar.addPermanentWidget(self.cache_status_label)

        # Budget status label
        self.budget_label = QLabel("💰 Budget: Loading...")
        self.status_bar.addPermanentWidget(self.budget_label)

        # Processing status
        self.processing_status_label = QLabel("⚡ Ready")
        self.status_bar.addPermanentWidget(self.processing_status_label)

        # Update status every 10 seconds for more responsive UI
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status_bar)
        self.status_timer.start(10000)  # 10 seconds

        # Initial update
        QTimer.singleShot(1000, self.update_status_bar)  # Update after 1 second

        # Also update AI dashboard
        QTimer.singleShot(2000, self.update_ai_dashboard)  # Update dashboard after 2 seconds

    def update_status_bar(self):
        """Update status bar with current AI, budget and system information"""
        try:
            # Get AI system status
            system_status = self.integrated_categorizer.get_system_status()

            # Update AI system status
            ai_features = system_status.get('enhanced_features', {})
            ai_coordinator_available = ai_features.get('ai_categorization_coordinator', False)

            if ai_coordinator_available:
                self.ai_system_label.setText("🤖 AI: Enhanced Ready")
                self.ai_system_label.setStyleSheet("color: green;")
                self.ai_status_indicator.setText("🤖 AI: Enhanced")
                self.ai_status_indicator.setStyleSheet("""
                    QLabel {
                        background-color: #e8f5e8;
                        color: #27ae60;
                        border: 1px solid #27ae60;
                        border-radius: 4px;
                        padding: 5px 10px;
                        font-weight: bold;
                    }
                """)
            else:
                self.ai_system_label.setText("🤖 AI: Basic Mode")
                self.ai_system_label.setStyleSheet("color: orange;")
                self.ai_status_indicator.setText("🤖 AI: Basic")
                self.ai_status_indicator.setStyleSheet("""
                    QLabel {
                        background-color: #fff3cd;
                        color: #856404;
                        border: 1px solid #ffeaa7;
                        border-radius: 4px;
                        padding: 5px 10px;
                        font-weight: bold;
                    }
                """)

            # Get merchant cache status
            merchant_stats = self.integrated_categorizer.get_merchant_mapping_stats()
            if merchant_stats and 'total_patterns' in merchant_stats:
                cache_patterns = merchant_stats.get('total_patterns', 0)
                cache_hit_rate = merchant_stats.get('cache_hit_rate', 0) * 100
                self.cache_status_label.setText(f"🏪 Cache: {cache_patterns} patterns ({cache_hit_rate:.1f}%)")
                self.cache_status_label.setStyleSheet("color: blue;")
            else:
                self.cache_status_label.setText("🏪 Cache: Loading...")
                self.cache_status_label.setStyleSheet("color: gray;")

            # Get budget status
            budget_status = self.integrated_categorizer.get_budget_status()
            remaining = budget_status.get('total_remaining', 0)
            usage_pct = budget_status.get('total_usage_pct', 0)
            warning_level = budget_status.get('warning_level', 'unknown')

            # Color code based on warning level
            if warning_level == 'safe':
                color = "green"
                icon = "✅"
            elif warning_level == 'warning':
                color = "orange"
                icon = "⚠️"
            elif warning_level == 'critical':
                color = "red"
                icon = "🚨"
            else:
                color = "gray"
                icon = "❓"

            budget_text = f"💰 Budget: ${remaining:.4f} ({usage_pct:.1f}% used)"
            self.budget_label.setText(f'<span style="color: {color};">{budget_text}</span>')

            # Update processing status
            self.processing_status_label.setText("⚡ Ready")
            self.processing_status_label.setStyleSheet("color: green;")

        except Exception as e:
            self.logger.error(f"Error updating status bar: {str(e)}")
            self.ai_system_label.setText("🤖 AI: Error")
            self.ai_system_label.setStyleSheet("color: red;")
            self.cache_status_label.setText("🏪 Cache: Error")
            self.cache_status_label.setStyleSheet("color: red;")
            self.budget_label.setText("💰 Budget: Error")
            self.processing_status_label.setText("⚡ Error")

    def update_ai_dashboard(self):
        """Update AI dashboard with current statistics"""
        try:
            # Get comprehensive system status
            system_status = self.integrated_categorizer.get_system_status()

            # Update merchant cache stats
            merchant_stats = self.integrated_categorizer.get_merchant_mapping_stats()
            if merchant_stats and 'total_patterns' in merchant_stats:
                self.cache_patterns_label.setText(f"Patterns: {merchant_stats.get('total_patterns', 0)}")
                self.cache_hit_rate_label.setText(f"Hit Rate: {merchant_stats.get('cache_hit_rate', 0):.1%}")

                # Calculate savings
                cost_saved = merchant_stats.get('cost_saved', 0)
                if cost_saved > 0:
                    self.cache_savings_label.setText(f"Savings: ${cost_saved:.4f}")
                    self.cache_savings_label.setStyleSheet("color: green; font-weight: bold;")
                else:
                    self.cache_savings_label.setText("Savings: $0.00")
                    self.cache_savings_label.setStyleSheet("color: gray;")
            else:
                self.cache_patterns_label.setText("Patterns: Loading...")
                self.cache_hit_rate_label.setText("Hit Rate: Loading...")
                self.cache_savings_label.setText("Savings: Loading...")

            # Update cost tracking
            ai_coordinator_status = system_status.get('ai_coordinator_status', {})
            coordinator_stats = ai_coordinator_status.get('coordinator_stats', {})

            total_cost = coordinator_stats.get('total_cost', 0)
            total_saved = coordinator_stats.get('total_cost_saved', 0)

            self.total_cost_label.setText(f"Total: ${total_cost:.4f}")
            if total_cost > 0:
                self.total_cost_label.setStyleSheet("color: red;")
            else:
                self.total_cost_label.setStyleSheet("color: green;")

            # Budget remaining
            budget_status = self.integrated_categorizer.get_budget_status()
            remaining = budget_status.get('total_remaining', 0)
            self.budget_remaining_label.setText(f"Budget: ${remaining:.4f}")

            if remaining > 0.5:
                self.budget_remaining_label.setStyleSheet("color: green;")
            elif remaining > 0.1:
                self.budget_remaining_label.setStyleSheet("color: orange;")
            else:
                self.budget_remaining_label.setStyleSheet("color: red;")

            # Update processing stats
            total_sessions = coordinator_stats.get('total_sessions', 0)
            total_processed = coordinator_stats.get('total_transactions_processed', 0)
            patterns_learned = coordinator_stats.get('patterns_learned', 0)

            # These will be updated during actual processing
            self.ai_processed_label.setText(f"AI: {total_processed}")
            self.patterns_learned_label.setText(f"Learned: {patterns_learned}")

            # Update system health
            ai_features = system_status.get('enhanced_features', {})
            ai_available = ai_features.get('ai_categorization_coordinator', False)

            if ai_available:
                self.ai_available_label.setText("AI: Available")
                self.ai_available_label.setStyleSheet("color: green;")
            else:
                self.ai_available_label.setText("AI: Basic Mode")
                self.ai_available_label.setStyleSheet("color: orange;")

            # Last update time
            from datetime import datetime
            self.last_update_label.setText(f"Updated: {datetime.now().strftime('%H:%M:%S')}")

        except Exception as e:
            self.logger.error(f"Error updating AI dashboard: {str(e)}")
            # Set error states
            self.cache_patterns_label.setText("Patterns: Error")
            self.cache_hit_rate_label.setText("Hit Rate: Error")
            self.cache_savings_label.setText("Savings: Error")
            self.ai_available_label.setText("AI: Error")
            self.ai_available_label.setStyleSheet("color: red;")

        except Exception as e:
            # Fallback status
            self.ai_system_label.setText("🤖 AI: Error")
            self.ai_system_label.setStyleSheet("color: red;")
            self.cache_status_label.setText("🏪 Cache: Error")
            self.cache_status_label.setStyleSheet("color: red;")
            self.budget_label.setText("💰 Budget: Error")
            self.processing_status_label.setText("⚡ Error")
            self.logger.debug(f"Status bar update failed: {str(e)}")

    def open_ml_labeling(self):
        """Open ML labeling window"""
        try:
            if self.ml_labeling_window is None:
                self.ml_labeling_window = MLLabelingWindow(self)

            self.ml_labeling_window.show()
            self.ml_labeling_window.raise_()
            self.ml_labeling_window.activateWindow()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to open ML labeling window: {str(e)}")

    def open_ml_review(self):
        """Open ML review window"""
        try:
            if self.ml_review_window is None:
                self.ml_review_window = MLReviewWindow(self)

            self.ml_review_window.show()
            self.ml_review_window.raise_()
            self.ml_review_window.activateWindow()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to open ML review window: {str(e)}")

    def trigger_model_training(self):
        """Trigger ML model training"""
        try:
            # Check if training is already in progress
            status = self.integrated_categorizer.get_system_status()

            if status['training_in_progress']:
                QMessageBox.information(self, "Training in Progress",
                                      "Model training is already in progress.")
                return

            # Check if we have sufficient training data
            if status['labeled_transactions'] < 10:
                reply = QMessageBox.question(
                    self, "Insufficient Training Data",
                    f"Only {status['labeled_transactions']} transactions are labeled. "
                    f"At least 10 labeled transactions are recommended for training.\n\n"
                    f"Would you like to open the labeling interface to label more transactions?",
                    QMessageBox.Yes | QMessageBox.No
                )

                if reply == QMessageBox.Yes:
                    self.open_ml_labeling()
                return

            # Confirm training
            reply = QMessageBox.question(
                self, "Train Model",
                f"Start training ML model with {status['labeled_transactions']} labeled transactions?\n\n"
                f"This may take a few minutes.",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                job_id = self.integrated_categorizer.trigger_model_training()

                if job_id:
                    QMessageBox.information(self, "Training Started",
                                          f"Model training started (Job ID: {job_id})\n\n"
                                          f"You can check the status in the ML System Status dialog.")
                else:
                    QMessageBox.warning(self, "Training Failed",
                                      "Failed to start model training. Check the logs for details.")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to trigger model training: {str(e)}")

    def open_ml_analytics(self):
        """Open ML analytics dashboard"""
        try:
            if self.ml_analytics_window is None:
                self.ml_analytics_window = MLAnalyticsWindow(self)

            self.ml_analytics_window.show()
            self.ml_analytics_window.raise_()
            self.ml_analytics_window.activateWindow()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to open ML analytics dashboard: {str(e)}")

    def open_category_manager(self):
        """Open category management dialog"""
        try:
            dialog = MLCategoryDialog(self)
            dialog.exec()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to open category manager: {str(e)}")

    def show_ai_review(self):
        """Show AI categorization review dialog"""
        try:
            if not hasattr(self, 'raw_transactions') or not self.raw_transactions:
                QMessageBox.information(self, "No Data",
                                      "Please process some bank statements first.")
                return

            # Create and show AI review dialog
            dialog = AIReviewDialog(self, self.integrated_categorizer)

            # Set transactions for review
            processed_transactions = getattr(self, 'processed_transactions', [])
            dialog.set_transactions(self.raw_transactions, processed_transactions)

            # Connect signals
            dialog.transactions_updated.connect(self.on_ai_review_transactions_updated)
            dialog.merchant_patterns_updated.connect(self.on_merchant_patterns_updated)

            # Show dialog
            if dialog.exec_() == QDialog.Accepted:
                self.logger.info("AI review dialog completed successfully")
                # Refresh status displays
                self.update_status_bar()
                self.update_ai_dashboard()

        except Exception as e:
            self.logger.error(f"Error showing AI review dialog: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to open AI review dialog: {str(e)}")

    def open_hybrid_categorization(self):
        """Open hybrid categorization system"""
        try:
            # Check if we have transactions to process
            if not hasattr(self, 'raw_transactions') or not self.raw_transactions:
                QMessageBox.information(self, "No Data",
                                      "Please load and process bank statements first.\n\n"
                                      "Use File → Open Statements to load PDF/Excel files.")
                return

            from .hybrid_categorization_qt import HybridCategorizationDialog

            # Create and show hybrid categorization dialog
            dialog = HybridCategorizationDialog(self)

            # Pass the current transactions directly (no CSV needed)
            dialog.set_transactions(self.raw_transactions, getattr(self, 'processed_transactions', []))

            # Show dialog
            if dialog.exec() == QDialog.Accepted:
                self.logger.info("Hybrid categorization completed successfully")
                # Handle any results if needed

        except Exception as e:
            self.logger.error(f"Error opening hybrid categorization: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to open hybrid categorization: {str(e)}")



    def on_ai_review_transactions_updated(self, updated_transactions):
        """Handle updated transactions from AI review dialog"""
        try:
            self.processed_transactions = updated_transactions

            # Update preview widget if it exists
            if hasattr(self, 'preview_widget'):
                self.preview_widget.set_transactions(updated_transactions, self.categories)

            self.logger.info(f"Updated {len(updated_transactions)} transactions from AI review")

        except Exception as e:
            self.logger.error(f"Error handling updated transactions: {str(e)}")

    def on_merchant_patterns_updated(self):
        """Handle merchant patterns update notification"""
        try:
            # Refresh AI dashboard and status
            self.update_ai_dashboard()
            self.update_status_bar()

            self.logger.info("Merchant patterns updated")

        except Exception as e:
            self.logger.error(f"Error handling merchant patterns update: {str(e)}")

    def closeEvent(self, event):
        """Handle window close event"""
        if self.processing_thread and self.processing_thread.isRunning():
            reply = QMessageBox.question(
                self, "Processing in Progress",
                "File processing is still in progress. Are you sure you want to exit?",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                self.processing_thread.terminate()
                self.processing_thread.wait()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()
