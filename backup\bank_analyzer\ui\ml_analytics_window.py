"""
ML Analytics and Insights Dashboard
Provides comprehensive analytics for ML categorization system performance and insights
"""

from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QTabWidget,
    QPushButton, QLabel, QTableWidget, QTableWidgetItem, QTextEdit,
    QSplitter, QGroupBox, QFormLayout, QMessageBox, QHeaderView,
    QProgressBar, QFrame, QScrollArea, QGridLayout
)
from PySide6.QtCore import Qt, QThread, QTimer, Signal
from PySide6.QtGui import QFont, QColor, QPalette

# Optional charts import
try:
    from PySide6.QtCharts import QChart, QChartView, QPieSeries, QBarSeries, QBarSet, QLineSeries, QValueAxis, QBarCategoryAxis
    CHARTS_AVAILABLE = True
except ImportError:
    CHARTS_AVAILABLE = False

import pandas as pd
from typing import List, Dict, Optional, Any
from datetime import datetime, timedelta
import logging

from ..ml.integrated_categorizer import IntegratedCategorizer
from ..ml.training_data_manager import TrainingDataManager
from ..ml.model_trainer import ModelTrainer
from ..ml.data_preparation import TransactionDataPreparator
from ..core.logger import get_logger


class AnalyticsDataWorker(QThread):
    """Worker thread for loading analytics data"""
    data_loaded = Signal(dict)
    error_occurred = Signal(str)
    
    def __init__(self, integrated_categorizer: IntegratedCategorizer):
        super().__init__()
        self.integrated_categorizer = integrated_categorizer
    
    def run(self):
        try:
            # Collect all analytics data
            data = {
                "system_status": self.integrated_categorizer.get_system_status(),
                "model_performance": self._get_model_performance(),
                "categorization_insights": self._get_categorization_insights(),
                "training_progress": self._get_training_progress()
            }
            
            self.data_loaded.emit(data)
            
        except Exception as e:
            self.error_occurred.emit(str(e))
    
    def _get_model_performance(self) -> Dict[str, Any]:
        """Get model performance metrics"""
        try:
            model_trainer = ModelTrainer()
            return model_trainer.get_performance_summary()
        except:
            return {"error": "No performance data available"}
    
    def _get_categorization_insights(self) -> Dict[str, Any]:
        """Get categorization insights"""
        try:
            data_preparator = TransactionDataPreparator()
            unique_transactions = data_preparator.load_unique_transactions()
            
            # Category distribution
            category_dist = {}
            for txn in unique_transactions.values():
                if txn.category:
                    if txn.category not in category_dist:
                        category_dist[txn.category] = 0
                    category_dist[txn.category] += txn.frequency
            
            # Confidence distribution
            confidence_ranges = {"High (>80%)": 0, "Medium (60-80%)": 0, "Low (<60%)": 0}
            for txn in unique_transactions.values():
                if txn.confidence > 0.8:
                    confidence_ranges["High (>80%)"] += txn.frequency
                elif txn.confidence > 0.6:
                    confidence_ranges["Medium (60-80%)"] += txn.frequency
                else:
                    confidence_ranges["Low (<60%)"] += txn.frequency
            
            # Ensure all values are within reasonable bounds
            total_transactions = sum(txn.frequency for txn in unique_transactions.values())

            # Limit values to prevent overflow
            MAX_VALUE = 2147483647  # 32-bit signed integer max

            # Clean category distribution
            clean_category_dist = {}
            for category, count in category_dist.items():
                if category and isinstance(category, str):
                    clean_count = min(MAX_VALUE, max(0, int(count)))
                    clean_category_dist[str(category)[:100]] = clean_count

            # Clean confidence distribution
            clean_confidence_ranges = {}
            for range_name, count in confidence_ranges.items():
                clean_count = min(MAX_VALUE, max(0, int(count)))
                clean_confidence_ranges[str(range_name)] = clean_count

            return {
                "category_distribution": clean_category_dist,
                "confidence_distribution": clean_confidence_ranges,
                "total_transactions": min(MAX_VALUE, max(1, int(total_transactions))),
                "unique_descriptions": min(MAX_VALUE, max(0, len(unique_transactions)))
            }
        except:
            return {"error": "No categorization data available"}
    
    def _get_training_progress(self) -> Dict[str, Any]:
        """Get training progress data"""
        try:
            training_manager = TrainingDataManager()
            stats = training_manager.get_labeling_stats()
            
            return {
                "total_unique": stats.total_unique_transactions,
                "labeled": stats.labeled_transactions,
                "unlabeled": stats.unlabeled_transactions,
                "progress_percentage": stats.labeling_progress,
                "categories_used": len(stats.categories_used),
                "most_frequent_unlabeled": stats.most_frequent_unlabeled[:5]
            }
        except:
            return {"error": "No training data available"}


class MLAnalyticsWindow(QMainWindow):
    """
    Main analytics dashboard for ML categorization system
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger(__name__)
        
        # Initialize components
        self.integrated_categorizer = IntegratedCategorizer()
        
        # Data
        self.analytics_data: Dict[str, Any] = {}
        
        # Worker thread
        self.data_worker: Optional[AnalyticsDataWorker] = None
        
        self.setup_ui()
        self.load_analytics_data()
    
    def setup_ui(self):
        """Setup the main window UI"""
        self.setWindowTitle("ML Analytics Dashboard")
        self.setGeometry(100, 100, 1200, 800)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        
        # Header
        header = self.create_header()
        main_layout.addWidget(header)
        
        # Tab widget for different analytics views
        self.tab_widget = QTabWidget()
        
        # Overview tab
        self.create_overview_tab()
        
        # Performance tab
        self.create_performance_tab()
        
        # Insights tab
        self.create_insights_tab()
        
        # Training tab
        self.create_training_tab()
        
        main_layout.addWidget(self.tab_widget)
        
        # Status bar
        self.statusBar().showMessage("Loading analytics data...")
    
    def create_header(self) -> QWidget:
        """Create header section"""
        header_frame = QFrame()
        header_frame.setFrameStyle(QFrame.StyledPanel)
        header_layout = QHBoxLayout(header_frame)
        
        # Title
        title_label = QLabel("ML Analytics Dashboard")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # Refresh button
        self.refresh_button = QPushButton("Refresh Data")
        self.refresh_button.clicked.connect(self.load_analytics_data)
        header_layout.addWidget(self.refresh_button)
        
        return header_frame
    
    def create_overview_tab(self):
        """Create overview tab"""
        overview_widget = QWidget()
        layout = QVBoxLayout(overview_widget)
        
        # System status cards
        status_frame = QFrame()
        status_frame.setFrameStyle(QFrame.StyledPanel)
        status_layout = QGridLayout(status_frame)
        
        # ML Status card
        self.ml_status_card = self.create_status_card("ML System", "Loading...")
        status_layout.addWidget(self.ml_status_card, 0, 0)
        
        # Model Status card
        self.model_status_card = self.create_status_card("Model", "Loading...")
        status_layout.addWidget(self.model_status_card, 0, 1)
        
        # Training Status card
        self.training_status_card = self.create_status_card("Training", "Loading...")
        status_layout.addWidget(self.training_status_card, 0, 2)
        
        # Data Status card
        self.data_status_card = self.create_status_card("Data", "Loading...")
        status_layout.addWidget(self.data_status_card, 1, 0)
        
        # Performance card
        self.performance_card = self.create_status_card("Performance", "Loading...")
        status_layout.addWidget(self.performance_card, 1, 1)
        
        # Usage card
        self.usage_card = self.create_status_card("Usage", "Loading...")
        status_layout.addWidget(self.usage_card, 1, 2)
        
        layout.addWidget(status_frame)
        
        # Recent activity
        activity_group = QGroupBox("Recent Activity")
        activity_layout = QVBoxLayout(activity_group)
        
        self.activity_text = QTextEdit()
        self.activity_text.setMaximumHeight(200)
        self.activity_text.setReadOnly(True)
        activity_layout.addWidget(self.activity_text)
        
        layout.addWidget(activity_group)
        
        layout.addStretch()
        
        self.tab_widget.addTab(overview_widget, "Overview")
    
    def create_performance_tab(self):
        """Create performance analytics tab"""
        performance_widget = QWidget()
        layout = QVBoxLayout(performance_widget)
        
        # Model metrics
        metrics_group = QGroupBox("Model Performance Metrics")
        metrics_layout = QFormLayout(metrics_group)
        
        self.accuracy_label = QLabel("Loading...")
        self.cv_accuracy_label = QLabel("Loading...")
        self.training_samples_label = QLabel("Loading...")
        self.categories_count_label = QLabel("Loading...")
        
        metrics_layout.addRow("Accuracy:", self.accuracy_label)
        metrics_layout.addRow("Cross-validation Accuracy:", self.cv_accuracy_label)
        metrics_layout.addRow("Training Samples:", self.training_samples_label)
        metrics_layout.addRow("Categories:", self.categories_count_label)
        
        layout.addWidget(metrics_group)
        
        # Performance trends (placeholder for charts)
        trends_group = QGroupBox("Performance Trends")
        trends_layout = QVBoxLayout(trends_group)
        
        self.trends_placeholder = QLabel("Performance trend charts will be displayed here")
        self.trends_placeholder.setAlignment(Qt.AlignCenter)
        self.trends_placeholder.setStyleSheet("color: gray; font-style: italic;")
        trends_layout.addWidget(self.trends_placeholder)
        
        layout.addWidget(trends_group)
        
        layout.addStretch()
        
        self.tab_widget.addTab(performance_widget, "Performance")
    
    def create_insights_tab(self):
        """Create insights tab"""
        insights_widget = QWidget()
        layout = QVBoxLayout(insights_widget)
        
        # Category distribution
        category_group = QGroupBox("Category Distribution")
        category_layout = QVBoxLayout(category_group)
        
        self.category_table = QTableWidget()
        self.category_table.setColumnCount(3)
        self.category_table.setHorizontalHeaderLabels(["Category", "Transactions", "Percentage"])
        
        header = self.category_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        
        category_layout.addWidget(self.category_table)
        layout.addWidget(category_group)
        
        # Confidence distribution
        confidence_group = QGroupBox("Confidence Distribution")
        confidence_layout = QVBoxLayout(confidence_group)
        
        self.confidence_table = QTableWidget()
        self.confidence_table.setColumnCount(3)
        self.confidence_table.setHorizontalHeaderLabels(["Confidence Range", "Transactions", "Percentage"])
        
        header = self.confidence_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        
        confidence_layout.addWidget(self.confidence_table)
        layout.addWidget(confidence_group)
        
        self.tab_widget.addTab(insights_widget, "Insights")
    
    def create_training_tab(self):
        """Create training progress tab"""
        training_widget = QWidget()
        layout = QVBoxLayout(training_widget)
        
        # Training progress
        progress_group = QGroupBox("Training Progress")
        progress_layout = QFormLayout(progress_group)
        
        self.total_unique_label = QLabel("Loading...")
        self.labeled_count_label = QLabel("Loading...")
        self.unlabeled_count_label = QLabel("Loading...")
        
        self.training_progress_bar = QProgressBar()
        
        progress_layout.addRow("Total Unique Transactions:", self.total_unique_label)
        progress_layout.addRow("Labeled:", self.labeled_count_label)
        progress_layout.addRow("Unlabeled:", self.unlabeled_count_label)
        progress_layout.addRow("Progress:", self.training_progress_bar)
        
        layout.addWidget(progress_group)
        
        # Most frequent unlabeled
        unlabeled_group = QGroupBox("Most Frequent Unlabeled Transactions")
        unlabeled_layout = QVBoxLayout(unlabeled_group)
        
        self.unlabeled_table = QTableWidget()
        self.unlabeled_table.setColumnCount(3)
        self.unlabeled_table.setHorizontalHeaderLabels(["Description", "Frequency", "Amount Range"])
        
        header = self.unlabeled_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)

        # Set minimum row height to accommodate full descriptions
        self.unlabeled_table.verticalHeader().setDefaultSectionSize(50)

        # Enable word wrap for table items
        self.unlabeled_table.setWordWrap(True)
        
        unlabeled_layout.addWidget(self.unlabeled_table)
        layout.addWidget(unlabeled_group)
        
        self.tab_widget.addTab(training_widget, "Training")
    
    def create_status_card(self, title: str, content: str) -> QWidget:
        """Create a status card widget"""
        card = QFrame()
        card.setFrameStyle(QFrame.StyledPanel)
        card.setStyleSheet("QFrame { background-color: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; }")
        
        layout = QVBoxLayout(card)
        
        # Title
        title_label = QLabel(title)
        title_font = QFont()
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # Content
        content_label = QLabel(content)
        content_label.setAlignment(Qt.AlignCenter)
        content_label.setWordWrap(True)
        layout.addWidget(content_label)
        
        # Store reference to content label for updates
        setattr(card, 'content_label', content_label)
        
        return card
    
    def load_analytics_data(self):
        """Load analytics data in background"""
        if self.data_worker and self.data_worker.isRunning():
            return
        
        self.refresh_button.setEnabled(False)
        self.statusBar().showMessage("Loading analytics data...")
        
        self.data_worker = AnalyticsDataWorker(self.integrated_categorizer)
        self.data_worker.data_loaded.connect(self.on_data_loaded)
        self.data_worker.error_occurred.connect(self.on_data_error)
        self.data_worker.start()
    
    def on_data_loaded(self, data: Dict[str, Any]):
        """Handle loaded analytics data"""
        self.analytics_data = data
        
        # Update all UI components
        self.update_overview_tab()
        self.update_performance_tab()
        self.update_insights_tab()
        self.update_training_tab()
        
        self.refresh_button.setEnabled(True)
        self.statusBar().showMessage("Analytics data loaded successfully")
    
    def on_data_error(self, error_message: str):
        """Handle data loading error"""
        self.refresh_button.setEnabled(True)
        self.statusBar().showMessage("Failed to load analytics data")
        
        QMessageBox.critical(self, "Error", f"Failed to load analytics data: {error_message}")
    
    def update_overview_tab(self):
        """Update overview tab with loaded data"""
        status = self.analytics_data.get("system_status", {})
        
        # ML Status card
        ml_status = "Available" if status.get("ml_available") else "Not Available"
        self.ml_status_card.content_label.setText(ml_status)
        
        # Model Status card
        if status.get("ml_trained"):
            model_text = f"Trained\nv{status.get('model_version', 'Unknown')}"
        else:
            model_text = "Not Trained"
        self.model_status_card.content_label.setText(model_text)
        
        # Training Status card
        if status.get("training_in_progress"):
            training_text = "In Progress"
        elif status.get("can_auto_retrain"):
            training_text = "Ready for Retrain"
        else:
            training_text = "Up to Date"
        self.training_status_card.content_label.setText(training_text)
        
        # Data Status card
        data_text = f"{status.get('labeled_transactions', 0)} Labeled\n{status.get('labeling_progress', 0):.1f}% Complete"
        self.data_status_card.content_label.setText(data_text)
        
        # Performance card
        performance = self.analytics_data.get("model_performance", {})
        if "error" not in performance:
            perf_text = f"{performance.get('latest_accuracy', 0):.1%} Accuracy\n{performance.get('training_samples', 0)} Samples"
        else:
            perf_text = "No Data"
        self.performance_card.content_label.setText(perf_text)
        
        # Usage card
        stats = status.get("categorization_stats", {})
        usage_text = f"{stats.get('total_categorized', 0)} Categorized\n{stats.get('ml_used', 0)} ML Used"
        self.usage_card.content_label.setText(usage_text)
        
        # Recent activity
        activity_text = f"Last updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        activity_text += f"ML categorizations: {stats.get('ml_used', 0)}\n"
        activity_text += f"Rule-based categorizations: {stats.get('rules_used', 0)}\n"
        activity_text += f"Auto-learned transactions: {stats.get('auto_learned', 0)}"
        self.activity_text.setText(activity_text)
    
    def update_performance_tab(self):
        """Update performance tab with loaded data"""
        performance = self.analytics_data.get("model_performance", {})
        
        if "error" not in performance:
            self.accuracy_label.setText(f"{performance.get('latest_accuracy', 0):.1%}")
            self.cv_accuracy_label.setText(f"{performance.get('cv_accuracy_mean', 0):.1%} ± {performance.get('cv_accuracy_std', 0):.1%}")
            self.training_samples_label.setText(str(performance.get('training_samples', 0)))
            self.categories_count_label.setText(str(performance.get('categories_count', 0)))
        else:
            self.accuracy_label.setText("N/A")
            self.cv_accuracy_label.setText("N/A")
            self.training_samples_label.setText("N/A")
            self.categories_count_label.setText("N/A")
    
    def update_insights_tab(self):
        """Update insights tab with loaded data"""
        try:
            insights = self.analytics_data.get("categorization_insights", {})

            if "error" not in insights:
                # Category distribution
                category_dist = insights.get("category_distribution", {})
                total_transactions = max(1, insights.get("total_transactions", 1))  # Ensure minimum 1

                # Filter and validate category data
                valid_categories = {}
                for category, count in category_dist.items():
                    if category and isinstance(category, str) and isinstance(count, (int, float)) and count >= 0:
                        valid_categories[str(category)[:100]] = int(count)  # Limit category name length

                # Update category table
                self.category_table.setRowCount(len(valid_categories))
                for i, (category, count) in enumerate(sorted(valid_categories.items(), key=lambda x: x[1], reverse=True)):
                    try:
                        percentage = (count / total_transactions) * 100

                        self.category_table.setItem(i, 0, QTableWidgetItem(str(category)))
                        self.category_table.setItem(i, 1, QTableWidgetItem(str(count)))
                        self.category_table.setItem(i, 2, QTableWidgetItem(f"{percentage:.1f}%"))
                    except Exception as e:
                        self.logger.error(f"Error setting category table item {i}: {str(e)}")
                        continue

                # Confidence distribution
                confidence_dist = insights.get("confidence_distribution", {})

                # Filter and validate confidence data
                valid_confidence = {}
                for range_name, count in confidence_dist.items():
                    if range_name and isinstance(range_name, str) and isinstance(count, (int, float)) and count >= 0:
                        valid_confidence[str(range_name)[:50]] = int(count)

                # Update confidence table
                self.confidence_table.setRowCount(len(valid_confidence))
                for i, (range_name, count) in enumerate(valid_confidence.items()):
                    try:
                        percentage = (count / total_transactions) * 100 if total_transactions > 0 else 0

                        self.confidence_table.setItem(i, 0, QTableWidgetItem(str(range_name)))
                        self.confidence_table.setItem(i, 1, QTableWidgetItem(str(count)))
                        self.confidence_table.setItem(i, 2, QTableWidgetItem(f"{percentage:.1f}%"))
                    except Exception as e:
                        self.logger.error(f"Error setting confidence table item {i}: {str(e)}")
                        continue
            else:
                # Clear tables on error
                self.category_table.setRowCount(0)
                self.confidence_table.setRowCount(0)

        except Exception as e:
            self.logger.error(f"Error updating insights tab: {str(e)}")
            # Clear tables on error
            self.category_table.setRowCount(0)
            self.confidence_table.setRowCount(0)
    
    def update_training_tab(self):
        """Update training tab with loaded data"""
        try:
            training = self.analytics_data.get("training_progress", {})

            if "error" not in training:
                # Update labels with safe conversion
                self.total_unique_label.setText(str(training.get("total_unique", 0)))
                self.labeled_count_label.setText(str(training.get("labeled", 0)))
                self.unlabeled_count_label.setText(str(training.get("unlabeled", 0)))

                # Update progress bar with bounds checking
                progress = max(0, min(100, int(training.get("progress_percentage", 0))))
                self.training_progress_bar.setValue(progress)

                # Most frequent unlabeled
                unlabeled_list = training.get("most_frequent_unlabeled", [])
                if isinstance(unlabeled_list, list):
                    self.unlabeled_table.setRowCount(len(unlabeled_list))

                    for i, txn_data in enumerate(unlabeled_list):
                        try:
                            if not isinstance(txn_data, dict):
                                continue

                            description = str(txn_data.get("description", ""))[:200]  # Limit length
                            frequency = str(txn_data.get("frequency", 0))

                            # Safe amount range handling
                            amount_range_data = txn_data.get('amount_range', [0, 0])
                            if isinstance(amount_range_data, (list, tuple)) and len(amount_range_data) >= 2:
                                min_amt = float(amount_range_data[0]) if amount_range_data[0] is not None else 0
                                max_amt = float(amount_range_data[1]) if amount_range_data[1] is not None else 0
                                amount_range = f"₹{min_amt:.0f}-{max_amt:.0f}"
                            else:
                                amount_range = "₹0-0"

                            # Create description item with full text and tooltip
                            desc_item = QTableWidgetItem(description)
                            desc_item.setToolTip(description)

                            self.unlabeled_table.setItem(i, 0, desc_item)
                            self.unlabeled_table.setItem(i, 1, QTableWidgetItem(frequency))
                            self.unlabeled_table.setItem(i, 2, QTableWidgetItem(amount_range))

                        except Exception as e:
                            self.logger.error(f"Error setting unlabeled table item {i}: {str(e)}")
                            continue
                else:
                    self.unlabeled_table.setRowCount(0)
            else:
                # Clear on error
                self.unlabeled_table.setRowCount(0)

        except Exception as e:
            self.logger.error(f"Error updating training tab: {str(e)}")
            # Clear table on error
            self.unlabeled_table.setRowCount(0)
