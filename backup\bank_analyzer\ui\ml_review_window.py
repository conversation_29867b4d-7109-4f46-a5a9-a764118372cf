"""
ML Prediction Review Interface
Provides UI for reviewing and correcting automatic categorizations
"""

from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QPushButton, QLabel, QComboBox, QLineEdit, QProgressBar, QTextEdit,
    QSplitter, QGroupBox, QFormLayout, QMessageBox, QHeaderView,
    QCheckBox, QSpinBox, QTabWidget, QFrame, QSlider, QDoubleSpinBox, QDialog
)
from PySide6.QtCore import Qt, QThread, QTimer, Signal, QSize
from PySide6.QtGui import QFont, QColor, QPalette, QBrush

import pandas as pd
from typing import List, Dict, Optional, Any, Tuple
from datetime import datetime
import logging

from ..ml.ml_categorizer import MLTransactionCategorizer, MLPrediction
from ..ml.training_data_manager import TrainingDataManager
from ..ml.category_manager import CategoryManager
from ..ml.data_preparation import TransactionDataPreparator, UniqueTransaction
from ..models.transaction import RawTransaction, ProcessedTransaction
from ..core.logger import get_logger
from .ml_category_dialog import MLCategoryDialog


class PredictionReviewWorker(QThread):
    """Worker thread for batch prediction operations"""
    progress_updated = Signal(int)
    prediction_completed = Signal(list)
    error_occurred = Signal(str)
    
    def __init__(self, ml_categorizer: MLTransactionCategorizer, transactions: List[UniqueTransaction]):
        super().__init__()
        self.ml_categorizer = ml_categorizer
        self.transactions = transactions
    
    def run(self):
        try:
            predictions = []
            total = len(self.transactions)
            
            for i, transaction in enumerate(self.transactions):
                prediction = self.ml_categorizer.predict_category(transaction.description)
                
                predictions.append({
                    "transaction": transaction,
                    "prediction": prediction
                })
                
                # Update progress
                progress = int((i + 1) / total * 100)
                self.progress_updated.emit(progress)
            
            self.prediction_completed.emit(predictions)
            
        except Exception as e:
            self.error_occurred.emit(str(e))


class PredictionReviewTable(QTableWidget):
    """Custom table widget for prediction review"""
    
    def __init__(self, category_manager: CategoryManager):
        super().__init__()
        self.category_manager = category_manager
        self.predictions_data: List[Dict[str, Any]] = []
        
        self.setup_table()
    
    def setup_table(self):
        """Setup table structure"""
        self.setColumnCount(8)
        self.setHorizontalHeaderLabels([
            "Description", "Frequency", "Amount Range", 
            "Predicted Category", "Predicted Sub-category", "Confidence",
            "Corrected Category", "Status"
        ])
        
        # Set column widths and row height
        header = self.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(7, QHeaderView.ResizeToContents)

        # Set minimum row height to accommodate full descriptions
        self.verticalHeader().setDefaultSectionSize(60)

        # Enable word wrap for table items
        self.setWordWrap(True)
        
        # Enable sorting
        self.setSortingEnabled(True)
        
        # Selection behavior
        self.setSelectionBehavior(QTableWidget.SelectRows)
        self.setAlternatingRowColors(True)
    
    def load_predictions(self, predictions_data: List[Dict[str, Any]]):
        """Load prediction data into table"""
        self.predictions_data = predictions_data
        self.setRowCount(len(predictions_data))
        
        for row, data in enumerate(predictions_data):
            transaction = data["transaction"]
            prediction = data["prediction"]
            
            # Description - show full description with word wrap
            desc_item = QTableWidgetItem(transaction.description)
            desc_item.setToolTip(transaction.description)
            self.setItem(row, 0, desc_item)
            
            # Frequency
            freq_item = QTableWidgetItem(str(transaction.frequency))
            self.setItem(row, 1, freq_item)
            
            # Amount range
            amount_item = QTableWidgetItem(f"₹{transaction.amount_range[0]:.0f}-{transaction.amount_range[1]:.0f}")
            self.setItem(row, 2, amount_item)
            
            if prediction:
                # Predicted category
                pred_cat_item = QTableWidgetItem(prediction.category)
                self.setItem(row, 3, pred_cat_item)
                
                # Predicted sub-category
                pred_subcat_item = QTableWidgetItem(prediction.sub_category)
                self.setItem(row, 4, pred_subcat_item)
                
                # Confidence
                confidence_item = QTableWidgetItem(f"{prediction.confidence:.2f}")
                
                # Color code by confidence
                if prediction.confidence >= 0.8:
                    confidence_item.setBackground(QColor(144, 238, 144))  # Light green
                elif prediction.confidence >= 0.6:
                    confidence_item.setBackground(QColor(255, 255, 224))  # Light yellow
                else:
                    confidence_item.setBackground(QColor(255, 182, 193))  # Light red
                
                self.setItem(row, 5, confidence_item)
            else:
                # No prediction
                self.setItem(row, 3, QTableWidgetItem("No prediction"))
                self.setItem(row, 4, QTableWidgetItem(""))
                
                no_pred_item = QTableWidgetItem("0.00")
                no_pred_item.setBackground(QColor(211, 211, 211))  # Light gray
                self.setItem(row, 5, no_pred_item)
            
            # Corrected category (initially empty)
            self.setItem(row, 6, QTableWidgetItem(""))
            
            # Status
            status_item = QTableWidgetItem("Pending Review")
            self.setItem(row, 7, status_item)
    
    def get_selected_prediction(self) -> Optional[Dict[str, Any]]:
        """Get currently selected prediction data"""
        current_row = self.currentRow()
        if 0 <= current_row < len(self.predictions_data):
            return self.predictions_data[current_row]
        return None
    
    def update_correction(self, row: int, category: str, sub_category: str):
        """Update correction for a specific row"""
        if 0 <= row < self.rowCount():
            correction_text = f"{category} > {sub_category}"
            self.setItem(row, 6, QTableWidgetItem(correction_text))
            
            status_item = QTableWidgetItem("Corrected")
            status_item.setBackground(QColor(173, 216, 230))  # Light blue
            self.setItem(row, 7, status_item)
    
    def mark_as_approved(self, row: int):
        """Mark prediction as approved"""
        if 0 <= row < self.rowCount():
            status_item = QTableWidgetItem("Approved")
            status_item.setBackground(QColor(144, 238, 144))  # Light green
            self.setItem(row, 7, status_item)


class MLReviewWindow(QMainWindow):
    """
    Main window for reviewing ML predictions
    Allows users to review and correct automatic categorizations
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger(__name__)
        
        # Initialize components
        self.ml_categorizer = MLTransactionCategorizer()
        self.training_manager = TrainingDataManager()
        self.category_manager = CategoryManager()
        self.data_preparator = TransactionDataPreparator()
        
        # Current data
        self.current_predictions: List[Dict[str, Any]] = []
        self.filtered_predictions: List[Dict[str, Any]] = []
        
        # Worker thread
        self.prediction_worker: Optional[PredictionReviewWorker] = None
        
        self.setup_ui()
        self.load_model_info()
    
    def setup_ui(self):
        """Setup the main window UI"""
        self.setWindowTitle("ML Prediction Review")
        self.setGeometry(100, 100, 1400, 800)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        
        # Top panel - Controls and filters
        top_panel = self.create_top_panel()
        main_layout.addWidget(top_panel)
        
        # Main content - Splitter with table and review panel
        content_splitter = QSplitter(Qt.Horizontal)
        
        # Left panel - Predictions table
        table_panel = self.create_table_panel()
        
        # Right panel - Review and correction
        review_panel = self.create_review_panel()
        
        content_splitter.addWidget(table_panel)
        content_splitter.addWidget(review_panel)
        content_splitter.setSizes([800, 600])
        
        main_layout.addWidget(content_splitter)
        
        # Status bar
        self.statusBar().showMessage("Ready")
    
    def create_top_panel(self) -> QWidget:
        """Create top control panel"""
        panel = QWidget()
        layout = QHBoxLayout(panel)
        
        # Model info group
        model_group = QGroupBox("Model Information")
        model_layout = QFormLayout(model_group)
        
        self.model_status_label = QLabel("Loading...")
        self.model_version_label = QLabel("Unknown")
        
        model_layout.addRow("Status:", self.model_status_label)
        model_layout.addRow("Version:", self.model_version_label)
        
        layout.addWidget(model_group)
        
        # Filters group
        filters_group = QGroupBox("Filters")
        filters_layout = QFormLayout(filters_group)
        
        # Confidence filter
        self.confidence_slider = QSlider(Qt.Horizontal)
        self.confidence_slider.setRange(0, 100)
        self.confidence_slider.setValue(0)
        self.confidence_slider.valueChanged.connect(self.apply_filters)
        
        self.confidence_label = QLabel("0%")
        
        confidence_layout = QHBoxLayout()
        confidence_layout.addWidget(self.confidence_slider)
        confidence_layout.addWidget(self.confidence_label)
        
        filters_layout.addRow("Min Confidence:", confidence_layout)
        
        # Category filter
        self.category_filter_combo = QComboBox()
        self.category_filter_combo.addItem("All Categories")
        self.category_filter_combo.currentTextChanged.connect(self.apply_filters)
        
        filters_layout.addRow("Category:", self.category_filter_combo)
        
        # Status filter
        self.status_filter_combo = QComboBox()
        self.status_filter_combo.addItems(["All", "Pending Review", "Approved", "Corrected"])
        self.status_filter_combo.currentTextChanged.connect(self.apply_filters)
        
        filters_layout.addRow("Status:", self.status_filter_combo)
        
        layout.addWidget(filters_group)
        
        # Actions group
        actions_group = QGroupBox("Actions")
        actions_layout = QVBoxLayout(actions_group)
        
        # Load predictions button
        self.load_predictions_button = QPushButton("Load Predictions")
        self.load_predictions_button.clicked.connect(self.load_predictions)
        self.load_predictions_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        
        # Batch operations
        batch_layout = QHBoxLayout()
        
        self.approve_all_button = QPushButton("Approve High Confidence")
        self.approve_all_button.clicked.connect(self.approve_high_confidence)
        
        self.export_corrections_button = QPushButton("Export Corrections")
        self.export_corrections_button.clicked.connect(self.export_corrections)
        
        batch_layout.addWidget(self.approve_all_button)
        batch_layout.addWidget(self.export_corrections_button)
        
        actions_layout.addWidget(self.load_predictions_button)
        actions_layout.addLayout(batch_layout)
        
        layout.addWidget(actions_group)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        layout.addStretch()
        
        return panel
    
    def create_table_panel(self) -> QWidget:
        """Create table panel"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Table
        self.predictions_table = PredictionReviewTable(self.category_manager)
        self.predictions_table.itemSelectionChanged.connect(self.on_prediction_selected)
        
        layout.addWidget(QLabel("Predictions"))
        layout.addWidget(self.predictions_table)
        
        return panel
    
    def create_review_panel(self) -> QWidget:
        """Create review and correction panel"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Current prediction info
        info_group = QGroupBox("Current Prediction")
        info_layout = QFormLayout(info_group)
        
        self.current_description_label = QLabel()
        self.current_description_label.setWordWrap(True)
        self.current_description_label.setStyleSheet("font-weight: bold; padding: 8px; background-color: #f0f0f0; border: 1px solid #ddd; border-radius: 4px;")
        self.current_description_label.setMinimumHeight(60)  # Ensure enough space for full description
        self.current_description_label.setAlignment(Qt.AlignTop)  # Align text to top
        
        self.current_prediction_label = QLabel()
        self.current_confidence_label = QLabel()
        
        info_layout.addRow("Description:", self.current_description_label)
        info_layout.addRow("Prediction:", self.current_prediction_label)
        info_layout.addRow("Confidence:", self.current_confidence_label)
        
        layout.addWidget(info_group)
        
        # Correction group
        correction_group = QGroupBox("Correction")
        correction_layout = QFormLayout(correction_group)
        
        self.correction_category_combo = QComboBox()
        self.correction_category_combo.currentTextChanged.connect(self.on_correction_category_changed)
        
        self.correction_subcategory_combo = QComboBox()
        
        correction_layout.addRow("Correct Category:", self.correction_category_combo)
        correction_layout.addRow("Correct Sub-category:", self.correction_subcategory_combo)

        # Add category management button
        manage_categories_button = QPushButton("Manage Categories")
        manage_categories_button.clicked.connect(self.open_category_manager)
        manage_categories_button.setStyleSheet("QPushButton { background-color: #9C27B0; color: white; font-weight: bold; padding: 6px; }")
        correction_layout.addRow("", manage_categories_button)
        
        layout.addWidget(correction_group)
        
        # Action buttons
        button_layout = QHBoxLayout()
        
        self.approve_button = QPushButton("Approve Prediction")
        self.approve_button.clicked.connect(self.approve_current_prediction)
        self.approve_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; }")
        
        self.correct_button = QPushButton("Apply Correction")
        self.correct_button.clicked.connect(self.apply_correction)
        self.correct_button.setStyleSheet("QPushButton { background-color: #FF9800; color: white; }")
        
        self.skip_button = QPushButton("Skip")
        self.skip_button.clicked.connect(self.skip_current_prediction)
        
        button_layout.addWidget(self.approve_button)
        button_layout.addWidget(self.correct_button)
        button_layout.addWidget(self.skip_button)
        
        layout.addLayout(button_layout)
        
        # Statistics
        stats_group = QGroupBox("Review Statistics")
        stats_layout = QFormLayout(stats_group)
        
        self.total_predictions_label = QLabel("0")
        self.approved_count_label = QLabel("0")
        self.corrected_count_label = QLabel("0")
        self.pending_count_label = QLabel("0")
        
        stats_layout.addRow("Total:", self.total_predictions_label)
        stats_layout.addRow("Approved:", self.approved_count_label)
        stats_layout.addRow("Corrected:", self.corrected_count_label)
        stats_layout.addRow("Pending:", self.pending_count_label)
        
        layout.addWidget(stats_group)
        
        layout.addStretch()
        
        # Load categories
        self.load_correction_categories()
        
        return panel
    
    def load_model_info(self):
        """Load and display model information"""
        try:
            model_info = self.ml_categorizer.get_model_info()
            
            if model_info["is_trained"]:
                self.model_status_label.setText("Trained")
                self.model_status_label.setStyleSheet("color: green; font-weight: bold;")
            else:
                self.model_status_label.setText("Not Trained")
                self.model_status_label.setStyleSheet("color: red; font-weight: bold;")
            
            self.model_version_label.setText(model_info.get("model_version", "Unknown"))
            
            # Enable/disable load button based on model status
            self.load_predictions_button.setEnabled(model_info["is_trained"])
            
        except Exception as e:
            self.logger.error(f"Error loading model info: {str(e)}")
            self.model_status_label.setText("Error")
            self.model_status_label.setStyleSheet("color: red; font-weight: bold;")
    
    def load_correction_categories(self):
        """Load categories for correction"""
        self.correction_category_combo.clear()
        self.correction_category_combo.addItem("Select Category...")
        
        main_categories = self.category_manager.get_main_categories()
        for category in main_categories:
            self.correction_category_combo.addItem(category.name)
        
        # Also load category filter
        self.category_filter_combo.clear()
        self.category_filter_combo.addItem("All Categories")
        for category in main_categories:
            self.category_filter_combo.addItem(category.name)
    
    def on_correction_category_changed(self, category_name: str):
        """Handle correction category change"""
        self.correction_subcategory_combo.clear()
        self.correction_subcategory_combo.addItem("Select Sub-category...")
        
        if category_name and category_name != "Select Category...":
            subcategories = self.category_manager.get_subcategories(category_name)
            for subcategory in subcategories:
                self.correction_subcategory_combo.addItem(subcategory.name)
    
    def load_predictions(self):
        """Load predictions for unlabeled transactions"""
        try:
            # Get unlabeled transactions
            unlabeled_transactions = self.data_preparator.get_unlabeled_transactions()
            
            if not unlabeled_transactions:
                QMessageBox.information(self, "Info", "No unlabeled transactions found")
                return
            
            # Limit to reasonable batch size
            batch_size = min(100, len(unlabeled_transactions))
            transactions_batch = unlabeled_transactions[:batch_size]
            
            # Start prediction worker
            self.prediction_worker = PredictionReviewWorker(self.ml_categorizer, transactions_batch)
            self.prediction_worker.progress_updated.connect(self.progress_bar.setValue)
            self.prediction_worker.prediction_completed.connect(self.on_predictions_loaded)
            self.prediction_worker.error_occurred.connect(self.on_prediction_error)
            
            # Show progress
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            self.load_predictions_button.setEnabled(False)
            
            self.prediction_worker.start()
            
            self.statusBar().showMessage(f"Loading predictions for {batch_size} transactions...")
            
        except Exception as e:
            self.logger.error(f"Error loading predictions: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to load predictions: {str(e)}")
    
    def on_predictions_loaded(self, predictions_data: List[Dict[str, Any]]):
        """Handle completed predictions"""
        self.current_predictions = predictions_data
        self.filtered_predictions = predictions_data.copy()
        
        # Load into table
        self.predictions_table.load_predictions(self.filtered_predictions)
        
        # Update statistics
        self.update_statistics()
        
        # Hide progress
        self.progress_bar.setVisible(False)
        self.load_predictions_button.setEnabled(True)
        
        self.statusBar().showMessage(f"Loaded {len(predictions_data)} predictions")
    
    def on_prediction_error(self, error_message: str):
        """Handle prediction error"""
        self.progress_bar.setVisible(False)
        self.load_predictions_button.setEnabled(True)
        
        QMessageBox.critical(self, "Error", f"Prediction failed: {error_message}")
        self.statusBar().showMessage("Prediction failed")
    
    def on_prediction_selected(self):
        """Handle prediction selection"""
        selected_data = self.predictions_table.get_selected_prediction()
        
        if selected_data:
            transaction = selected_data["transaction"]
            prediction = selected_data["prediction"]
            
            # Update info panel
            self.current_description_label.setText(transaction.description)
            
            if prediction:
                pred_text = f"{prediction.category} > {prediction.sub_category}"
                self.current_prediction_label.setText(pred_text)
                self.current_confidence_label.setText(f"{prediction.confidence:.2f} ({prediction.confidence*100:.0f}%)")
                
                # Set confidence color
                if prediction.confidence >= 0.8:
                    self.current_confidence_label.setStyleSheet("color: green; font-weight: bold;")
                elif prediction.confidence >= 0.6:
                    self.current_confidence_label.setStyleSheet("color: orange; font-weight: bold;")
                else:
                    self.current_confidence_label.setStyleSheet("color: red; font-weight: bold;")
            else:
                self.current_prediction_label.setText("No prediction available")
                self.current_confidence_label.setText("N/A")
                self.current_confidence_label.setStyleSheet("color: gray;")
            
            # Reset correction form
            self.correction_category_combo.setCurrentIndex(0)
            self.correction_subcategory_combo.setCurrentIndex(0)
    
    def approve_current_prediction(self):
        """Approve the current prediction"""
        current_row = self.predictions_table.currentRow()
        selected_data = self.predictions_table.get_selected_prediction()
        
        if selected_data and selected_data["prediction"]:
            prediction = selected_data["prediction"]
            transaction = selected_data["transaction"]
            
            # Apply the prediction as a label
            success = self.training_manager.label_transaction(
                transaction.hash_id,
                prediction.category,
                prediction.sub_category,
                prediction.confidence
            )
            
            if success:
                self.predictions_table.mark_as_approved(current_row)
                self.update_statistics()
                self.statusBar().showMessage(f"Approved: {prediction.category}/{prediction.sub_category}")
            else:
                QMessageBox.warning(self, "Warning", "Failed to approve prediction")
    
    def apply_correction(self):
        """Apply correction to current prediction"""
        current_row = self.predictions_table.currentRow()
        selected_data = self.predictions_table.get_selected_prediction()
        
        category = self.correction_category_combo.currentText()
        subcategory = self.correction_subcategory_combo.currentText()
        
        if category == "Select Category..." or subcategory == "Select Sub-category...":
            QMessageBox.warning(self, "Warning", "Please select both category and sub-category")
            return
        
        if selected_data:
            transaction = selected_data["transaction"]
            
            # Apply the correction as a label
            success = self.training_manager.label_transaction(
                transaction.hash_id,
                category,
                subcategory,
                1.0  # Full confidence for manual corrections
            )
            
            if success:
                self.predictions_table.update_correction(current_row, category, subcategory)
                self.update_statistics()
                self.statusBar().showMessage(f"Corrected to: {category}/{subcategory}")
            else:
                QMessageBox.warning(self, "Warning", "Failed to apply correction")
    
    def skip_current_prediction(self):
        """Skip current prediction"""
        # Just move to next row
        current_row = self.predictions_table.currentRow()
        if current_row < self.predictions_table.rowCount() - 1:
            self.predictions_table.selectRow(current_row + 1)
        
        self.statusBar().showMessage("Skipped prediction")
    
    def approve_high_confidence(self):
        """Approve all high confidence predictions"""
        confidence_threshold = 0.8
        approved_count = 0
        
        for i, data in enumerate(self.filtered_predictions):
            prediction = data["prediction"]
            transaction = data["transaction"]
            
            if prediction and prediction.confidence >= confidence_threshold:
                # Check if not already processed
                status_item = self.predictions_table.item(i, 7)
                if status_item and status_item.text() == "Pending Review":
                    success = self.training_manager.label_transaction(
                        transaction.hash_id,
                        prediction.category,
                        prediction.sub_category,
                        prediction.confidence
                    )
                    
                    if success:
                        self.predictions_table.mark_as_approved(i)
                        approved_count += 1
        
        self.update_statistics()
        self.statusBar().showMessage(f"Approved {approved_count} high confidence predictions")
    
    def apply_filters(self):
        """Apply filters to predictions"""
        if not self.current_predictions:
            return
        
        min_confidence = self.confidence_slider.value() / 100.0
        category_filter = self.category_filter_combo.currentText()
        status_filter = self.status_filter_combo.currentText()
        
        # Update confidence label
        self.confidence_label.setText(f"{int(min_confidence * 100)}%")
        
        # Filter predictions
        filtered = []
        
        for data in self.current_predictions:
            prediction = data["prediction"]
            
            # Confidence filter
            if prediction and prediction.confidence < min_confidence:
                continue
            
            # Category filter
            if category_filter != "All Categories" and prediction:
                if prediction.category != category_filter:
                    continue
            
            # Status filter (would need to track status in data)
            # For now, skip status filtering
            
            filtered.append(data)
        
        self.filtered_predictions = filtered
        self.predictions_table.load_predictions(self.filtered_predictions)
        self.update_statistics()
    
    def update_statistics(self):
        """Update review statistics"""
        if not self.filtered_predictions:
            return
        
        total = len(self.filtered_predictions)
        approved = 0
        corrected = 0
        pending = 0
        
        for i in range(self.predictions_table.rowCount()):
            status_item = self.predictions_table.item(i, 7)
            if status_item:
                status = status_item.text()
                if status == "Approved":
                    approved += 1
                elif status == "Corrected":
                    corrected += 1
                elif status == "Pending Review":
                    pending += 1
        
        self.total_predictions_label.setText(str(total))
        self.approved_count_label.setText(str(approved))
        self.corrected_count_label.setText(str(corrected))
        self.pending_count_label.setText(str(pending))
    
    def export_corrections(self):
        """Export corrections for further analysis"""
        try:
            corrections = []
            
            for i in range(self.predictions_table.rowCount()):
                status_item = self.predictions_table.item(i, 7)
                if status_item and status_item.text() in ["Approved", "Corrected"]:
                    data = self.filtered_predictions[i]
                    transaction = data["transaction"]
                    prediction = data["prediction"]
                    
                    correction_item = self.predictions_table.item(i, 6)
                    correction_text = correction_item.text() if correction_item else ""
                    
                    corrections.append({
                        "description": transaction.description,
                        "predicted_category": prediction.category if prediction else "",
                        "predicted_subcategory": prediction.sub_category if prediction else "",
                        "predicted_confidence": prediction.confidence if prediction else 0.0,
                        "correction": correction_text,
                        "status": status_item.text()
                    })
            
            if corrections:
                # Export to CSV
                df = pd.DataFrame(corrections)
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                export_file = f"ml_review_corrections_{timestamp}.csv"
                df.to_csv(export_file, index=False)
                
                QMessageBox.information(self, "Export Complete", f"Corrections exported to {export_file}")
            else:
                QMessageBox.information(self, "No Data", "No corrections to export")
                
        except Exception as e:
            self.logger.error(f"Error exporting corrections: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to export corrections: {str(e)}")

    def open_category_manager(self):
        """Open category management dialog"""
        try:
            dialog = MLCategoryDialog(self)
            result = dialog.exec()
            # Always reload categories after dialog closes (regardless of result)
            self.refresh_categories()
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to open category manager: {str(e)}")

    def refresh_categories(self):
        """Refresh categories in all components"""
        try:
            # Refresh the category manager first
            self.category_manager.refresh_categories()
            # Then refresh the correction categories
            self.load_correction_categories()
            self.logger.info("Categories refreshed successfully")
        except Exception as e:
            self.logger.error(f"Error refreshing categories: {str(e)}")
