"""
Performance utilities for UI optimization
"""

import time
import threading
from typing import Callable, Any, Dict, Optional
from PySide6.QtCore import QTimer, QObject, Signal
from ..core.logger import get_logger


class Debouncer(QObject):
    """Debounce function calls to prevent excessive execution"""
    
    triggered = Signal()
    
    def __init__(self, delay_ms: int = 300, parent=None):
        super().__init__(parent)
        self.delay_ms = delay_ms
        self.timer = QTimer(self)
        self.timer.setSingleShot(True)
        self.timer.timeout.connect(self.triggered.emit)
        self._pending_args = None
        self._pending_kwargs = None
    
    def call(self, *args, **kwargs):
        """Call the debounced function with arguments"""
        self._pending_args = args
        self._pending_kwargs = kwargs
        self.timer.start(self.delay_ms)
    
    def get_pending_args(self):
        """Get the pending arguments"""
        return self._pending_args, self._pending_kwargs
    
    def cancel(self):
        """Cancel the pending call"""
        self.timer.stop()
        self._pending_args = None
        self._pending_kwargs = None


class ThrottledRefreshManager(QObject):
    """Manage throttled refresh operations to prevent UI lag"""
    
    refresh_requested = Signal(str)  # component_name
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger(__name__)
        self._last_refresh_times = {}
        self._min_refresh_interval = 0.5  # Minimum 500ms between refreshes
        self._pending_refreshes = set()
        self._refresh_timer = QTimer(self)
        self._refresh_timer.setSingleShot(True)
        self._refresh_timer.timeout.connect(self._process_pending_refreshes)
    
    def request_refresh(self, component_name: str, force: bool = False):
        """Request a refresh for a component"""
        current_time = time.time()
        last_refresh = self._last_refresh_times.get(component_name, 0)
        
        if force or (current_time - last_refresh) >= self._min_refresh_interval:
            # Can refresh immediately
            self._execute_refresh(component_name)
        else:
            # Queue for later refresh
            self._pending_refreshes.add(component_name)
            if not self._refresh_timer.isActive():
                remaining_time = self._min_refresh_interval - (current_time - last_refresh)
                self._refresh_timer.start(int(remaining_time * 1000))
    
    def _execute_refresh(self, component_name: str):
        """Execute the refresh for a component"""
        self._last_refresh_times[component_name] = time.time()
        self.refresh_requested.emit(component_name)
        self.logger.debug(f"Executed refresh for {component_name}")
    
    def _process_pending_refreshes(self):
        """Process all pending refreshes"""
        for component_name in list(self._pending_refreshes):
            self._execute_refresh(component_name)
        self._pending_refreshes.clear()
    
    def set_min_interval(self, interval_seconds: float):
        """Set the minimum refresh interval"""
        self._min_refresh_interval = interval_seconds


class PerformanceProfiler:
    """Simple performance profiler for identifying bottlenecks"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self._timers = {}
        self._results = {}
        self._lock = threading.Lock()
    
    def start(self, operation_name: str):
        """Start timing an operation"""
        with self._lock:
            self._timers[operation_name] = time.time()
    
    def end(self, operation_name: str) -> float:
        """End timing an operation and return elapsed time"""
        with self._lock:
            if operation_name in self._timers:
                elapsed = time.time() - self._timers[operation_name]
                self._results[operation_name] = elapsed
                del self._timers[operation_name]
                
                if elapsed > 0.1:  # Log slow operations
                    self.logger.warning(f"Slow operation detected: {operation_name} took {elapsed:.3f}s")
                else:
                    self.logger.debug(f"Operation {operation_name} completed in {elapsed:.3f}s")
                
                return elapsed
        return 0.0
    
    def get_results(self) -> Dict[str, float]:
        """Get all timing results"""
        with self._lock:
            return self._results.copy()
    
    def reset(self):
        """Reset all timing data"""
        with self._lock:
            self._timers.clear()
            self._results.clear()


class CacheManager:
    """Simple cache manager for UI data"""
    
    def __init__(self, max_size: int = 100, ttl_seconds: int = 300):
        self.max_size = max_size
        self.ttl_seconds = ttl_seconds
        self._cache = {}
        self._timestamps = {}
        self._lock = threading.Lock()
        self.logger = get_logger(__name__)
    
    def get(self, key: str) -> Optional[Any]:
        """Get a value from cache"""
        with self._lock:
            if key in self._cache:
                # Check if expired
                if time.time() - self._timestamps[key] > self.ttl_seconds:
                    del self._cache[key]
                    del self._timestamps[key]
                    return None
                return self._cache[key]
        return None
    
    def set(self, key: str, value: Any):
        """Set a value in cache"""
        with self._lock:
            # Remove oldest entries if cache is full
            if len(self._cache) >= self.max_size:
                oldest_key = min(self._timestamps.keys(), key=lambda k: self._timestamps[k])
                del self._cache[oldest_key]
                del self._timestamps[oldest_key]
            
            self._cache[key] = value
            self._timestamps[key] = time.time()
    
    def invalidate(self, key: str):
        """Invalidate a cache entry"""
        with self._lock:
            if key in self._cache:
                del self._cache[key]
                del self._timestamps[key]
    
    def clear(self):
        """Clear all cache entries"""
        with self._lock:
            self._cache.clear()
            self._timestamps.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        with self._lock:
            return {
                'size': len(self._cache),
                'max_size': self.max_size,
                'ttl_seconds': self.ttl_seconds,
                'keys': list(self._cache.keys())
            }


# Global instances for easy access
global_profiler = PerformanceProfiler()
global_cache = CacheManager()
global_refresh_manager = ThrottledRefreshManager()


def profile_operation(operation_name: str):
    """Decorator for profiling operations"""
    def decorator(func: Callable) -> Callable:
        def wrapper(*args, **kwargs):
            global_profiler.start(operation_name)
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                global_profiler.end(operation_name)
        return wrapper
    return decorator


def cached_result(cache_key: str, ttl_seconds: int = 300):
    """Decorator for caching function results"""
    def decorator(func: Callable) -> Callable:
        def wrapper(*args, **kwargs):
            # Create cache key with function name and args
            full_key = f"{cache_key}_{hash(str(args) + str(kwargs))}"
            
            # Try to get from cache
            result = global_cache.get(full_key)
            if result is not None:
                return result
            
            # Execute function and cache result
            result = func(*args, **kwargs)
            global_cache.set(full_key, result)
            return result
        return wrapper
    return decorator
