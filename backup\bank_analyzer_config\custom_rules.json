[{"name": "Indian Restaurants", "category": "Food & Dining", "sub_category": "Restaurants", "keywords": ["dhaba", "punjabi", "south indian", "<PERSON><PERSON>i", "thali", "restaurant", "hotel"], "patterns": ["\\b(dhaba|punjabi|south\\s*indian|biryani|thali|restaurant|hotel)\\b"], "confidence_boost": 0.3, "created_at": "2025-06-23T05:36:41.795864", "is_active": true}, {"name": "Local Transport", "category": "Transportation", "sub_category": "Public Transport", "keywords": ["bmtc", "best", "dtc", "metro", "bus", "railway", "irctc"], "patterns": ["\\b(bmtc|best|dtc|metro|bus|railway|irctc)\\b"], "confidence_boost": 0.4, "created_at": "2025-06-23T05:36:41.795864", "is_active": true}, {"name": "Indian Fuel Stations", "category": "Transportation", "sub_category": "Fuel", "keywords": ["indian oil", "bharat petroleum", "hp petrol", "ioc", "bpcl"], "patterns": ["\\b(indian\\s*oil|bharat\\s*petroleum|hp\\s*petrol|ioc|bpcl)\\b"], "confidence_boost": 0.4, "created_at": "2025-06-23T05:36:41.795864", "is_active": true}, {"name": "UPI Transactions", "category": "Bills & Utilities", "sub_category": "Phone", "keywords": ["upi", "mobile recharge", "prepaid", "postpaid"], "patterns": ["\\bupi.*\\b(recharge|prepaid|postpaid)\\b"], "confidence_boost": 0.3, "created_at": "2025-06-23T05:36:41.808048", "is_active": true}, {"name": "ATM Withdrawals", "category": "Other", "sub_category": "Miscellaneous", "keywords": ["atm", "withdrawal", "cash"], "patterns": ["\\b(atm|withdrawal|cash)\\b"], "confidence_boost": 0.5, "created_at": "2025-06-23T05:36:41.809296", "is_active": true}, {"name": "Electricity Bills", "category": "Bills & Utilities", "sub_category": "Electricity", "keywords": ["electricity", "power", "bescom", "mseb", "kseb"], "patterns": ["\\b(electricity|power|bescom|mseb|kseb)\\b"], "confidence_boost": 0.4, "created_at": "2025-06-23T05:36:41.809296", "is_active": true}, {"name": "Movie Tickets", "category": "Entertainment", "sub_category": "Movies", "keywords": ["pvr", "inox", "cinepolis", "movie", "ticket"], "patterns": ["\\b(pvr|inox|cinepolis|movie|ticket)\\b"], "confidence_boost": 0.4, "created_at": "2025-06-23T05:36:41.809296", "is_active": true}, {"name": "Gym and Fitness", "category": "Healthcare", "sub_category": "Fitness", "keywords": ["gym", "fitness", "yoga", "zumba", "workout"], "patterns": ["\\b(gym|fitness|yoga|zumba|workout)\\b"], "confidence_boost": 0.3, "created_at": "2025-06-23T05:36:41.809296", "is_active": true}, {"name": "Personal Food & Dining - Groceries", "category": "Food & Dining", "sub_category": "Groceries", "keywords": ["weekly", "grocery", "shopping", "vegetables", "and", "fruits"], "patterns": [], "confidence_boost": 0.4, "created_at": "2025-06-23T05:41:22.359293", "is_active": true}, {"name": "Personal Transportation - Fuel", "category": "Transportation", "sub_category": "Fuel", "keywords": ["petrol", "for", "bike"], "patterns": [], "confidence_boost": 0.4, "created_at": "2025-06-23T05:41:22.361163", "is_active": true}, {"name": "Personal Food & Dining - Restaurant", "category": "Food & Dining", "sub_category": "Restaurant", "keywords": ["lunch", "with", "friends"], "patterns": [], "confidence_boost": 0.4, "created_at": "2025-06-23T05:41:22.363136", "is_active": true}, {"name": "Personal Shopping - Clothing", "category": "Shopping", "sub_category": "Clothing", "keywords": ["new", "shirt", "and", "jeans"], "patterns": [], "confidence_boost": 0.4, "created_at": "2025-06-23T05:41:22.375955", "is_active": true}, {"name": "Personal Utilities - Internet", "category": "Utilities", "sub_category": "Internet", "keywords": ["monthly", "internet", "bill"], "patterns": [], "confidence_boost": 0.4, "created_at": "2025-06-23T05:41:22.379129", "is_active": true}, {"name": "Personal Food & Dining - Snacks", "category": "Food & Dining", "sub_category": "Snacks", "keywords": ["evening", "tea", "and", "snacks"], "patterns": [], "confidence_boost": 0.4, "created_at": "2025-06-23T05:41:22.383582", "is_active": true}, {"name": "Personal Transportation - Public Transport", "category": "Transportation", "sub_category": "Public Transport", "keywords": ["bus", "fare"], "patterns": [], "confidence_boost": 0.4, "created_at": "2025-06-23T05:41:22.383582", "is_active": true}, {"name": "Personal Entertainment - Movies", "category": "Entertainment", "sub_category": "Movies", "keywords": ["movie", "ticket"], "patterns": [], "confidence_boost": 0.4, "created_at": "2025-06-23T05:41:22.383582", "is_active": true}, {"name": "Personal Health & Fitness - Medicine", "category": "Health & Fitness", "sub_category": "Medicine", "keywords": ["pharmacy", "purchase"], "patterns": [], "confidence_boost": 0.4, "created_at": "2025-06-23T05:41:22.383582", "is_active": true}, {"name": "Amount Pattern: Entertainment/Movies", "category": "Entertainment", "sub_category": "Movies", "keywords": ["entertainment", "movies"], "patterns": ["amount.*350", "amount.*350"], "confidence_boost": 0.2, "created_at": "2025-06-23T05:41:22.383582", "is_active": true}, {"name": "Amount Pattern: Food & Dining/Groceries", "category": "Food & Dining", "sub_category": "Groceries", "keywords": ["food & dining", "groceries"], "patterns": ["amount.*1800", "amount.*2500"], "confidence_boost": 0.2, "created_at": "2025-06-23T05:41:22.383582", "is_active": true}, {"name": "Amount Pattern: Food & Dining/Restaurant", "category": "Food & Dining", "sub_category": "Restaurant", "keywords": ["food & dining", "restaurant"], "patterns": ["amount.*850", "amount.*850"], "confidence_boost": 0.2, "created_at": "2025-06-23T05:41:22.402382", "is_active": true}, {"name": "Amount Pattern: Food & Dining/Snacks", "category": "Food & Dining", "sub_category": "Snacks", "keywords": ["food & dining", "snacks"], "patterns": ["amount.*150", "amount.*150"], "confidence_boost": 0.2, "created_at": "2025-06-23T05:41:22.406001", "is_active": true}, {"name": "Amount Pattern: Health & Fitness/Medicine", "category": "Health & Fitness", "sub_category": "Medicine", "keywords": ["health & fitness", "medicine"], "patterns": ["amount.*280", "amount.*280"], "confidence_boost": 0.2, "created_at": "2025-06-23T05:41:22.426336", "is_active": true}, {"name": "Amount Pattern: Shopping/Clothing", "category": "Shopping", "sub_category": "Clothing", "keywords": ["shopping", "clothing"], "patterns": ["amount.*3200", "amount.*3200"], "confidence_boost": 0.2, "created_at": "2025-06-23T05:41:22.426336", "is_active": true}, {"name": "Amount Pattern: Transportation/Fuel", "category": "Transportation", "sub_category": "Fuel", "keywords": ["transportation", "fuel"], "patterns": ["amount.*1200", "amount.*1200"], "confidence_boost": 0.2, "created_at": "2025-06-23T05:41:22.426336", "is_active": true}, {"name": "Amount Pattern: Transportation/Public Transport", "category": "Transportation", "sub_category": "Public Transport", "keywords": ["transportation", "public transport"], "patterns": ["amount.*45", "amount.*45"], "confidence_boost": 0.2, "created_at": "2025-06-23T05:41:22.426336", "is_active": true}, {"name": "Amount Pattern: Utilities/Internet", "category": "Utilities", "sub_category": "Internet", "keywords": ["utilities", "internet"], "patterns": ["amount.*999", "amount.*999"], "confidence_boost": 0.2, "created_at": "2025-06-23T05:41:22.444121", "is_active": true}, {"name": "Mode Pattern: Entertainment via Card", "category": "Entertainment", "sub_category": "Auto", "keywords": ["entertainment", "card"], "patterns": [], "confidence_boost": 0.15, "created_at": "2025-06-23T05:41:22.446175", "is_active": true}, {"name": "Mode Pattern: Food & Dining via Cash", "category": "Food & Dining", "sub_category": "Auto", "keywords": ["food & dining", "cash"], "patterns": [], "confidence_boost": 0.15, "created_at": "2025-06-23T05:41:22.453461", "is_active": true}, {"name": "Mode Pattern: Food & Dining via UPI", "category": "Food & Dining", "sub_category": "Auto", "keywords": ["food & dining", "upi"], "patterns": [], "confidence_boost": 0.15, "created_at": "2025-06-23T05:41:22.481060", "is_active": true}, {"name": "Mode Pattern: Health & Fitness via Cash", "category": "Health & Fitness", "sub_category": "Auto", "keywords": ["health & fitness", "cash"], "patterns": [], "confidence_boost": 0.15, "created_at": "2025-06-23T05:41:22.515093", "is_active": true}, {"name": "Mode Pattern: Shopping via Card", "category": "Shopping", "sub_category": "Auto", "keywords": ["shopping", "card"], "patterns": [], "confidence_boost": 0.15, "created_at": "2025-06-23T05:41:22.518957", "is_active": true}, {"name": "Mode Pattern: Transportation via Card", "category": "Transportation", "sub_category": "Auto", "keywords": ["transportation", "card"], "patterns": [], "confidence_boost": 0.15, "created_at": "2025-06-23T05:41:22.523287", "is_active": true}, {"name": "Mode Pattern: Transportation via UPI", "category": "Transportation", "sub_category": "Auto", "keywords": ["transportation", "upi"], "patterns": [], "confidence_boost": 0.15, "created_at": "2025-06-23T05:41:22.542095", "is_active": true}, {"name": "Mode Pattern: Utilities via Bank Transfer", "category": "Utilities", "sub_category": "Auto", "keywords": ["utilities", "bank transfer"], "patterns": [], "confidence_boost": 0.15, "created_at": "2025-06-23T05:41:22.542945", "is_active": true}]