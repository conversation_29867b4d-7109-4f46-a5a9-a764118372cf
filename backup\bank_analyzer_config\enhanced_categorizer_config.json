{"enhanced_features": {"enable_smart_filtering": true, "enable_batch_processing": true, "enable_dynamic_categories": true, "enable_transaction_prioritization": true, "enable_budget_protection": true}, "budget_settings": {"max_daily_cost": 1.0, "max_total_cost": 5.0, "safety_margin": 0.1, "minimum_reserve": 0.25, "warning_threshold": 0.7, "critical_threshold": 0.85, "emergency_threshold": 0.95}, "batch_processing": {"min_batch_size": 20, "max_batch_size": 30, "batch_delay_seconds": 1.0, "max_daily_budget_usage": 0.9}, "filtering": {"min_ai_confidence": 0.6, "enable_generic_patterns": true, "enable_merchant_detection": true, "exclude_person_names": true, "exclude_unique_identifiers": true}, "prioritization": {"high_priority_threshold": 8, "medium_priority_threshold": 5, "amount_weight": 0.25, "frequency_weight": 0.2, "complexity_weight": 0.15, "recency_weight": 0.15, "ai_benefit_weight": 0.15, "business_impact_weight": 0.1}, "category_management": {"auto_create_categories": true, "validate_category_names": true, "log_category_creation": true, "normalize_category_names": true}}