[{"timestamp": "2025-06-27T16:57:13.273016", "description": "UPI-ZOMATO ONLINE FOOD", "amount": 250.0, "transaction_type": "DEBIT", "category": "Food & Dining", "sub_category": "Delivery", "confidence": 0.9, "source": "ai"}, {"timestamp": "2025-06-27T16:57:19.304160", "description": "POS PURCHASE RELIANCE FRESH", "amount": 1200.0, "transaction_type": "DEBIT", "category": "Supermarket", "sub_category": "Miscellaneous", "confidence": 0.7, "source": "ai"}, {"timestamp": "2025-06-27T16:57:19.310271", "description": "NEFT CREDIT SALARY", "amount": 50000.0, "transaction_type": "CREDIT", "category": "Salary", "sub_category": "Miscellaneous", "confidence": 0.7, "source": "ai"}, {"timestamp": "2025-06-27T16:57:19.317553", "description": "TRANSFER TO JOHN DOE", "amount": 5000.0, "transaction_type": "DEBIT", "category": "Personal", "sub_category": "Transfers", "confidence": 0.8, "source": "ai"}, {"timestamp": "2025-06-27T16:57:19.324088", "description": "ONLINE PURCHASE AMAZON", "amount": 15000.0, "transaction_type": "DEBIT", "category": "Shopping", "sub_category": "Online Shopping", "confidence": 0.8, "source": "ai"}, {"timestamp": "2025-06-27T16:57:19.331068", "description": "UPI-SWIGGY FOOD DELIVERY", "amount": 300.0, "transaction_type": "DEBIT", "category": "Takeout/Delivery", "sub_category": "Miscellaneous", "confidence": 0.7, "source": "ai"}, {"timestamp": "2025-06-27T16:57:19.338049", "description": "UPI-ZOMATO ONLINE FOOD", "amount": 250.0, "transaction_type": "DEBIT", "category": "Online Food Delivery", "sub_category": "Miscellaneous", "confidence": 0.7, "source": "ai"}, {"timestamp": "2025-06-27T16:57:19.342038", "description": "PAYMENT TO UNIQUE SHOP XYZ", "amount": 150.0, "transaction_type": "DEBIT", "category": "Shopping", "sub_category": "Retail/Store Purchases", "confidence": 0.8, "source": "ai"}, {"timestamp": "2025-06-27T16:57:19.347025", "description": "UPI-SWIGGY FOOD DELIVERY", "amount": 280.0, "transaction_type": "DEBIT", "category": "Takeout/Delivery", "sub_category": "Miscellaneous", "confidence": 0.7, "source": "ai"}, {"timestamp": "2025-06-27T16:57:21.067574", "description": "POS PURCHASE RELIANCE FRESH", "amount": 1200.0, "transaction_type": "DEBIT", "category": "Supermarket", "sub_category": "Miscellaneous", "confidence": 0.7, "source": "ai"}, {"timestamp": "2025-06-27T16:57:21.070132", "description": "NEFT CREDIT SALARY", "amount": 50000.0, "transaction_type": "CREDIT", "category": "Salary", "sub_category": "Miscellaneous", "confidence": 0.7, "source": "ai"}, {"timestamp": "2025-06-27T16:57:21.071142", "description": "TRANSFER TO JOHN DOE", "amount": 5000.0, "transaction_type": "DEBIT", "category": "Personal", "sub_category": "Transfers", "confidence": 0.8, "source": "ai"}, {"timestamp": "2025-06-27T16:57:21.073449", "description": "ONLINE PURCHASE AMAZON", "amount": 15000.0, "transaction_type": "DEBIT", "category": "Shopping", "sub_category": "Online Shopping", "confidence": 0.8, "source": "ai"}, {"timestamp": "2025-06-27T16:57:21.075461", "description": "UPI-ZOMATO ONLINE FOOD", "amount": 250.0, "transaction_type": "DEBIT", "category": "Online Food Delivery", "sub_category": "Miscellaneous", "confidence": 0.7, "source": "ai"}, {"timestamp": "2025-06-27T16:57:21.078454", "description": "PAYMENT TO UNIQUE SHOP XYZ", "amount": 150.0, "transaction_type": "DEBIT", "category": "Shopping", "sub_category": "Retail/Store Purchases", "confidence": 0.8, "source": "ai"}, {"timestamp": "2025-06-27T16:59:30.918057", "description": "UPI-ZOMATO ONLINE FOOD", "amount": 250.0, "transaction_type": "DEBIT", "category": "Food & Dining", "sub_category": "Delivery", "confidence": 0.9, "source": "ai"}, {"timestamp": "2025-06-27T17:30:25.387512", "description": "ATM-CASH WITHDRAWAL HDFC", "amount": 500.0, "transaction_type": "DEBIT", "category": "Test Category", "sub_category": "Test Sub-Category", "confidence": 0.85, "source": "test"}, {"timestamp": "2025-06-27T17:30:25.389954", "description": "UPI-ZOMATO ONLINE FOOD DELIVERY", "amount": 250.0, "transaction_type": "DEBIT", "category": "Test Category", "sub_category": "Test Sub-Category", "confidence": 0.85, "source": "test"}, {"timestamp": "2025-06-27T17:30:25.392347", "description": "POS PURCHASE RELIANCE FRESH GROCERY", "amount": 1200.0, "transaction_type": "DEBIT", "category": "Test Category", "sub_category": "Test Sub-Category", "confidence": 0.85, "source": "test"}, {"timestamp": "2025-06-27T17:30:25.393344", "description": "NEFT CREDIT SALARY COMPANY XYZ", "amount": 50000.0, "transaction_type": "CREDIT", "category": "Test Category", "sub_category": "Test Sub-Category", "confidence": 0.85, "source": "test"}, {"timestamp": "2025-06-27T17:30:25.395338", "description": "INTEREST CREDIT SAVINGS ACCOUNT", "amount": 125.5, "transaction_type": "CREDIT", "category": "Test Category", "sub_category": "Test Sub-Category", "confidence": 0.85, "source": "test"}, {"timestamp": "2025-06-27T17:30:30.178779", "description": "ONLINE PURCHASE FLIPKART", "amount": 2000.0, "transaction_type": "DEBIT", "category": "Shopping", "sub_category": "Online Shopping", "confidence": 0.9, "source": "ai"}, {"timestamp": "2025-06-27T17:30:31.108052", "description": "UNUSUAL MERCHANT XYZ123 PAYMENT", "amount": 7777.77, "transaction_type": "DEBIT", "category": "Unknown/Unidentified", "sub_category": "Miscellaneous", "confidence": 0.85, "source": "ai"}, {"timestamp": "2025-06-27T17:30:31.110206", "description": "ANNUAL FEE CREDIT CARD", "amount": 590.0, "transaction_type": "DEBIT", "category": "Finance", "sub_category": "Credit Card Fees", "confidence": 0.8, "source": "ai"}, {"timestamp": "2025-06-27T17:30:31.111357", "description": "INTEREST CREDIT SAVINGS ACCOUNT", "amount": 125.5, "transaction_type": "CREDIT", "category": "Interest Income", "sub_category": "Miscellaneous", "confidence": 0.7, "source": "ai"}, {"timestamp": "2025-06-27T17:30:31.114834", "description": "SMS CHARGE MONTHLY", "amount": 25.0, "transaction_type": "DEBIT", "category": "Communications", "sub_category": "Mobile Phone Bill", "confidence": 0.8, "source": "ai"}, {"timestamp": "2025-06-27T17:30:33.026628", "description": "ONLINE PURCHASE FLIPKART", "amount": 2000.0, "transaction_type": "DEBIT", "category": "Shopping", "sub_category": "Online Shopping", "confidence": 0.9, "source": "ai"}, {"timestamp": "2025-06-27T17:30:33.485920", "description": "UNUSUAL MERCHANT XYZ123 PAYMENT", "amount": 7777.77, "transaction_type": "DEBIT", "category": "Unknown/Unidentified", "sub_category": "Miscellaneous", "confidence": 0.85, "source": "ai"}, {"timestamp": "2025-06-27T17:30:33.489544", "description": "ANNUAL FEE CREDIT CARD", "amount": 590.0, "transaction_type": "DEBIT", "category": "Finance", "sub_category": "Credit Card Fees", "confidence": 0.8, "source": "ai"}, {"timestamp": "2025-06-27T17:30:33.491743", "description": "INTEREST CREDIT SAVINGS ACCOUNT", "amount": 125.5, "transaction_type": "CREDIT", "category": "Interest Income", "sub_category": "Miscellaneous", "confidence": 0.7, "source": "ai"}, {"timestamp": "2025-06-27T17:30:33.495287", "description": "SMS CHARGE MONTHLY", "amount": 25.0, "transaction_type": "DEBIT", "category": "Communications", "sub_category": "Mobile Phone Bill", "confidence": 0.8, "source": "ai"}, {"timestamp": "2025-06-27T17:30:35.986095", "description": "INTEREST CREDIT SAVINGS ACCOUNT", "amount": 125.5, "transaction_type": "CREDIT", "category": "Interest Income", "sub_category": "Miscellaneous", "confidence": 0.7, "source": "ai"}, {"timestamp": "2025-06-27T17:30:40.916459", "description": "INTEREST CREDIT SAVINGS ACCOUNT", "amount": 125.5, "transaction_type": "CREDIT", "category": "Interest Income", "sub_category": "Miscellaneous", "confidence": 0.7, "source": "ai"}, {"timestamp": "2025-06-27T17:45:15.673962", "description": "PETROL PUMP FUEL PURCHASE", "amount": 2000.0, "transaction_type": "DEBIT", "category": "Transportation", "sub_category": "Fuel", "confidence": 0.9, "source": "ai"}, {"timestamp": "2025-06-27T17:45:16.263369", "description": "AMAZON PURCHASE ONLINE", "amount": 1500.0, "transaction_type": "DEBIT", "category": "Shopping", "sub_category": "Online Shopping", "confidence": 0.9, "source": "ai"}, {"timestamp": "2025-06-27T17:45:16.266661", "description": "INTEREST CREDIT SAVINGS", "amount": 125.5, "transaction_type": "CREDIT", "category": "Interest Income", "sub_category": "Miscellaneous", "confidence": 0.7, "source": "ai"}, {"timestamp": "2025-06-27T17:45:23.290954", "description": "PETROL PUMP FUEL PURCHASE", "amount": 2000.0, "transaction_type": "DEBIT", "category": "Transportation", "sub_category": "Fuel", "confidence": 0.9, "source": "ai"}, {"timestamp": "2025-06-27T17:45:23.747577", "description": "AMAZON PURCHASE ONLINE", "amount": 1500.0, "transaction_type": "DEBIT", "category": "Shopping", "sub_category": "Online Shopping", "confidence": 0.9, "source": "ai"}, {"timestamp": "2025-06-27T17:45:23.750754", "description": "INTEREST CREDIT SAVINGS", "amount": 125.5, "transaction_type": "CREDIT", "category": "Interest Income", "sub_category": "Miscellaneous", "confidence": 0.7, "source": "ai"}, {"timestamp": "2025-06-27T18:20:37.515905", "description": "SALARY CREDIT FROM COMPANY", "amount": 50000.0, "transaction_type": "CREDIT", "category": "Income", "sub_category": "Salary", "confidence": 0.9, "source": "ai"}, {"timestamp": "2025-06-27T18:20:37.960801", "description": "AMAZON ONLINE SHOPPING", "amount": 1200.0, "transaction_type": "DEBIT", "category": "Shopping", "sub_category": "Online Shopping", "confidence": 0.9, "source": "ai"}, {"timestamp": "2025-06-27T18:22:19.331866", "description": "SALARY CREDIT FROM COMPANY", "amount": 50000.0, "transaction_type": "CREDIT", "category": "Income", "sub_category": "Salary", "confidence": 0.9, "source": "ai"}, {"timestamp": "2025-06-27T18:22:19.720092", "description": "AMAZON ONLINE SHOPPING", "amount": 1200.0, "transaction_type": "DEBIT", "category": "Shopping", "sub_category": "Online Shopping", "confidence": 0.9, "source": "ai"}, {"timestamp": "2025-06-27T18:30:30.382652", "description": "GROCERY STORE PURCHASE", "amount": 1200.0, "transaction_type": "Debit", "category": "**Shopping**", "sub_category": "**Groceries**", "confidence": 0.9, "source": "ai"}, {"timestamp": "2025-06-27T18:30:30.807070", "description": "AMAZON PURCHASE", "amount": 899.0, "transaction_type": "Debit", "category": "Online Shopping", "sub_category": "Miscellaneous", "confidence": 0.7, "source": "ai"}]