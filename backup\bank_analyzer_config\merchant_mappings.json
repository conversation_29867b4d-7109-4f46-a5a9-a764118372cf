[{"merchant_name": "ZOMATO", "category": "Food & Dining", "sub_category": "Delivery", "confidence": 0.95, "transaction_mode": "UPI", "notes": "Food delivery app", "created_at": "2025-06-23T05:36:41.639355"}, {"merchant_name": "SWIGGY", "category": "Food & Dining", "sub_category": "Delivery", "confidence": 0.95, "transaction_mode": "UPI", "notes": "Food delivery app", "created_at": "2025-06-23T05:36:41.639355"}, {"merchant_name": "UBER EATS", "category": "Food & Dining", "sub_category": "Delivery", "confidence": 0.9, "transaction_mode": "UPI", "notes": "Food delivery app", "created_at": "2025-06-23T05:36:41.639355"}, {"merchant_name": "FOODPANDA", "category": "Food & Dining", "sub_category": "Delivery", "confidence": 0.85, "transaction_mode": "UPI", "notes": "Food delivery app", "created_at": "2025-06-23T05:36:41.639355"}, {"merchant_name": "UBER", "category": "Transportation", "sub_category": "Taxi/Uber", "confidence": 0.95, "transaction_mode": "UPI", "notes": "Ride sharing", "created_at": "2025-06-23T05:36:41.639355"}, {"merchant_name": "OLA", "category": "Transportation", "sub_category": "Taxi/Uber", "confidence": 0.95, "transaction_mode": "UPI", "notes": "Ride sharing", "created_at": "2025-06-23T05:36:41.663139"}, {"merchant_name": "RAPIDO", "category": "Transportation", "sub_category": "Taxi/Uber", "confidence": 0.9, "transaction_mode": "UPI", "notes": "Bike taxi", "created_at": "2025-06-23T05:36:41.666308"}, {"merchant_name": "BIG BAZAAR", "category": "Food & Dining", "sub_category": "Groceries", "confidence": 0.9, "transaction_mode": "Debit Card", "notes": "Retail chain", "created_at": "2025-06-23T05:36:41.668782"}, {"merchant_name": "DMART", "category": "Food & Dining", "sub_category": "Groceries", "confidence": 0.9, "transaction_mode": "Debit Card", "notes": "Retail chain", "created_at": "2025-06-23T05:36:41.673978"}, {"merchant_name": "RELIANCE FRESH", "category": "Food & Dining", "sub_category": "Groceries", "confidence": 0.9, "transaction_mode": "Debit Card", "notes": "Grocery store", "created_at": "2025-06-23T05:36:41.680125"}, {"merchant_name": "MORE", "category": "Food & Dining", "sub_category": "Groceries", "confidence": 0.85, "transaction_mode": "Debit Card", "notes": "Grocery store", "created_at": "2025-06-23T05:36:41.680125"}, {"merchant_name": "SPENCER", "category": "Food & Dining", "sub_category": "Groceries", "confidence": 0.85, "transaction_mode": "Debit Card", "notes": "Grocery store", "created_at": "2025-06-23T05:36:41.680125"}, {"merchant_name": "FOOD WORLD", "category": "Food & Dining", "sub_category": "Groceries", "confidence": 0.85, "transaction_mode": "Debit Card", "notes": "Grocery store", "created_at": "2025-06-23T05:36:41.680125"}, {"merchant_name": "AMAZON", "category": "Shopping", "sub_category": "Electronics", "confidence": 0.85, "transaction_mode": "Credit Card", "notes": "E-commerce", "created_at": "2025-06-23T05:36:41.680125"}, {"merchant_name": "FLIPKART", "category": "Shopping", "sub_category": "Electronics", "confidence": 0.85, "transaction_mode": "Credit Card", "notes": "E-commerce", "created_at": "2025-06-23T05:36:41.705602"}, {"merchant_name": "MYNTRA", "category": "Shopping", "sub_category": "Clothing", "confidence": 0.9, "transaction_mode": "Credit Card", "notes": "Fashion e-commerce", "created_at": "2025-06-23T05:36:41.712196"}, {"merchant_name": "AJIO", "category": "Shopping", "sub_category": "Clothing", "confidence": 0.85, "transaction_mode": "Credit Card", "notes": "Fashion e-commerce", "created_at": "2025-06-23T05:36:41.712726"}, {"merchant_name": "SNAPDEAL", "category": "Shopping", "sub_category": "Electronics", "confidence": 0.8, "transaction_mode": "Credit Card", "notes": "E-commerce", "created_at": "2025-06-23T05:36:41.712726"}, {"merchant_name": "INDIAN OIL", "category": "Transportation", "sub_category": "Fuel", "confidence": 0.95, "transaction_mode": "Debit Card", "notes": "Petrol pump", "created_at": "2025-06-23T05:36:41.712726"}, {"merchant_name": "BHARAT PETROLEUM", "category": "Transportation", "sub_category": "Fuel", "confidence": 0.95, "transaction_mode": "Debit Card", "notes": "Petrol pump", "created_at": "2025-06-23T05:36:41.712726"}, {"merchant_name": "HP", "category": "Transportation", "sub_category": "Fuel", "confidence": 0.9, "transaction_mode": "Debit Card", "notes": "Petrol pump", "created_at": "2025-06-23T05:36:41.725423"}, {"merchant_name": "SHELL", "category": "Transportation", "sub_category": "Fuel", "confidence": 0.9, "transaction_mode": "Credit Card", "notes": "Petrol pump", "created_at": "2025-06-23T05:36:41.729062"}, {"merchant_name": "ESSAR", "category": "Transportation", "sub_category": "Fuel", "confidence": 0.85, "transaction_mode": "Debit Card", "notes": "Petrol pump", "created_at": "2025-06-23T05:36:41.729062"}, {"merchant_name": "AIRTEL", "category": "Bills & Utilities", "sub_category": "Internet", "confidence": 0.9, "transaction_mode": "Net Banking", "notes": "Telecom", "created_at": "2025-06-23T05:36:41.729062"}, {"merchant_name": "JIO", "category": "Bills & Utilities", "sub_category": "Internet", "confidence": 0.9, "transaction_mode": "UPI", "notes": "Telecom", "created_at": "2025-06-23T05:36:41.729062"}, {"merchant_name": "BSNL", "category": "Bills & Utilities", "sub_category": "Internet", "confidence": 0.85, "transaction_mode": "Net Banking", "notes": "Telecom", "created_at": "2025-06-23T05:36:41.740476"}, {"merchant_name": "ACT FIBERNET", "category": "Bills & Utilities", "sub_category": "Internet", "confidence": 0.85, "transaction_mode": "Net Banking", "notes": "ISP", "created_at": "2025-06-23T05:36:41.743601"}, {"merchant_name": "HATHWAY", "category": "Bills & Utilities", "sub_category": "Internet", "confidence": 0.8, "transaction_mode": "Net Banking", "notes": "ISP", "created_at": "2025-06-23T05:36:41.745962"}, {"merchant_name": "APOLLO PHARMACY", "category": "Healthcare", "sub_category": "Pharmacy", "confidence": 0.95, "transaction_mode": "Debit Card", "notes": "Pharmacy chain", "created_at": "2025-06-23T05:36:41.745962"}, {"merchant_name": "MEDPLUS", "category": "Healthcare", "sub_category": "Pharmacy", "confidence": 0.9, "transaction_mode": "Debit Card", "notes": "Pharmacy chain", "created_at": "2025-06-23T05:36:41.745962"}, {"merchant_name": "1MG", "category": "Healthcare", "sub_category": "Pharmacy", "confidence": 0.9, "transaction_mode": "UPI", "notes": "Online pharmacy", "created_at": "2025-06-23T05:36:41.745962"}, {"merchant_name": "PHARMEASY", "category": "Healthcare", "sub_category": "Pharmacy", "confidence": 0.85, "transaction_mode": "UPI", "notes": "Online pharmacy", "created_at": "2025-06-23T05:36:41.761285"}, {"merchant_name": "NETMEDS", "category": "Healthcare", "sub_category": "Pharmacy", "confidence": 0.85, "transaction_mode": "UPI", "notes": "Online pharmacy", "created_at": "2025-06-23T05:36:41.761285"}, {"merchant_name": "MCDONALD", "category": "Food & Dining", "sub_category": "Fast Food", "confidence": 0.95, "transaction_mode": "Credit Card", "notes": "Fast food chain", "created_at": "2025-06-23T05:36:41.761285"}, {"merchant_name": "KFC", "category": "Food & Dining", "sub_category": "Fast Food", "confidence": 0.95, "transaction_mode": "Credit Card", "notes": "Fast food chain", "created_at": "2025-06-23T05:36:41.761285"}, {"merchant_name": "PIZZA HUT", "category": "Food & Dining", "sub_category": "Fast Food", "confidence": 0.9, "transaction_mode": "Credit Card", "notes": "Pizza chain", "created_at": "2025-06-23T05:36:41.773093"}, {"merchant_name": "DOMINOS", "category": "Food & Dining", "sub_category": "Fast Food", "confidence": 0.9, "transaction_mode": "Credit Card", "notes": "Pizza chain", "created_at": "2025-06-23T05:36:41.779510"}, {"merchant_name": "SUBWAY", "category": "Food & Dining", "sub_category": "Fast Food", "confidence": 0.85, "transaction_mode": "Credit Card", "notes": "Sandwich chain", "created_at": "2025-06-23T05:36:41.779510"}, {"merchant_name": "BURGER KING", "category": "Food & Dining", "sub_category": "Fast Food", "confidence": 0.85, "transaction_mode": "Credit Card", "notes": "Fast food chain", "created_at": "2025-06-23T05:36:41.779510"}, {"merchant_name": "CAFE COFFEE DAY", "category": "Food & Dining", "sub_category": "Coffee", "confidence": 0.9, "transaction_mode": "Credit Card", "notes": "Coffee chain", "created_at": "2025-06-23T05:36:41.793359"}, {"merchant_name": "STARBUCKS", "category": "Food & Dining", "sub_category": "Coffee", "confidence": 0.9, "transaction_mode": "Credit Card", "notes": "Coffee chain", "created_at": "2025-06-23T05:36:41.795864"}, {"merchant_name": "BARISTA", "category": "Food & Dining", "sub_category": "Coffee", "confidence": 0.85, "transaction_mode": "Credit Card", "notes": "Coffee chain", "created_at": "2025-06-23T05:36:41.795864"}, {"merchant_name": "GROCERY", "category": "Food & Dining", "sub_category": "Groceries", "confidence": 0.9, "transaction_mode": "UPI", "notes": "Personal pattern", "created_at": "2025-06-23T05:41:22.542945"}, {"merchant_name": "VEGETABLES", "category": "Food & Dining", "sub_category": "Groceries", "confidence": 0.85, "transaction_mode": "UPI", "notes": "Personal pattern", "created_at": "2025-06-23T05:41:22.557278"}, {"merchant_name": "FRUITS", "category": "Food & Dining", "sub_category": "Groceries", "confidence": 0.85, "transaction_mode": "Cash", "notes": "Personal pattern", "created_at": "2025-06-23T05:41:22.561290"}, {"merchant_name": "WEEKLY SHOPPING", "category": "Food & Dining", "sub_category": "Groceries", "confidence": 0.8, "transaction_mode": "Cash", "notes": "Personal pattern", "created_at": "2025-06-23T05:41:22.575226"}, {"merchant_name": "PETROL", "category": "Transportation", "sub_category": "Fuel", "confidence": 0.95, "transaction_mode": "Card", "notes": "Personal pattern", "created_at": "2025-06-23T05:41:22.578940"}, {"merchant_name": "BIKE", "category": "Transportation", "sub_category": "Fuel", "confidence": 0.85, "transaction_mode": "Card", "notes": "Personal pattern", "created_at": "2025-06-23T05:41:22.578940"}, {"merchant_name": "LUNCH", "category": "Food & Dining", "sub_category": "Restaurant", "confidence": 0.8, "transaction_mode": "UPI", "notes": "Personal pattern", "created_at": "2025-06-23T05:41:22.578940"}, {"merchant_name": "FRIENDS", "category": "Food & Dining", "sub_category": "Restaurant", "confidence": 0.75, "transaction_mode": "UPI", "notes": "Personal pattern", "created_at": "2025-06-23T05:41:22.594933"}, {"merchant_name": "INTERNET", "category": "Utilities", "sub_category": "Internet", "confidence": 0.95, "transaction_mode": "Bank Transfer", "notes": "Personal pattern", "created_at": "2025-06-23T05:41:22.604369"}, {"merchant_name": "MONTHLY", "category": "Utilities", "sub_category": "Internet", "confidence": 0.8, "transaction_mode": "Bank Transfer", "notes": "Personal pattern", "created_at": "2025-06-23T05:41:22.613803"}, {"merchant_name": "MOVIE", "category": "Entertainment", "sub_category": "Movies", "confidence": 0.9, "transaction_mode": "Card", "notes": "Personal pattern", "created_at": "2025-06-23T05:41:22.621330"}, {"merchant_name": "TICKET", "category": "Entertainment", "sub_category": "Movies", "confidence": 0.85, "transaction_mode": "Card", "notes": "Personal pattern", "created_at": "2025-06-23T05:41:22.624804"}, {"merchant_name": "PHARMACY", "category": "Health & Fitness", "sub_category": "Medicine", "confidence": 0.9, "transaction_mode": "Cash", "notes": "Personal pattern", "created_at": "2025-06-23T05:41:22.629089"}, {"merchant_name": "MEDICINE", "category": "Health & Fitness", "sub_category": "Medicine", "confidence": 0.95, "transaction_mode": "Cash", "notes": "Personal pattern", "created_at": "2025-06-23T05:41:22.632607"}, {"merchant_name": "BUS", "category": "Transportation", "sub_category": "Public Transport", "confidence": 0.95, "transaction_mode": "UPI", "notes": "Personal pattern", "created_at": "2025-06-23T05:41:22.637222"}, {"merchant_name": "FARE", "category": "Transportation", "sub_category": "Public Transport", "confidence": 0.9, "transaction_mode": "UPI", "notes": "Personal pattern", "created_at": "2025-06-23T05:41:22.640526"}, {"merchant_name": "TEA", "category": "Food & Dining", "sub_category": "Snacks", "confidence": 0.85, "transaction_mode": "Cash", "notes": "Personal pattern", "created_at": "2025-06-23T05:41:22.644440"}, {"merchant_name": "SNACKS", "category": "Food & Dining", "sub_category": "Snacks", "confidence": 0.9, "transaction_mode": "Cash", "notes": "Personal pattern", "created_at": "2025-06-23T05:41:22.648971"}, {"merchant_name": "EVENING", "category": "Food & Dining", "sub_category": "Snacks", "confidence": 0.75, "transaction_mode": "Cash", "notes": "Personal pattern", "created_at": "2025-06-23T05:41:22.653536"}]