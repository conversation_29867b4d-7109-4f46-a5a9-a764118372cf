[{"model_version": "1.0_20250628_015303", "training_date": "2025-06-28T01:53:03.966028", "accuracy": 0.****************, "training_samples": 192, "categories_count": 17, "cv_accuracy_mean": 0.****************, "cv_accuracy_std": 0.*****************, "evaluation_metrics": {"Bank charges": {"precision": 1.0, "recall": 0.****************, "f1-score": 0.****************, "support": 9.0}, "Bills & Utilities": {"precision": 0.****************, "recall": 1.0, "f1-score": 0.****************, "support": 12.0}, "Borrowed": {"precision": 0.*****************, "recall": 0.*****************, "f1-score": 0.*****************, "support": 7.0}, "Cash limit": {"precision": 1.0, "recall": 0.****************, "f1-score": 0.****************, "support": 9.0}, "EMI": {"precision": 0.****************, "recall": 0.****************, "f1-score": 0.****************, "support": 14.0}, "Gifts & Donations": {"precision": 0.****************, "recall": 0.****************, "f1-score": 0.****************, "support": 15.0}, "House Hold Expenses": {"precision": 0.****************, "recall": 0.****************, "f1-score": 0.****************, "support": 6.0}, "Investment": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 8.0}, "Lend To": {"precision": 1.0, "recall": 0.25, "f1-score": 0.4, "support": 4.0}, "Online Stores": {"precision": 0.****************, "recall": 0.9090909090909091, "f1-score": 0.8695652173913043, "support": 11.0}, "Refunds": {"precision": 0.8, "recall": 0.5714285714285714, "f1-score": 0.****************, "support": 7.0}, "Returned": {"precision": 0.6363636363636364, "recall": 0.****************, "f1-score": 0.7, "support": 9.0}, "Salary": {"precision": 0.9791666666666666, "recall": 0.9791666666666666, "f1-score": 0.9791666666666666, "support": 48.0}, "Shared Money": {"precision": 0.8, "recall": 0.****************, "f1-score": 0.7272727272727273, "support": 6.0}, "Shopping": {"precision": 0.875, "recall": 0.****************, "f1-score": 0.8235294117647058, "support": 9.0}, "Transportation": {"precision": 0.****************, "recall": 0.****************, "f1-score": 0.****************, "support": 15.0}, "Youtube Income": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 3.0}, "accuracy": 0.****************, "macro avg": {"precision": 0.****************, "recall": 0.****************, "f1-score": 0.****************, "support": 192.0}, "weighted avg": {"precision": 0.****************, "recall": 0.****************, "f1-score": 0.****************, "support": 192.0}}}, {"model_version": "1.0_20250628_021857", "training_date": "2025-06-28T02:18:57.836277", "accuracy": 0.****************, "training_samples": 192, "categories_count": 17, "cv_accuracy_mean": 0.****************, "cv_accuracy_std": 0.*****************, "evaluation_metrics": {"Bank charges": {"precision": 1.0, "recall": 0.****************, "f1-score": 0.****************, "support": 9.0}, "Bills & Utilities": {"precision": 0.****************, "recall": 1.0, "f1-score": 0.****************, "support": 12.0}, "Borrowed": {"precision": 0.*****************, "recall": 0.*****************, "f1-score": 0.*****************, "support": 7.0}, "Cash limit": {"precision": 1.0, "recall": 0.****************, "f1-score": 0.****************, "support": 9.0}, "EMI": {"precision": 0.****************, "recall": 0.****************, "f1-score": 0.****************, "support": 14.0}, "Gifts & Donations": {"precision": 0.****************, "recall": 0.****************, "f1-score": 0.****************, "support": 15.0}, "House Hold Expenses": {"precision": 0.****************, "recall": 0.****************, "f1-score": 0.****************, "support": 6.0}, "Investment": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 8.0}, "Lend To": {"precision": 1.0, "recall": 0.25, "f1-score": 0.4, "support": 4.0}, "Online Stores": {"precision": 0.****************, "recall": 0.9090909090909091, "f1-score": 0.8695652173913043, "support": 11.0}, "Refunds": {"precision": 0.8, "recall": 0.5714285714285714, "f1-score": 0.****************, "support": 7.0}, "Returned": {"precision": 0.6363636363636364, "recall": 0.****************, "f1-score": 0.7, "support": 9.0}, "Salary": {"precision": 0.9791666666666666, "recall": 0.9791666666666666, "f1-score": 0.9791666666666666, "support": 48.0}, "Shared Money": {"precision": 0.8, "recall": 0.****************, "f1-score": 0.7272727272727273, "support": 6.0}, "Shopping": {"precision": 0.875, "recall": 0.****************, "f1-score": 0.8235294117647058, "support": 9.0}, "Transportation": {"precision": 0.****************, "recall": 0.****************, "f1-score": 0.****************, "support": 15.0}, "Youtube Income": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 3.0}, "accuracy": 0.****************, "macro avg": {"precision": 0.****************, "recall": 0.****************, "f1-score": 0.****************, "support": 192.0}, "weighted avg": {"precision": 0.****************, "recall": 0.****************, "f1-score": 0.****************, "support": 192.0}}}, {"model_version": "1.0_20250628_022407", "training_date": "2025-06-28T02:24:08.475596", "accuracy": 0.****************, "training_samples": 198, "categories_count": 17, "cv_accuracy_mean": 0.****************, "cv_accuracy_std": 0.****************, "evaluation_metrics": {"Bank charges": {"precision": 1.0, "recall": 0.****************, "f1-score": 0.****************, "support": 9.0}, "Bills & Utilities": {"precision": 1.0, "recall": 0.****************, "f1-score": 0.****************, "support": 13.0}, "Borrowed": {"precision": 0.*****************, "recall": 0.*****************, "f1-score": 0.*****************, "support": 7.0}, "Cash limit": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 9.0}, "EMI": {"precision": 0.****************, "recall": 0.****************, "f1-score": 0.***************, "support": 14.0}, "Gifts & Donations": {"precision": 0.7, "recall": 0.****************, "f1-score": 0.8, "support": 15.0}, "House Hold Expenses": {"precision": 0.****************, "recall": 0.****************, "f1-score": 0.****************, "support": 9.0}, "Investment": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 8.0}, "Lend To": {"precision": 1.0, "recall": 0.5, "f1-score": 0.****************, "support": 4.0}, "Online Stores": {"precision": 1.0, "recall": 0.9090909090909091, "f1-score": 0.9523809523809523, "support": 11.0}, "Refunds": {"precision": 0.****************, "recall": 0.****************, "f1-score": 0.****************, "support": 7.0}, "Returned": {"precision": 0.5833333333333334, "recall": 0.****************, "f1-score": 0.****************, "support": 9.0}, "Salary": {"precision": 0.9791666666666666, "recall": 0.9791666666666666, "f1-score": 0.9791666666666666, "support": 48.0}, "Shared Money": {"precision": 1.0, "recall": 0.****************, "f1-score": 0.8, "support": 6.0}, "Shopping": {"precision": 0.****************, "recall": 0.****************, "f1-score": 0.75, "support": 9.0}, "Transportation": {"precision": 0.7727272727272727, "recall": 1.0, "f1-score": 0.8717948717948718, "support": 17.0}, "Youtube Income": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 3.0}, "accuracy": 0.****************, "macro avg": {"precision": 0.8051534250063661, "recall": 0.7591670584317644, "f1-score": 0.7709140911786777, "support": 198.0}, "weighted avg": {"precision": 0.8614510909965455, "recall": 0.****************, "f1-score": 0.8527119413529151, "support": 198.0}}}]