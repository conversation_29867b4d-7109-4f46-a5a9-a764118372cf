#!/usr/bin/env python3
"""
Bank Statement Analyzer and Importer
A comprehensive application for analyzing bank statements and importing transactions
into the main Personal Finance Dashboard application.

Features:
- Parse multiple bank statement formats (PDF, CSV, Excel)
- Automatic transaction categorization
- Preview and review interface with editing capabilities
- Data validation and import to main application
- Modern PySide6 UI with comprehensive logging

Author: Personal Finance Dashboard
Version: 1.0.0
"""

import sys
import os
import logging
from pathlib import Path
from datetime import datetime

# Add the main application's src directory to Python path
current_dir = Path(__file__).parent
main_app_src = current_dir / "src"
if main_app_src.exists():
    sys.path.insert(0, str(main_app_src))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt
from PySide6.QtGui import QIcon

# Import our modules
from bank_analyzer.ui.main_window import BankAnalyzerMainWindow
from bank_analyzer.core.logger import setup_logging


def main():
    """Main entry point for the Bank Statement Analyzer application"""
    
    # Setup logging
    setup_logging()
    logger = logging.getLogger(__name__)
    logger.info("Starting Bank Statement Analyzer application")
    
    # Create QApplication
    app = QApplication(sys.argv)
    app.setApplicationName("Bank Statement Analyzer")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("Personal Finance Dashboard")
    
    # Set application icon if available
    icon_path = current_dir / "assets" / "icons" / "bank_analyzer.png"
    if icon_path.exists():
        app.setWindowIcon(QIcon(str(icon_path)))
    
    # Enable high DPI scaling (suppress deprecation warnings)
    import warnings
    with warnings.catch_warnings():
        warnings.simplefilter("ignore", DeprecationWarning)
        try:
            app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
            app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        except AttributeError:
            # These attributes are deprecated in newer Qt versions
            pass
    
    try:
        # Create and show main window
        main_window = BankAnalyzerMainWindow()
        main_window.show()
        
        logger.info("Bank Statement Analyzer application started successfully")
        
        # Run the application
        exit_code = app.exec()
        logger.info(f"Bank Statement Analyzer application exited with code: {exit_code}")
        
        return exit_code
        
    except Exception as e:
        logger.error(f"Failed to start Bank Statement Analyzer application: {str(e)}", exc_info=True)
        return 1


if __name__ == "__main__":
    sys.exit(main())
