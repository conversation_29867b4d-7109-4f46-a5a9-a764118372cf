#!/usr/bin/env python3
"""
Simple Launcher for Bank Statement Analyzer
One-click setup and launch with automatic dependency checking
"""

import sys
import subprocess
import os
from pathlib import Path

def print_header():
    """Print application header"""
    print("=" * 60)
    print("🏦 BANK STATEMENT ANALYZER LAUNCHER")
    print("=" * 60)
    print("📊 Analyze • 🎯 Categorize • 📥 Import")
    print("Seamlessly import bank statements into your Personal Finance Dashboard")
    print("=" * 60)

def check_python():
    """Check if Python is available and version"""
    try:
        version = sys.version_info
        if version.major < 3 or (version.major == 3 and version.minor < 8):
            print("❌ Python 3.8+ required. Current version:", f"{version.major}.{version.minor}")
            return False
        
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} detected")
        return True
    except Exception as e:
        print(f"❌ Python check failed: {e}")
        return False

def check_dependencies():
    """Check and install required dependencies"""
    print("\n🔍 Checking dependencies...")
    
    required_packages = [
        ("pandas", "Data processing"),
        ("PySide6", "GUI framework"),
        ("PyPDF2", "PDF parsing"),
        ("pdfplumber", "Advanced PDF parsing"),
        ("openpyxl", "Excel file support"),
    ]
    
    missing_packages = []
    
    for package, description in required_packages:
        try:
            __import__(package)
            print(f"   ✅ {package} - {description}")
        except ImportError:
            print(f"   ❌ {package} - {description} (MISSING)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n📦 Installing missing packages: {', '.join(missing_packages)}")
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", 
                *missing_packages
            ])
            print("✅ All dependencies installed successfully!")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install dependencies: {e}")
            print("💡 Try running manually: pip install pandas PySide6 PyPDF2 pdfplumber openpyxl")
            return False
    
    print("✅ All dependencies are available!")
    return True

def check_data_structure():
    """Check if main application data structure exists"""
    print("\n📁 Checking data structure...")
    
    data_dir = Path("data")
    expenses_dir = data_dir / "expenses"
    expenses_file = expenses_dir / "expenses.csv"
    
    if not data_dir.exists():
        print("   ⚠️  Main application data directory not found")
        print("   💡 Make sure you're running from the Personal Finance Dashboard directory")
        return False
    
    if not expenses_dir.exists():
        print("   ⚠️  Expenses directory not found")
        return False
    
    if expenses_file.exists():
        print(f"   ✅ Found existing expense data: {expenses_file}")
        try:
            import pandas as pd
            df = pd.read_csv(expenses_file)
            print(f"   📊 Current expenses: {len(df)} transactions")
        except Exception as e:
            print(f"   ⚠️  Could not read expense data: {e}")
    else:
        print("   ℹ️  No existing expense data (first time setup)")
    
    return True

def check_statements_folder():
    """Check if statements folder exists"""
    print("\n📄 Checking statements folder...")
    
    statements_dir = Path("statements")
    if not statements_dir.exists():
        print("   ⚠️  Statements folder not found")
        print("   💡 Creating statements folder...")
        try:
            statements_dir.mkdir(exist_ok=True)
            print("   ✅ Created statements folder")
        except Exception as e:
            print(f"   ❌ Could not create statements folder: {e}")
            return False
    
    # Check for statement files
    statement_files = []
    for ext in ['*.pdf', '*.csv', '*.xlsx', '*.xls']:
        statement_files.extend(statements_dir.glob(ext))
    
    if statement_files:
        print(f"   ✅ Found {len(statement_files)} statement files:")
        for file in statement_files[:5]:  # Show first 5
            print(f"      📄 {file.name}")
        if len(statement_files) > 5:
            print(f"      ... and {len(statement_files) - 5} more files")
    else:
        print("   ℹ️  No statement files found")
        print("   💡 Add your bank statement files (PDF, CSV, Excel) to the statements folder")
    
    return True

def run_tests():
    """Run basic tests to ensure everything works"""
    print("\n🧪 Running system tests...")
    
    try:
        # Test basic imports
        from bank_analyzer.core.categorizer import TransactionCategorizer
        from bank_analyzer.core.data_importer import DataImporter
        from bank_analyzer.parsers.parser_factory import parser_factory
        print("   ✅ Core modules import successfully")
        
        # Test UI imports
        from bank_analyzer.ui.main_window import BankAnalyzerMainWindow
        print("   ✅ UI modules import successfully")
        
        # Test basic functionality
        categorizer = TransactionCategorizer()
        importer = DataImporter()
        print("   ✅ Core components initialize successfully")
        
        return True
        
    except Exception as e:
        print(f"   ❌ System test failed: {e}")
        return False

def setup_confidence_improvements():
    """Setup confidence improvements"""
    print("\n🎯 Setting up confidence improvements...")
    
    try:
        # Check if personalized setup should be run
        expenses_file = Path("data/expenses/expenses.csv")
        if expenses_file.exists():
            print("   📊 Found your expense data - setting up personalized improvements")
            result = subprocess.run([sys.executable, "personalized_confidence_booster.py"], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print("   ✅ Personalized confidence improvements setup complete")
            else:
                print("   ⚠️  Personalized setup had issues, using standard improvements")
                # Fallback to standard improvements
                result = subprocess.run([sys.executable, "improve_confidence.py"], 
                                      capture_output=True, text=True)
        else:
            print("   📈 Setting up standard confidence improvements")
            result = subprocess.run([sys.executable, "improve_confidence.py"], 
                                  capture_output=True, text=True)
        
        return True
        
    except Exception as e:
        print(f"   ⚠️  Could not setup confidence improvements: {e}")
        print("   💡 You can run this manually later")
        return True  # Non-critical, continue anyway

def show_quick_start_guide():
    """Show quick start guide"""
    print("\n📚 QUICK START GUIDE")
    print("=" * 20)
    print("1️⃣  Add bank statements to the 'statements' folder")
    print("   📄 Supported: PDF, CSV, Excel (.xlsx, .xls)")
    print()
    print("2️⃣  Launch the application (starting now...)")
    print("   🖱️  Click 'Select Files' to choose your statements")
    print()
    print("3️⃣  Review and edit transactions")
    print("   🎯 Green = High confidence (auto-categorized)")
    print("   🟡 Yellow = Medium confidence (review recommended)")
    print("   🔴 Red = Low confidence (manual review needed)")
    print()
    print("4️⃣  Import to your Personal Finance Dashboard")
    print("   💾 Automatic backup created before import")
    print("   ✅ Data validation ensures compatibility")
    print()

def launch_application():
    """Launch the Bank Statement Analyzer application"""
    print("\n🚀 LAUNCHING BANK STATEMENT ANALYZER")
    print("=" * 40)
    
    try:
        # Launch the application
        subprocess.run([sys.executable, "bank_statement_analyzer.py"])
        print("\n👋 Application closed. Thank you for using Bank Statement Analyzer!")
        
    except KeyboardInterrupt:
        print("\n⏹️  Application interrupted by user")
    except Exception as e:
        print(f"\n❌ Error launching application: {e}")
        print("💡 Try running manually: python bank_statement_analyzer.py")

def main():
    """Main launcher function"""
    print_header()
    
    # System checks
    if not check_python():
        input("\nPress Enter to exit...")
        return False
    
    if not check_dependencies():
        input("\nPress Enter to exit...")
        return False
    
    if not check_data_structure():
        print("\n⚠️  Data structure issues detected")
        choice = input("Continue anyway? (y/n): ").lower().strip()
        if choice != 'y':
            return False
    
    check_statements_folder()
    
    if not run_tests():
        print("\n⚠️  System tests failed")
        choice = input("Continue anyway? (y/n): ").lower().strip()
        if choice != 'y':
            return False
    
    # Setup improvements
    setup_confidence_improvements()
    
    # Show guide and launch
    show_quick_start_guide()
    
    print("\n" + "=" * 60)
    input("📱 Press Enter to launch Bank Statement Analyzer...")
    
    launch_application()
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n👋 Launcher interrupted. Goodbye!")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Launcher error: {e}")
        input("Press Enter to exit...")
        sys.exit(1)
