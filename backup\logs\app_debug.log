2025-06-27 01:20:47,583 - __main__ - INFO - <module>:34 - ================================================================================
2025-06-27 01:20:47,583 - __main__ - INFO - <module>:35 - PERSONAL FINANCE DASHBOARD - DETAILED LOGGING SESSION
2025-06-27 01:20:47,584 - __main__ - INFO - <module>:36 - ================================================================================
2025-06-27 01:20:48,124 - __main__ - INFO - main:76 - Starting Personal Finance Dashboard...
2025-06-27 01:20:48,124 - __main__ - INFO - main:79 - Setting up QApplication...
2025-06-27 01:20:48,150 - __main__ - INFO - main:83 - Initializing configuration...
2025-06-27 01:20:48,150 - __main__ - INFO - main:85 - Config loaded: data_directory=data
2025-06-27 01:20:48,150 - __main__ - INFO - main:88 - Initializing data manager...
2025-06-27 01:20:48,150 - src.core.data_manager.DataManager - INFO - __init__:29 - Initializing DataManager with directory: data
2025-06-27 01:20:48,150 - src.core.data_manager.DataManager - DEBUG - __init__:32 - Data directory path: C:\Users\<USER>\Documents\GitHub\VARSYS-Traqify\V3\original code\data
2025-06-27 01:20:48,151 - src.core.data_manager.DataManager - DEBUG - ensure_directories:40 - Ensuring data directories exist
2025-06-27 01:20:48,151 - src.core.data_manager.DataManager - DEBUG - ensure_directories:42 - Main data directory created/verified: data
2025-06-27 01:20:48,153 - src.core.data_manager.DataManager - DEBUG - ensure_directories:53 - Module directory created/verified: data\expenses
2025-06-27 01:20:48,153 - src.core.data_manager.DataManager - DEBUG - ensure_directories:53 - Module directory created/verified: data\income
2025-06-27 01:20:48,153 - src.core.data_manager.DataManager - DEBUG - ensure_directories:53 - Module directory created/verified: data\habits
2025-06-27 01:20:48,155 - src.core.data_manager.DataManager - DEBUG - ensure_directories:53 - Module directory created/verified: data\attendance
2025-06-27 01:20:48,155 - src.core.data_manager.DataManager - DEBUG - ensure_directories:53 - Module directory created/verified: data\todos
2025-06-27 01:20:48,155 - src.core.data_manager.DataManager - DEBUG - ensure_directories:53 - Module directory created/verified: data\investments
2025-06-27 01:20:48,155 - src.core.data_manager.DataManager - DEBUG - ensure_directories:53 - Module directory created/verified: data\budget
2025-06-27 01:20:48,155 - src.core.data_manager.DataManager - DEBUG - ensure_directories:53 - Module directory created/verified: data\config
2025-06-27 01:20:48,156 - src.core.data_manager.DataManager - INFO - ensure_directories:55 - All 8 module directories ensured
2025-06-27 01:20:48,156 - src.core.data_manager.DataManager - INFO - __init__:36 - DataManager initialization complete
2025-06-27 01:20:48,156 - __main__ - INFO - main:90 - Data manager initialized successfully
2025-06-27 01:20:48,156 - __main__ - INFO - main:93 - Creating main window...
2025-06-27 01:20:48,156 - src.ui.main_window.MainWindow - INFO - __init__:40 - ============================================================
2025-06-27 01:20:48,156 - src.ui.main_window.MainWindow - INFO - __init__:41 - INITIALIZING MAIN WINDOW
2025-06-27 01:20:48,156 - src.ui.main_window.MainWindow - INFO - __init__:42 - ============================================================
2025-06-27 01:20:48,157 - src.ui.main_window.MainWindow - DEBUG - __init__:48 - Core components initialized
2025-06-27 01:20:48,157 - src.ui.main_window.MainWindow - INFO - __init__:51 - Setting up UI components...
2025-06-27 01:20:48,208 - src.ui.main_window.MainWindow - INFO - setup_content_pages:108 - Setting up content pages...
2025-06-27 01:20:48,208 - src.ui.main_window.MainWindow - DEBUG - setup_content_pages:112 - Creating Dashboard widget...
2025-06-27 01:20:48,383 - src.ui.main_window.MainWindow - DEBUG - setup_content_pages:115 - Dashboard widget created successfully
2025-06-27 01:20:48,383 - src.ui.main_window.MainWindow - DEBUG - setup_content_pages:118 - Creating Expense Tracker widget...
2025-06-27 01:20:48,416 - src.ui.main_window.MainWindow - DEBUG - setup_content_pages:121 - Expense Tracker widget created successfully
2025-06-27 01:20:48,417 - src.ui.main_window.MainWindow - DEBUG - setup_content_pages:124 - Creating Income Tracker widget...
2025-06-27 01:20:48,628 - src.ui.main_window.MainWindow - DEBUG - setup_content_pages:127 - Income Tracker widget created successfully
2025-06-27 01:20:48,629 - src.ui.main_window.MainWindow - DEBUG - setup_content_pages:130 - Creating Habit Tracker widget...
2025-06-27 01:20:48,743 - src.ui.main_window.MainWindow - DEBUG - setup_content_pages:133 - Habit Tracker widget created successfully
2025-06-27 01:20:48,743 - src.ui.main_window.MainWindow - DEBUG - setup_content_pages:136 - Creating Simplified Attendance Tracker widget...
2025-06-27 01:20:48,815 - modules.attendance.simple_widgets - INFO - __init__:46 - ✅ SimpleAttendanceTrackerWidget initialized successfully
2025-06-27 01:20:48,815 - src.ui.main_window.MainWindow - DEBUG - setup_content_pages:141 - Simplified Attendance Tracker widget created successfully
2025-06-27 01:20:48,815 - src.ui.main_window.MainWindow - DEBUG - setup_content_pages:147 - Creating To-Do List widget...
2025-06-27 01:20:48,815 - src.modules.todos.widgets.TodoTrackerWidget - INFO - __init__:311 - ==================================================
2025-06-27 01:20:48,815 - src.modules.todos.widgets.TodoTrackerWidget - INFO - __init__:312 - INITIALIZING TODO TRACKER WIDGET
2025-06-27 01:20:48,816 - src.modules.todos.widgets.TodoTrackerWidget - INFO - __init__:313 - ==================================================
2025-06-27 01:20:48,816 - src.modules.todos.models.TodoDataModel - INFO - __init__:197 - ========================================
2025-06-27 01:20:48,816 - src.modules.todos.models.TodoDataModel - INFO - __init__:198 - INITIALIZING TODO DATA MODEL
2025-06-27 01:20:48,816 - src.modules.todos.models.TodoDataModel - INFO - __init__:199 - ========================================
2025-06-27 01:20:49,322 - googleapiclient.discovery_cache - INFO - autodetect:49 - file_cache is only supported with oauth2client<4.0.0
2025-06-27 01:20:49,323 - src.modules.todos.google_tasks_integration - INFO - try_initialize_with_existing_credentials:71 - ✅ Google Tasks service initialized with existing valid credentials
2025-06-27 01:20:49,323 - src.modules.todos.models.TodoDataModel - INFO - __init__:220 - ✅ Google Tasks integration initialized and authenticated
2025-06-27 01:20:49,323 - src.modules.todos.models.TodoDataModel - INFO - __init__:235 - 🔄 Attempting automatic sync from Google Tasks...
2025-06-27 01:20:49,325 - googleapiclient.discovery - DEBUG - method:1205 - URL being requested: GET https://tasks.googleapis.com/tasks/v1/lists/%40default/tasks?showCompleted=true&showHidden=true&alt=json
2025-06-27 01:20:50,091 - src.modules.todos.google_tasks_integration - INFO - get_tasks:244 - Retrieved 20 tasks from task list @default (completed: True)
2025-06-27 01:20:50,092 - src.modules.todos.models.TodoDataModel - INFO - sync_from_google_tasks:555 - Retrieved 20 tasks from Google Tasks
2025-06-27 01:20:50,097 - src.modules.todos.models.TodoDataModel - DEBUG - sync_from_google_tasks:575 - Task already exists locally: Kitchen daily track
2025-06-27 01:20:50,098 - src.modules.todos.models.TodoDataModel - DEBUG - sync_from_google_tasks:575 - Task already exists locally: Calorie Tracking
2025-06-27 01:20:50,098 - src.modules.todos.models.TodoDataModel - DEBUG - sync_from_google_tasks:575 - Task already exists locally: Excel Updation daily basis
2025-06-27 01:20:50,098 - src.modules.todos.models.TodoDataModel - DEBUG - sync_from_google_tasks:575 - Task already exists locally: Tablet manmatters
2025-06-27 01:20:50,121 - src.modules.todos.models.TodoDataModel - DEBUG - sync_from_google_tasks:575 - Task already exists locally: Kridtbee repayment(1.8k)
2025-06-27 01:20:50,122 - src.modules.todos.models.TodoDataModel - DEBUG - sync_from_google_tasks:575 - Task already exists locally: Slice repayment (1k)
2025-06-27 01:20:50,122 - src.modules.todos.models.TodoDataModel - DEBUG - sync_from_google_tasks:575 - Task already exists locally: Mpocket repayment
2025-06-27 01:20:50,122 - src.modules.todos.models.TodoDataModel - DEBUG - sync_from_google_tasks:575 - Task already exists locally: iPhone emi(5k)
2025-06-27 01:20:50,122 - src.modules.todos.models.TodoDataModel - DEBUG - sync_from_google_tasks:575 - Task already exists locally: Fridge emi (3k)
2025-06-27 01:20:50,123 - src.modules.todos.models.TodoDataModel - DEBUG - sync_from_google_tasks:575 - Task already exists locally: Ola s1 x emi(3k)
2025-06-27 01:20:50,124 - src.modules.todos.models.TodoDataModel - DEBUG - sync_from_google_tasks:575 - Task already exists locally: Ola s1 x emi(3k)
2025-06-27 01:20:50,124 - src.modules.todos.models.TodoDataModel - DEBUG - sync_from_google_tasks:575 - Task already exists locally: Mpocket 2nd loan repayment
2025-06-27 01:20:50,124 - src.modules.todos.models.TodoDataModel - DEBUG - sync_from_google_tasks:575 - Task already exists locally: Ola s1x warranty extention(10k)
2025-06-27 01:20:50,124 - src.modules.todos.models.TodoDataModel - DEBUG - sync_from_google_tasks:575 - Task already exists locally: Cancel the premium plan for linkedin
2025-06-27 01:20:50,124 - src.modules.todos.models.TodoDataModel - DEBUG - sync_from_google_tasks:575 - Task already exists locally: Internship review 2nd extended date
2025-06-27 01:20:50,125 - src.modules.todos.models.TodoDataModel - DEBUG - sync_from_google_tasks:575 - Task already exists locally: Moneyview emi(1.8k)
2025-06-27 01:20:50,125 - src.modules.todos.models.TodoDataModel - DEBUG - sync_from_google_tasks:575 - Task already exists locally: E business cia 2
2025-06-27 01:20:50,125 - src.modules.todos.models.TodoDataModel - DEBUG - sync_from_google_tasks:575 - Task already exists locally: Wt cia 2
2025-06-27 01:20:50,125 - src.modules.todos.models.TodoDataModel - DEBUG - sync_from_google_tasks:575 - Task already exists locally: ERP cia 2
2025-06-27 01:20:50,125 - src.modules.todos.models.TodoDataModel - DEBUG - sync_from_google_tasks:575 - Task already exists locally: Satya shop balance amount
2025-06-27 01:20:50,125 - src.modules.todos.models.TodoDataModel - INFO - sync_from_google_tasks:592 - Sync from Google Tasks completed: 0 added, 0 updated
2025-06-27 01:20:50,125 - src.modules.todos.models.TodoDataModel - INFO - __init__:241 - ℹ️ No new tasks found in Google Tasks during automatic sync
2025-06-27 01:20:50,126 - src.modules.todos.models.TodoDataModel - INFO - __init__:245 - ✅ TodoDataModel initialization SUCCESSFUL
2025-06-27 01:20:50,207 - src.modules.todos.widgets.TodoTrackerWidget - INFO - __init__:324 - ✅ TodoTrackerWidget initialization SUCCESSFUL
2025-06-27 01:20:50,207 - src.ui.main_window.MainWindow - DEBUG - setup_content_pages:151 - To-Do List widget created successfully
2025-06-27 01:20:50,207 - src.ui.main_window.MainWindow - DEBUG - setup_content_pages:157 - Creating Investment Tracker widget...
2025-06-27 01:20:50,208 - src.modules.investments.widgets.InvestmentTrackerWidget - INFO - __init__:356 - ==================================================
2025-06-27 01:20:50,208 - src.modules.investments.widgets.InvestmentTrackerWidget - INFO - __init__:357 - INITIALIZING INVESTMENT TRACKER WIDGET
2025-06-27 01:20:50,208 - src.modules.investments.widgets.InvestmentTrackerWidget - INFO - __init__:358 - ==================================================
2025-06-27 01:20:50,208 - src.modules.investments.models.InvestmentDataModel - INFO - __init__:173 - ========================================
2025-06-27 01:20:50,208 - src.modules.investments.models.InvestmentDataModel - INFO - __init__:174 - INITIALIZING INVESTMENT DATA MODEL
2025-06-27 01:20:50,208 - src.modules.investments.models.InvestmentDataModel - INFO - __init__:175 - ========================================
2025-06-27 01:20:50,208 - src.modules.investments.models.InvestmentDataModel - INFO - __init__:190 - ✅ InvestmentDataModel initialization SUCCESSFUL
2025-06-27 01:20:50,221 - src.modules.investments.widgets.InvestmentTrackerWidget - INFO - __init__:369 - ✅ InvestmentTrackerWidget initialization SUCCESSFUL
2025-06-27 01:20:50,221 - src.ui.main_window.MainWindow - DEBUG - setup_content_pages:161 - Investment Tracker widget created successfully
2025-06-27 01:20:50,221 - src.ui.main_window.MainWindow - DEBUG - setup_content_pages:167 - Creating Budget Planner widget...
2025-06-27 01:20:50,222 - src.modules.budget.widgets.BudgetPlannerWidget - INFO - __init__:812 - ==================================================
2025-06-27 01:20:50,222 - src.modules.budget.widgets.BudgetPlannerWidget - INFO - __init__:813 - INITIALIZING BUDGET PLANNER WIDGET
2025-06-27 01:20:50,222 - src.modules.budget.widgets.BudgetPlannerWidget - INFO - __init__:814 - ==================================================
2025-06-27 01:20:50,222 - src.modules.budget.models.BudgetDataModel - INFO - __init__:277 - ========================================
2025-06-27 01:20:50,223 - src.modules.budget.models.BudgetDataModel - INFO - __init__:278 - INITIALIZING BUDGET DATA MODEL
2025-06-27 01:20:50,223 - src.modules.budget.models.BudgetDataModel - INFO - __init__:279 - ========================================
2025-06-27 01:20:50,225 - src.modules.budget.models.BudgetDataModel - INFO - __init__:308 - ✅ BudgetDataModel initialization SUCCESSFUL
2025-06-27 01:20:50,258 - src.modules.budget.widgets.BudgetPlannerWidget - INFO - __init__:825 - ✅ BudgetPlannerWidget initialization SUCCESSFUL
2025-06-27 01:20:50,258 - src.ui.main_window.MainWindow - DEBUG - setup_content_pages:171 - Budget Planner widget created successfully
2025-06-27 01:20:50,258 - src.ui.main_window.MainWindow - INFO - setup_content_pages:195 - Module 'dashboard': ✅ LOADED
2025-06-27 01:20:50,258 - src.ui.main_window.MainWindow - INFO - setup_content_pages:195 - Module 'expenses': ✅ LOADED
2025-06-27 01:20:50,258 - src.ui.main_window.MainWindow - INFO - setup_content_pages:195 - Module 'income': ✅ LOADED
2025-06-27 01:20:50,259 - src.ui.main_window.MainWindow - INFO - setup_content_pages:195 - Module 'habits': ✅ LOADED
2025-06-27 01:20:50,259 - src.ui.main_window.MainWindow - INFO - setup_content_pages:195 - Module 'attendance': ✅ LOADED
2025-06-27 01:20:50,259 - src.ui.main_window.MainWindow - INFO - setup_content_pages:195 - Module 'todos': ✅ LOADED
2025-06-27 01:20:50,259 - src.ui.main_window.MainWindow - INFO - setup_content_pages:195 - Module 'investments': ✅ LOADED
2025-06-27 01:20:50,259 - src.ui.main_window.MainWindow - INFO - setup_content_pages:195 - Module 'budget': ✅ LOADED
2025-06-27 01:20:50,259 - src.ui.main_window.MainWindow - INFO - setup_content_pages:199 - Content pages setup complete
2025-06-27 01:20:50,262 - src.ui.main_window.MainWindow - DEBUG - setup_connections:308 - Sidebar module_selected signal connected
2025-06-27 01:20:50,262 - src.ui.main_window.MainWindow - DEBUG - setup_connections:312 - Sidebar toggle signal connected
2025-06-27 01:20:50,262 - src.ui.main_window.MainWindow - DEBUG - setup_connections:317 - Data manager data_changed signal connected
2025-06-27 01:20:50,262 - src.ui.main_window.MainWindow - DEBUG - setup_connections:321 - Data manager error_occurred signal connected
2025-06-27 01:20:50,262 - src.ui.main_window.MainWindow - INFO - setup_connections:323 - ✅ All signal connections established successfully
2025-06-27 01:20:50,411 - src.ui.main_window.MainWindow - INFO - __init__:57 - UI setup complete
2025-06-27 01:20:50,412 - src.ui.main_window.MainWindow - DEBUG - __init__:60 - Restoring window state...
2025-06-27 01:20:50,458 - src.ui.main_window.MainWindow - DEBUG - __init__:64 - Setting up auto-save...
2025-06-27 01:20:50,458 - src.ui.main_window.MainWindow - INFO - __init__:67 - MainWindow initialization complete
2025-06-27 01:20:50,458 - __main__ - INFO - main:95 - Main window created successfully
2025-06-27 01:20:50,486 - __main__ - INFO - main:98 - Main window shown, starting event loop...
