2025-06-23 05:25:23 - bank_analyzer.core.logger - INFO - setup_logging:64 - Logging system initialized
2025-06-23 05:25:23 - bank_analyzer.core.logger - INFO - setup_logging:65 - Log file: logs\bank_analyzer_20250623.log
2025-06-23 05:25:23 - bank_analyzer.core.logger - INFO - setup_logging:66 - Log level: INFO
2025-06-23 05:25:23 - test - INFO - test_logging:190 - Test log message
2025-06-23 05:25:23 - test - WARNING - test_logging:191 - Test warning message
2025-06-23 05:25:57 - bank_analyzer.core.logger - INFO - setup_logging:64 - Logging system initialized
2025-06-23 05:25:57 - bank_analyzer.core.logger - INFO - setup_logging:65 - Log file: logs\bank_analyzer_20250623.log
2025-06-23 05:25:57 - bank_analyzer.core.logger - INFO - setup_logging:66 - Log level: INFO
2025-06-23 05:25:57 - test - INFO - test_logging:190 - Test log message
2025-06-23 05:25:57 - test - WARNING - test_logging:191 - Test warning message
2025-06-23 05:26:07 - bank_analyzer.core.logger - INFO - setup_logging:64 - Logging system initialized
2025-06-23 05:26:07 - bank_analyzer.core.logger - INFO - setup_logging:65 - Log file: logs\bank_analyzer_20250623.log
2025-06-23 05:26:07 - bank_analyzer.core.logger - INFO - setup_logging:66 - Log level: INFO
2025-06-23 05:26:07 - __main__ - INFO - main:45 - Starting Bank Statement Analyzer application
2025-06-23 05:26:07 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-23 05:26:07 - __main__ - ERROR - main:76 - Failed to start Bank Statement Analyzer application: 'BankAnalyzerMainWindow' object has no attribute 'open_statements'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\New folder\bank_statement_analyzer.py", line 64, in main
    main_window = BankAnalyzerMainWindow()
  File "C:\Users\<USER>\Desktop\New folder\bank_analyzer\ui\main_window.py", line 93, in __init__
    self.setup_ui()
  File "C:\Users\<USER>\Desktop\New folder\bank_analyzer\ui\main_window.py", line 105, in setup_ui
    self.create_menu_bar()
  File "C:\Users\<USER>\Desktop\New folder\bank_analyzer\ui\main_window.py", line 135, in create_menu_bar
    open_action.triggered.connect(self.open_statements)
AttributeError: 'BankAnalyzerMainWindow' object has no attribute 'open_statements'
2025-06-23 05:30:22 - bank_analyzer.core.logger - INFO - setup_logging:64 - Logging system initialized
2025-06-23 05:30:22 - bank_analyzer.core.logger - INFO - setup_logging:65 - Log file: logs\bank_analyzer_20250623.log
2025-06-23 05:30:22 - bank_analyzer.core.logger - INFO - setup_logging:66 - Log level: INFO
2025-06-23 05:30:22 - bank_analyzer.parsers.parser_factory - INFO - get_parser:80 - Selected csv parser for statements\sample_bank_statement.csv
2025-06-23 05:30:22 - bank_analyzer.parsers.base_parser.CSVStatementParser - INFO - _detect_columns:144 - Detected column mappings: {'date': 'Date', 'description': 'Description', 'debit': 'Debit', 'credit': 'Description', 'balance': 'Balance'}
2025-06-23 05:30:22 - bank_analyzer.parsers.base_parser.CSVStatementParser - ERROR - _extract_transaction_from_row:239 - Error extracting transaction from CSV row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:30:22 - bank_analyzer.parsers.base_parser.CSVStatementParser - ERROR - _extract_transaction_from_row:239 - Error extracting transaction from CSV row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:30:22 - bank_analyzer.parsers.base_parser.CSVStatementParser - ERROR - _extract_transaction_from_row:239 - Error extracting transaction from CSV row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:30:22 - bank_analyzer.parsers.base_parser.CSVStatementParser - ERROR - _extract_transaction_from_row:239 - Error extracting transaction from CSV row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:30:22 - bank_analyzer.parsers.base_parser.CSVStatementParser - ERROR - _extract_transaction_from_row:239 - Error extracting transaction from CSV row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:30:22 - bank_analyzer.parsers.base_parser.CSVStatementParser - ERROR - _extract_transaction_from_row:239 - Error extracting transaction from CSV row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:30:22 - bank_analyzer.parsers.base_parser.CSVStatementParser - ERROR - _extract_transaction_from_row:239 - Error extracting transaction from CSV row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:30:22 - bank_analyzer.parsers.base_parser.CSVStatementParser - ERROR - _extract_transaction_from_row:239 - Error extracting transaction from CSV row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:30:22 - bank_analyzer.parsers.base_parser.CSVStatementParser - ERROR - _extract_transaction_from_row:239 - Error extracting transaction from CSV row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:30:22 - bank_analyzer.parsers.base_parser.CSVStatementParser - ERROR - _extract_transaction_from_row:239 - Error extracting transaction from CSV row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:30:22 - bank_analyzer.parsers.base_parser.CSVStatementParser - ERROR - _extract_transaction_from_row:239 - Error extracting transaction from CSV row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:30:22 - bank_analyzer.parsers.base_parser.CSVStatementParser - ERROR - _extract_transaction_from_row:239 - Error extracting transaction from CSV row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:30:22 - bank_analyzer.parsers.base_parser.CSVStatementParser - INFO - parse:110 - Extracted 6 transactions from statements\sample_bank_statement.csv
2025-06-23 05:30:22 - bank_analyzer.parsers.parser_factory - INFO - parse_file:113 - Successfully parsed 6 transactions from statements\sample_bank_statement.csv
2025-06-23 05:30:22 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-23 05:31:29 - bank_analyzer.core.logger - INFO - setup_logging:64 - Logging system initialized
2025-06-23 05:31:29 - bank_analyzer.core.logger - INFO - setup_logging:65 - Log file: logs\bank_analyzer_20250623.log
2025-06-23 05:31:29 - bank_analyzer.core.logger - INFO - setup_logging:66 - Log level: INFO
2025-06-23 05:31:29 - bank_analyzer.parsers.parser_factory - INFO - get_parser:80 - Selected csv parser for statements\sample_bank_statement.csv
2025-06-23 05:31:29 - bank_analyzer.parsers.base_parser.CSVStatementParser - INFO - _detect_columns:145 - Detected column mappings: {'date': 'Date', 'description': 'Description', 'debit': 'Debit', 'credit': 'Description', 'balance': 'Balance'}
2025-06-23 05:31:29 - bank_analyzer.parsers.base_parser.CSVStatementParser - INFO - parse:111 - Extracted 18 transactions from statements\sample_bank_statement.csv
2025-06-23 05:31:29 - bank_analyzer.parsers.parser_factory - INFO - parse_file:113 - Successfully parsed 18 transactions from statements\sample_bank_statement.csv
2025-06-23 05:31:29 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-23 05:43:18 - bank_analyzer.core.logger - INFO - setup_logging:64 - Logging system initialized
2025-06-23 05:43:18 - bank_analyzer.core.logger - INFO - setup_logging:65 - Log file: logs\bank_analyzer_20250623.log
2025-06-23 05:43:18 - bank_analyzer.core.logger - INFO - setup_logging:66 - Log level: INFO
2025-06-23 05:43:18 - __main__ - INFO - main:45 - Starting Bank Statement Analyzer application
2025-06-23 05:43:18 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-23 05:43:18 - __main__ - ERROR - main:76 - Failed to start Bank Statement Analyzer application: 'BankAnalyzerMainWindow' object has no attribute 'open_statements'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\New folder\bank_statement_analyzer.py", line 64, in main
    main_window = BankAnalyzerMainWindow()
  File "C:\Users\<USER>\Desktop\New folder\bank_analyzer\ui\main_window.py", line 93, in __init__
    self.setup_ui()
  File "C:\Users\<USER>\Desktop\New folder\bank_analyzer\ui\main_window.py", line 105, in setup_ui
    self.create_menu_bar()
  File "C:\Users\<USER>\Desktop\New folder\bank_analyzer\ui\main_window.py", line 135, in create_menu_bar
    open_action.triggered.connect(self.open_statements)
AttributeError: 'BankAnalyzerMainWindow' object has no attribute 'open_statements'
2025-06-23 05:44:40 - bank_analyzer.core.logger - INFO - setup_logging:64 - Logging system initialized
2025-06-23 05:44:40 - bank_analyzer.core.logger - INFO - setup_logging:65 - Log file: logs\bank_analyzer_20250623.log
2025-06-23 05:44:40 - bank_analyzer.core.logger - INFO - setup_logging:66 - Log level: INFO
2025-06-23 05:44:40 - __main__ - INFO - main:45 - Starting Bank Statement Analyzer application
2025-06-23 05:44:40 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-23 05:44:40 - bank_analyzer.ui.main_window - INFO - load_initial_data:362 - Initial data loaded successfully
2025-06-23 05:44:40 - bank_analyzer.ui.main_window - INFO - __init__:97 - Bank Analyzer main window initialized
2025-06-23 05:44:40 - __main__ - INFO - main:71 - Bank Statement Analyzer application started successfully
2025-06-23 05:45:44 - bank_analyzer.core.logger - INFO - setup_logging:64 - Logging system initialized
2025-06-23 05:45:44 - bank_analyzer.core.logger - INFO - setup_logging:65 - Log file: logs\bank_analyzer_20250623.log
2025-06-23 05:45:44 - bank_analyzer.core.logger - INFO - setup_logging:66 - Log level: INFO
2025-06-23 05:45:44 - __main__ - INFO - main:45 - Starting Bank Statement Analyzer application
2025-06-23 05:45:44 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-23 05:45:44 - bank_analyzer.ui.main_window - INFO - load_initial_data:362 - Initial data loaded successfully
2025-06-23 05:45:44 - bank_analyzer.ui.main_window - INFO - __init__:97 - Bank Analyzer main window initialized
2025-06-23 05:45:44 - __main__ - INFO - main:71 - Bank Statement Analyzer application started successfully
2025-06-23 05:46:17 - bank_analyzer.parsers.parser_factory - INFO - get_parser:80 - Selected pdf parser for C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 05:46:18 - bank_analyzer.parsers.base_parser.PDFStatementParser - WARNING - parse_date_string:178 - Could not parse date: TRANSACTION
DATE
2025-06-23 05:46:18 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:18 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:18 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:18 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:18 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:18 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:18 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:18 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:18 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:19 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:19 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:19 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:19 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:19 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:19 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:19 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:19 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:19 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:19 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:19 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:19 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:20 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:20 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:20 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:20 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:20 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:20 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:20 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:20 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:20 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:20 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:20 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:20 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:20 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:20 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:20 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:20 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:21 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:21 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:21 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:21 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:21 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:21 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:21 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:21 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:21 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:21 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:21 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:22 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:22 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:22 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:22 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:22 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:22 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:22 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:22 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:22 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:22 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:22 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:22 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:22 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:23 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:23 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:23 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:23 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:23 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:23 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:23 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:23 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:23 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:23 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:23 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:23 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:23 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:23 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:23 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:23 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:23 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:23 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:23 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:23 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:23 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:23 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:24 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:24 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:24 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:24 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:24 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:24 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:24 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:24 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:24 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:24 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:25 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:25 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:25 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:25 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:25 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:25 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:25 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:25 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:25 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:25 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:25 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:25 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:25 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:25 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:25 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:25 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:25 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:25 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:25 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:25 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:25 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:26 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:26 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:26 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:26 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:26 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:26 - bank_analyzer.parsers.base_parser.PDFStatementParser - ERROR - _extract_transaction_from_row:275 - Error extracting transaction from row: [<class 'decimal.ConversionSyntax'>]
2025-06-23 05:46:26 - bank_analyzer.parsers.base_parser.PDFStatementParser - WARNING - parse_date_string:178 - Could not parse date: Available Balance: 44.22(Forty Four Rupees and Twenty Two Paisa Only)
Statement Legends:
NEFT: National Electronic Fund Transfer, UPI: Unified Payment
Interface, RTGS: Real Time Gross Settlement, INT: Intra Fund
Transfer, BBPS: Bharat Bill Payment Service
This statement is system-generated and does not require a signature. Customers are requested to notify immediately in case of discrepancies.
Registered Office: Indian Bank, Corporate office, PB No: 5555, 254-260, Avvai Shanmugam Salai,Royapettah, Chennai- 600 014.
Email : <EMAIL> , Website: https://indianbank.in
2025-06-23 05:46:26 - bank_analyzer.parsers.base_parser.PDFStatementParser - INFO - parse:84 - Extracted 181 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 05:46:26 - bank_analyzer.parsers.parser_factory - INFO - parse_file:113 - Successfully parsed 181 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 05:46:26 - bank_analyzer.ui.main_window - INFO - run:58 - Parsed 181 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 05:46:26 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-23 05:49:07 - __main__ - INFO - main:75 - Bank Statement Analyzer application exited with code: 0
2025-06-23 19:09:52 - bank_analyzer.core.logger - INFO - setup_logging:64 - Logging system initialized
2025-06-23 19:09:52 - bank_analyzer.core.logger - INFO - setup_logging:65 - Log file: logs\bank_analyzer_20250623.log
2025-06-23 19:09:52 - bank_analyzer.core.logger - INFO - setup_logging:66 - Log level: INFO
2025-06-23 19:09:52 - __main__ - INFO - main:45 - Starting Bank Statement Analyzer application
2025-06-23 19:09:52 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-23 19:09:52 - bank_analyzer.ui.main_window - INFO - load_initial_data:362 - Initial data loaded successfully
2025-06-23 19:09:52 - bank_analyzer.ui.main_window - INFO - __init__:97 - Bank Analyzer main window initialized
2025-06-23 19:09:53 - __main__ - INFO - main:71 - Bank Statement Analyzer application started successfully
2025-06-23 19:10:00 - bank_analyzer.parsers.parser_factory - INFO - get_parser:80 - Selected pdf parser for C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 19:10:01 - bank_analyzer.parsers.base_parser.PDFStatementParser - WARNING - parse_date_string:212 - Could not parse date: TRANSACTION
DATE
2025-06-23 19:10:06 - bank_analyzer.parsers.base_parser.PDFStatementParser - WARNING - parse_date_string:212 - Could not parse date: Available Balance: 44.22(Forty Four Rupees and Twenty Two Paisa Only)
Statement Legends:
NEFT: National Electronic Fund Transfer, UPI: Unified Payment
Interface, RTGS: Real Time Gross Settlement, INT: Intra Fund
Transfer, BBPS: Bharat Bill Payment Service
This statement is system-generated and does not require a signature. Customers are requested to notify immediately in case of discrepancies.
Registered Office: Indian Bank, Corporate office, PB No: 5555, 254-260, Avvai Shanmugam Salai,Royapettah, Chennai- 600 014.
Email : <EMAIL> , Website: https://indianbank.in
2025-06-23 19:10:06 - bank_analyzer.parsers.base_parser.PDFStatementParser - INFO - parse:85 - Extracted 181 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 19:10:06 - bank_analyzer.parsers.parser_factory - INFO - parse_file:113 - Successfully parsed 181 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 19:10:06 - bank_analyzer.ui.main_window - INFO - run:58 - Parsed 181 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 19:10:06 - bank_analyzer.parsers.parser_factory - INFO - get_parser:80 - Selected pdf parser for C:\Users\<USER>\Desktop\New folder\statements\Indian bank\june to august.pdf
2025-06-23 19:10:07 - bank_analyzer.parsers.base_parser.PDFStatementParser - WARNING - parse_date_string:212 - Could not parse date: TRANSACTION
DATE
2025-06-23 19:10:08 - bank_analyzer.parsers.base_parser.PDFStatementParser - WARNING - parse_date_string:212 - Could not parse date: Available Balance: 44.22(Forty Four Rupees and Twenty Two Paisa Only)
Statement Legends:
NEFT: National Electronic Fund Transfer, UPI: Unified Payment
Interface, RTGS: Real Time Gross Settlement, INT: Intra Fund
Transfer, BBPS: Bharat Bill Payment Service
This statement is system-generated and does not require a signature. Customers are requested to notify immediately in case of discrepancies.
Registered Office: Indian Bank, Corporate office, PB No: 5555, 254-260, Avvai Shanmugam Salai,Royapettah, Chennai- 600 014.
Email : <EMAIL> , Website: https://indianbank.in
2025-06-23 19:10:08 - bank_analyzer.parsers.base_parser.PDFStatementParser - INFO - parse:85 - Extracted 52 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\june to august.pdf
2025-06-23 19:10:08 - bank_analyzer.parsers.parser_factory - INFO - parse_file:113 - Successfully parsed 52 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\june to august.pdf
2025-06-23 19:10:08 - bank_analyzer.ui.main_window - INFO - run:58 - Parsed 52 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\june to august.pdf
2025-06-23 19:10:08 - bank_analyzer.parsers.parser_factory - INFO - get_parser:80 - Selected pdf parser for C:\Users\<USER>\Desktop\New folder\statements\Indian bank\March to june.pdf
2025-06-23 19:10:09 - bank_analyzer.parsers.base_parser.PDFStatementParser - WARNING - parse_date_string:212 - Could not parse date: TRANSACTION
DATE
2025-06-23 19:10:14 - bank_analyzer.parsers.base_parser.PDFStatementParser - WARNING - parse_date_string:212 - Could not parse date: Available Balance: 44.22(Forty Four Rupees and Twenty Two Paisa Only)
Statement Legends:
NEFT: National Electronic Fund Transfer, UPI: Unified Payment
Interface, RTGS: Real Time Gross Settlement, INT: Intra Fund
Transfer, BBPS: Bharat Bill Payment Service
This statement is system-generated and does not require a signature. Customers are requested to notify immediately in case of discrepancies.
Registered Office: Indian Bank, Corporate office, PB No: 5555, 254-260, Avvai Shanmugam Salai,Royapettah, Chennai- 600 014.
Email : <EMAIL> , Website: https://indianbank.in
2025-06-23 19:10:14 - bank_analyzer.parsers.base_parser.PDFStatementParser - INFO - parse:85 - Extracted 137 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\March to june.pdf
2025-06-23 19:10:14 - bank_analyzer.parsers.parser_factory - INFO - parse_file:113 - Successfully parsed 137 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\March to june.pdf
2025-06-23 19:10:14 - bank_analyzer.ui.main_window - INFO - run:58 - Parsed 137 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\March to june.pdf
2025-06-23 19:10:14 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-23 19:16:03 - __main__ - INFO - main:75 - Bank Statement Analyzer application exited with code: 0
2025-06-23 19:16:09 - bank_analyzer.core.logger - INFO - setup_logging:64 - Logging system initialized
2025-06-23 19:16:09 - bank_analyzer.core.logger - INFO - setup_logging:65 - Log file: logs\bank_analyzer_20250623.log
2025-06-23 19:16:09 - bank_analyzer.core.logger - INFO - setup_logging:66 - Log level: INFO
2025-06-23 19:16:09 - __main__ - INFO - main:45 - Starting Bank Statement Analyzer application
2025-06-23 19:16:09 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-23 19:16:09 - bank_analyzer.ui.main_window - INFO - load_initial_data:362 - Initial data loaded successfully
2025-06-23 19:16:09 - bank_analyzer.ui.main_window - INFO - __init__:97 - Bank Analyzer main window initialized
2025-06-23 19:16:09 - __main__ - INFO - main:71 - Bank Statement Analyzer application started successfully
2025-06-23 19:16:15 - bank_analyzer.parsers.parser_factory - INFO - get_parser:81 - Selected pdf parser for C:\Users\<USER>\Desktop\New folder\statements\Indian bank\june to august.pdf
2025-06-23 19:16:15 - bank_analyzer.parsers.base_parser.PDFStatementParser - WARNING - parse_date_string:212 - Could not parse date: TRANSACTION
DATE
2025-06-23 19:16:16 - bank_analyzer.parsers.base_parser.PDFStatementParser - WARNING - parse_date_string:212 - Could not parse date: Available Balance: 44.22(Forty Four Rupees and Twenty Two Paisa Only)
Statement Legends:
NEFT: National Electronic Fund Transfer, UPI: Unified Payment
Interface, RTGS: Real Time Gross Settlement, INT: Intra Fund
Transfer, BBPS: Bharat Bill Payment Service
This statement is system-generated and does not require a signature. Customers are requested to notify immediately in case of discrepancies.
Registered Office: Indian Bank, Corporate office, PB No: 5555, 254-260, Avvai Shanmugam Salai,Royapettah, Chennai- 600 014.
Email : <EMAIL> , Website: https://indianbank.in
2025-06-23 19:16:16 - bank_analyzer.parsers.base_parser.PDFStatementParser - INFO - parse:85 - Extracted 52 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\june to august.pdf
2025-06-23 19:16:16 - bank_analyzer.parsers.parser_factory - INFO - parse_file:114 - Successfully parsed 52 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\june to august.pdf
2025-06-23 19:16:16 - bank_analyzer.ui.main_window - INFO - run:58 - Parsed 52 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\june to august.pdf
2025-06-23 19:16:16 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-23 19:17:35 - __main__ - INFO - main:75 - Bank Statement Analyzer application exited with code: 0
2025-06-23 19:54:16 - bank_analyzer.core.logger - INFO - setup_logging:64 - Logging system initialized
2025-06-23 19:54:16 - bank_analyzer.core.logger - INFO - setup_logging:65 - Log file: logs\bank_analyzer_20250623.log
2025-06-23 19:54:16 - bank_analyzer.core.logger - INFO - setup_logging:66 - Log level: INFO
2025-06-23 19:54:16 - __main__ - INFO - main:45 - Starting Bank Statement Analyzer application
2025-06-23 19:54:16 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-23 19:54:16 - bank_analyzer.ui.main_window - INFO - load_initial_data:362 - Initial data loaded successfully
2025-06-23 19:54:16 - bank_analyzer.ui.main_window - INFO - __init__:97 - Bank Analyzer main window initialized
2025-06-23 19:54:16 - __main__ - INFO - main:71 - Bank Statement Analyzer application started successfully
2025-06-23 19:54:30 - bank_analyzer.parsers.parser_factory - INFO - get_parser:83 - Selected pdf parser for C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 19:54:30 - bank_analyzer.parsers.pdf_parser - INFO - parse:53 - Parsing PDF with rewritten parser: C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 19:54:38 - bank_analyzer.parsers.pdf_parser - INFO - parse:68 - Rewritten parser extracted 316 transactions
2025-06-23 19:54:38 - bank_analyzer.parsers.parser_factory - INFO - parse_file:116 - Successfully parsed 316 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 19:54:38 - bank_analyzer.ui.main_window - INFO - run:58 - Parsed 316 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 19:54:38 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-23 19:54:58 - bank_analyzer.parsers.parser_factory - INFO - get_parser:83 - Selected excel parser for C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Format Changed\Ind Jan to March.xlsx
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.ExcelStatementParser - INFO - parse:49 - Found 1 sheets: ['Jan to Mar']
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.ExcelStatementParser - INFO - _find_transaction_sheet:157 - Sheet scores: {'Jan to Mar': 75}
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.ExcelStatementParser - INFO - parse:58 - Using sheet: Jan to Mar
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.ExcelStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-31 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.ExcelStatementParser - WARNING - parse_date_string:212 - Could not parse date: credit interest
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.ExcelStatementParser - WARNING - parse_date_string:212 - Could not parse date: 0.0
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.ExcelStatementParser - WARNING - parse_date_string:212 - Could not parse date: 8.0
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.ExcelStatementParser - WARNING - parse_date_string:212 - Could not parse date: false
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.ExcelStatementParser - WARNING - parse_date_string:212 - Could not parse date: true
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.ExcelStatementParser - WARNING - parse_date_string:212 - Could not parse date: 42.06cr
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.ExcelStatementParser - WARNING - parse_date_string:212 - Could not parse date: 8.0
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - INFO - _detect_columns:145 - Detected column mappings: {'date': 'TRANSACTION DATE', 'description': 'PARTICULARS', 'amount': 'WITHDRAWALS', 'debit': 'WITHDRAWALS', 'credit': 'DEPOSIT', 'balance': 'BALANCE'}
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-31 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-31 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-31 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-31 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-31 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-31 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-31 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-31 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-31 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-31 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-31 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-31 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-30 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-30 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-30 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-30 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-29 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-29 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-29 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-29 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-29 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-29 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-29 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-29 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-29 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-29 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-29 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-29 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-28 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-28 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-28 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-28 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-27 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-26 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-26 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-26 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-26 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-26 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-26 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-26 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-25 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-25 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-25 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-25 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-25 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-25 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-24 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-24 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-23 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-23 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-22 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-22 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-22 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-22 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-21 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-21 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-21 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-21 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-21 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-20 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-20 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-20 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-20 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-20 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-20 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-20 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-20 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-20 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-20 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-20 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-20 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-20 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-19 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-19 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-19 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-18 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-18 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-18 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-18 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-18 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-18 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-18 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-18 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-18 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-18 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-18 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-16 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-16 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-16 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-15 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-15 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-15 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-15 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-15 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-15 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-14 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-14 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-14 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-14 00:00:00
2025-06-23 19:54:59 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-14 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-14 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-14 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-14 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-14 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-12 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-12 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-12 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-11 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-11 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-11 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-11 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-11 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-11 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-10 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-09 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-09 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-09 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-09 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-08 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-08 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-08 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-08 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-07 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-07 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-07 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-07 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-07 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-07 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-07 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-07 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-07 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-07 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-06 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-06 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-06 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-06 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-05 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-05 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-05 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-05 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-05 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-04 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-04 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-04 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-04 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-04 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-04 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-04 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-04 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-04 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-04 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-04 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-04 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-03 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-03 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-03 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-03 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-03 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-02 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-02 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-02 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-02 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-02 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-02 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-02 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-02 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-02 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-01 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-01 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-03-01 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-29 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-29 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-29 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-29 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-28 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-28 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-28 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-28 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-28 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-27 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-27 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-27 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-27 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-25 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-25 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-24 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-24 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-24 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-24 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-23 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-22 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-22 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-21 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-21 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-20 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-20 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-19 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-19 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-18 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-18 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-18 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-18 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-18 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-17 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-17 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-17 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-17 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-16 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-16 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-14 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-14 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-14 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-14 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-13 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-13 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-13 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-13 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-12 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-11 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-11 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-11 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-11 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-11 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-11 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-11 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-11 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-11 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-10 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-10 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-09 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-09 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-08 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-08 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-07 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-07 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-07 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-06 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-06 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-06 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-06 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-06 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-05 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-05 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-05 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-05 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-05 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-05 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-04 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-04 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-04 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-04 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-04 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-04 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-03 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-03 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-03 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-03 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-03 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-02 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-02 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-02 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-02 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-02 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-02 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-02 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-01 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-01 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-01 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-01 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-01 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-01 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-01 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-01 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-01 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-02-01 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-01-31 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-01-31 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-01-31 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-01-30 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-01-30 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-01-30 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-01-29 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-01-29 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-01-29 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-01-27 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-01-26 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-01-25 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-01-25 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-01-24 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-01-24 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-01-24 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-01-24 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-01-23 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-01-23 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-01-23 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-01-23 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-01-22 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-01-21 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-01-21 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-01-20 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-01-19 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-01-19 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-01-19 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-01-17 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-01-16 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-01-16 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-01-11 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-01-11 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-01-10 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-01-10 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-01-09 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-01-09 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-01-08 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-01-07 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-01-05 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.CSVStatementParser - WARNING - parse_date_string:212 - Could not parse date: 2024-01-05 00:00:00
2025-06-23 19:55:00 - bank_analyzer.parsers.base_parser.ExcelStatementParser - INFO - parse:95 - Extracted 0 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Format Changed\Ind Jan to March.xlsx
2025-06-23 19:55:00 - bank_analyzer.parsers.parser_factory - INFO - parse_file:116 - Successfully parsed 0 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Format Changed\Ind Jan to March.xlsx
2025-06-23 19:55:00 - bank_analyzer.ui.main_window - INFO - run:58 - Parsed 0 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Format Changed\Ind Jan to March.xlsx
2025-06-23 19:55:00 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-23 19:59:13 - __main__ - INFO - main:75 - Bank Statement Analyzer application exited with code: 0
2025-06-23 19:59:18 - bank_analyzer.core.logger - INFO - setup_logging:64 - Logging system initialized
2025-06-23 19:59:18 - bank_analyzer.core.logger - INFO - setup_logging:65 - Log file: logs\bank_analyzer_20250623.log
2025-06-23 19:59:18 - bank_analyzer.core.logger - INFO - setup_logging:66 - Log level: INFO
2025-06-23 19:59:18 - __main__ - INFO - main:45 - Starting Bank Statement Analyzer application
2025-06-23 19:59:18 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-23 19:59:18 - bank_analyzer.ui.main_window - INFO - load_initial_data:362 - Initial data loaded successfully
2025-06-23 19:59:18 - bank_analyzer.ui.main_window - INFO - __init__:97 - Bank Analyzer main window initialized
2025-06-23 19:59:18 - __main__ - INFO - main:74 - Bank Statement Analyzer application started successfully
2025-06-23 19:59:28 - bank_analyzer.parsers.parser_factory - INFO - get_parser:83 - Selected excel parser for C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Format Changed\Ind Jan to March.xlsx
2025-06-23 19:59:28 - bank_analyzer.parsers.base_parser.ExcelStatementParser - INFO - parse:49 - Found 1 sheets: ['Jan to Mar']
2025-06-23 19:59:29 - bank_analyzer.parsers.base_parser.ExcelStatementParser - INFO - _find_transaction_sheet:157 - Sheet scores: {'Jan to Mar': 75}
2025-06-23 19:59:29 - bank_analyzer.parsers.base_parser.ExcelStatementParser - INFO - parse:58 - Using sheet: Jan to Mar
2025-06-23 19:59:29 - bank_analyzer.parsers.base_parser.CSVStatementParser - INFO - _detect_columns:145 - Detected column mappings: {'date': 'TRANSACTION DATE', 'description': 'PARTICULARS', 'amount': 'WITHDRAWALS', 'debit': 'WITHDRAWALS', 'credit': 'DEPOSIT', 'balance': 'BALANCE'}
2025-06-23 19:59:29 - bank_analyzer.parsers.base_parser.ExcelStatementParser - INFO - parse:95 - Extracted 190 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Format Changed\Ind Jan to March.xlsx
2025-06-23 19:59:29 - bank_analyzer.parsers.parser_factory - INFO - parse_file:116 - Successfully parsed 190 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Format Changed\Ind Jan to March.xlsx
2025-06-23 19:59:29 - bank_analyzer.ui.main_window - INFO - run:58 - Parsed 190 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Format Changed\Ind Jan to March.xlsx
2025-06-23 19:59:29 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-23 19:59:41 - __main__ - INFO - main:78 - Bank Statement Analyzer application exited with code: 0
2025-06-23 19:59:51 - bank_analyzer.core.logger - INFO - setup_logging:64 - Logging system initialized
2025-06-23 19:59:51 - bank_analyzer.core.logger - INFO - setup_logging:65 - Log file: logs\bank_analyzer_20250623.log
2025-06-23 19:59:51 - bank_analyzer.core.logger - INFO - setup_logging:66 - Log level: INFO
2025-06-23 19:59:51 - __main__ - INFO - main:45 - Starting Bank Statement Analyzer application
2025-06-23 19:59:51 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-23 19:59:51 - bank_analyzer.ui.main_window - INFO - load_initial_data:362 - Initial data loaded successfully
2025-06-23 19:59:51 - bank_analyzer.ui.main_window - INFO - __init__:97 - Bank Analyzer main window initialized
2025-06-23 19:59:51 - __main__ - INFO - main:74 - Bank Statement Analyzer application started successfully
2025-06-23 19:59:59 - bank_analyzer.parsers.parser_factory - INFO - get_parser:83 - Selected pdf parser for C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 19:59:59 - bank_analyzer.parsers.pdf_parser - INFO - parse:53 - Parsing PDF with rewritten parser: C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 20:00:08 - bank_analyzer.parsers.pdf_parser - INFO - parse:68 - Rewritten parser extracted 316 transactions
2025-06-23 20:00:08 - bank_analyzer.parsers.parser_factory - INFO - parse_file:116 - Successfully parsed 316 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 20:00:08 - bank_analyzer.ui.main_window - INFO - run:58 - Parsed 316 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 20:00:08 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-23 20:32:07 - __main__ - INFO - main:78 - Bank Statement Analyzer application exited with code: 0
2025-06-23 20:57:30 - bank_analyzer.core.logger - INFO - setup_logging:64 - Logging system initialized
2025-06-23 20:57:30 - bank_analyzer.core.logger - INFO - setup_logging:65 - Log file: logs\bank_analyzer_20250623.log
2025-06-23 20:57:30 - bank_analyzer.core.logger - INFO - setup_logging:66 - Log level: INFO
2025-06-23 20:57:30 - __main__ - INFO - main:45 - Starting Bank Statement Analyzer application
2025-06-23 20:57:30 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-23 20:57:30 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-23 20:57:32 - bank_analyzer.ui.main_window - INFO - load_initial_data:407 - Initial data loaded successfully
2025-06-23 20:57:32 - bank_analyzer.ui.main_window - INFO - __init__:113 - Bank Analyzer main window initialized
2025-06-23 20:57:32 - __main__ - INFO - main:74 - Bank Statement Analyzer application started successfully
2025-06-23 20:57:45 - bank_analyzer.parsers.parser_factory - INFO - get_parser:83 - Selected excel parser for C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Format Changed\Ind Jan to March.xlsx
2025-06-23 20:57:45 - bank_analyzer.parsers.excel_parser - INFO - parse:43 - Parsing Excel with rewritten parser: C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Format Changed\Ind Jan to March.xlsx
2025-06-23 20:57:46 - bank_analyzer.parsers.excel_parser - INFO - parse:48 - Excel file has 316 rows and 9 columns
2025-06-23 20:57:46 - bank_analyzer.parsers.excel_parser - INFO - parse:54 - Rewritten Excel parser extracted 316 transactions
2025-06-23 20:57:46 - bank_analyzer.parsers.parser_factory - INFO - parse_file:116 - Successfully parsed 316 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Format Changed\Ind Jan to March.xlsx
2025-06-23 20:57:46 - bank_analyzer.ui.main_window - INFO - run:62 - Parsed 316 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Format Changed\Ind Jan to March.xlsx
2025-06-23 20:57:46 - bank_analyzer.parsers.parser_factory - INFO - get_parser:83 - Selected excel parser for C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Format Changed\Jun To Aug.xlsx
2025-06-23 20:57:46 - bank_analyzer.parsers.excel_parser - INFO - parse:43 - Parsing Excel with rewritten parser: C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Format Changed\Jun To Aug.xlsx
2025-06-23 20:57:46 - bank_analyzer.parsers.excel_parser - INFO - parse:48 - Excel file has 109 rows and 5 columns
2025-06-23 20:57:46 - bank_analyzer.parsers.excel_parser - INFO - parse:54 - Rewritten Excel parser extracted 109 transactions
2025-06-23 20:57:46 - bank_analyzer.parsers.parser_factory - INFO - parse_file:116 - Successfully parsed 109 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Format Changed\Jun To Aug.xlsx
2025-06-23 20:57:46 - bank_analyzer.ui.main_window - INFO - run:62 - Parsed 109 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Format Changed\Jun To Aug.xlsx
2025-06-23 20:57:46 - bank_analyzer.parsers.parser_factory - INFO - get_parser:83 - Selected excel parser for C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Format Changed\mar to jun.xlsx
2025-06-23 20:57:46 - bank_analyzer.parsers.excel_parser - INFO - parse:43 - Parsing Excel with rewritten parser: C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Format Changed\mar to jun.xlsx
2025-06-23 20:57:46 - bank_analyzer.parsers.excel_parser - INFO - parse:48 - Excel file has 268 rows and 7 columns
2025-06-23 20:57:46 - bank_analyzer.parsers.excel_parser - INFO - parse:54 - Rewritten Excel parser extracted 268 transactions
2025-06-23 20:57:46 - bank_analyzer.parsers.parser_factory - INFO - parse_file:116 - Successfully parsed 268 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Format Changed\mar to jun.xlsx
2025-06-23 20:57:46 - bank_analyzer.ui.main_window - INFO - run:62 - Parsed 268 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Format Changed\mar to jun.xlsx
2025-06-23 20:57:46 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-23 20:57:47 - bank_analyzer.ml.data_preparation - INFO - extract_unique_transactions:187 - Extracted 400 unique transaction descriptions from 693 transactions
2025-06-23 20:57:47 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:229 - Loaded 20 unique transactions from storage
2025-06-23 20:57:47 - bank_analyzer.ml.data_preparation - INFO - save_unique_transactions:279 - Saved 420 unique transactions to storage
2025-06-23 20:58:03 - bank_analyzer.parsers.parser_factory - INFO - get_parser:83 - Selected pdf parser for C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 20:58:03 - bank_analyzer.parsers.pdf_parser - INFO - parse:53 - Parsing PDF with rewritten parser: C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 20:58:12 - bank_analyzer.parsers.pdf_parser - INFO - parse:68 - Rewritten parser extracted 316 transactions
2025-06-23 20:58:12 - bank_analyzer.parsers.parser_factory - INFO - parse_file:116 - Successfully parsed 316 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 20:58:12 - bank_analyzer.ui.main_window - INFO - run:62 - Parsed 316 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 20:58:12 - bank_analyzer.parsers.parser_factory - INFO - get_parser:83 - Selected pdf parser for C:\Users\<USER>\Desktop\New folder\statements\Indian bank\june to august.pdf
2025-06-23 20:58:12 - bank_analyzer.parsers.pdf_parser - INFO - parse:53 - Parsing PDF with rewritten parser: C:\Users\<USER>\Desktop\New folder\statements\Indian bank\june to august.pdf
2025-06-23 20:58:15 - bank_analyzer.parsers.pdf_parser - INFO - parse:68 - Rewritten parser extracted 109 transactions
2025-06-23 20:58:15 - bank_analyzer.parsers.parser_factory - INFO - parse_file:116 - Successfully parsed 109 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\june to august.pdf
2025-06-23 20:58:15 - bank_analyzer.ui.main_window - INFO - run:62 - Parsed 109 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\june to august.pdf
2025-06-23 20:58:15 - bank_analyzer.parsers.parser_factory - INFO - get_parser:83 - Selected pdf parser for C:\Users\<USER>\Desktop\New folder\statements\Indian bank\March to june.pdf
2025-06-23 20:58:15 - bank_analyzer.parsers.pdf_parser - INFO - parse:53 - Parsing PDF with rewritten parser: C:\Users\<USER>\Desktop\New folder\statements\Indian bank\March to june.pdf
2025-06-23 20:58:23 - bank_analyzer.parsers.pdf_parser - INFO - parse:68 - Rewritten parser extracted 281 transactions
2025-06-23 20:58:23 - bank_analyzer.parsers.parser_factory - INFO - parse_file:116 - Successfully parsed 281 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\March to june.pdf
2025-06-23 20:58:23 - bank_analyzer.ui.main_window - INFO - run:62 - Parsed 281 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\March to june.pdf
2025-06-23 20:58:23 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-23 20:58:23 - bank_analyzer.ml.data_preparation - INFO - extract_unique_transactions:187 - Extracted 405 unique transaction descriptions from 706 transactions
2025-06-23 20:58:24 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:229 - Loaded 420 unique transactions from storage
2025-06-23 20:58:24 - bank_analyzer.ml.data_preparation - INFO - save_unique_transactions:279 - Saved 430 unique transactions to storage
2025-06-23 20:58:38 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:229 - Loaded 430 unique transactions from storage
2025-06-23 20:58:39 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:229 - Loaded 430 unique transactions from storage
2025-06-23 20:59:00 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:109 - Loaded 60 categories from main application
2025-06-23 20:59:01 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:229 - Loaded 430 unique transactions from storage
2025-06-23 21:01:04 - bank_analyzer.ml.data_preparation - INFO - save_unique_transactions:279 - Saved 430 unique transactions to storage
2025-06-23 21:01:04 - bank_analyzer.ml.training_data_manager - INFO - label_transaction:133 - Labeled transaction dd7a5171a473 as Other/Unexpected
2025-06-23 21:15:53 - __main__ - INFO - main:78 - Bank Statement Analyzer application exited with code: 0
2025-06-23 21:16:10 - bank_analyzer.core.logger - INFO - setup_logging:64 - Logging system initialized
2025-06-23 21:16:10 - bank_analyzer.core.logger - INFO - setup_logging:65 - Log file: logs\bank_analyzer_20250623.log
2025-06-23 21:16:10 - bank_analyzer.core.logger - INFO - setup_logging:66 - Log level: INFO
2025-06-23 21:16:10 - __main__ - INFO - main:45 - Starting Bank Statement Analyzer application
2025-06-23 21:16:10 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-23 21:16:10 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-23 21:16:11 - bank_analyzer.ui.main_window - INFO - load_initial_data:414 - Initial data loaded successfully
2025-06-23 21:16:11 - bank_analyzer.ui.main_window - INFO - __init__:114 - Bank Analyzer main window initialized
2025-06-23 21:16:11 - __main__ - INFO - main:74 - Bank Statement Analyzer application started successfully
2025-06-23 21:16:41 - bank_analyzer.parsers.parser_factory - INFO - get_parser:83 - Selected pdf parser for C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 21:16:41 - bank_analyzer.parsers.pdf_parser - INFO - parse:53 - Parsing PDF with rewritten parser: C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 21:16:50 - bank_analyzer.parsers.pdf_parser - INFO - parse:68 - Rewritten parser extracted 316 transactions
2025-06-23 21:16:50 - bank_analyzer.parsers.parser_factory - INFO - parse_file:116 - Successfully parsed 316 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 21:16:50 - bank_analyzer.ui.main_window - INFO - run:63 - Parsed 316 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 21:16:50 - bank_analyzer.parsers.parser_factory - INFO - get_parser:83 - Selected pdf parser for C:\Users\<USER>\Desktop\New folder\statements\Indian bank\june to august.pdf
2025-06-23 21:16:50 - bank_analyzer.parsers.pdf_parser - INFO - parse:53 - Parsing PDF with rewritten parser: C:\Users\<USER>\Desktop\New folder\statements\Indian bank\june to august.pdf
2025-06-23 21:16:53 - bank_analyzer.parsers.pdf_parser - INFO - parse:68 - Rewritten parser extracted 109 transactions
2025-06-23 21:16:53 - bank_analyzer.parsers.parser_factory - INFO - parse_file:116 - Successfully parsed 109 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\june to august.pdf
2025-06-23 21:16:53 - bank_analyzer.ui.main_window - INFO - run:63 - Parsed 109 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\june to august.pdf
2025-06-23 21:16:53 - bank_analyzer.parsers.parser_factory - INFO - get_parser:83 - Selected pdf parser for C:\Users\<USER>\Desktop\New folder\statements\Indian bank\March to june.pdf
2025-06-23 21:16:53 - bank_analyzer.parsers.pdf_parser - INFO - parse:53 - Parsing PDF with rewritten parser: C:\Users\<USER>\Desktop\New folder\statements\Indian bank\March to june.pdf
2025-06-23 21:17:02 - bank_analyzer.parsers.pdf_parser - INFO - parse:68 - Rewritten parser extracted 281 transactions
2025-06-23 21:17:02 - bank_analyzer.parsers.parser_factory - INFO - parse_file:116 - Successfully parsed 281 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\March to june.pdf
2025-06-23 21:17:02 - bank_analyzer.ui.main_window - INFO - run:63 - Parsed 281 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\March to june.pdf
2025-06-23 21:17:02 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-23 21:17:02 - bank_analyzer.ml.data_preparation - INFO - extract_unique_transactions:187 - Extracted 405 unique transaction descriptions from 706 transactions
2025-06-23 21:17:03 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:229 - Loaded 430 unique transactions from storage
2025-06-23 21:17:03 - bank_analyzer.ml.data_preparation - INFO - save_unique_transactions:279 - Saved 430 unique transactions to storage
2025-06-23 21:17:19 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:109 - Loaded 60 categories from main application
2025-06-23 21:17:20 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:229 - Loaded 430 unique transactions from storage
2025-06-23 21:17:20 - bank_analyzer.ui.ml_labeling_window - ERROR - load_transaction_batch:482 - Error loading transaction batch: unsupported operand type(s) for |: 'ItemFlag' and 'TextFlag'
2025-06-23 21:17:38 - __main__ - INFO - main:78 - Bank Statement Analyzer application exited with code: 0
2025-06-23 22:02:18 - bank_analyzer.core.logger - INFO - setup_logging:64 - Logging system initialized
2025-06-23 22:02:18 - bank_analyzer.core.logger - INFO - setup_logging:65 - Log file: logs\bank_analyzer_20250623.log
2025-06-23 22:02:18 - bank_analyzer.core.logger - INFO - setup_logging:66 - Log level: INFO
2025-06-23 22:02:18 - __main__ - INFO - main:45 - Starting Bank Statement Analyzer application
2025-06-23 22:02:18 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-23 22:02:18 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-23 22:02:18 - bank_analyzer.ui.main_window - INFO - load_initial_data:414 - Initial data loaded successfully
2025-06-23 22:02:18 - bank_analyzer.ui.main_window - INFO - __init__:114 - Bank Analyzer main window initialized
2025-06-23 22:02:18 - __main__ - INFO - main:74 - Bank Statement Analyzer application started successfully
2025-06-23 22:02:33 - bank_analyzer.parsers.parser_factory - INFO - get_parser:83 - Selected pdf parser for C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 22:02:33 - bank_analyzer.parsers.pdf_parser - INFO - parse:53 - Parsing PDF with rewritten parser: C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 22:02:42 - bank_analyzer.parsers.pdf_parser - INFO - parse:68 - Rewritten parser extracted 316 transactions
2025-06-23 22:02:42 - bank_analyzer.parsers.parser_factory - INFO - parse_file:116 - Successfully parsed 316 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 22:02:42 - bank_analyzer.ui.main_window - INFO - run:63 - Parsed 316 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 22:02:42 - bank_analyzer.parsers.parser_factory - INFO - get_parser:83 - Selected pdf parser for C:\Users\<USER>\Desktop\New folder\statements\Indian bank\june to august.pdf
2025-06-23 22:02:42 - bank_analyzer.parsers.pdf_parser - INFO - parse:53 - Parsing PDF with rewritten parser: C:\Users\<USER>\Desktop\New folder\statements\Indian bank\june to august.pdf
2025-06-23 22:02:46 - bank_analyzer.parsers.pdf_parser - INFO - parse:68 - Rewritten parser extracted 109 transactions
2025-06-23 22:02:46 - bank_analyzer.parsers.parser_factory - INFO - parse_file:116 - Successfully parsed 109 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\june to august.pdf
2025-06-23 22:02:46 - bank_analyzer.ui.main_window - INFO - run:63 - Parsed 109 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\june to august.pdf
2025-06-23 22:02:46 - bank_analyzer.parsers.parser_factory - INFO - get_parser:83 - Selected pdf parser for C:\Users\<USER>\Desktop\New folder\statements\Indian bank\March to june.pdf
2025-06-23 22:02:46 - bank_analyzer.parsers.pdf_parser - INFO - parse:53 - Parsing PDF with rewritten parser: C:\Users\<USER>\Desktop\New folder\statements\Indian bank\March to june.pdf
2025-06-23 22:02:54 - bank_analyzer.parsers.pdf_parser - INFO - parse:68 - Rewritten parser extracted 281 transactions
2025-06-23 22:02:54 - bank_analyzer.parsers.parser_factory - INFO - parse_file:116 - Successfully parsed 281 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\March to june.pdf
2025-06-23 22:02:54 - bank_analyzer.ui.main_window - INFO - run:63 - Parsed 281 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\March to june.pdf
2025-06-23 22:02:54 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-23 22:02:55 - bank_analyzer.ml.data_preparation - INFO - extract_unique_transactions:187 - Extracted 405 unique transaction descriptions from 706 transactions
2025-06-23 22:02:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:229 - Loaded 430 unique transactions from storage
2025-06-23 22:02:56 - bank_analyzer.ml.data_preparation - INFO - save_unique_transactions:279 - Saved 430 unique transactions to storage
2025-06-23 22:03:17 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:109 - Loaded 60 categories from main application
2025-06-23 22:03:18 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:229 - Loaded 430 unique transactions from storage
2025-06-23 22:03:21 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:109 - Loaded 60 categories from main application
2025-06-23 22:04:17 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:109 - Loaded 60 categories from main application
2025-06-23 22:05:18 - bank_analyzer.ml.training_data_manager - INFO - start_labeling_session:82 - Started labeling session: session_20250623_220518
2025-06-23 22:05:58 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:109 - Loaded 60 categories from main application
2025-06-23 22:06:47 - bank_analyzer.ml.category_manager - INFO - create_category:297 - Created category: zomato cash limit (ID: ml_zomato_cash_limit_1750696607)
2025-06-23 22:07:10 - bank_analyzer.ml.category_manager - INFO - create_category:297 - Created category: cash limit (ID: ml_cash_limit_1750696630)
2025-06-23 22:07:38 - bank_analyzer.ml.training_data_manager - INFO - end_labeling_session:101 - Ended labeling session: session_20250623_220518
2025-06-23 22:08:00 - __main__ - INFO - main:78 - Bank Statement Analyzer application exited with code: 0
2025-06-23 22:14:12 - bank_analyzer.core.logger - INFO - setup_logging:64 - Logging system initialized
2025-06-23 22:14:12 - bank_analyzer.core.logger - INFO - setup_logging:65 - Log file: logs\bank_analyzer_20250623.log
2025-06-23 22:14:12 - bank_analyzer.core.logger - INFO - setup_logging:66 - Log level: INFO
2025-06-23 22:14:12 - __main__ - INFO - main:45 - Starting Bank Statement Analyzer application
2025-06-23 22:14:12 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-23 22:14:12 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-23 22:14:12 - bank_analyzer.ui.main_window - INFO - load_initial_data:414 - Initial data loaded successfully
2025-06-23 22:14:12 - bank_analyzer.ui.main_window - INFO - __init__:114 - Bank Analyzer main window initialized
2025-06-23 22:14:12 - __main__ - INFO - main:74 - Bank Statement Analyzer application started successfully
2025-06-23 22:14:21 - bank_analyzer.parsers.parser_factory - INFO - get_parser:83 - Selected pdf parser for C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 22:14:21 - bank_analyzer.parsers.pdf_parser - INFO - parse:53 - Parsing PDF with rewritten parser: C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 22:14:29 - bank_analyzer.parsers.pdf_parser - INFO - parse:68 - Rewritten parser extracted 316 transactions
2025-06-23 22:14:29 - bank_analyzer.parsers.parser_factory - INFO - parse_file:116 - Successfully parsed 316 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 22:14:29 - bank_analyzer.ui.main_window - INFO - run:63 - Parsed 316 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 22:14:29 - bank_analyzer.parsers.parser_factory - INFO - get_parser:83 - Selected pdf parser for C:\Users\<USER>\Desktop\New folder\statements\Indian bank\june to august.pdf
2025-06-23 22:14:29 - bank_analyzer.parsers.pdf_parser - INFO - parse:53 - Parsing PDF with rewritten parser: C:\Users\<USER>\Desktop\New folder\statements\Indian bank\june to august.pdf
2025-06-23 22:14:33 - bank_analyzer.parsers.pdf_parser - INFO - parse:68 - Rewritten parser extracted 109 transactions
2025-06-23 22:14:33 - bank_analyzer.parsers.parser_factory - INFO - parse_file:116 - Successfully parsed 109 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\june to august.pdf
2025-06-23 22:14:33 - bank_analyzer.ui.main_window - INFO - run:63 - Parsed 109 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\june to august.pdf
2025-06-23 22:14:33 - bank_analyzer.parsers.parser_factory - INFO - get_parser:83 - Selected pdf parser for C:\Users\<USER>\Desktop\New folder\statements\Indian bank\March to june.pdf
2025-06-23 22:14:33 - bank_analyzer.parsers.pdf_parser - INFO - parse:53 - Parsing PDF with rewritten parser: C:\Users\<USER>\Desktop\New folder\statements\Indian bank\March to june.pdf
2025-06-23 22:14:41 - bank_analyzer.parsers.pdf_parser - INFO - parse:68 - Rewritten parser extracted 281 transactions
2025-06-23 22:14:41 - bank_analyzer.parsers.parser_factory - INFO - parse_file:116 - Successfully parsed 281 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\March to june.pdf
2025-06-23 22:14:41 - bank_analyzer.ui.main_window - INFO - run:63 - Parsed 281 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\March to june.pdf
2025-06-23 22:14:41 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-23 22:14:42 - bank_analyzer.ml.data_preparation - INFO - extract_unique_transactions:187 - Extracted 405 unique transaction descriptions from 706 transactions
2025-06-23 22:14:43 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:229 - Loaded 430 unique transactions from storage
2025-06-23 22:14:43 - bank_analyzer.ml.data_preparation - INFO - save_unique_transactions:279 - Saved 430 unique transactions to storage
2025-06-23 22:15:01 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:109 - Loaded 60 categories from main application
2025-06-23 22:15:01 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:139 - Loaded 2 ML-specific categories
2025-06-23 22:15:09 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:229 - Loaded 430 unique transactions from storage
2025-06-23 22:15:10 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:229 - Loaded 430 unique transactions from storage
2025-06-23 22:15:20 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:109 - Loaded 60 categories from main application
2025-06-23 22:15:20 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:139 - Loaded 2 ML-specific categories
2025-06-23 22:15:21 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:229 - Loaded 430 unique transactions from storage
2025-06-23 22:15:34 - bank_analyzer.ml.training_data_manager - INFO - start_labeling_session:82 - Started labeling session: session_20250623_221534
2025-06-23 22:15:54 - bank_analyzer.ml.data_preparation - INFO - save_unique_transactions:279 - Saved 430 unique transactions to storage
2025-06-23 22:15:54 - bank_analyzer.ml.training_data_manager - INFO - label_transaction:133 - Labeled transaction 5b9ced220278 as zomato cash limit/cash limit
2025-06-23 22:16:17 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:109 - Loaded 60 categories from main application
2025-06-23 22:16:17 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:139 - Loaded 2 ML-specific categories
2025-06-23 22:16:56 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:109 - Loaded 60 categories from main application
2025-06-23 22:16:56 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:139 - Loaded 2 ML-specific categories
2025-06-23 22:17:34 - bank_analyzer.ml.category_manager - INFO - create_category:297 - Created category: GF (ID: ml_gf_1750697254)
2025-06-23 22:17:53 - bank_analyzer.ml.category_manager - INFO - create_category:297 - Created category: Investment (ID: ml_investment_1750697273)
2025-06-23 22:18:01 - bank_analyzer.ml.category_manager - INFO - create_category:297 - Created category: zerodha (ID: ml_zerodha_1750697281)
2025-06-23 22:18:12 - bank_analyzer.ml.category_manager - INFO - create_category:297 - Created category: aura gold (ID: ml_aura_gold_1750697292)
2025-06-23 22:18:23 - bank_analyzer.ml.category_manager - INFO - create_category:297 - Created category: aura silver (ID: ml_aura_silver_1750697303)
2025-06-23 22:18:44 - bank_analyzer.ml.training_data_manager - INFO - end_labeling_session:101 - Ended labeling session: session_20250623_221534
2025-06-23 22:18:52 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:109 - Loaded 60 categories from main application
2025-06-23 22:18:52 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:139 - Loaded 7 ML-specific categories
2025-06-23 22:19:00 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:109 - Loaded 60 categories from main application
2025-06-23 22:19:00 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:139 - Loaded 7 ML-specific categories
2025-06-23 22:19:06 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:109 - Loaded 60 categories from main application
2025-06-23 22:19:06 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:139 - Loaded 7 ML-specific categories
2025-06-23 22:20:06 - bank_analyzer.ml.training_data_manager - INFO - start_labeling_session:82 - Started labeling session: session_20250623_222006
2025-06-23 22:23:14 - bank_analyzer.ml.training_data_manager - INFO - end_labeling_session:101 - Ended labeling session: session_20250623_222006
2025-06-23 22:23:20 - __main__ - INFO - main:78 - Bank Statement Analyzer application exited with code: 0
2025-06-23 22:26:36 - bank_analyzer.core.logger - INFO - setup_logging:64 - Logging system initialized
2025-06-23 22:26:36 - bank_analyzer.core.logger - INFO - setup_logging:65 - Log file: logs\bank_analyzer_20250623.log
2025-06-23 22:26:36 - bank_analyzer.core.logger - INFO - setup_logging:66 - Log level: INFO
2025-06-23 22:26:36 - __main__ - INFO - main:45 - Starting Bank Statement Analyzer application
2025-06-23 22:26:36 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-23 22:26:36 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-23 22:26:36 - bank_analyzer.ui.main_window - INFO - load_initial_data:414 - Initial data loaded successfully
2025-06-23 22:26:36 - bank_analyzer.ui.main_window - INFO - __init__:114 - Bank Analyzer main window initialized
2025-06-23 22:26:36 - __main__ - INFO - main:74 - Bank Statement Analyzer application started successfully
2025-06-23 22:26:45 - bank_analyzer.parsers.parser_factory - INFO - get_parser:83 - Selected pdf parser for C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 22:26:45 - bank_analyzer.parsers.pdf_parser - INFO - parse:53 - Parsing PDF with rewritten parser: C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 22:26:54 - bank_analyzer.parsers.pdf_parser - INFO - parse:68 - Rewritten parser extracted 316 transactions
2025-06-23 22:26:54 - bank_analyzer.parsers.parser_factory - INFO - parse_file:116 - Successfully parsed 316 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 22:26:54 - bank_analyzer.ui.main_window - INFO - run:63 - Parsed 316 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 22:26:54 - bank_analyzer.parsers.parser_factory - INFO - get_parser:83 - Selected pdf parser for C:\Users\<USER>\Desktop\New folder\statements\Indian bank\june to august.pdf
2025-06-23 22:26:54 - bank_analyzer.parsers.pdf_parser - INFO - parse:53 - Parsing PDF with rewritten parser: C:\Users\<USER>\Desktop\New folder\statements\Indian bank\june to august.pdf
2025-06-23 22:26:58 - bank_analyzer.parsers.pdf_parser - INFO - parse:68 - Rewritten parser extracted 109 transactions
2025-06-23 22:26:58 - bank_analyzer.parsers.parser_factory - INFO - parse_file:116 - Successfully parsed 109 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\june to august.pdf
2025-06-23 22:26:58 - bank_analyzer.ui.main_window - INFO - run:63 - Parsed 109 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\june to august.pdf
2025-06-23 22:26:58 - bank_analyzer.parsers.parser_factory - INFO - get_parser:83 - Selected pdf parser for C:\Users\<USER>\Desktop\New folder\statements\Indian bank\March to june.pdf
2025-06-23 22:26:58 - bank_analyzer.parsers.pdf_parser - INFO - parse:53 - Parsing PDF with rewritten parser: C:\Users\<USER>\Desktop\New folder\statements\Indian bank\March to june.pdf
2025-06-23 22:27:05 - bank_analyzer.parsers.pdf_parser - INFO - parse:68 - Rewritten parser extracted 281 transactions
2025-06-23 22:27:05 - bank_analyzer.parsers.parser_factory - INFO - parse_file:116 - Successfully parsed 281 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\March to june.pdf
2025-06-23 22:27:05 - bank_analyzer.ui.main_window - INFO - run:63 - Parsed 281 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\March to june.pdf
2025-06-23 22:27:05 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-23 22:27:06 - bank_analyzer.ml.data_preparation - INFO - extract_unique_transactions:187 - Extracted 405 unique transaction descriptions from 706 transactions
2025-06-23 22:27:07 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:229 - Loaded 430 unique transactions from storage
2025-06-23 22:27:07 - bank_analyzer.ml.data_preparation - INFO - save_unique_transactions:279 - Saved 430 unique transactions to storage
2025-06-23 22:27:14 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:116 - Loaded 60 categories from main application
2025-06-23 22:27:14 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:146 - Loaded 7 ML-specific categories
2025-06-23 22:27:15 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:229 - Loaded 430 unique transactions from storage
2025-06-23 22:27:25 - bank_analyzer.ml.training_data_manager - INFO - start_labeling_session:82 - Started labeling session: session_20250623_222725
2025-06-23 22:27:36 - bank_analyzer.ml.data_preparation - INFO - save_unique_transactions:279 - Saved 430 unique transactions to storage
2025-06-23 22:27:36 - bank_analyzer.ml.training_data_manager - INFO - label_transaction:133 - Labeled transaction 89a60bd14fd5 as Other/GF
2025-06-23 22:28:04 - bank_analyzer.ml.data_preparation - INFO - save_unique_transactions:279 - Saved 430 unique transactions to storage
2025-06-23 22:28:04 - bank_analyzer.ml.training_data_manager - INFO - label_transaction:133 - Labeled transaction 1c2cf891cee4 as Other/GF
2025-06-23 22:29:02 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:116 - Loaded 60 categories from main application
2025-06-23 22:29:02 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:146 - Loaded 7 ML-specific categories
2025-06-23 22:29:15 - bank_analyzer.ml.category_manager - INFO - create_category:304 - Created category: EMI (ID: ml_emi_1750697955)
2025-06-23 22:29:29 - bank_analyzer.ml.category_manager - INFO - create_category:304 - Created category: Housing Loan (ID: ml_housing_loan_1750697969)
2025-06-23 22:29:42 - bank_analyzer.ml.category_manager - INFO - create_category:304 - Created category: Ola s1 x 1 (ID: ml_ola_s1_x_1_1750697982)
2025-06-23 22:29:52 - bank_analyzer.ml.category_manager - INFO - create_category:304 - Created category: ola s1 x 2 (ID: ml_ola_s1_x_2_1750697992)
2025-06-23 22:30:00 - bank_analyzer.ml.category_manager - INFO - create_category:304 - Created category: ola s1 pro (ID: ml_ola_s1_pro_1750698000)
2025-06-23 22:30:13 - bank_analyzer.ml.category_manager - INFO - create_category:304 - Created category: iphone 16 (ID: ml_iphone_16_1750698013)
2025-06-23 22:30:25 - bank_analyzer.ml.category_manager - INFO - create_category:304 - Created category: dji osmo action 2 (ID: ml_dji_osmo_action_2_1750698025)
2025-06-23 22:30:39 - bank_analyzer.ml.category_manager - INFO - create_category:304 - Created category: Moneyview app (ID: ml_moneyview_app_1750698039)
2025-06-23 22:31:21 - bank_analyzer.ml.category_manager - INFO - create_category:304 - Created category: Kreditbee (ID: ml_kreditbee_1750698081)
2025-06-23 22:31:28 - bank_analyzer.ml.category_manager - INFO - create_category:304 - Created category: slice (ID: ml_slice_1750698088)
2025-06-23 22:31:40 - bank_analyzer.ml.category_manager - INFO - create_category:304 - Created category: Mpokket (ID: ml_mpokket_1750698100)
2025-06-23 22:31:50 - bank_analyzer.ml.category_manager - INFO - create_category:304 - Created category: TrueBalance (ID: ml_truebalance_1750698110)
2025-06-23 22:32:16 - bank_analyzer.ml.category_manager - INFO - create_category:304 - Created category: Youtube (ID: ml_youtube_1750698136)
2025-06-23 22:32:23 - bank_analyzer.ml.category_manager - INFO - create_category:304 - Created category: Adsence (ID: ml_adsence_1750698143)
2025-06-23 22:32:32 - bank_analyzer.ml.category_manager - INFO - create_category:304 - Created category: setttings (ID: ml_setttings_1750698152)
2025-06-23 22:32:56 - bank_analyzer.ml.category_manager - INFO - create_category:304 - Created category: gplinks (ID: ml_gplinks_1750698176)
2025-06-23 22:33:08 - bank_analyzer.ml.category_manager - INFO - create_category:304 - Created category: id sales (ID: ml_id_sales_1750698188)
2025-06-23 22:33:19 - bank_analyzer.ml.category_manager - INFO - create_category:304 - Created category: card account sales (ID: ml_card_account_sales_1750698199)
2025-06-23 22:33:33 - bank_analyzer.ml.category_manager - INFO - refresh_categories:64 - Refreshing categories...
2025-06-23 22:33:33 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:116 - Loaded 60 categories from main application
2025-06-23 22:33:33 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:146 - Loaded 25 ML-specific categories
2025-06-23 22:33:33 - bank_analyzer.ml.category_manager - INFO - refresh_categories:67 - Categories refreshed successfully
2025-06-23 22:33:33 - bank_analyzer.ui.ml_labeling_window - INFO - refresh_categories:620 - Categories refreshed successfully
2025-06-23 22:34:03 - bank_analyzer.ml.data_preparation - INFO - save_unique_transactions:279 - Saved 430 unique transactions to storage
2025-06-23 22:34:03 - bank_analyzer.ml.training_data_manager - INFO - label_transaction:133 - Labeled transaction ac31229ac6c5 as EMI/ola s1 pro
2025-06-23 22:34:22 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:116 - Loaded 60 categories from main application
2025-06-23 22:34:22 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:146 - Loaded 25 ML-specific categories
2025-06-23 22:34:23 - bank_analyzer.ml.category_manager - INFO - refresh_categories:64 - Refreshing categories...
2025-06-23 22:34:24 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:116 - Loaded 60 categories from main application
2025-06-23 22:34:24 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:146 - Loaded 25 ML-specific categories
2025-06-23 22:34:24 - bank_analyzer.ml.category_manager - INFO - refresh_categories:67 - Categories refreshed successfully
2025-06-23 22:34:24 - bank_analyzer.ui.ml_labeling_window - INFO - refresh_categories:620 - Categories refreshed successfully
2025-06-23 22:35:05 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:116 - Loaded 60 categories from main application
2025-06-23 22:35:05 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:146 - Loaded 25 ML-specific categories
2025-06-23 22:35:18 - bank_analyzer.ml.category_manager - INFO - create_category:304 - Created category: House Hold Expenses (ID: ml_house_hold_expenses_1750698318)
2025-06-23 22:35:38 - bank_analyzer.ml.category_manager - INFO - create_category:304 - Created category: Daily expenses (ID: ml_daily_expenses_1750698338)
2025-06-23 22:35:40 - bank_analyzer.ml.category_manager - INFO - refresh_categories:64 - Refreshing categories...
2025-06-23 22:35:40 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:116 - Loaded 60 categories from main application
2025-06-23 22:35:40 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:146 - Loaded 27 ML-specific categories
2025-06-23 22:35:40 - bank_analyzer.ml.category_manager - INFO - refresh_categories:67 - Categories refreshed successfully
2025-06-23 22:35:40 - bank_analyzer.ui.ml_labeling_window - INFO - refresh_categories:620 - Categories refreshed successfully
2025-06-23 22:35:47 - bank_analyzer.ml.data_preparation - INFO - save_unique_transactions:279 - Saved 430 unique transactions to storage
2025-06-23 22:35:47 - bank_analyzer.ml.training_data_manager - INFO - label_transaction:133 - Labeled transaction 09e87f12d70b as House Hold Expenses/Daily expenses
2025-06-23 22:36:02 - bank_analyzer.ml.data_preparation - INFO - save_unique_transactions:279 - Saved 430 unique transactions to storage
2025-06-23 22:36:02 - bank_analyzer.ml.training_data_manager - INFO - label_transaction:133 - Labeled transaction cf29fae34d81 as House Hold Expenses/Daily expenses
2025-06-23 22:36:37 - bank_analyzer.ml.data_preparation - INFO - save_unique_transactions:279 - Saved 430 unique transactions to storage
2025-06-23 22:36:37 - bank_analyzer.ml.training_data_manager - INFO - label_transaction:133 - Labeled transaction 1782a67877da as House Hold Expenses/Daily expenses
2025-06-23 22:36:46 - bank_analyzer.ml.data_preparation - INFO - save_unique_transactions:279 - Saved 430 unique transactions to storage
2025-06-23 22:36:46 - bank_analyzer.ml.training_data_manager - INFO - label_transaction:133 - Labeled transaction e64b38ec76a2 as Bills & Utilities/Phone
2025-06-23 22:36:52 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:116 - Loaded 60 categories from main application
2025-06-23 22:36:52 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:146 - Loaded 27 ML-specific categories
2025-06-23 22:37:44 - bank_analyzer.ml.category_manager - INFO - create_category:304 - Created category: Swiggy Cash Limit (ID: ml_swiggy_cash_limit_1750698464)
2025-06-23 22:37:54 - bank_analyzer.ml.category_manager - INFO - create_category:304 - Created category: cash limit (ID: ml_cash_limit_1750698474)
2025-06-23 22:38:12 - bank_analyzer.ml.category_manager - INFO - refresh_categories:64 - Refreshing categories...
2025-06-23 22:38:12 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:116 - Loaded 60 categories from main application
2025-06-23 22:38:12 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:146 - Loaded 29 ML-specific categories
2025-06-23 22:38:12 - bank_analyzer.ml.category_manager - INFO - refresh_categories:67 - Categories refreshed successfully
2025-06-23 22:38:12 - bank_analyzer.ui.ml_labeling_window - INFO - refresh_categories:620 - Categories refreshed successfully
2025-06-23 22:38:20 - bank_analyzer.ml.data_preparation - INFO - save_unique_transactions:279 - Saved 430 unique transactions to storage
2025-06-23 22:38:20 - bank_analyzer.ml.training_data_manager - INFO - label_transaction:133 - Labeled transaction 5cb8c323b66e as Swiggy Cash Limit/cash limit
2025-06-23 22:38:46 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:116 - Loaded 60 categories from main application
2025-06-23 22:38:46 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:146 - Loaded 29 ML-specific categories
2025-06-23 22:39:22 - bank_analyzer.ml.category_manager - INFO - create_category:304 - Created category: Online Stores (ID: ml_online_stores_1750698562)
2025-06-23 22:39:41 - bank_analyzer.ml.category_manager - INFO - create_category:304 - Created category: amazon (ID: ml_amazon_1750698581)
2025-06-23 22:39:48 - bank_analyzer.ml.category_manager - INFO - create_category:304 - Created category: meesho (ID: ml_meesho_1750698588)
2025-06-23 22:39:57 - bank_analyzer.ml.category_manager - INFO - create_category:304 - Created category: flipkart (ID: ml_flipkart_1750698597)
2025-06-23 22:40:15 - bank_analyzer.ml.category_manager - INFO - create_category:304 - Created category: Appliances (ID: ml_appliances_1750698615)
2025-06-23 22:40:25 - bank_analyzer.ml.category_manager - INFO - create_category:304 - Created category: House Hold (ID: ml_house_hold_1750698625)
2025-06-23 22:40:48 - bank_analyzer.ml.category_manager - INFO - create_category:304 - Created category: Vasanth Needs (ID: ml_vasanth_needs_1750698648)
2025-06-23 22:40:59 - bank_analyzer.ml.category_manager - INFO - create_category:304 - Created category: Common Needs (ID: ml_common_needs_1750698659)
2025-06-23 22:41:02 - bank_analyzer.ml.category_manager - INFO - refresh_categories:64 - Refreshing categories...
2025-06-23 22:41:02 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:116 - Loaded 60 categories from main application
2025-06-23 22:41:02 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:146 - Loaded 37 ML-specific categories
2025-06-23 22:41:02 - bank_analyzer.ml.category_manager - INFO - refresh_categories:67 - Categories refreshed successfully
2025-06-23 22:41:02 - bank_analyzer.ui.ml_labeling_window - INFO - refresh_categories:620 - Categories refreshed successfully
2025-06-23 22:41:12 - bank_analyzer.ml.data_preparation - INFO - save_unique_transactions:279 - Saved 430 unique transactions to storage
2025-06-23 22:41:12 - bank_analyzer.ml.training_data_manager - INFO - label_transaction:133 - Labeled transaction 73aef582e30e as Appliances/Vasanth Needs
2025-06-23 22:41:22 - bank_analyzer.ml.data_preparation - INFO - save_unique_transactions:279 - Saved 430 unique transactions to storage
2025-06-23 22:41:22 - bank_analyzer.ml.training_data_manager - INFO - label_transaction:133 - Labeled transaction 0a598f308e3d as House Hold Expenses/Daily expenses
2025-06-23 22:42:05 - bank_analyzer.ml.data_preparation - INFO - save_unique_transactions:279 - Saved 430 unique transactions to storage
2025-06-23 22:42:05 - bank_analyzer.ml.training_data_manager - INFO - label_transaction:133 - Labeled transaction 27c441159331 as Transportation/Maintenance
2025-06-23 22:42:50 - bank_analyzer.ml.data_preparation - INFO - save_unique_transactions:279 - Saved 430 unique transactions to storage
2025-06-23 22:42:50 - bank_analyzer.ml.training_data_manager - INFO - label_transaction:133 - Labeled transaction 9b2f43111012 as House Hold Expenses/Daily expenses
2025-06-23 22:43:41 - bank_analyzer.ml.data_preparation - INFO - save_unique_transactions:279 - Saved 430 unique transactions to storage
2025-06-23 22:43:41 - bank_analyzer.ml.training_data_manager - INFO - label_transaction:133 - Labeled transaction 3365c33ed7af as House Hold Expenses/Daily expenses
2025-06-23 22:44:27 - bank_analyzer.ml.data_preparation - INFO - save_unique_transactions:279 - Saved 430 unique transactions to storage
2025-06-23 22:44:27 - bank_analyzer.ml.training_data_manager - INFO - label_transaction:133 - Labeled transaction 7106c369471a as Online Stores/meesho
2025-06-23 22:45:06 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:116 - Loaded 60 categories from main application
2025-06-23 22:45:06 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:146 - Loaded 37 ML-specific categories
2025-06-23 22:45:18 - bank_analyzer.ml.category_manager - INFO - create_category:304 - Created category: Initial Payment (ID: ml_initial_payment_1750698918)
2025-06-23 22:45:19 - bank_analyzer.ml.category_manager - INFO - refresh_categories:64 - Refreshing categories...
2025-06-23 22:45:19 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:116 - Loaded 60 categories from main application
2025-06-23 22:45:19 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:146 - Loaded 38 ML-specific categories
2025-06-23 22:45:19 - bank_analyzer.ml.category_manager - INFO - refresh_categories:67 - Categories refreshed successfully
2025-06-23 22:45:19 - bank_analyzer.ui.ml_labeling_window - INFO - refresh_categories:620 - Categories refreshed successfully
2025-06-23 22:45:57 - bank_analyzer.ml.data_preparation - INFO - save_unique_transactions:279 - Saved 430 unique transactions to storage
2025-06-23 22:45:57 - bank_analyzer.ml.training_data_manager - INFO - label_transaction:133 - Labeled transaction 0e06c513a672 as Other/Initial Payment
2025-06-23 22:46:10 - bank_analyzer.ml.data_preparation - INFO - save_unique_transactions:279 - Saved 430 unique transactions to storage
2025-06-23 22:46:10 - bank_analyzer.ml.training_data_manager - INFO - label_transaction:133 - Labeled transaction 206237148e3d as zomato cash limit/cash limit
2025-06-23 22:46:38 - bank_analyzer.ml.data_preparation - INFO - save_unique_transactions:279 - Saved 430 unique transactions to storage
2025-06-23 22:46:38 - bank_analyzer.ml.training_data_manager - INFO - label_transaction:133 - Labeled transaction e5775e9de0b6 as House Hold Expenses/Daily expenses
2025-06-23 22:46:46 - bank_analyzer.ml.data_preparation - INFO - save_unique_transactions:279 - Saved 430 unique transactions to storage
2025-06-23 22:46:46 - bank_analyzer.ml.training_data_manager - INFO - label_transaction:133 - Labeled transaction b21e0939f424 as House Hold Expenses/Daily expenses
2025-06-23 22:46:55 - bank_analyzer.ml.data_preparation - INFO - save_unique_transactions:279 - Saved 430 unique transactions to storage
2025-06-23 22:46:55 - bank_analyzer.ml.training_data_manager - INFO - label_transaction:133 - Labeled transaction 6e3c11885ad2 as Bills & Utilities/Electricity
2025-06-23 22:47:25 - bank_analyzer.ml.data_preparation - INFO - save_unique_transactions:279 - Saved 430 unique transactions to storage
2025-06-23 22:47:25 - bank_analyzer.ml.training_data_manager - INFO - label_transaction:133 - Labeled transaction 15422bf2b6f4 as Transportation/Maintenance
2025-06-23 22:47:42 - bank_analyzer.ml.data_preparation - INFO - save_unique_transactions:279 - Saved 430 unique transactions to storage
2025-06-23 22:47:42 - bank_analyzer.ml.training_data_manager - INFO - label_transaction:133 - Labeled transaction 7d7f6f1eb1e6 as House Hold Expenses/Daily expenses
2025-06-23 22:47:56 - bank_analyzer.ml.data_preparation - INFO - save_unique_transactions:279 - Saved 430 unique transactions to storage
2025-06-23 22:47:56 - bank_analyzer.ml.training_data_manager - INFO - label_transaction:133 - Labeled transaction 08504da61c6f as House Hold Expenses/Daily expenses
2025-06-23 22:48:07 - bank_analyzer.ml.data_preparation - INFO - save_unique_transactions:279 - Saved 430 unique transactions to storage
2025-06-23 22:48:07 - bank_analyzer.ml.training_data_manager - INFO - label_transaction:133 - Labeled transaction 44bb83b7ec7c as zomato cash limit/cash limit
2025-06-23 22:48:44 - bank_analyzer.ml.data_preparation - INFO - save_unique_transactions:279 - Saved 430 unique transactions to storage
2025-06-23 22:48:44 - bank_analyzer.ml.training_data_manager - INFO - label_transaction:133 - Labeled transaction 59a391760e86 as House Hold Expenses/Daily expenses
2025-06-23 22:48:48 - bank_analyzer.ml.training_data_manager - INFO - end_labeling_session:101 - Ended labeling session: session_20250623_222725
2025-06-23 22:48:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:229 - Loaded 430 unique transactions from storage
2025-06-23 22:48:57 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:229 - Loaded 430 unique transactions from storage
2025-06-23 22:49:00 - bank_analyzer.ml.model_trainer - INFO - create_training_job:175 - Created training job: job_20250623_224900 (type: manual)
2025-06-23 22:49:00 - bank_analyzer.ml.integrated_categorizer - INFO - trigger_model_training:426 - Started model training job: job_20250623_224900
2025-06-23 22:49:00 - bank_analyzer.ml.model_trainer - INFO - _run_training_job:225 - Starting training job: job_20250623_224900
2025-06-23 22:49:01 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:229 - Loaded 430 unique transactions from storage
2025-06-23 22:49:01 - bank_analyzer.ml.ml_categorizer - WARNING - prepare_training_data:188 - Insufficient training data for categories: ['Shopping', 'Bills & Utilities', 'Healthcare', 'Online Stores', 'EMI', 'Appliances', 'Swiggy Cash Limit']
2025-06-23 22:49:01 - bank_analyzer.ml.ml_categorizer - INFO - prepare_training_data:196 - Training data: 27 samples, 5 categories
2025-06-23 22:49:02 - bank_analyzer.ml.ml_categorizer - INFO - train_models:237 - Training category classification model...
2025-06-23 22:49:02 - bank_analyzer.ml.ml_categorizer - INFO - train_models:261 - Category model trained with accuracy: 0.500
2025-06-23 22:49:02 - bank_analyzer.ml.ml_categorizer - INFO - train_models:294 - Trained subcategory model for Food & Dining
2025-06-23 22:49:02 - bank_analyzer.ml.ml_categorizer - INFO - train_models:294 - Trained subcategory model for Transportation
2025-06-23 22:49:02 - bank_analyzer.ml.ml_categorizer - INFO - train_models:294 - Trained subcategory model for Other
2025-06-23 22:49:02 - bank_analyzer.ml.ml_categorizer - INFO - _save_models:395 - ML models saved successfully
2025-06-23 22:49:02 - bank_analyzer.ml.ml_categorizer - INFO - train_models:309 - ML models training completed successfully
2025-06-23 22:49:02 - bank_analyzer.ml.ml_categorizer - WARNING - prepare_training_data:188 - Insufficient training data for categories: ['Shopping', 'Bills & Utilities', 'Healthcare', 'Online Stores', 'EMI', 'Appliances', 'Swiggy Cash Limit']
2025-06-23 22:49:02 - bank_analyzer.ml.ml_categorizer - INFO - prepare_training_data:196 - Training data: 27 samples, 5 categories
2025-06-23 22:49:02 - bank_analyzer.ml.model_trainer - INFO - _run_training_job:261 - Training job completed: job_20250623_224900 (accuracy: 0.815)
2025-06-23 22:54:22 - __main__ - INFO - main:78 - Bank Statement Analyzer application exited with code: 0
2025-06-23 23:14:15 - bank_analyzer.core.logger - INFO - setup_logging:64 - Logging system initialized
2025-06-23 23:14:15 - bank_analyzer.core.logger - INFO - setup_logging:65 - Log file: logs\bank_analyzer_20250623.log
2025-06-23 23:14:15 - bank_analyzer.core.logger - INFO - setup_logging:66 - Log level: INFO
2025-06-23 23:14:15 - __main__ - INFO - main:45 - Starting Bank Statement Analyzer application
2025-06-23 23:14:15 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-23 23:14:15 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-23 23:14:16 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:425 - ML models loaded successfully (version: 1.0)
2025-06-23 23:14:16 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:425 - ML models loaded successfully (version: 1.0)
2025-06-23 23:14:16 - bank_analyzer.ui.main_window - INFO - load_initial_data:414 - Initial data loaded successfully
2025-06-23 23:14:16 - bank_analyzer.ui.main_window - INFO - __init__:114 - Bank Analyzer main window initialized
2025-06-23 23:14:16 - __main__ - INFO - main:74 - Bank Statement Analyzer application started successfully
2025-06-23 23:14:39 - bank_analyzer.parsers.parser_factory - INFO - get_parser:83 - Selected pdf parser for C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 23:14:39 - bank_analyzer.parsers.pdf_parser - INFO - parse:53 - Parsing PDF with rewritten parser: C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 23:14:48 - bank_analyzer.parsers.pdf_parser - INFO - parse:68 - Rewritten parser extracted 316 transactions
2025-06-23 23:14:48 - bank_analyzer.parsers.parser_factory - INFO - parse_file:116 - Successfully parsed 316 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 23:14:48 - bank_analyzer.ui.main_window - INFO - run:63 - Parsed 316 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 23:14:48 - bank_analyzer.parsers.parser_factory - INFO - get_parser:83 - Selected pdf parser for C:\Users\<USER>\Desktop\New folder\statements\Indian bank\june to august.pdf
2025-06-23 23:14:48 - bank_analyzer.parsers.pdf_parser - INFO - parse:53 - Parsing PDF with rewritten parser: C:\Users\<USER>\Desktop\New folder\statements\Indian bank\june to august.pdf
2025-06-23 23:14:53 - bank_analyzer.parsers.pdf_parser - INFO - parse:68 - Rewritten parser extracted 109 transactions
2025-06-23 23:14:53 - bank_analyzer.parsers.parser_factory - INFO - parse_file:116 - Successfully parsed 109 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\june to august.pdf
2025-06-23 23:14:53 - bank_analyzer.ui.main_window - INFO - run:63 - Parsed 109 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\june to august.pdf
2025-06-23 23:14:53 - bank_analyzer.parsers.parser_factory - INFO - get_parser:83 - Selected pdf parser for C:\Users\<USER>\Desktop\New folder\statements\Indian bank\March to june.pdf
2025-06-23 23:14:53 - bank_analyzer.parsers.pdf_parser - INFO - parse:53 - Parsing PDF with rewritten parser: C:\Users\<USER>\Desktop\New folder\statements\Indian bank\March to june.pdf
2025-06-23 23:15:01 - bank_analyzer.parsers.pdf_parser - INFO - parse:68 - Rewritten parser extracted 281 transactions
2025-06-23 23:15:01 - bank_analyzer.parsers.parser_factory - INFO - parse_file:116 - Successfully parsed 281 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\March to june.pdf
2025-06-23 23:15:01 - bank_analyzer.ui.main_window - INFO - run:63 - Parsed 281 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\March to june.pdf
2025-06-23 23:15:01 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-23 23:15:01 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:425 - ML models loaded successfully (version: 1.0)
2025-06-23 23:15:01 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:425 - ML models loaded successfully (version: 1.0)
2025-06-23 23:15:02 - bank_analyzer.ml.data_preparation - INFO - extract_unique_transactions:187 - Extracted 405 unique transaction descriptions from 706 transactions
2025-06-23 23:15:03 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:229 - Loaded 430 unique transactions from storage
2025-06-23 23:15:03 - bank_analyzer.ml.data_preparation - INFO - save_unique_transactions:279 - Saved 430 unique transactions to storage
2025-06-23 23:16:42 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:116 - Loaded 60 categories from main application
2025-06-23 23:16:42 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:146 - Loaded 38 ML-specific categories
2025-06-23 23:16:43 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:229 - Loaded 430 unique transactions from storage
2025-06-23 23:16:43 - bank_analyzer.ui.ml_labeling_window - ERROR - load_transaction_batch:613 - Error loading transaction batch: 
2025-06-23 23:17:03 - __main__ - INFO - main:78 - Bank Statement Analyzer application exited with code: 0
2025-06-23 23:21:15 - bank_analyzer.core.logger - INFO - setup_logging:64 - Logging system initialized
2025-06-23 23:21:15 - bank_analyzer.core.logger - INFO - setup_logging:65 - Log file: logs\bank_analyzer_20250623.log
2025-06-23 23:21:15 - bank_analyzer.core.logger - INFO - setup_logging:66 - Log level: INFO
2025-06-23 23:21:15 - __main__ - INFO - main:45 - Starting Bank Statement Analyzer application
2025-06-23 23:21:15 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-23 23:21:15 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-23 23:21:15 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:425 - ML models loaded successfully (version: 1.0)
2025-06-23 23:21:15 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:425 - ML models loaded successfully (version: 1.0)
2025-06-23 23:21:15 - bank_analyzer.ui.main_window - INFO - load_initial_data:414 - Initial data loaded successfully
2025-06-23 23:21:15 - bank_analyzer.ui.main_window - INFO - __init__:114 - Bank Analyzer main window initialized
2025-06-23 23:21:15 - __main__ - INFO - main:74 - Bank Statement Analyzer application started successfully
2025-06-23 23:21:50 - bank_analyzer.parsers.parser_factory - INFO - get_parser:83 - Selected pdf parser for C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 23:21:50 - bank_analyzer.parsers.pdf_parser - INFO - parse:53 - Parsing PDF with rewritten parser: C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 23:22:00 - bank_analyzer.parsers.pdf_parser - INFO - parse:68 - Rewritten parser extracted 316 transactions
2025-06-23 23:22:00 - bank_analyzer.parsers.parser_factory - INFO - parse_file:116 - Successfully parsed 316 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 23:22:00 - bank_analyzer.ui.main_window - INFO - run:63 - Parsed 316 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 23:22:00 - bank_analyzer.parsers.parser_factory - INFO - get_parser:83 - Selected pdf parser for C:\Users\<USER>\Desktop\New folder\statements\Indian bank\june to august.pdf
2025-06-23 23:22:00 - bank_analyzer.parsers.pdf_parser - INFO - parse:53 - Parsing PDF with rewritten parser: C:\Users\<USER>\Desktop\New folder\statements\Indian bank\june to august.pdf
2025-06-23 23:22:04 - bank_analyzer.parsers.pdf_parser - INFO - parse:68 - Rewritten parser extracted 109 transactions
2025-06-23 23:22:04 - bank_analyzer.parsers.parser_factory - INFO - parse_file:116 - Successfully parsed 109 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\june to august.pdf
2025-06-23 23:22:04 - bank_analyzer.ui.main_window - INFO - run:63 - Parsed 109 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\june to august.pdf
2025-06-23 23:22:04 - bank_analyzer.parsers.parser_factory - INFO - get_parser:83 - Selected pdf parser for C:\Users\<USER>\Desktop\New folder\statements\Indian bank\March to june.pdf
2025-06-23 23:22:04 - bank_analyzer.parsers.pdf_parser - INFO - parse:53 - Parsing PDF with rewritten parser: C:\Users\<USER>\Desktop\New folder\statements\Indian bank\March to june.pdf
2025-06-23 23:22:13 - bank_analyzer.parsers.pdf_parser - INFO - parse:68 - Rewritten parser extracted 281 transactions
2025-06-23 23:22:13 - bank_analyzer.parsers.parser_factory - INFO - parse_file:116 - Successfully parsed 281 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\March to june.pdf
2025-06-23 23:22:13 - bank_analyzer.ui.main_window - INFO - run:63 - Parsed 281 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\March to june.pdf
2025-06-23 23:22:13 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-23 23:22:13 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:425 - ML models loaded successfully (version: 1.0)
2025-06-23 23:22:13 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:425 - ML models loaded successfully (version: 1.0)
2025-06-23 23:22:13 - bank_analyzer.ml.data_preparation - INFO - extract_unique_transactions:187 - Extracted 405 unique transaction descriptions from 706 transactions
2025-06-23 23:22:14 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:229 - Loaded 430 unique transactions from storage
2025-06-23 23:22:14 - bank_analyzer.ml.data_preparation - INFO - save_unique_transactions:279 - Saved 430 unique transactions to storage
2025-06-23 23:22:25 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:116 - Loaded 60 categories from main application
2025-06-23 23:22:25 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:146 - Loaded 38 ML-specific categories
2025-06-23 23:22:27 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:229 - Loaded 430 unique transactions from storage
2025-06-23 23:22:27 - bank_analyzer.ui.ml_labeling_window - ERROR - load_transaction_batch:618 - Error loading transaction batch: 'MLLabelingWindow' object has no attribute 'transaction_table'
2025-06-23 23:23:48 - bank_analyzer.core.logger - INFO - setup_logging:64 - Logging system initialized
2025-06-23 23:23:48 - bank_analyzer.core.logger - INFO - setup_logging:65 - Log file: logs\bank_analyzer_20250623.log
2025-06-23 23:23:48 - bank_analyzer.core.logger - INFO - setup_logging:66 - Log level: INFO
2025-06-23 23:23:48 - __main__ - INFO - main:45 - Starting Bank Statement Analyzer application
2025-06-23 23:23:48 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-23 23:23:48 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-23 23:23:48 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:425 - ML models loaded successfully (version: 1.0)
2025-06-23 23:23:48 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:425 - ML models loaded successfully (version: 1.0)
2025-06-23 23:23:48 - bank_analyzer.ui.main_window - INFO - load_initial_data:414 - Initial data loaded successfully
2025-06-23 23:23:48 - bank_analyzer.ui.main_window - INFO - __init__:114 - Bank Analyzer main window initialized
2025-06-23 23:23:48 - __main__ - INFO - main:74 - Bank Statement Analyzer application started successfully
2025-06-23 23:23:56 - bank_analyzer.parsers.parser_factory - INFO - get_parser:83 - Selected pdf parser for C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 23:23:56 - bank_analyzer.parsers.pdf_parser - INFO - parse:53 - Parsing PDF with rewritten parser: C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 23:24:06 - bank_analyzer.parsers.pdf_parser - INFO - parse:68 - Rewritten parser extracted 316 transactions
2025-06-23 23:24:06 - bank_analyzer.parsers.parser_factory - INFO - parse_file:116 - Successfully parsed 316 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 23:24:06 - bank_analyzer.ui.main_window - INFO - run:63 - Parsed 316 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 23:24:06 - bank_analyzer.parsers.parser_factory - INFO - get_parser:83 - Selected pdf parser for C:\Users\<USER>\Desktop\New folder\statements\Indian bank\june to august.pdf
2025-06-23 23:24:06 - bank_analyzer.parsers.pdf_parser - INFO - parse:53 - Parsing PDF with rewritten parser: C:\Users\<USER>\Desktop\New folder\statements\Indian bank\june to august.pdf
2025-06-23 23:24:09 - bank_analyzer.parsers.pdf_parser - INFO - parse:68 - Rewritten parser extracted 109 transactions
2025-06-23 23:24:09 - bank_analyzer.parsers.parser_factory - INFO - parse_file:116 - Successfully parsed 109 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\june to august.pdf
2025-06-23 23:24:09 - bank_analyzer.ui.main_window - INFO - run:63 - Parsed 109 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\june to august.pdf
2025-06-23 23:24:09 - bank_analyzer.parsers.parser_factory - INFO - get_parser:83 - Selected pdf parser for C:\Users\<USER>\Desktop\New folder\statements\Indian bank\March to june.pdf
2025-06-23 23:24:09 - bank_analyzer.parsers.pdf_parser - INFO - parse:53 - Parsing PDF with rewritten parser: C:\Users\<USER>\Desktop\New folder\statements\Indian bank\March to june.pdf
2025-06-23 23:24:17 - bank_analyzer.parsers.pdf_parser - INFO - parse:68 - Rewritten parser extracted 281 transactions
2025-06-23 23:24:17 - bank_analyzer.parsers.parser_factory - INFO - parse_file:116 - Successfully parsed 281 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\March to june.pdf
2025-06-23 23:24:17 - bank_analyzer.ui.main_window - INFO - run:63 - Parsed 281 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\March to june.pdf
2025-06-23 23:24:17 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-23 23:24:17 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:425 - ML models loaded successfully (version: 1.0)
2025-06-23 23:24:17 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:425 - ML models loaded successfully (version: 1.0)
2025-06-23 23:24:18 - bank_analyzer.ml.data_preparation - INFO - extract_unique_transactions:187 - Extracted 405 unique transaction descriptions from 706 transactions
2025-06-23 23:24:19 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:229 - Loaded 430 unique transactions from storage
2025-06-23 23:24:19 - bank_analyzer.ml.data_preparation - INFO - save_unique_transactions:279 - Saved 430 unique transactions to storage
2025-06-23 23:24:26 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:116 - Loaded 60 categories from main application
2025-06-23 23:24:26 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:146 - Loaded 38 ML-specific categories
2025-06-23 23:24:27 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:229 - Loaded 430 unique transactions from storage
2025-06-23 23:25:47 - bank_analyzer.ml.training_data_manager - INFO - start_labeling_session:133 - Started labeling session: session_20250623_232547
2025-06-23 23:30:32 - bank_analyzer.core.logger - INFO - setup_logging:64 - Logging system initialized
2025-06-23 23:30:32 - bank_analyzer.core.logger - INFO - setup_logging:65 - Log file: logs\bank_analyzer_20250623.log
2025-06-23 23:30:32 - bank_analyzer.core.logger - INFO - setup_logging:66 - Log level: INFO
2025-06-23 23:30:32 - __main__ - INFO - main:45 - Starting Bank Statement Analyzer application
2025-06-23 23:30:32 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-23 23:30:32 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-23 23:30:32 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:425 - ML models loaded successfully (version: 1.0)
2025-06-23 23:30:32 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:425 - ML models loaded successfully (version: 1.0)
2025-06-23 23:30:32 - bank_analyzer.ui.main_window - INFO - load_initial_data:414 - Initial data loaded successfully
2025-06-23 23:30:32 - bank_analyzer.ui.main_window - INFO - __init__:114 - Bank Analyzer main window initialized
2025-06-23 23:30:32 - __main__ - INFO - main:74 - Bank Statement Analyzer application started successfully
2025-06-23 23:30:51 - bank_analyzer.parsers.parser_factory - INFO - get_parser:83 - Selected pdf parser for C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 23:30:51 - bank_analyzer.parsers.pdf_parser - INFO - parse:53 - Parsing PDF with rewritten parser: C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 23:31:01 - bank_analyzer.parsers.pdf_parser - INFO - parse:68 - Rewritten parser extracted 316 transactions
2025-06-23 23:31:01 - bank_analyzer.parsers.parser_factory - INFO - parse_file:116 - Successfully parsed 316 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 23:31:01 - bank_analyzer.ui.main_window - INFO - run:63 - Parsed 316 transactions from C:\Users\<USER>\Desktop\New folder\statements\Indian bank\Jan to march.pdf
2025-06-23 23:31:01 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-23 23:31:01 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:425 - ML models loaded successfully (version: 1.0)
2025-06-23 23:31:01 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:425 - ML models loaded successfully (version: 1.0)
2025-06-23 23:31:02 - bank_analyzer.ml.data_preparation - INFO - extract_unique_transactions:187 - Extracted 194 unique transaction descriptions from 316 transactions
2025-06-23 23:31:03 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:229 - Loaded 430 unique transactions from storage
2025-06-23 23:31:03 - bank_analyzer.ml.data_preparation - INFO - save_unique_transactions:279 - Saved 430 unique transactions to storage
2025-06-23 23:31:07 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:116 - Loaded 60 categories from main application
2025-06-23 23:31:07 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:146 - Loaded 38 ML-specific categories
2025-06-23 23:31:08 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:229 - Loaded 430 unique transactions from storage
2025-06-23 23:51:52 - bank_analyzer.core.logger - INFO - setup_logging:64 - Logging system initialized
2025-06-23 23:51:52 - bank_analyzer.core.logger - INFO - setup_logging:65 - Log file: logs\bank_analyzer_20250623.log
2025-06-23 23:51:52 - bank_analyzer.core.logger - INFO - setup_logging:66 - Log level: INFO
2025-06-23 23:51:52 - __main__ - INFO - main:45 - Starting Bank Statement Analyzer application
2025-06-23 23:51:52 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-23 23:51:52 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-23 23:51:52 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:425 - ML models loaded successfully (version: 1.0)
2025-06-23 23:51:52 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:425 - ML models loaded successfully (version: 1.0)
2025-06-23 23:51:52 - bank_analyzer.ui.main_window - INFO - load_initial_data:414 - Initial data loaded successfully
2025-06-23 23:51:52 - bank_analyzer.ui.main_window - INFO - __init__:114 - Bank Analyzer main window initialized
2025-06-23 23:51:53 - __main__ - INFO - main:74 - Bank Statement Analyzer application started successfully
2025-06-23 23:51:59 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:116 - Loaded 60 categories from main application
2025-06-23 23:51:59 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:146 - Loaded 38 ML-specific categories
2025-06-23 23:52:00 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:370 - Loaded 430 unique transactions from storage
2025-06-23 23:55:48 - __main__ - INFO - main:78 - Bank Statement Analyzer application exited with code: 0
2025-06-23 23:58:23 - bank_analyzer.core.logger - INFO - setup_logging:64 - Logging system initialized
2025-06-23 23:58:23 - bank_analyzer.core.logger - INFO - setup_logging:65 - Log file: logs\bank_analyzer_20250623.log
2025-06-23 23:58:23 - bank_analyzer.core.logger - INFO - setup_logging:66 - Log level: INFO
2025-06-23 23:58:23 - __main__ - INFO - main:45 - Starting Bank Statement Analyzer application
2025-06-23 23:58:24 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-23 23:58:24 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-23 23:58:24 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:425 - ML models loaded successfully (version: 1.0)
2025-06-23 23:58:24 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:425 - ML models loaded successfully (version: 1.0)
2025-06-23 23:58:24 - bank_analyzer.ui.main_window - INFO - load_initial_data:414 - Initial data loaded successfully
2025-06-23 23:58:24 - bank_analyzer.ui.main_window - INFO - __init__:114 - Bank Analyzer main window initialized
2025-06-23 23:58:24 - __main__ - INFO - main:74 - Bank Statement Analyzer application started successfully
2025-06-23 23:58:29 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:116 - Loaded 60 categories from main application
2025-06-23 23:58:29 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:146 - Loaded 38 ML-specific categories
2025-06-23 23:58:31 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:370 - Loaded 430 unique transactions from storage
2025-06-23 23:59:24 - bank_analyzer.ml.data_preparation - INFO - save_unique_transactions:423 - Saved 430 unique transactions to storage
2025-06-23 23:59:24 - bank_analyzer.ml.training_data_manager - INFO - label_transaction:187 - Labeled transaction 750168a3562a as zomato cash limit/cash limit
2025-06-23 23:59:47 - bank_analyzer.ml.data_preparation - INFO - save_unique_transactions:423 - Saved 430 unique transactions to storage
2025-06-23 23:59:47 - bank_analyzer.ml.training_data_manager - INFO - label_transaction:187 - Labeled transaction be33166053fe as zomato cash limit/cash limit
2025-06-24 00:00:02 - bank_analyzer.ml.data_preparation - INFO - save_unique_transactions:423 - Saved 430 unique transactions to storage
2025-06-24 00:00:02 - bank_analyzer.ml.training_data_manager - INFO - label_transaction:187 - Labeled transaction 0b61d9aabeae as zomato cash limit/cash limit
