2025-06-24 00:11:59 - bank_analyzer.core.logger - INFO - setup_logging:64 - Logging system initialized
2025-06-24 00:11:59 - bank_analyzer.core.logger - INFO - setup_logging:65 - Log file: logs\bank_analyzer_20250624.log
2025-06-24 00:11:59 - bank_analyzer.core.logger - INFO - setup_logging:66 - Log level: INFO
2025-06-24 00:11:59 - __main__ - INFO - main:45 - Starting Bank Statement Analyzer application
2025-06-24 00:11:59 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-24 00:11:59 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-24 00:12:00 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:425 - ML models loaded successfully (version: 1.0)
2025-06-24 00:12:00 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:425 - ML models loaded successfully (version: 1.0)
2025-06-24 00:12:00 - bank_analyzer.ui.main_window - INFO - load_initial_data:414 - Initial data loaded successfully
2025-06-24 00:12:00 - bank_analyzer.ui.main_window - INFO - __init__:114 - Bank Analyzer main window initialized
2025-06-24 00:12:00 - __main__ - INFO - main:74 - Bank Statement Analyzer application started successfully
2025-06-24 00:12:04 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:116 - Loaded 60 categories from main application
2025-06-24 00:12:04 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:146 - Loaded 38 ML-specific categories
2025-06-24 00:12:05 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:370 - Loaded 430 unique transactions from storage
2025-06-24 03:45:08 - bank_analyzer.core.logger - INFO - setup_logging:64 - Logging system initialized
2025-06-24 03:45:08 - bank_analyzer.core.logger - INFO - setup_logging:65 - Log file: logs\bank_analyzer_20250624.log
2025-06-24 03:45:08 - bank_analyzer.core.logger - INFO - setup_logging:66 - Log level: INFO
2025-06-24 03:45:08 - __main__ - INFO - main:45 - Starting Bank Statement Analyzer application
2025-06-24 03:45:08 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-24 03:45:08 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-24 03:45:13 - bank_analyzer.ml.ml_categorizer - ERROR - _load_models:429 - Error loading models: No module named 'numpy._core'
2025-06-24 03:45:13 - bank_analyzer.ml.ml_categorizer - ERROR - _load_models:429 - Error loading models: No module named 'numpy._core'
2025-06-24 03:45:13 - bank_analyzer.ui.main_window - INFO - load_initial_data:414 - Initial data loaded successfully
2025-06-24 03:45:13 - bank_analyzer.ui.main_window - INFO - __init__:114 - Bank Analyzer main window initialized
2025-06-24 03:45:13 - __main__ - INFO - main:74 - Bank Statement Analyzer application started successfully
2025-06-24 03:45:48 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:116 - Loaded 60 categories from main application
2025-06-24 03:45:48 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:146 - Loaded 38 ML-specific categories
2025-06-24 03:45:49 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:370 - Loaded 430 unique transactions from storage
2025-06-24 03:47:02 - bank_analyzer.ml.data_preparation - INFO - save_unique_transactions:423 - Saved 430 unique transactions to storage
2025-06-24 03:47:02 - bank_analyzer.ml.training_data_manager - INFO - label_transaction:187 - Labeled transaction 4a0b64fd11df as House Hold Expenses/Daily expenses
2025-06-24 03:47:28 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:116 - Loaded 60 categories from main application
2025-06-24 03:47:28 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:146 - Loaded 38 ML-specific categories
2025-06-24 03:47:29 - bank_analyzer.ml.category_manager - INFO - refresh_categories:64 - Refreshing categories...
2025-06-24 03:47:29 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:116 - Loaded 60 categories from main application
2025-06-24 03:47:29 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:146 - Loaded 38 ML-specific categories
2025-06-24 03:47:29 - bank_analyzer.ml.category_manager - INFO - refresh_categories:67 - Categories refreshed successfully
2025-06-24 03:47:29 - bank_analyzer.ui.ml_labeling_window - INFO - refresh_categories:1099 - Categories refreshed successfully
2025-06-24 03:47:38 - bank_analyzer.ml.data_preparation - INFO - save_unique_transactions:423 - Saved 430 unique transactions to storage
2025-06-24 03:47:38 - bank_analyzer.ml.training_data_manager - INFO - label_transaction:187 - Labeled transaction 4a0b64fd11df as House Hold Expenses/Daily expenses
2025-06-24 03:48:03 - bank_analyzer.ml.data_preparation - INFO - save_unique_transactions:423 - Saved 430 unique transactions to storage
2025-06-24 03:48:03 - bank_analyzer.ml.training_data_manager - INFO - label_transaction:187 - Labeled transaction 57de3d547a55 as House Hold Expenses/Daily expenses
2025-06-24 03:48:13 - bank_analyzer.ml.data_preparation - INFO - save_unique_transactions:423 - Saved 430 unique transactions to storage
2025-06-24 03:48:13 - bank_analyzer.ml.training_data_manager - INFO - label_transaction:187 - Labeled transaction 80b30f093b28 as House Hold Expenses/Daily expenses
2025-06-24 03:49:18 - bank_analyzer.ml.data_preparation - INFO - save_unique_transactions:423 - Saved 430 unique transactions to storage
2025-06-24 03:49:18 - bank_analyzer.ml.training_data_manager - INFO - label_transaction:187 - Labeled transaction f7655ac2a637 as House Hold Expenses/Daily expenses
2025-06-24 03:49:28 - bank_analyzer.ml.data_preparation - INFO - save_unique_transactions:423 - Saved 430 unique transactions to storage
2025-06-24 03:49:28 - bank_analyzer.ml.training_data_manager - INFO - label_transaction:187 - Labeled transaction f7655ac2a637 as House Hold Expenses/Daily expenses
2025-06-24 03:49:36 - bank_analyzer.ui.ml_labeling_window - ERROR - open_category_editor:1047 - Error opening category editor: No module named 'bank_analyzer.utils'
2025-06-24 03:49:46 - __main__ - INFO - main:78 - Bank Statement Analyzer application exited with code: 0
