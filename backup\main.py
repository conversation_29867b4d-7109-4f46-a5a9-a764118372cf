#!/usr/bin/env python3
"""
Personal Finance Dashboard - Desktop Application
A comprehensive PySide6-based personal finance management application
"""

import sys
import os
import traceback
import logging
from pathlib import Path
from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import Qt, QDir
from PySide6.QtGui import QIcon, QFont

# Set up comprehensive logging
logs_dir = Path(__file__).parent / "logs"
logs_dir.mkdir(exist_ok=True)

logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
    handlers=[
        logging.FileHandler(logs_dir / 'app_debug.log', mode='w', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

# Set specific logger levels
logging.getLogger('PySide6').setLevel(logging.WARNING)
logging.getLogger('matplotlib').setLevel(logging.WARNING)

logger = logging.getLogger(__name__)
logger.info("="*80)
logger.info("PERSONAL FINANCE DASHBOARD - DETAILED LOGGING SESSION")
logger.info("="*80)

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.ui.main_window import MainWindow
from src.core.config import AppConfig
from src.core.data_manager import DataManager


def setup_application():
    """Setup the QApplication with proper configuration"""
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName("Personal Finance Dashboard")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("Personal Finance")
    app.setOrganizationDomain("personalfinance.local")
    
    # Set application icon
    icon_path = Path(__file__).parent / "assets" / "icons" / "app_icon.png"
    if icon_path.exists():
        app.setWindowIcon(QIcon(str(icon_path)))
    
    # Set default font
    font = QFont("Segoe UI", 9)
    app.setFont(font)
    
    # Enable high DPI scaling (Qt 6 handles this automatically)
    # These attributes are deprecated in Qt 6
    # app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    # app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    return app


def main():
    """Main application entry point"""
    try:
        logger.info("Starting Personal Finance Dashboard...")

        # Create application
        logger.info("Setting up QApplication...")
        app = setup_application()

        # Initialize configuration
        logger.info("Initializing configuration...")
        config = AppConfig()
        logger.info(f"Config loaded: data_directory={config.data_directory}")

        # Initialize data manager
        logger.info("Initializing data manager...")
        data_manager = DataManager(config.data_directory)
        logger.info("Data manager initialized successfully")

        # Create and show main window
        logger.info("Creating main window...")
        main_window = MainWindow(data_manager, config)
        logger.info("Main window created successfully")

        main_window.show()
        logger.info("Main window shown, starting event loop...")

        # Start event loop
        sys.exit(app.exec())

    except Exception as e:
        error_msg = f"Error starting application: {e}"
        logger.error(error_msg)
        logger.error(f"Traceback: {traceback.format_exc()}")

        # Show error dialog if possible
        try:
            app = QApplication.instance()
            if app is None:
                app = QApplication(sys.argv)

            QMessageBox.critical(None, "Application Error",
                               f"Failed to start application:\n\n{e}\n\nCheck app_debug.log for details.")
        except:
            pass

        print(error_msg)
        print(traceback.format_exc())
        sys.exit(1)


if __name__ == "__main__":
    main()
