#!/usr/bin/env python3
"""
Google Tasks Authentication Setup Helper
This script helps you set up Google Tasks authentication for Traqify
"""

import os
import sys
import webbrowser
from pathlib import Path

def print_header():
    print("=" * 60)
    print("🔧 TRAQIFY - Google Tasks Authentication Setup")
    print("=" * 60)
    print()

def print_issue_explanation():
    print("📋 CURRENT ISSUE:")
    print("Your Google OAuth app 'traqify' hasn't completed Google's verification process.")
    print("This causes the 'Access blocked' error you're seeing.")
    print()

def print_solutions():
    print("🔧 SOLUTIONS (Choose one):")
    print()
    
    print("1️⃣  ADD TEST USER (QUICKEST - Recommended for development)")
    print("   • Go to Google Cloud Console")
    print("   • Add <EMAIL> as a test user")
    print("   • This allows immediate access for your account")
    print()
    
    print("2️⃣  PUBLISH APP (For production use)")
    print("   • Submit app for Google verification")
    print("   • Takes several days/weeks")
    print("   • Required for public use")
    print()
    
    print("3️⃣  USE INTERNAL APP (Organization only)")
    print("   • Change User Type to 'Internal'")
    print("   • Only works with Google Workspace accounts")
    print()

def open_google_console():
    """Open Google Cloud Console in browser"""
    project_id = "wired-aspect-463822-h2"
    oauth_url = f"https://console.cloud.google.com/apis/credentials/consent?project={project_id}"
    
    print("🌐 Opening Google Cloud Console...")
    print(f"Project: {project_id}")
    print(f"URL: {oauth_url}")
    print()
    
    try:
        webbrowser.open(oauth_url)
        return True
    except Exception as e:
        print(f"❌ Failed to open browser: {e}")
        print(f"Please manually open: {oauth_url}")
        return False

def show_step_by_step_guide():
    print("📝 STEP-BY-STEP GUIDE:")
    print()
    print("1. Open Google Cloud Console (option below)")
    print("2. Navigate to 'OAuth consent screen'")
    print("3. Scroll down to 'Test users' section")
    print("4. Click '+ ADD USERS'")
    print("5. Add email: <EMAIL>")
    print("6. Click 'SAVE'")
    print("7. Return to Traqify and try authentication again")
    print()

def check_credentials_file():
    """Check if credentials file exists"""
    creds_file = Path("data/temp_credentials.json")
    if creds_file.exists():
        print("✅ Google API credentials file found")
        return True
    else:
        print("❌ Google API credentials file not found")
        return False

def main():
    print_header()
    print_issue_explanation()
    print_solutions()
    print()
    
    # Check credentials
    check_credentials_file()
    print()
    
    show_step_by_step_guide()
    
    # Ask user what they want to do
    while True:
        print("What would you like to do?")
        print("1. Open Google Cloud Console")
        print("2. Show detailed instructions")
        print("3. Exit")
        print()
        
        choice = input("Enter your choice (1-3): ").strip()
        
        if choice == "1":
            if open_google_console():
                print("✅ Browser opened successfully!")
                print("Follow the steps above to add yourself as a test user.")
            break
        elif choice == "2":
            print("\n" + "="*60)
            print("DETAILED INSTRUCTIONS:")
            print("="*60)
            print()
            print("OPTION 1: Add Test User (Recommended)")
            print("1. Go to: https://console.cloud.google.com/")
            print("2. Select project: wired-aspect-463822-h2")
            print("3. Go to: APIs & Services → OAuth consent screen")
            print("4. Scroll to 'Test users' section")
            print("5. Click '+ ADD USERS'")
            print("6. Enter: <EMAIL>")
            print("7. Click 'SAVE'")
            print("8. Try authentication in Traqify again")
            print()
            print("OPTION 2: Alternative - Create New Project")
            print("1. Create a new Google Cloud project")
            print("2. Enable Google Tasks API")
            print("3. Create new OAuth 2.0 credentials")
            print("4. Update credentials in the code")
            print()
            break
        elif choice == "3":
            print("👋 Goodbye! Remember to add yourself as a test user.")
            break
        else:
            print("❌ Invalid choice. Please enter 1, 2, or 3.")
            print()

if __name__ == "__main__":
    main()
