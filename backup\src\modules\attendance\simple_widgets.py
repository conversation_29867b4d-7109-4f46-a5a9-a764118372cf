#!/usr/bin/env python3
"""
Simplified Attendance Widget - No Pandas Dependencies
"""

import logging
from datetime import datetime, date
from typing import Optional

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QCalendarWidget, QGroupBox,
    QCheckBox, QTextEdit, QMessageBox, QFrame,
    QScrollArea, QButtonGroup, QTabWidget, QTableWidget,
    QTableWidgetItem, QHeaderView, QAbstractItemView
)
from PySide6.QtCore import Qt, Signal, QDate
from PySide6.QtGui import QFont, QColor

from .simple_models import SimpleAttendanceDataManager, SimpleAttendanceRecord


class SimpleAttendanceTrackerWidget(QWidget):
    """Simplified attendance tracker widget"""
    
    # Signals
    data_changed = Signal()
    
    def __init__(self, data_manager, config, parent=None):
        super().__init__(parent)

        self.data_manager = data_manager  # Store the data_manager reference
        self.config = config
        self.logger = logging.getLogger(__name__)

        # Initialize simple data manager
        self.attendance_manager = SimpleAttendanceDataManager(str(data_manager.data_dir))
        
        # Current state
        self.current_date = date.today()
        self.current_record = None
        
        self.setup_ui()
        self.load_current_date()
        
        self.logger.info("✅ SimpleAttendanceTrackerWidget initialized successfully")
    
    def setup_ui(self):
        """Setup the user interface"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)  # Reduced from 20px to 10px
        layout.setSpacing(10)  # Reduced from 20px to 10px
        
        # Title
        title = QLabel("Attendance Tracker")
        title.setObjectName("pageTitle")
        font = QFont()
        font.setPointSize(18)
        font.setBold(True)
        title.setFont(font)
        layout.addWidget(title)
        
        # Tab widget for different views
        self.tab_widget = QTabWidget()

        # Today's attendance tab
        today_tab = QWidget()
        today_layout = QVBoxLayout(today_tab)

        # Main content in scroll area
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)

        # Date selection section
        self.setup_date_section(content_layout)

        # Attendance marking section
        self.setup_attendance_section(content_layout)

        # Summary section
        self.setup_summary_section(content_layout)

        # Action buttons
        self.setup_action_buttons(content_layout)

        scroll_area.setWidget(content_widget)
        today_layout.addWidget(scroll_area)

        # All records tab
        records_tab = QWidget()
        self.setup_records_tab(records_tab)

        # Semester management tab
        semester_tab = QWidget()
        self.setup_semester_tab(semester_tab)

        # Add tabs
        self.tab_widget.addTab(today_tab, "Mark Attendance")
        self.tab_widget.addTab(records_tab, "All Records")
        self.tab_widget.addTab(semester_tab, "Semester Management")

        layout.addWidget(self.tab_widget)
    
    def setup_date_section(self, layout):
        """Setup date selection section"""
        date_group = QGroupBox("Select Date")
        date_layout = QHBoxLayout(date_group)
        
        # Calendar widget
        self.calendar = QCalendarWidget()
        self.calendar.setSelectedDate(QDate(self.current_date))
        self.calendar.clicked.connect(self.on_date_selected)
        # Set calendar to show current month and limit to reasonable date range
        self.calendar.setMinimumDate(QDate(2020, 1, 1))
        self.calendar.setMaximumDate(QDate(2030, 12, 31))
        date_layout.addWidget(self.calendar)
        
        # Current date info
        info_layout = QVBoxLayout()
        
        self.date_label = QLabel(f"Selected: {self.current_date.strftime('%A, %B %d, %Y')}")
        self.date_label.setStyleSheet("font-size: 14px; font-weight: bold;")
        info_layout.addWidget(self.date_label)
        
        self.status_label = QLabel("Status: Not marked")
        self.status_label.setStyleSheet("font-size: 12px; color: #666;")
        info_layout.addWidget(self.status_label)
        
        info_layout.addStretch()
        date_layout.addLayout(info_layout)
        
        layout.addWidget(date_group)
    
    def setup_attendance_section(self, layout):
        """Setup attendance marking section"""
        attendance_group = QGroupBox("Mark Attendance")
        attendance_layout = QVBoxLayout(attendance_group)
        
        # Quick actions
        quick_layout = QHBoxLayout()
        
        self.present_all_btn = QPushButton("Mark All Present")
        self.present_all_btn.clicked.connect(self.mark_all_present)
        self.present_all_btn.setStyleSheet("background-color: #4CAF50; color: white; padding: 8px;")
        quick_layout.addWidget(self.present_all_btn)
        
        self.absent_all_btn = QPushButton("Mark All Absent")
        self.absent_all_btn.clicked.connect(self.mark_all_absent)
        self.absent_all_btn.setStyleSheet("background-color: #f44336; color: white; padding: 8px;")
        quick_layout.addWidget(self.absent_all_btn)
        
        self.holiday_btn = QPushButton("Mark as Holiday")
        self.holiday_btn.clicked.connect(self.mark_as_holiday)
        self.holiday_btn.setStyleSheet("background-color: #FF9800; color: white; padding: 8px;")
        quick_layout.addWidget(self.holiday_btn)
        
        attendance_layout.addLayout(quick_layout)
        
        # Period-wise attendance
        periods_layout = QGridLayout()
        self.period_checkboxes = {}
        
        for i in range(1, 9):
            checkbox = QCheckBox(f"Period {i}")
            checkbox.stateChanged.connect(self.on_period_changed)
            periods_layout.addWidget(checkbox, (i-1) // 4, (i-1) % 4)
            self.period_checkboxes[i] = checkbox
        
        attendance_layout.addLayout(periods_layout)
        
        # Notes
        notes_label = QLabel("Notes:")
        attendance_layout.addWidget(notes_label)
        
        self.notes_text = QTextEdit()
        self.notes_text.setMaximumHeight(80)
        self.notes_text.setPlaceholderText("Add any notes about today's attendance...")
        attendance_layout.addWidget(self.notes_text)
        
        layout.addWidget(attendance_group)
    
    def setup_summary_section(self, layout):
        """Setup summary section"""
        summary_group = QGroupBox("Attendance Summary")
        summary_layout = QGridLayout(summary_group)
        
        # Summary labels
        self.total_days_label = QLabel("Total Days: 0")
        self.present_days_label = QLabel("Present Days: 0")
        self.absent_days_label = QLabel("Absent Days: 0")
        self.percentage_label = QLabel("Percentage: 0%")
        
        summary_layout.addWidget(self.total_days_label, 0, 0)
        summary_layout.addWidget(self.present_days_label, 0, 1)
        summary_layout.addWidget(self.absent_days_label, 1, 0)
        summary_layout.addWidget(self.percentage_label, 1, 1)
        
        layout.addWidget(summary_group)
    
    def setup_action_buttons(self, layout):
        """Setup action buttons"""
        button_layout = QHBoxLayout()
        
        self.save_btn = QPushButton("Save Attendance")
        self.save_btn.clicked.connect(self.save_attendance)
        self.save_btn.setStyleSheet("background-color: #2196F3; color: white; padding: 10px; font-weight: bold;")
        button_layout.addWidget(self.save_btn)
        
        self.refresh_btn = QPushButton("Refresh")
        self.refresh_btn.clicked.connect(self.refresh_data)
        self.refresh_btn.setStyleSheet("background-color: #607D8B; color: white; padding: 10px;")
        button_layout.addWidget(self.refresh_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
    
    def on_date_selected(self, selected_date):
        """Handle date selection"""
        self.current_date = selected_date.toPython()
        self.load_current_date()
    
    def load_current_date(self):
        """Load attendance for current date"""
        try:
            # Update date label
            self.date_label.setText(f"Selected: {self.current_date.strftime('%A, %B %d, %Y')}")
            
            # Load existing record
            date_str = self.current_date.strftime('%Y-%m-%d')
            self.current_record = self.attendance_manager.get_record_by_date(date_str)
            
            if self.current_record:
                self.load_record_to_ui(self.current_record)
                self.status_label.setText("Status: Previously marked")
            else:
                self.clear_ui()
                self.status_label.setText("Status: Not marked")
            
            self.refresh_summary()
            
        except Exception as e:
            self.logger.error(f"Error loading date: {e}")
            QMessageBox.warning(self, "Error", f"Failed to load attendance data: {str(e)}")
    
    def load_record_to_ui(self, record: SimpleAttendanceRecord):
        """Load record data to UI"""
        try:
            if not record:
                self.logger.warning("Attempted to load None record to UI")
                return

            # Load period checkboxes with validation
            for i in range(1, 9):
                try:
                    period_status = getattr(record, f'period_{i}', "Absent")
                    if i in self.period_checkboxes:
                        self.period_checkboxes[i].setChecked(period_status == "Present")
                except Exception as e:
                    self.logger.warning(f"Error loading period {i}: {e}")
                    # Set to default state if error
                    if i in self.period_checkboxes:
                        self.period_checkboxes[i].setChecked(False)

            # Load notes with validation
            try:
                notes = getattr(record, 'notes', '')
                self.notes_text.setPlainText(str(notes) if notes else '')
            except Exception as e:
                self.logger.warning(f"Error loading notes: {e}")
                self.notes_text.clear()

        except Exception as e:
            self.logger.error(f"Error loading record to UI: {e}")
            # Clear UI on error
            self.clear_ui()
    
    def clear_ui(self):
        """Clear UI for new record"""
        for checkbox in self.period_checkboxes.values():
            checkbox.setChecked(False)
        self.notes_text.clear()
    
    def on_period_changed(self):
        """Handle period checkbox changes"""
        # Auto-calculate and update UI
        present_count = sum(1 for cb in self.period_checkboxes.values() if cb.isChecked())
        percentage = (present_count / 8) * 100
        self.status_label.setText(f"Status: {present_count}/8 periods present ({percentage:.1f}%)")
    
    def mark_all_present(self):
        """Mark all periods as present"""
        for checkbox in self.period_checkboxes.values():
            checkbox.setChecked(True)
    
    def mark_all_absent(self):
        """Mark all periods as absent"""
        for checkbox in self.period_checkboxes.values():
            checkbox.setChecked(False)
    
    def mark_as_holiday(self):
        """Mark day as holiday"""
        for checkbox in self.period_checkboxes.values():
            checkbox.setChecked(False)
        self.notes_text.setPlainText("Holiday")
    
    def save_attendance(self):
        """Save current attendance"""
        try:
            # Validate that at least something is marked or it's a holiday
            has_attendance = any(cb.isChecked() for cb in self.period_checkboxes.values())
            notes = self.notes_text.toPlainText().strip()
            is_holiday = "holiday" in notes.lower()

            if not has_attendance and not is_holiday and not notes:
                reply = QMessageBox.question(
                    self, "Confirm Save",
                    "No attendance marked and no notes added. Save as all absent?",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )
                if reply == QMessageBox.No:
                    return

            # Create record
            date_str = self.current_date.strftime('%Y-%m-%d')
            day_name = self.current_date.strftime('%A')

            # Determine semester and academic year based on date
            semester_number = 1
            academic_year = f"{self.current_date.year}-{self.current_date.year + 1}"

            try:
                from .semester_models import SemesterManager
                semester_manager = SemesterManager(str(self.data_manager.data_dir))

                # Find semester for this date
                semester_info = semester_manager.get_semester_for_date(self.current_date)
                if semester_info:
                    semester_number = semester_info.semester_number
                    academic_year = semester_info.academic_year
                else:
                    # Use active semester if no specific semester found
                    active_semester = semester_manager.get_active_semester()
                    if active_semester:
                        semester_number = active_semester.semester_number
                        academic_year = active_semester.academic_year

            except Exception as e:
                self.logger.warning(f"Error determining semester: {e}")

            record = SimpleAttendanceRecord(
                date=date_str,
                day=day_name,
                semester=semester_number,
                academic_year=academic_year,
                notes=notes
            )

            # Set period statuses
            for i in range(1, 9):
                status = "Present" if self.period_checkboxes[i].isChecked() else "Absent"
                setattr(record, f'period_{i}', status)

            # Check if it's a holiday
            if is_holiday:
                record.is_holiday = True
                for i in range(1, 9):
                    setattr(record, f'period_{i}', "Holiday")

            # Save record
            if self.attendance_manager.save_record(record):
                QMessageBox.information(self, "Success",
                    f"Attendance saved successfully!\n"
                    f"Date: {self.current_date.strftime('%A, %B %d, %Y')}\n"
                    f"Periods Present: {record.present_periods}/{record.total_periods}\n"
                    f"Percentage: {record.percentage:.1f}%")
                self.current_record = record
                self.refresh_summary()
                self.data_changed.emit()
            else:
                QMessageBox.warning(self, "Error", "Failed to save attendance!")

        except Exception as e:
            self.logger.error(f"Error saving attendance: {e}")
            QMessageBox.critical(self, "Error", f"Failed to save attendance: {str(e)}")
    
    def refresh_data(self):
        """Refresh all data"""
        self.load_current_date()
    
    def refresh_summary(self):
        """Refresh summary statistics (semester-aware)"""
        try:
            # Get overall summary
            summary = self.attendance_manager.get_summary()

            # Validate summary data before displaying
            if not isinstance(summary, dict):
                raise ValueError("Summary is not a dictionary")

            required_fields = ['total_days', 'present_days', 'absent_days', 'overall_percentage']
            for field in required_fields:
                if field not in summary:
                    raise KeyError(f"Missing required field: {field}")

            # Try to get semester-specific summary
            try:
                from .semester_models import SemesterManager
                semester_manager = SemesterManager(str(self.data_manager.data_dir))
                active_semester = semester_manager.get_active_semester()

                if active_semester:
                    semester_summary = semester_manager.get_semester_summary(active_semester.semester_number)
                    if semester_summary:
                        # Use semester-specific data
                        self.total_days_label.setText(f"Total Days (Sem {active_semester.semester_number}): {semester_summary['total_days']}")
                        self.present_days_label.setText(f"Present Days: {semester_summary['present_days']}")
                        self.absent_days_label.setText(f"Absent Days: {semester_summary['absent_days']}")

                        # Format percentage safely
                        try:
                            percentage = float(semester_summary['attendance_percentage'])
                            self.percentage_label.setText(f"Attendance: {percentage:.1f}%")
                        except (ValueError, TypeError):
                            self.percentage_label.setText("Attendance: 0.0%")

                        return  # Exit early if semester summary worked

            except Exception as e:
                self.logger.warning(f"Error getting semester summary: {e}")

            # Fallback to overall summary
            self.total_days_label.setText(f"Total Days: {summary['total_days']}")
            self.present_days_label.setText(f"Present Days: {summary['present_days']}")
            self.absent_days_label.setText(f"Absent Days: {summary['absent_days']}")

            # Format percentage safely
            try:
                percentage = float(summary['overall_percentage'])
                self.percentage_label.setText(f"Percentage: {percentage:.1f}%")
            except (ValueError, TypeError):
                self.percentage_label.setText("Percentage: 0.0%")
                self.logger.warning("Invalid percentage value in summary")

        except Exception as e:
            self.logger.error(f"Error refreshing summary: {e}")
            # Set default values on error
            self.total_days_label.setText("Total Days: 0")
            self.present_days_label.setText("Present Days: 0")
            self.absent_days_label.setText("Absent Days: 0")
            self.percentage_label.setText("Percentage: 0.0%")

    def setup_records_tab(self, tab_widget):
        """Setup the records viewing and editing tab"""
        layout = QVBoxLayout(tab_widget)
        layout.setContentsMargins(10, 10, 10, 10)  # Reduced from 20px to 10px
        layout.setSpacing(8)  # Reduced from 15px to 8px

        # Header with action buttons
        header_layout = QHBoxLayout()

        # Title
        records_title = QLabel("Attendance Records")
        records_title.setObjectName("pageTitle")
        font = QFont()
        font.setBold(True)
        font.setPointSize(16)
        records_title.setFont(font)
        header_layout.addWidget(records_title)

        header_layout.addStretch()

        # Action buttons
        self.edit_record_button = QPushButton("Edit Selected")
        self.edit_record_button.setObjectName("attendanceEditRecordButton")
        self.edit_record_button.setMinimumHeight(35)
        self.edit_record_button.setEnabled(False)
        self.edit_record_button.clicked.connect(self.edit_selected_record)
        header_layout.addWidget(self.edit_record_button)

        self.delete_record_button = QPushButton("Delete Selected")
        self.delete_record_button.setObjectName("attendanceDeleteRecordButton")
        self.delete_record_button.setMinimumHeight(35)
        self.delete_record_button.setEnabled(False)
        self.delete_record_button.clicked.connect(self.delete_selected_record)
        header_layout.addWidget(self.delete_record_button)

        self.refresh_records_button = QPushButton("Refresh")
        self.refresh_records_button.setObjectName("attendanceRefreshRecordsButton")
        self.refresh_records_button.setMinimumHeight(35)
        self.refresh_records_button.clicked.connect(self.load_all_records)
        header_layout.addWidget(self.refresh_records_button)

        layout.addLayout(header_layout)

        # Records table
        self.records_table = QTableWidget()
        self.records_table.setObjectName("attendanceRecordsTable")
        self.records_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.records_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.records_table.setAlternatingRowColors(True)
        self.records_table.setSortingEnabled(True)

        # Set up table columns
        columns = ["Date", "Day", "Semester", "P1", "P2", "P3", "P4", "P5", "P6", "P7", "P8", "Present", "Total", "Percentage", "Holiday", "Notes"]
        self.records_table.setColumnCount(len(columns))
        self.records_table.setHorizontalHeaderLabels(columns)

        # Set column widths
        header = self.records_table.horizontalHeader()
        header.setStretchLastSection(True)  # Notes column stretches
        header.resizeSection(0, 100)  # Date
        header.resizeSection(1, 80)   # Day
        header.resizeSection(2, 80)   # Semester
        # Period columns (P1-P8)
        for i in range(3, 11):
            header.resizeSection(i, 40)
        header.resizeSection(11, 60)  # Present
        header.resizeSection(12, 50)  # Total
        header.resizeSection(13, 80)  # Percentage
        header.resizeSection(14, 60)  # Holiday

        # Connect selection change
        self.records_table.selectionModel().selectionChanged.connect(self.on_record_selection_changed)
        self.records_table.doubleClicked.connect(self.edit_selected_record)

        layout.addWidget(self.records_table)

        # Load records initially
        self.load_all_records()

    def load_all_records(self):
        """Load all attendance records into the table"""
        try:
            records = self.attendance_manager.get_all_records()

            if not records:
                self.records_table.setRowCount(0)
                return

            # Sort records by date (most recent first)
            records.sort(key=lambda r: r.date, reverse=True)

            self.records_table.setRowCount(len(records))

            for row, record in enumerate(records):
                # Date
                self.records_table.setItem(row, 0, QTableWidgetItem(record.date))

                # Day
                self.records_table.setItem(row, 1, QTableWidgetItem(record.day))

                # Semester
                self.records_table.setItem(row, 2, QTableWidgetItem(str(record.semester)))

                # Period columns (P1-P8)
                for i in range(1, 9):
                    period_status = getattr(record, f'period_{i}', 'Absent')
                    item = QTableWidgetItem(period_status)

                    # Color code the periods
                    if period_status == "Present":
                        item.setBackground(QColor("#e8f5e8"))
                    elif period_status == "Holiday":
                        item.setBackground(QColor("#fff3e0"))
                    else:  # Absent
                        item.setBackground(QColor("#ffebee"))

                    self.records_table.setItem(row, 2 + i, item)

                # Present count
                self.records_table.setItem(row, 11, QTableWidgetItem(str(record.present_periods)))

                # Total periods
                self.records_table.setItem(row, 12, QTableWidgetItem(str(record.total_periods)))

                # Percentage
                percentage_item = QTableWidgetItem(f"{record.percentage:.1f}%")
                if record.percentage >= 75:
                    percentage_item.setBackground(QColor("#e8f5e8"))
                elif record.percentage >= 50:
                    percentage_item.setBackground(QColor("#fff3e0"))
                else:
                    percentage_item.setBackground(QColor("#ffebee"))
                self.records_table.setItem(row, 13, percentage_item)

                # Holiday
                holiday_item = QTableWidgetItem("Yes" if record.is_holiday else "No")
                if record.is_holiday:
                    holiday_item.setBackground(QColor("#fff3e0"))
                self.records_table.setItem(row, 14, holiday_item)

                # Notes
                notes = record.notes if record.notes else ""
                self.records_table.setItem(row, 15, QTableWidgetItem(notes))

        except Exception as e:
            self.logger.error(f"Error loading records: {e}")
            QMessageBox.warning(self, "Error", f"Failed to load records: {str(e)}")

    def on_record_selection_changed(self):
        """Handle record selection change"""
        has_selection = self.records_table.currentRow() >= 0
        self.edit_record_button.setEnabled(has_selection)
        self.delete_record_button.setEnabled(has_selection)

    def edit_selected_record(self):
        """Edit the selected attendance record"""
        current_row = self.records_table.currentRow()
        if current_row >= 0:
            # Get the date from the selected row
            date_item = self.records_table.item(current_row, 0)
            if date_item:
                selected_date = date_item.text()
                # Switch to the first tab and load the selected date
                self.tab_widget.setCurrentIndex(0)
                # Parse the date and set it in the calendar
                try:
                    date_obj = datetime.strptime(selected_date, '%Y-%m-%d').date()
                    self.current_date = date_obj
                    self.calendar.setSelectedDate(QDate(date_obj))
                    self.load_current_date()
                    QMessageBox.information(self, "Edit Mode",
                        f"Switched to edit mode for {date_obj.strftime('%A, %B %d, %Y')}.\n"
                        f"Make your changes and click 'Save Attendance'.")
                except ValueError as e:
                    QMessageBox.warning(self, "Error", f"Invalid date format: {e}")

    def delete_selected_record(self):
        """Delete the selected attendance record"""
        current_row = self.records_table.currentRow()
        if current_row >= 0:
            date_item = self.records_table.item(current_row, 0)
            if date_item:
                selected_date = date_item.text()

                reply = QMessageBox.question(
                    self, "Confirm Delete",
                    f"Are you sure you want to delete the attendance record for {selected_date}?\n\n"
                    f"This action cannot be undone.",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )

                if reply == QMessageBox.Yes:
                    if self.attendance_manager.delete_record(selected_date):
                        QMessageBox.information(self, "Success", "Record deleted successfully!")
                        self.load_all_records()
                        self.refresh_summary()
                        self.data_changed.emit()
                    else:
                        QMessageBox.warning(self, "Error", "Failed to delete record!")

    def setup_semester_tab(self, tab_widget):
        """Setup the semester management tab"""
        layout = QVBoxLayout(tab_widget)
        layout.setContentsMargins(10, 10, 10, 10)  # Reduced from 20px to 10px

        try:
            from .semester_widgets import SemesterManagementWidget

            # Create semester management widget
            self.semester_widget = SemesterManagementWidget(self.data_manager)
            self.semester_widget.semester_changed.connect(self.on_semester_changed)

            layout.addWidget(self.semester_widget)

        except Exception as e:
            self.logger.error(f"Error setting up semester tab: {e}")

            # Fallback UI
            error_label = QLabel(f"Error loading semester management: {str(e)}")
            error_label.setStyleSheet("color: red; font-style: italic;")
            layout.addWidget(error_label)

    def on_semester_changed(self):
        """Handle semester change"""
        try:
            # Refresh current date display to show correct semester
            self.load_current_date()
            self.refresh_summary()
            self.data_changed.emit()
        except Exception as e:
            self.logger.error(f"Error handling semester change: {e}")


# Alias for compatibility
AttendanceTrackerWidget = SimpleAttendanceTrackerWidget
