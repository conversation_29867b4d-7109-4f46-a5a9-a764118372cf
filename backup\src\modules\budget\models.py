"""
Budget Planning Data Models
Handles budget planning data structure and calculations
"""

import logging
import pandas as pd
from datetime import datetime, date, timedelta
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, asdict
from enum import Enum


class BudgetType(Enum):
    """Budget types"""
    MONTHLY = "Monthly"
    WEEKLY = "Weekly"
    YEARLY = "Yearly"


class CategoryType(Enum):
    """Budget category types"""
    INCOME = "Income"
    EXPENSE = "Expense"
    SAVINGS = "Savings"
    INVESTMENT = "Investment"


@dataclass
class BudgetCategory:
    """Data class for budget categories"""
    id: Optional[int] = None
    name: str = ""
    category_type: str = CategoryType.EXPENSE.value
    planned_amount: float = 0.0
    actual_amount: float = 0.0
    description: str = ""
    is_essential: bool = True  # Essential vs discretionary spending
    parent_category: Optional[str] = None  # For subcategories
    
    def get_variance(self) -> float:
        """Get variance between planned and actual"""
        return self.actual_amount - self.planned_amount
    
    def get_variance_percentage(self) -> float:
        """Get variance percentage"""
        if self.planned_amount == 0:
            return 0.0
        return (self.get_variance() / self.planned_amount) * 100
    
    def is_over_budget(self) -> bool:
        """Check if over budget"""
        return self.actual_amount > self.planned_amount
    
    def get_remaining_budget(self) -> float:
        """Get remaining budget amount"""
        return self.planned_amount - self.actual_amount
    
    def get_utilization_percentage(self) -> float:
        """Get budget utilization percentage"""
        if self.planned_amount == 0:
            return 0.0
        return (self.actual_amount / self.planned_amount) * 100
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for CSV storage"""
        data = asdict(self)
        
        # Add calculated fields
        data['variance'] = self.get_variance()
        data['variance_percentage'] = self.get_variance_percentage()
        data['remaining_budget'] = self.get_remaining_budget()
        data['utilization_percentage'] = self.get_utilization_percentage()
        data['is_over_budget'] = self.is_over_budget()
        
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BudgetCategory':
        """Create from dictionary"""
        # Remove calculated fields
        calc_fields = ['variance', 'variance_percentage', 'remaining_budget', 
                      'utilization_percentage', 'is_over_budget']
        for field in calc_fields:
            data.pop(field, None)
        
        return cls(**data)
    
    def validate(self) -> List[str]:
        """Validate the budget category"""
        errors = []
        
        if not self.name.strip():
            errors.append("Category name is required")
        
        if self.category_type not in [t.value for t in CategoryType]:
            errors.append(f"Invalid category type: {self.category_type}")
        
        if self.planned_amount < 0:
            errors.append("Planned amount cannot be negative")
        
        if self.actual_amount < 0:
            errors.append("Actual amount cannot be negative")
        
        return errors


@dataclass
class BudgetPlan:
    """Data class for budget plans"""
    id: Optional[int] = None
    name: str = ""
    budget_type: str = BudgetType.MONTHLY.value
    period_start: Optional[Union[str, datetime, date]] = None
    period_end: Optional[Union[str, datetime, date]] = None
    total_income_planned: float = 0.0
    total_income_actual: float = 0.0
    total_expenses_planned: float = 0.0
    total_expenses_actual: float = 0.0
    total_savings_planned: float = 0.0
    total_savings_actual: float = 0.0
    notes: str = ""
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def __post_init__(self):
        """Post-initialization processing"""
        if self.created_at is None:
            self.created_at = datetime.now()
        
        self.updated_at = datetime.now()
        
        # Handle date conversions
        for field in ['period_start', 'period_end']:
            value = getattr(self, field)
            if value and isinstance(value, str):
                try:
                    setattr(self, field, datetime.strptime(value, '%Y-%m-%d').date())
                except ValueError:
                    setattr(self, field, None)
            elif isinstance(value, datetime):
                setattr(self, field, value.date())
    
    def get_net_income_planned(self) -> float:
        """Get planned net income (income - expenses)"""
        return self.total_income_planned - self.total_expenses_planned
    
    def get_net_income_actual(self) -> float:
        """Get actual net income"""
        return self.total_income_actual - self.total_expenses_actual
    
    def get_savings_rate_planned(self) -> float:
        """Get planned savings rate percentage"""
        if self.total_income_planned == 0:
            return 0.0
        return (self.total_savings_planned / self.total_income_planned) * 100
    
    def get_savings_rate_actual(self) -> float:
        """Get actual savings rate percentage"""
        if self.total_income_actual == 0:
            return 0.0
        return (self.total_savings_actual / self.total_income_actual) * 100
    
    def get_expense_ratio_planned(self) -> float:
        """Get planned expense ratio percentage"""
        if self.total_income_planned == 0:
            return 0.0
        return (self.total_expenses_planned / self.total_income_planned) * 100
    
    def get_expense_ratio_actual(self) -> float:
        """Get actual expense ratio percentage"""
        if self.total_income_actual == 0:
            return 0.0
        return (self.total_expenses_actual / self.total_income_actual) * 100
    
    def is_on_track(self) -> bool:
        """Check if budget is on track"""
        return (self.total_expenses_actual <= self.total_expenses_planned and 
                self.total_savings_actual >= self.total_savings_planned * 0.9)  # 90% of savings goal
    
    def get_budget_health_score(self) -> float:
        """Get budget health score (0-100)"""
        score = 100.0
        
        # Deduct points for overspending
        if self.total_expenses_actual > self.total_expenses_planned:
            overspend_ratio = (self.total_expenses_actual - self.total_expenses_planned) / self.total_expenses_planned
            score -= min(overspend_ratio * 50, 50)  # Max 50 points deduction
        
        # Deduct points for under-saving
        if self.total_savings_actual < self.total_savings_planned:
            undersave_ratio = (self.total_savings_planned - self.total_savings_actual) / self.total_savings_planned
            score -= min(undersave_ratio * 30, 30)  # Max 30 points deduction
        
        # Bonus points for exceeding savings
        if self.total_savings_actual > self.total_savings_planned:
            oversave_ratio = (self.total_savings_actual - self.total_savings_planned) / self.total_savings_planned
            score += min(oversave_ratio * 20, 20)  # Max 20 bonus points
        
        return max(0, min(100, score))
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for CSV storage"""
        data = asdict(self)
        
        # Convert date objects to strings
        for field in ['period_start', 'period_end']:
            if isinstance(data[field], date):
                data[field] = data[field].strftime('%Y-%m-%d')
        
        for field in ['created_at', 'updated_at']:
            if isinstance(data[field], datetime):
                data[field] = data[field].strftime('%Y-%m-%d %H:%M:%S')
        
        # Add calculated fields
        data['net_income_planned'] = self.get_net_income_planned()
        data['net_income_actual'] = self.get_net_income_actual()
        data['savings_rate_planned'] = self.get_savings_rate_planned()
        data['savings_rate_actual'] = self.get_savings_rate_actual()
        data['expense_ratio_planned'] = self.get_expense_ratio_planned()
        data['expense_ratio_actual'] = self.get_expense_ratio_actual()
        data['is_on_track'] = self.is_on_track()
        data['budget_health_score'] = self.get_budget_health_score()
        
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BudgetPlan':
        """Create from dictionary"""
        # Remove calculated fields
        calc_fields = ['net_income_planned', 'net_income_actual', 'savings_rate_planned',
                      'savings_rate_actual', 'expense_ratio_planned', 'expense_ratio_actual',
                      'is_on_track', 'budget_health_score']
        for field in calc_fields:
            data.pop(field, None)
        
        # Handle datetime strings
        for field in ['created_at', 'updated_at']:
            if field in data and isinstance(data[field], str) and data[field]:
                try:
                    data[field] = datetime.strptime(data[field], '%Y-%m-%d %H:%M:%S')
                except ValueError:
                    data[field] = None
        
        return cls(**data)
    
    def validate(self) -> List[str]:
        """Validate the budget plan"""
        errors = []
        
        if not self.name.strip():
            errors.append("Budget plan name is required")
        
        if self.budget_type not in [t.value for t in BudgetType]:
            errors.append(f"Invalid budget type: {self.budget_type}")
        
        if self.total_income_planned < 0:
            errors.append("Planned income cannot be negative")
        
        if self.total_expenses_planned < 0:
            errors.append("Planned expenses cannot be negative")
        
        if self.total_savings_planned < 0:
            errors.append("Planned savings cannot be negative")
        
        if self.period_start and self.period_end and self.period_start > self.period_end:
            errors.append("Period start date must be before end date")
        
        return errors


class BudgetDataModel:
    """Data model for budget planning management"""
    
    def __init__(self, data_manager):
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self.logger.info("="*40)
        self.logger.info("INITIALIZING BUDGET DATA MODEL")
        self.logger.info("="*40)
        
        try:
            self.data_manager = data_manager
            self.module_name = "budget"
            self.plans_filename = "budget_plans.csv"
            self.categories_filename = "budget_categories.csv"
            
            # Default columns for budget plans CSV
            self.plans_columns = [
                'id', 'name', 'budget_type', 'period_start', 'period_end',
                'total_income_planned', 'total_income_actual', 'total_expenses_planned',
                'total_expenses_actual', 'total_savings_planned', 'total_savings_actual',
                'notes', 'created_at', 'updated_at', 'net_income_planned', 'net_income_actual',
                'savings_rate_planned', 'savings_rate_actual', 'expense_ratio_planned',
                'expense_ratio_actual', 'is_on_track', 'budget_health_score'
            ]
            
            # Default columns for budget categories CSV
            self.categories_columns = [
                'id', 'name', 'category_type', 'planned_amount', 'actual_amount',
                'description', 'is_essential', 'parent_category', 'variance',
                'variance_percentage', 'remaining_budget', 'utilization_percentage',
                'is_over_budget'
            ]
            
            # Initialize default categories if none exist
            self.initialize_default_categories()

            self.logger.info("✅ BudgetDataModel initialization SUCCESSFUL")

        except Exception as e:
            self.logger.error(f"❌ CRITICAL ERROR in BudgetDataModel.__init__: {e}")
            raise

    def initialize_default_categories(self):
        """Initialize default budget categories if none exist"""
        try:
            # Check if categories already exist
            existing_categories = self.get_all_budget_categories()
            if not existing_categories.empty:
                return  # Categories already exist

            # Define default categories with ₹15,000 total budget
            default_categories = [
                BudgetCategory(name="EMI", category_type=CategoryType.EXPENSE.value,
                             planned_amount=4000.0, description="Loan EMIs", is_essential=True),
                BudgetCategory(name="Recharge", category_type=CategoryType.EXPENSE.value,
                             planned_amount=500.0, description="Mobile and internet recharge", is_essential=True),
                BudgetCategory(name="Bills", category_type=CategoryType.EXPENSE.value,
                             planned_amount=2000.0, description="Utility bills", is_essential=True),
                BudgetCategory(name="Food", category_type=CategoryType.EXPENSE.value,
                             planned_amount=3000.0, description="Groceries and food", is_essential=True),
                BudgetCategory(name="Snacks", category_type=CategoryType.EXPENSE.value,
                             planned_amount=1000.0, description="Snacks and beverages", is_essential=False),
                BudgetCategory(name="Entertainment", category_type=CategoryType.EXPENSE.value,
                             planned_amount=1500.0, description="Movies, games, subscriptions", is_essential=False),
                BudgetCategory(name="Unexpected", category_type=CategoryType.EXPENSE.value,
                             planned_amount=2000.0, description="Emergency and unexpected expenses", is_essential=True),
                BudgetCategory(name="Family", category_type=CategoryType.EXPENSE.value,
                             planned_amount=1000.0, description="Family expenses", is_essential=True),
                BudgetCategory(name="Other", category_type=CategoryType.EXPENSE.value,
                             planned_amount=0.0, description="Miscellaneous expenses", is_essential=False)
            ]

            # Add each default category
            for category in default_categories:
                self.add_budget_category(category)

            self.logger.info(f"Initialized {len(default_categories)} default budget categories")

        except Exception as e:
            self.logger.error(f"Error initializing default categories: {e}")

    # Budget Plans Methods
    def get_all_budget_plans(self) -> pd.DataFrame:
        """Get all budget plans"""
        try:
            df = self.data_manager.read_csv(self.module_name, self.plans_filename, self.plans_columns)
            return df
        except Exception as e:
            self.logger.error(f"Error getting budget plans: {e}")
            return pd.DataFrame(columns=self.plans_columns)

    def add_budget_plan(self, plan: BudgetPlan) -> bool:
        """Add a new budget plan"""
        errors = plan.validate()
        if errors:
            self.data_manager.error_occurred.emit(f"Validation errors: {', '.join(errors)}")
            return False

        return self.data_manager.append_row(
            self.module_name,
            self.plans_filename,
            plan.to_dict(),
            self.plans_columns
        )

    def update_budget_plan(self, plan_id: int, plan: BudgetPlan) -> bool:
        """Update an existing budget plan"""
        errors = plan.validate()
        if errors:
            self.data_manager.error_occurred.emit(f"Validation errors: {', '.join(errors)}")
            return False

        return self.data_manager.update_row(
            self.module_name,
            self.plans_filename,
            plan_id,
            plan.to_dict()
        )

    def delete_budget_plan(self, plan_id: int) -> bool:
        """Delete a budget plan"""
        return self.data_manager.delete_row(self.module_name, self.plans_filename, plan_id)

    # Budget Categories Methods
    def get_all_budget_categories(self) -> pd.DataFrame:
        """Get all budget categories"""
        try:
            df = self.data_manager.read_csv(self.module_name, self.categories_filename, self.categories_columns)
            return df
        except Exception as e:
            self.logger.error(f"Error getting budget categories: {e}")
            return pd.DataFrame(columns=self.categories_columns)

    def add_budget_category(self, category: BudgetCategory) -> bool:
        """Add a new budget category"""
        errors = category.validate()
        if errors:
            self.data_manager.error_occurred.emit(f"Validation errors: {', '.join(errors)}")
            return False

        return self.data_manager.append_row(
            self.module_name,
            self.categories_filename,
            category.to_dict(),
            self.categories_columns
        )

    def update_budget_category(self, category_id: int, category: BudgetCategory) -> bool:
        """Update an existing budget category"""
        errors = category.validate()
        if errors:
            self.data_manager.error_occurred.emit(f"Validation errors: {', '.join(errors)}")
            return False

        return self.data_manager.update_row(
            self.module_name,
            self.categories_filename,
            category_id,
            category.to_dict()
        )

    def delete_budget_category(self, category_id: int) -> bool:
        """Delete a budget category"""
        return self.data_manager.delete_row(self.module_name, self.categories_filename, category_id)

    def get_categories_by_type(self, category_type: str) -> pd.DataFrame:
        """Get categories filtered by type"""
        df = self.get_all_budget_categories()
        if df.empty:
            return df
        return df[df['category_type'] == category_type]

    def get_budget_summary(self) -> Dict[str, Any]:
        """Get budget summary statistics"""
        plans_df = self.get_all_budget_plans()
        categories_df = self.get_all_budget_categories()

        if plans_df.empty:
            return {
                'total_plans': 0,
                'active_plans': 0,
                'total_planned_income': 0.0,
                'total_actual_income': 0.0,
                'total_planned_expenses': 0.0,
                'total_actual_expenses': 0.0,
                'average_health_score': 0.0,
                'plans_on_track': 0,
                'categories_over_budget': 0,
                'total_categories': 0
            }

        # Current date for active plans
        today = date.today()

        # Filter active plans (current period)
        active_plans = plans_df[
            (pd.to_datetime(plans_df['period_start']).dt.date <= today) &
            (pd.to_datetime(plans_df['period_end']).dt.date >= today)
        ]

        total_planned_income = plans_df['total_income_planned'].sum()
        total_actual_income = plans_df['total_income_actual'].sum()
        total_planned_expenses = plans_df['total_expenses_planned'].sum()
        total_actual_expenses = plans_df['total_expenses_actual'].sum()

        average_health_score = plans_df['budget_health_score'].mean() if not plans_df.empty else 0.0
        plans_on_track = len(plans_df[plans_df['is_on_track'] == True])

        categories_over_budget = 0
        if not categories_df.empty:
            categories_over_budget = len(categories_df[categories_df['is_over_budget'] == True])

        return {
            'total_plans': len(plans_df),
            'active_plans': len(active_plans),
            'total_planned_income': total_planned_income,
            'total_actual_income': total_actual_income,
            'total_planned_expenses': total_planned_expenses,
            'total_actual_expenses': total_actual_expenses,
            'average_health_score': average_health_score,
            'plans_on_track': plans_on_track,
            'categories_over_budget': categories_over_budget,
            'total_categories': len(categories_df)
        }
