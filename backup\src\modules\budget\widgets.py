"""
Budget Planning UI Widgets
Contains all UI components for the budget planning module
"""

import logging
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QFormLayout,
    QLabel, QPushButton, QLineEdit, QComboBox, QDateEdit, QTextEdit,
    QCheckBox, QFrame, QGroupBox, QScrollArea, QTabWidget,
    QProgressBar, QSpinBox, QTableWidget, QTableWidgetItem, QHeaderView,
    QAbstractItemView, QSizePolicy, QButtonGroup, QDoubleSpinBox,
    QMessageBox, QDialog, QDialogButtonBox, QSplitter
)
from PySide6.QtCore import Qt, Signal, QDate, QTimer, QSize
from PySide6.QtGui import QFont, QIcon, QPixmap, QPalette, QColor

from datetime import datetime, date, timedelta
from typing import Dict, List, Any, Optional
import pandas as pd

from .models import BudgetPlan, BudgetCategory, BudgetDataModel, BudgetType, CategoryType


class SimplifiedBudgetWidget(QWidget):
    """Simplified budget planning widget with predefined categories"""

    def __init__(self, budget_model: BudgetDataModel, parent=None):
        super().__init__(parent)
        self.budget_model = budget_model
        self.logger = logging.getLogger(__name__)
        self.category_widgets = {}
        self.setup_ui()
        self.load_categories()

    def setup_ui(self):
        """Setup the simplified budget UI"""
        layout = QVBoxLayout(self)

        # Header
        header_layout = QHBoxLayout()

        title_label = QLabel("💰 Budget Planning")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        # Total budget display
        self.total_budget_label = QLabel("Total Budget: ₹15,000")
        self.total_budget_label.setFont(QFont("Arial", 14, QFont.Bold))
        self.total_budget_label.setStyleSheet("color: #2e7d32; padding: 5px;")
        header_layout.addWidget(self.total_budget_label)

        layout.addLayout(header_layout)

        # Budget overview
        overview_frame = QFrame()
        overview_frame.setFrameStyle(QFrame.Box)
        overview_frame.setStyleSheet("""
            QFrame {
                border: 1px solid #ddd;
                border-radius: 8px;
                padding: 15px;
                background-color: #f8f9fa;
                margin: 10px 0px;
            }
        """)

        overview_layout = QHBoxLayout(overview_frame)

        # Planned vs Actual
        self.planned_label = QLabel("Planned: ₹0")
        self.planned_label.setFont(QFont("Arial", 12, QFont.Bold))
        overview_layout.addWidget(self.planned_label)

        self.actual_label = QLabel("Actual: ₹0")
        self.actual_label.setFont(QFont("Arial", 12, QFont.Bold))
        overview_layout.addWidget(self.actual_label)

        self.remaining_label = QLabel("Remaining: ₹15,000")
        self.remaining_label.setFont(QFont("Arial", 12, QFont.Bold))
        self.remaining_label.setStyleSheet("color: #2e7d32;")
        overview_layout.addWidget(self.remaining_label)

        layout.addWidget(overview_frame)

        # Categories section
        categories_frame = QFrame()
        categories_frame.setFrameStyle(QFrame.Box)
        categories_frame.setStyleSheet("""
            QFrame {
                border: 1px solid #ddd;
                border-radius: 8px;
                padding: 15px;
                background-color: white;
            }
        """)

        categories_layout = QVBoxLayout(categories_frame)

        # Categories header
        categories_header = QHBoxLayout()
        categories_title = QLabel("Budget Categories")
        categories_title.setFont(QFont("Arial", 14, QFont.Bold))
        categories_header.addWidget(categories_title)

        categories_header.addStretch()

        # Add/Remove category buttons
        add_category_btn = QPushButton("➕ Add Category")
        add_category_btn.clicked.connect(self.add_custom_category)
        categories_header.addWidget(add_category_btn)

        categories_layout.addLayout(categories_header)

        # Scroll area for categories
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        self.categories_widget = QWidget()
        self.categories_layout = QVBoxLayout(self.categories_widget)
        self.categories_layout.setSpacing(10)

        scroll_area.setWidget(self.categories_widget)
        categories_layout.addWidget(scroll_area)

        layout.addWidget(categories_frame)

        # Action buttons
        buttons_layout = QHBoxLayout()
        buttons_layout.addStretch()

        save_btn = QPushButton("💾 Save Budget")
        save_btn.clicked.connect(self.save_budget)
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #4caf50;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        buttons_layout.addWidget(save_btn)

        reset_btn = QPushButton("🔄 Reset")
        reset_btn.clicked.connect(self.reset_budget)
        reset_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
        """)
        buttons_layout.addWidget(reset_btn)

        layout.addLayout(buttons_layout)

    def create_category_widget(self, category: BudgetCategory) -> QWidget:
        """Create a widget for a budget category"""
        widget = QFrame()
        widget.setFrameStyle(QFrame.Box)
        widget.setStyleSheet("""
            QFrame {
                border: 1px solid #e0e0e0;
                border-radius: 5px;
                padding: 10px;
                margin: 2px;
                background-color: #fafafa;
            }
        """)

        layout = QHBoxLayout(widget)

        # Category name
        name_label = QLabel(category.name)
        name_label.setFont(QFont("Arial", 11, QFont.Bold))
        name_label.setMinimumWidth(100)
        layout.addWidget(name_label)

        # Essential indicator
        if category.is_essential:
            essential_label = QLabel("⭐")
            essential_label.setToolTip("Essential category")
            layout.addWidget(essential_label)

        # Planned amount
        planned_spinbox = QDoubleSpinBox()
        planned_spinbox.setRange(0.0, 50000.0)
        planned_spinbox.setValue(category.planned_amount)
        planned_spinbox.setPrefix("₹ ")
        planned_spinbox.setSuffix(" (Planned)")
        planned_spinbox.valueChanged.connect(self.update_totals)
        layout.addWidget(planned_spinbox)

        # Actual amount
        actual_spinbox = QDoubleSpinBox()
        actual_spinbox.setRange(0.0, 50000.0)
        actual_spinbox.setValue(category.actual_amount)
        actual_spinbox.setPrefix("₹ ")
        actual_spinbox.setSuffix(" (Actual)")
        actual_spinbox.valueChanged.connect(self.update_totals)
        layout.addWidget(actual_spinbox)

        # Progress bar
        progress_bar = QProgressBar()
        progress_bar.setMaximum(100)
        if category.planned_amount > 0:
            progress = min(100, int((category.actual_amount / category.planned_amount) * 100))
            progress_bar.setValue(progress)
        layout.addWidget(progress_bar)

        # Remove button (only for non-essential categories)
        if not category.is_essential:
            remove_btn = QPushButton("🗑️")
            remove_btn.setToolTip("Remove category")
            remove_btn.clicked.connect(lambda: self.remove_category(category.name))
            remove_btn.setMaximumWidth(30)
            layout.addWidget(remove_btn)

        # Store references
        self.category_widgets[category.name] = {
            'widget': widget,
            'planned_spinbox': planned_spinbox,
            'actual_spinbox': actual_spinbox,
            'progress_bar': progress_bar,
            'category': category
        }

        return widget

    def load_categories(self):
        """Load and display budget categories"""
        try:
            categories_df = self.budget_model.get_all_budget_categories()

            # Clear existing widgets
            for category_name in list(self.category_widgets.keys()):
                self.remove_category_widget(category_name)

            if categories_df.empty:
                return

            # Create widgets for each category
            for _, row in categories_df.iterrows():
                category = BudgetCategory.from_dict(row.to_dict())
                widget = self.create_category_widget(category)
                self.categories_layout.addWidget(widget)

            self.update_totals()

        except Exception as e:
            self.logger.error(f"Error loading categories: {e}")

    def update_totals(self):
        """Update total planned, actual, and remaining amounts"""
        try:
            total_planned = 0.0
            total_actual = 0.0

            for category_data in self.category_widgets.values():
                planned = category_data['planned_spinbox'].value()
                actual = category_data['actual_spinbox'].value()

                total_planned += planned
                total_actual += actual

                # Update progress bar
                if planned > 0:
                    progress = min(100, int((actual / planned) * 100))
                    category_data['progress_bar'].setValue(progress)

                    # Color coding
                    if actual > planned:
                        category_data['progress_bar'].setStyleSheet("QProgressBar::chunk { background-color: #f44336; }")
                    elif actual >= planned * 0.8:
                        category_data['progress_bar'].setStyleSheet("QProgressBar::chunk { background-color: #ff9800; }")
                    else:
                        category_data['progress_bar'].setStyleSheet("QProgressBar::chunk { background-color: #4caf50; }")

            remaining = 15000.0 - total_actual

            # Update labels
            self.planned_label.setText(f"Planned: ₹{total_planned:,.0f}")
            self.actual_label.setText(f"Actual: ₹{total_actual:,.0f}")
            self.remaining_label.setText(f"Remaining: ₹{remaining:,.0f}")

            # Color coding for remaining
            if remaining < 0:
                self.remaining_label.setStyleSheet("color: #f44336; font-weight: bold;")
            elif remaining < 1000:
                self.remaining_label.setStyleSheet("color: #ff9800; font-weight: bold;")
            else:
                self.remaining_label.setStyleSheet("color: #2e7d32; font-weight: bold;")

        except Exception as e:
            self.logger.error(f"Error updating totals: {e}")

    def add_custom_category(self):
        """Add a custom budget category"""
        dialog = QDialog(self)
        dialog.setWindowTitle("Add Budget Category")
        dialog.setModal(True)

        layout = QVBoxLayout(dialog)

        # Category name
        name_layout = QHBoxLayout()
        name_layout.addWidget(QLabel("Category Name:"))
        name_edit = QLineEdit()
        name_layout.addWidget(name_edit)
        layout.addLayout(name_layout)

        # Planned amount
        amount_layout = QHBoxLayout()
        amount_layout.addWidget(QLabel("Planned Amount:"))
        amount_spinbox = QDoubleSpinBox()
        amount_spinbox.setRange(0.0, 50000.0)
        amount_spinbox.setPrefix("₹ ")
        amount_layout.addWidget(amount_spinbox)
        layout.addLayout(amount_layout)

        # Essential checkbox
        essential_checkbox = QCheckBox("Essential Category")
        layout.addWidget(essential_checkbox)

        # Description
        desc_layout = QVBoxLayout()
        desc_layout.addWidget(QLabel("Description:"))
        desc_edit = QTextEdit()
        desc_edit.setMaximumHeight(60)
        desc_layout.addWidget(desc_edit)
        layout.addLayout(desc_layout)

        # Buttons
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(dialog.accept)
        buttons.rejected.connect(dialog.reject)
        layout.addWidget(buttons)

        if dialog.exec() == QDialog.Accepted:
            name = name_edit.text().strip()
            if name and name not in self.category_widgets:
                category = BudgetCategory(
                    name=name,
                    category_type=CategoryType.EXPENSE.value,
                    planned_amount=amount_spinbox.value(),
                    description=desc_edit.toPlainText(),
                    is_essential=essential_checkbox.isChecked()
                )

                # Add to database
                if self.budget_model.add_budget_category(category):
                    # Add to UI
                    widget = self.create_category_widget(category)
                    self.categories_layout.addWidget(widget)
                    self.update_totals()
                else:
                    QMessageBox.warning(self, "Error", "Failed to add category")
            elif name in self.category_widgets:
                QMessageBox.warning(self, "Error", "Category already exists")

    def remove_category(self, category_name: str):
        """Remove a budget category"""
        reply = QMessageBox.question(
            self,
            "Remove Category",
            f"Are you sure you want to remove '{category_name}' category?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Remove from database
            categories_df = self.budget_model.get_all_budget_categories()
            category_row = categories_df[categories_df['name'] == category_name]

            if not category_row.empty:
                category_id = category_row.iloc[0]['id']
                if self.budget_model.delete_budget_category(category_id):
                    self.remove_category_widget(category_name)
                    self.update_totals()
                else:
                    QMessageBox.warning(self, "Error", "Failed to remove category")

    def remove_category_widget(self, category_name: str):
        """Remove category widget from UI"""
        if category_name in self.category_widgets:
            widget_data = self.category_widgets[category_name]
            widget_data['widget'].setParent(None)
            del self.category_widgets[category_name]

    def save_budget(self):
        """Save current budget settings"""
        try:
            # Update all categories with current values
            for category_name, widget_data in self.category_widgets.items():
                category = widget_data['category']
                category.planned_amount = widget_data['planned_spinbox'].value()
                category.actual_amount = widget_data['actual_spinbox'].value()

                # Update in database
                categories_df = self.budget_model.get_all_budget_categories()
                category_row = categories_df[categories_df['name'] == category_name]

                if not category_row.empty:
                    category_id = category_row.iloc[0]['id']
                    self.budget_model.update_budget_category(category_id, category)

            QMessageBox.information(self, "Success", "Budget saved successfully!")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to save budget: {str(e)}")
            self.logger.error(f"Error saving budget: {e}")

    def reset_budget(self):
        """Reset budget to default values"""
        reply = QMessageBox.question(
            self,
            "Reset Budget",
            "Are you sure you want to reset all budget values to defaults?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            for widget_data in self.category_widgets.values():
                widget_data['actual_spinbox'].setValue(0.0)
            self.update_totals()


class BudgetSummaryWidget(QFrame):
    """Widget for displaying budget summary"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the summary UI"""
        self.setFrameStyle(QFrame.Box)
        self.setStyleSheet("""
            QFrame {
                border: 1px solid #ddd;
                border-radius: 5px;
                padding: 15px;
                background-color: #f8f9fa;
            }
        """)
        
        layout = QGridLayout(self)
        
        # Title
        title_label = QLabel("💰 Budget Overview")
        title_label.setFont(QFont("Arial", 14, QFont.Bold))
        layout.addWidget(title_label, 0, 0, 1, 4)
        
        # Summary metrics
        self.total_plans_label = QLabel("Total Plans: 0")
        layout.addWidget(self.total_plans_label, 1, 0)
        
        self.active_plans_label = QLabel("Active Plans: 0")
        layout.addWidget(self.active_plans_label, 1, 1)
        
        self.health_score_label = QLabel("Avg Health Score: 0%")
        layout.addWidget(self.health_score_label, 1, 2)
        
        self.on_track_label = QLabel("Plans On Track: 0")
        layout.addWidget(self.on_track_label, 1, 3)
        
        # Financial metrics
        self.planned_income_label = QLabel("Planned Income: ₹0.00")
        layout.addWidget(self.planned_income_label, 2, 0)
        
        self.actual_income_label = QLabel("Actual Income: ₹0.00")
        layout.addWidget(self.actual_income_label, 2, 1)
        
        self.planned_expenses_label = QLabel("Planned Expenses: ₹0.00")
        layout.addWidget(self.planned_expenses_label, 2, 2)
        
        self.actual_expenses_label = QLabel("Actual Expenses: ₹0.00")
        layout.addWidget(self.actual_expenses_label, 2, 3)
    
    def update_summary(self, summary: Dict[str, Any]):
        """Update summary display"""
        self.total_plans_label.setText(f"Total Plans: {summary['total_plans']}")
        self.active_plans_label.setText(f"Active Plans: {summary['active_plans']}")
        
        health_score = summary['average_health_score']
        health_color = "#28a745" if health_score >= 80 else "#ffc107" if health_score >= 60 else "#dc3545"
        self.health_score_label.setText(f"Avg Health Score: {health_score:.1f}%")
        self.health_score_label.setStyleSheet(f"color: {health_color}; font-weight: bold;")
        
        self.on_track_label.setText(f"Plans On Track: {summary['plans_on_track']}")
        
        self.planned_income_label.setText(f"Planned Income: ₹{summary['total_planned_income']:,.2f}")
        self.actual_income_label.setText(f"Actual Income: ₹{summary['total_actual_income']:,.2f}")
        self.planned_expenses_label.setText(f"Planned Expenses: ₹{summary['total_planned_expenses']:,.2f}")
        self.actual_expenses_label.setText(f"Actual Expenses: ₹{summary['total_actual_expenses']:,.2f}")


class BudgetPlanTableWidget(QTableWidget):
    """Table widget for displaying budget plans"""
    
    plan_selected = Signal(int)  # Emits plan ID
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_table()
    
    def setup_table(self):
        """Setup the table"""
        # Define columns
        self.columns = [
            'Name', 'Type', 'Period Start', 'Period End', 'Planned Income',
            'Actual Income', 'Planned Expenses', 'Actual Expenses',
            'Health Score', 'On Track'
        ]
        
        self.setColumnCount(len(self.columns))
        self.setHorizontalHeaderLabels(self.columns)
        
        # Table settings
        self.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.setSelectionMode(QAbstractItemView.SingleSelection)
        self.setAlternatingRowColors(True)
        self.setSortingEnabled(True)
        
        # Resize columns
        header = self.horizontalHeader()
        header.setStretchLastSection(True)
        for i in range(len(self.columns)):
            header.setSectionResizeMode(i, QHeaderView.ResizeToContents)
        
        # Connect selection
        self.itemSelectionChanged.connect(self.on_selection_changed)
    
    def update_plans(self, df: pd.DataFrame):
        """Update table with budget plan data"""
        self.setRowCount(len(df))
        
        for row_idx, (_, row) in enumerate(df.iterrows()):
            # Name
            self.setItem(row_idx, 0, QTableWidgetItem(str(row['name'])))
            
            # Type
            self.setItem(row_idx, 1, QTableWidgetItem(str(row['budget_type'])))
            
            # Period Start
            self.setItem(row_idx, 2, QTableWidgetItem(str(row['period_start'])))
            
            # Period End
            self.setItem(row_idx, 3, QTableWidgetItem(str(row['period_end'])))
            
            # Planned Income
            self.setItem(row_idx, 4, QTableWidgetItem(f"₹{row['total_income_planned']:,.2f}"))
            
            # Actual Income
            self.setItem(row_idx, 5, QTableWidgetItem(f"₹{row['total_income_actual']:,.2f}"))
            
            # Planned Expenses
            self.setItem(row_idx, 6, QTableWidgetItem(f"₹{row['total_expenses_planned']:,.2f}"))
            
            # Actual Expenses
            self.setItem(row_idx, 7, QTableWidgetItem(f"₹{row['total_expenses_actual']:,.2f}"))
            
            # Health Score
            health_score = row['budget_health_score']
            health_item = QTableWidgetItem(f"{health_score:.1f}%")
            health_color = "#28a745" if health_score >= 80 else "#ffc107" if health_score >= 60 else "#dc3545"
            health_item.setForeground(QColor(health_color))
            self.setItem(row_idx, 8, health_item)
            
            # On Track
            on_track = "✅ Yes" if row['is_on_track'] else "❌ No"
            on_track_item = QTableWidgetItem(on_track)
            on_track_color = "#28a745" if row['is_on_track'] else "#dc3545"
            on_track_item.setForeground(QColor(on_track_color))
            self.setItem(row_idx, 9, on_track_item)
            
            # Store plan ID in first column
            self.item(row_idx, 0).setData(Qt.UserRole, row['id'])
    
    def on_selection_changed(self):
        """Handle selection change"""
        current_row = self.currentRow()
        if current_row >= 0:
            item = self.item(current_row, 0)
            if item:
                plan_id = item.data(Qt.UserRole)
                if plan_id:
                    self.plan_selected.emit(plan_id)


class BudgetPlanEditDialog(QDialog):
    """Dialog for editing budget plans"""
    
    def __init__(self, plan: BudgetPlan = None, parent=None):
        super().__init__(parent)
        self.plan = plan or BudgetPlan()
        self.setup_ui()
        self.populate_fields()
    
    def setup_ui(self):
        """Setup the dialog UI"""
        self.setWindowTitle("Edit Budget Plan")
        self.setModal(True)
        self.resize(600, 700)
        
        layout = QVBoxLayout(self)
        
        # Form layout
        form_layout = QFormLayout()
        
        # Name
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("e.g., January 2024 Budget")
        form_layout.addRow("Plan Name:", self.name_edit)
        
        # Budget Type
        self.type_combo = QComboBox()
        self.type_combo.addItems([t.value for t in BudgetType])
        form_layout.addRow("Budget Type:", self.type_combo)
        
        # Period dates
        period_layout = QHBoxLayout()
        self.period_start_edit = QDateEdit()
        self.period_start_edit.setCalendarPopup(True)
        self.period_start_edit.setDate(QDate.currentDate())
        period_layout.addWidget(self.period_start_edit)
        
        period_layout.addWidget(QLabel("to"))
        
        self.period_end_edit = QDateEdit()
        self.period_end_edit.setCalendarPopup(True)
        self.period_end_edit.setDate(QDate.currentDate().addDays(30))
        period_layout.addWidget(self.period_end_edit)
        
        form_layout.addRow("Period:", period_layout)
        
        # Income section
        income_group = QGroupBox("Income")
        income_layout = QFormLayout(income_group)
        
        self.planned_income_spin = QDoubleSpinBox()
        self.planned_income_spin.setRange(0, 9999999)
        self.planned_income_spin.setPrefix("₹")
        income_layout.addRow("Planned Income:", self.planned_income_spin)
        
        self.actual_income_spin = QDoubleSpinBox()
        self.actual_income_spin.setRange(0, 9999999)
        self.actual_income_spin.setPrefix("₹")
        income_layout.addRow("Actual Income:", self.actual_income_spin)
        
        layout.addWidget(income_group)
        
        # Expenses section
        expenses_group = QGroupBox("Expenses")
        expenses_layout = QFormLayout(expenses_group)
        
        self.planned_expenses_spin = QDoubleSpinBox()
        self.planned_expenses_spin.setRange(0, 9999999)
        self.planned_expenses_spin.setPrefix("₹")
        expenses_layout.addRow("Planned Expenses:", self.planned_expenses_spin)
        
        self.actual_expenses_spin = QDoubleSpinBox()
        self.actual_expenses_spin.setRange(0, 9999999)
        self.actual_expenses_spin.setPrefix("₹")
        expenses_layout.addRow("Actual Expenses:", self.actual_expenses_spin)
        
        layout.addWidget(expenses_group)
        
        # Savings section
        savings_group = QGroupBox("Savings")
        savings_layout = QFormLayout(savings_group)
        
        self.planned_savings_spin = QDoubleSpinBox()
        self.planned_savings_spin.setRange(0, 9999999)
        self.planned_savings_spin.setPrefix("₹")
        savings_layout.addRow("Planned Savings:", self.planned_savings_spin)
        
        self.actual_savings_spin = QDoubleSpinBox()
        self.actual_savings_spin.setRange(0, 9999999)
        self.actual_savings_spin.setPrefix("₹")
        savings_layout.addRow("Actual Savings:", self.actual_savings_spin)
        
        layout.addWidget(savings_group)
        
        layout.addLayout(form_layout)
        
        # Notes
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(100)
        self.notes_edit.setPlaceholderText("Additional notes about this budget plan...")
        form_layout.addRow("Notes:", self.notes_edit)
        
        # Calculated fields (read-only)
        calc_group = QGroupBox("Calculated Metrics")
        calc_layout = QFormLayout(calc_group)
        
        self.net_income_label = QLabel("₹0.00")
        calc_layout.addRow("Net Income:", self.net_income_label)
        
        self.savings_rate_label = QLabel("0.00%")
        calc_layout.addRow("Savings Rate:", self.savings_rate_label)
        
        self.health_score_label = QLabel("0.0")
        calc_layout.addRow("Health Score:", self.health_score_label)
        
        layout.addWidget(calc_group)
        
        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
        
        # Connect value changes to update calculations
        for spin in [self.planned_income_spin, self.actual_income_spin, 
                    self.planned_expenses_spin, self.actual_expenses_spin,
                    self.planned_savings_spin, self.actual_savings_spin]:
            spin.valueChanged.connect(self.update_calculations)
    
    def populate_fields(self):
        """Populate fields with plan data"""
        self.name_edit.setText(self.plan.name)
        self.type_combo.setCurrentText(self.plan.budget_type)
        
        if self.plan.period_start:
            self.period_start_edit.setDate(QDate.fromString(str(self.plan.period_start), "yyyy-MM-dd"))
        if self.plan.period_end:
            self.period_end_edit.setDate(QDate.fromString(str(self.plan.period_end), "yyyy-MM-dd"))
        
        self.planned_income_spin.setValue(self.plan.total_income_planned)
        self.actual_income_spin.setValue(self.plan.total_income_actual)
        self.planned_expenses_spin.setValue(self.plan.total_expenses_planned)
        self.actual_expenses_spin.setValue(self.plan.total_expenses_actual)
        self.planned_savings_spin.setValue(self.plan.total_savings_planned)
        self.actual_savings_spin.setValue(self.plan.total_savings_actual)
        
        self.notes_edit.setPlainText(self.plan.notes)
        
        self.update_calculations()
    
    def update_calculations(self):
        """Update calculated fields"""
        planned_income = self.planned_income_spin.value()
        actual_income = self.actual_income_spin.value()
        planned_expenses = self.planned_expenses_spin.value()
        actual_expenses = self.actual_expenses_spin.value()
        planned_savings = self.planned_savings_spin.value()
        actual_savings = self.actual_savings_spin.value()
        
        # Update plan object for calculations
        self.plan.total_income_planned = planned_income
        self.plan.total_income_actual = actual_income
        self.plan.total_expenses_planned = planned_expenses
        self.plan.total_expenses_actual = actual_expenses
        self.plan.total_savings_planned = planned_savings
        self.plan.total_savings_actual = actual_savings
        
        # Calculate metrics
        net_income = self.plan.get_net_income_actual()
        savings_rate = self.plan.get_savings_rate_actual()
        health_score = self.plan.get_budget_health_score()
        
        self.net_income_label.setText(f"₹{net_income:,.2f}")
        self.savings_rate_label.setText(f"{savings_rate:.2f}%")
        
        # Color code health score
        health_color = "#28a745" if health_score >= 80 else "#ffc107" if health_score >= 60 else "#dc3545"
        self.health_score_label.setText(f"{health_score:.1f}")
        self.health_score_label.setStyleSheet(f"color: {health_color}; font-weight: bold;")
    
    def get_budget_plan(self) -> BudgetPlan:
        """Get the budget plan from form data"""
        self.plan.name = self.name_edit.text().strip()
        self.plan.budget_type = self.type_combo.currentText()
        self.plan.period_start = self.period_start_edit.date().toPython()
        self.plan.period_end = self.period_end_edit.date().toPython()
        self.plan.total_income_planned = self.planned_income_spin.value()
        self.plan.total_income_actual = self.actual_income_spin.value()
        self.plan.total_expenses_planned = self.planned_expenses_spin.value()
        self.plan.total_expenses_actual = self.actual_expenses_spin.value()
        self.plan.total_savings_planned = self.planned_savings_spin.value()
        self.plan.total_savings_actual = self.actual_savings_spin.value()
        self.plan.notes = self.notes_edit.toPlainText().strip()
        self.plan.updated_at = datetime.now()
        
        return self.plan


class BudgetPlannerWidget(QWidget):
    """Main budget planner widget"""

    def __init__(self, data_manager, config, parent=None):
        super().__init__(parent)

        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self.logger.info("="*50)
        self.logger.info("INITIALIZING BUDGET PLANNER WIDGET")
        self.logger.info("="*50)

        try:
            self.data_manager = data_manager
            self.config = config
            self.budget_model = BudgetDataModel(data_manager)

            self.setup_ui()
            self.setup_connections()
            self.refresh_data()

            self.logger.info("✅ BudgetPlannerWidget initialization SUCCESSFUL")

        except Exception as e:
            self.logger.error(f"❌ CRITICAL ERROR in BudgetPlannerWidget.__init__: {e}")
            raise

    def setup_ui(self):
        """Setup the main UI"""
        layout = QVBoxLayout(self)

        # Use the simplified budget widget
        self.simplified_budget_widget = SimplifiedBudgetWidget(self.budget_model)
        layout.addWidget(self.simplified_budget_widget)

    def setup_connections(self):
        """Setup signal connections"""
        pass

    def refresh_data(self):
        """Refresh budget data and update display"""
        try:
            # Refresh the simplified budget widget
            self.simplified_budget_widget.load_categories()
        except Exception as e:
            self.logger.error(f"Error refreshing budget data: {e}")
