"""
Expense Data Models
Handles expense data structure and validation
"""

import pandas as pd
from datetime import datetime, date
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, asdict
from enum import Enum


class TransactionType(Enum):
    """Transaction type enumeration"""
    EXPENSE = "Expense"
    INCOME = "Income"
    TRANSFER = "Transfer"


class TransactionMode(Enum):
    """Transaction mode enumeration"""
    CASH = "Cash"
    CREDIT_CARD = "Credit Card"
    DEBIT_CARD = "Debit Card"
    UPI = "UPI"
    NET_BANKING = "Net Banking"
    WALLET = "Wallet"
    CHEQUE = "Cheque"
    BANK_TRANSFER = "Bank Transfer"
    OTHER = "Other"


@dataclass
class ExpenseRecord:
    """Data class for expense records"""
    id: Optional[int] = None
    date: Union[str, datetime, date] = None
    type: str = "Expense"
    category: str = ""
    sub_category: str = ""
    transaction_mode: str = "Cash"
    amount: float = 0.0
    notes: str = ""
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def __post_init__(self):
        """Post-initialization processing"""
        if self.date is None:
            self.date = date.today()
        elif isinstance(self.date, str):
            try:
                self.date = datetime.strptime(self.date, '%Y-%m-%d').date()
            except ValueError:
                self.date = date.today()
        elif isinstance(self.date, datetime):
            self.date = self.date.date()
        
        if self.created_at is None:
            self.created_at = datetime.now()
        
        self.updated_at = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for CSV storage"""
        data = asdict(self)
        
        # Convert date objects to strings
        if isinstance(data['date'], date):
            data['date'] = data['date'].strftime('%Y-%m-%d')
        if isinstance(data['created_at'], datetime):
            data['created_at'] = data['created_at'].strftime('%Y-%m-%d %H:%M:%S')
        if isinstance(data['updated_at'], datetime):
            data['updated_at'] = data['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
        
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ExpenseRecord':
        """Create from dictionary"""
        # Handle datetime strings
        if 'created_at' in data and isinstance(data['created_at'], str):
            try:
                data['created_at'] = datetime.strptime(data['created_at'], '%Y-%m-%d %H:%M:%S')
            except ValueError:
                data['created_at'] = datetime.now()
        
        if 'updated_at' in data and isinstance(data['updated_at'], str):
            try:
                data['updated_at'] = datetime.strptime(data['updated_at'], '%Y-%m-%d %H:%M:%S')
            except ValueError:
                data['updated_at'] = datetime.now()
        
        return cls(**data)
    
    def validate(self) -> List[str]:
        """Validate the expense record"""
        errors = []
        
        if not self.category:
            errors.append("Category is required")
        
        if not self.sub_category:
            errors.append("Sub-category is required")
        
        if self.amount <= 0:
            errors.append("Amount must be greater than 0")
        
        if not self.transaction_mode:
            errors.append("Transaction mode is required")
        
        return errors


class ExpenseDataModel:
    """Data model for expense management"""
    
    def __init__(self, data_manager):
        self.data_manager = data_manager
        self.module_name = "expenses"
        self.filename = "expenses.csv"
        self.categories_filename = "categories.csv"
        
        # Default columns for expenses CSV
        self.default_columns = [
            'id', 'date', 'type', 'category', 'sub_category',
            'transaction_mode', 'amount', 'notes', 'created_at', 'updated_at'
        ]
        
        # Default columns for categories CSV
        self.categories_columns = [
            'id', 'category', 'sub_category', 'is_active', 'created_at'
        ]
        
        # Initialize default categories if not exists
        self._initialize_default_categories()
    
    def _initialize_default_categories(self):
        """Initialize default categories if they don't exist"""
        if not self.data_manager.file_exists(self.module_name, self.categories_filename):
            default_categories = [
                ("Food & Dining", ["Restaurants", "Groceries", "Fast Food", "Coffee", "Delivery"]),
                ("Transportation", ["Fuel", "Public Transport", "Taxi/Uber", "Parking", "Maintenance"]),
                ("Shopping", ["Clothing", "Electronics", "Books", "Home & Garden", "Groceries"]),
                ("Entertainment", ["Movies", "Games", "Sports", "Hobbies", "Subscriptions"]),
                ("Bills & Utilities", ["Electricity", "Water", "Internet", "Phone", "Insurance"]),
                ("Healthcare", ["Doctor", "Pharmacy", "Dental", "Vision", "Fitness"]),
                ("Education", ["Courses", "Books", "Supplies", "Tuition", "Certification"]),
                ("Travel", ["Flights", "Hotels", "Car Rental", "Activities", "Food"]),
                ("Personal Care", ["Haircut", "Cosmetics", "Spa", "Clothing", "Accessories"]),
                ("Gifts & Donations", ["Gifts", "Charity", "Tips", "Religious", "Social"]),
                ("Business", ["Office Supplies", "Software", "Equipment", "Travel", "Meals"]),
                ("Other", ["Miscellaneous", "Unexpected", "Emergency", "Investment", "Savings"])
            ]
            
            categories_data = []
            cat_id = 1
            
            for category, subcategories in default_categories:
                for subcategory in subcategories:
                    categories_data.append({
                        'id': cat_id,
                        'category': category,
                        'sub_category': subcategory,
                        'is_active': True,
                        'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    })
                    cat_id += 1
            
            df = pd.DataFrame(categories_data)
            self.data_manager.write_csv(self.module_name, self.categories_filename, df)
    
    def get_all_expenses(self) -> pd.DataFrame:
        """Get all expense records"""
        return self.data_manager.read_csv(
            self.module_name, 
            self.filename, 
            self.default_columns
        )
    
    def add_expense(self, expense: ExpenseRecord) -> bool:
        """Add a new expense record"""
        errors = expense.validate()
        if errors:
            self.data_manager.error_occurred.emit(f"Validation errors: {', '.join(errors)}")
            return False
        
        return self.data_manager.append_row(
            self.module_name,
            self.filename,
            expense.to_dict(),
            self.default_columns
        )
    
    def update_expense(self, expense_id: int, expense: ExpenseRecord) -> bool:
        """Update an existing expense record"""
        errors = expense.validate()
        if errors:
            self.data_manager.error_occurred.emit(f"Validation errors: {', '.join(errors)}")
            return False
        
        return self.data_manager.update_row(
            self.module_name,
            self.filename,
            expense_id,
            expense.to_dict()
        )
    
    def delete_expense(self, expense_id: int) -> bool:
        """Delete an expense record"""
        return self.data_manager.delete_row(
            self.module_name,
            self.filename,
            expense_id
        )
    
    def get_expense_by_id(self, expense_id: int) -> Optional[ExpenseRecord]:
        """Get a specific expense by ID"""
        df = self.get_all_expenses()
        if df.empty:
            return None
        
        expense_data = df[df['id'] == expense_id]
        if expense_data.empty:
            return None
        
        return ExpenseRecord.from_dict(expense_data.iloc[0].to_dict())
    
    def search_expenses(self, search_term: str) -> pd.DataFrame:
        """Search expenses by term"""
        search_columns = ['category', 'sub_category', 'notes', 'transaction_mode']
        return self.data_manager.search_data(
            self.module_name,
            self.filename,
            search_term,
            search_columns
        )
    
    def get_expenses_by_date_range(self, start_date: date, end_date: date) -> pd.DataFrame:
        """Get expenses within a date range"""
        df = self.get_all_expenses()
        if df.empty:
            return df
        
        # Convert date column to datetime for filtering
        df['date'] = pd.to_datetime(df['date'])
        
        # Filter by date range
        mask = (df['date'] >= pd.Timestamp(start_date)) & (df['date'] <= pd.Timestamp(end_date))
        return df[mask]
    
    def get_expenses_by_category(self, category: str, subcategory: str = None) -> pd.DataFrame:
        """Get expenses by category and optionally subcategory"""
        df = self.get_all_expenses()
        if df.empty:
            return df
        
        # Filter by category
        df = df[df['category'] == category]
        
        # Filter by subcategory if provided
        if subcategory:
            df = df[df['sub_category'] == subcategory]
        
        return df
    
    def get_categories(self) -> pd.DataFrame:
        """Get all categories and subcategories"""
        return self.data_manager.read_csv(
            self.module_name,
            self.categories_filename,
            self.categories_columns
        )
    
    def get_category_list(self) -> List[str]:
        """Get list of unique categories"""
        df = self.get_categories()
        if df.empty:
            return []
        
        return sorted(df[df['is_active'] == True]['category'].unique().tolist())
    
    def get_subcategory_list(self, category: str) -> List[str]:
        """Get list of subcategories for a category"""
        df = self.get_categories()
        if df.empty:
            return []
        
        filtered_df = df[(df['category'] == category) & (df['is_active'] == True)]
        return sorted(filtered_df['sub_category'].unique().tolist())
    
    def add_category(self, category: str, subcategory: str) -> bool:
        """Add a new category/subcategory combination"""
        # Check if combination already exists
        df = self.get_categories()
        if not df.empty:
            existing = df[(df['category'] == category) & (df['sub_category'] == subcategory)]
            if not existing.empty:
                self.data_manager.error_occurred.emit("Category/subcategory combination already exists")
                return False
        
        category_data = {
            'category': category,
            'sub_category': subcategory,
            'is_active': True,
            'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        return self.data_manager.append_row(
            self.module_name,
            self.categories_filename,
            category_data,
            self.categories_columns
        )
    
    def get_expense_summary(self) -> Dict[str, Any]:
        """Get expense summary statistics"""
        df = self.get_all_expenses()
        
        if df.empty:
            return {
                'total_expenses': 0,
                'total_amount': 0.0,
                'average_amount': 0.0,
                'categories_count': 0,
                'this_month_amount': 0.0,
                'this_week_amount': 0.0
            }
        
        # Convert date column
        df['date'] = pd.to_datetime(df['date'])
        
        # Calculate summary statistics
        total_expenses = len(df)
        total_amount = df['amount'].sum()
        average_amount = df['amount'].mean()
        categories_count = df['category'].nunique()
        
        # This month expenses
        current_month = datetime.now().replace(day=1)
        this_month_df = df[df['date'] >= current_month]
        this_month_amount = this_month_df['amount'].sum()
        
        # This week expenses
        current_week = datetime.now() - pd.Timedelta(days=7)
        this_week_df = df[df['date'] >= current_week]
        this_week_amount = this_week_df['amount'].sum()
        
        return {
            'total_expenses': total_expenses,
            'total_amount': float(total_amount),
            'average_amount': float(average_amount),
            'categories_count': categories_count,
            'this_month_amount': float(this_month_amount),
            'this_week_amount': float(this_week_amount)
        }
