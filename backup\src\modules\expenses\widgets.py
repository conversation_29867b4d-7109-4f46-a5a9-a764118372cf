"""
Expense Tracker UI Widgets
Contains all UI components for the expense tracking module
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QFormLayout,
    QLabel, QPushButton, QLineEdit, QComboBox, QDateEdit, QTextEdit,
    QTableWidget, QTableWidgetItem, QHeaderView, QAbstractItemView,
    QFrame, QGroupBox, QSplitter, QTabWidget, QSpinBox, QDoubleSpinBox,
    QMessageBox, QDialog, QDialogButtonBox, QCheckBox, QProgressBar
)
from PySide6.QtCore import Qt, Signal, QDate, QTimer, QSortFilterProxyModel
from PySide6.QtGui import QFont, QIcon, QPixmap, QStandardItemModel, QStandardItem, QColor

from datetime import datetime, date
from typing import Dict, List, Any, Optional

from .models import ExpenseRecord, ExpenseDataModel


class ExpenseEntryDialog(QDialog):
    """Dialog for adding/editing expense entries"""
    
    expense_saved = Signal(dict)
    
    def __init__(self, data_model: ExpenseDataModel, expense: ExpenseRecord = None, parent=None):
        super().__init__(parent)
        
        self.data_model = data_model
        self.expense = expense
        self.is_edit_mode = expense is not None
        
        self.setup_ui()
        self.setup_connections()
        
        if self.is_edit_mode:
            self.populate_fields()
        
        self.setModal(True)
    
    def setup_ui(self):
        """Setup the dialog UI"""
        self.setWindowTitle("Edit Expense" if self.is_edit_mode else "Add New Expense")
        self.setMinimumSize(400, 500)
        self.resize(450, 550)
        
        # Main layout
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # Form layout
        form_frame = QFrame()
        form_frame.setObjectName("expenseFormFrame")
        form_layout = QFormLayout(form_frame)
        form_layout.setSpacing(10)
        
        # Date field
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setObjectName("expenseDateEdit")
        form_layout.addRow("Date:", self.date_edit)
        
        # Transaction type
        self.type_combo = QComboBox()
        self.type_combo.addItems(["Expense", "Income", "Transfer"])
        self.type_combo.setCurrentText("Expense")
        self.type_combo.setObjectName("expenseTypeCombo")
        form_layout.addRow("Type:", self.type_combo)
        
        # Category
        self.category_combo = QComboBox()
        self.category_combo.setEditable(True)
        self.category_combo.setObjectName("expenseCategoryCombo")
        self.populate_categories()
        form_layout.addRow("Category:", self.category_combo)
        
        # Sub-category
        self.subcategory_combo = QComboBox()
        self.subcategory_combo.setEditable(True)
        self.subcategory_combo.setObjectName("expenseSubcategoryCombo")
        form_layout.addRow("Sub-category:", self.subcategory_combo)
        
        # Transaction mode
        self.transaction_mode_combo = QComboBox()
        self.transaction_mode_combo.addItems([
            "Cash", "Credit Card", "Debit Card", "UPI", "Net Banking",
            "Wallet", "Cheque", "Bank Transfer", "Other"
        ])
        self.transaction_mode_combo.setObjectName("expenseTransactionModeCombo")
        form_layout.addRow("Payment Mode:", self.transaction_mode_combo)
        
        # Amount
        self.amount_spinbox = QDoubleSpinBox()
        self.amount_spinbox.setRange(0.01, 999999.99)
        self.amount_spinbox.setDecimals(2)
        self.amount_spinbox.setPrefix("₹ ")
        self.amount_spinbox.setObjectName("expenseAmountSpinbox")
        form_layout.addRow("Amount:", self.amount_spinbox)
        
        # Notes
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)
        self.notes_edit.setPlaceholderText("Optional notes about this expense...")
        self.notes_edit.setObjectName("expenseNotesEdit")
        form_layout.addRow("Notes:", self.notes_edit)
        
        layout.addWidget(form_frame)
        
        # Button box
        button_box = QDialogButtonBox(
            QDialogButtonBox.Save | QDialogButtonBox.Cancel
        )
        button_box.setObjectName("expenseDialogButtonBox")
        layout.addWidget(button_box)
        
        # Store references
        self.button_box = button_box
    
    def setup_connections(self):
        """Setup signal connections"""
        self.button_box.accepted.connect(self.save_expense)
        self.button_box.rejected.connect(self.reject)
        self.category_combo.currentTextChanged.connect(self.on_category_changed)
    
    def populate_categories(self):
        """Populate category dropdown"""
        categories = self.data_model.get_category_list()
        self.category_combo.clear()
        self.category_combo.addItems(categories)
    
    def on_category_changed(self, category: str):
        """Handle category change"""
        if category:
            subcategories = self.data_model.get_subcategory_list(category)
            self.subcategory_combo.clear()
            self.subcategory_combo.addItems(subcategories)
    
    def populate_fields(self):
        """Populate fields with existing expense data"""
        if not self.expense:
            return
        
        # Set date
        if isinstance(self.expense.date, date):
            self.date_edit.setDate(QDate(self.expense.date))
        
        # Set other fields
        self.type_combo.setCurrentText(self.expense.type)
        self.category_combo.setCurrentText(self.expense.category)
        self.on_category_changed(self.expense.category)  # Populate subcategories
        self.subcategory_combo.setCurrentText(self.expense.sub_category)
        self.transaction_mode_combo.setCurrentText(self.expense.transaction_mode)
        self.amount_spinbox.setValue(self.expense.amount)
        self.notes_edit.setPlainText(self.expense.notes)
    
    def save_expense(self):
        """Save the expense"""
        try:
            # Create expense record
            expense_data = ExpenseRecord(
                id=self.expense.id if self.is_edit_mode else None,
                date=self.date_edit.date().toPython(),
                type=self.type_combo.currentText(),
                category=self.category_combo.currentText(),
                sub_category=self.subcategory_combo.currentText(),
                transaction_mode=self.transaction_mode_combo.currentText(),
                amount=self.amount_spinbox.value(),
                notes=self.notes_edit.toPlainText()
            )
            
            # Validate
            errors = expense_data.validate()
            if errors:
                QMessageBox.warning(self, "Validation Error", "\n".join(errors))
                return
            
            # Save to data model
            if self.is_edit_mode:
                success = self.data_model.update_expense(self.expense.id, expense_data)
            else:
                success = self.data_model.add_expense(expense_data)
            
            if success:
                self.expense_saved.emit(expense_data.to_dict())
                self.accept()
            else:
                QMessageBox.critical(self, "Error", "Failed to save expense")
        
        except Exception as e:
            QMessageBox.critical(self, "Error", f"An error occurred: {str(e)}")


class ExpenseTableWidget(QTableWidget):
    """Custom table widget for displaying expenses with multi-select and labeling"""

    expense_selected = Signal(int)  # expense_id
    expense_edit_requested = Signal(int)  # expense_id
    expense_delete_requested = Signal(int)  # expense_id
    expenses_multi_selected = Signal(list)  # list of expense_ids
    label_requested = Signal(list)  # list of expense_ids for labeling
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.setup_table()
        self.setup_connections()
    
    def setup_table(self):
        """Setup table properties"""
        # Set table properties
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.setSelectionMode(QAbstractItemView.ExtendedSelection)  # Enable multi-selection
        self.setSortingEnabled(True)
        self.setObjectName("expenseTable")

        # Enable context menu for labeling
        self.setContextMenuPolicy(Qt.CustomContextMenu)
        self.customContextMenuRequested.connect(self.show_context_menu)
        
        # Set columns
        columns = ["ID", "Date", "Type", "Category", "Sub-category", "Mode", "Amount", "Notes"]
        self.setColumnCount(len(columns))
        self.setHorizontalHeaderLabels(columns)
        
        # Configure header
        header = self.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # ID
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # Date
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # Type
        header.setSectionResizeMode(3, QHeaderView.Stretch)           # Category
        header.setSectionResizeMode(4, QHeaderView.Stretch)           # Sub-category
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Mode
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # Amount
        
        # Hide ID column
        self.setColumnHidden(0, True)
    
    def setup_connections(self):
        """Setup signal connections"""
        self.itemDoubleClicked.connect(self.on_item_double_clicked)
        self.itemSelectionChanged.connect(self.on_selection_changed)
    
    def populate_table(self, expenses_df):
        """Populate table with expense data"""
        self.setRowCount(0)
        
        if expenses_df.empty:
            return
        
        # Sort by date (newest first)
        expenses_df = expenses_df.sort_values('date', ascending=False)
        
        self.setRowCount(len(expenses_df))
        
        for row, (_, expense) in enumerate(expenses_df.iterrows()):
            # ID (hidden)
            self.setItem(row, 0, QTableWidgetItem(str(expense['id'])))
            
            # Date
            date_str = expense['date']
            if isinstance(date_str, str):
                try:
                    date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()
                    date_str = date_obj.strftime('%d/%m/%Y')
                except ValueError:
                    pass
            elif hasattr(date_str, 'strftime'):
                # Handle pandas Timestamp objects
                date_str = date_str.strftime('%d/%m/%Y')
            else:
                date_str = str(date_str)
            self.setItem(row, 1, QTableWidgetItem(str(date_str)))
            
            # Type
            self.setItem(row, 2, QTableWidgetItem(str(expense['type'])))
            
            # Category
            self.setItem(row, 3, QTableWidgetItem(str(expense['category'])))
            
            # Sub-category
            self.setItem(row, 4, QTableWidgetItem(str(expense['sub_category'])))
            
            # Transaction mode
            self.setItem(row, 5, QTableWidgetItem(str(expense['transaction_mode'])))
            
            # Amount
            amount_item = QTableWidgetItem(f"₹{expense['amount']:.2f}")
            amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.setItem(row, 6, amount_item)
            
            # Notes
            notes = str(expense['notes']) if expense['notes'] else ""
            if len(notes) > 50:
                notes = notes[:47] + "..."
            self.setItem(row, 7, QTableWidgetItem(notes))
    
    def on_item_double_clicked(self, item):
        """Handle item double click"""
        row = item.row()
        expense_id = int(self.item(row, 0).text())
        self.expense_edit_requested.emit(expense_id)
    
    def on_selection_changed(self):
        """Handle selection change"""
        selected_rows = self.selectionModel().selectedRows()

        if len(selected_rows) == 1:
            # Single selection - emit single expense selected
            row = selected_rows[0].row()
            expense_id = int(self.item(row, 0).text())
            self.expense_selected.emit(expense_id)
        elif len(selected_rows) > 1:
            # Multi-selection - emit list of expense IDs
            expense_ids = []
            for index in selected_rows:
                row = index.row()
                expense_id = int(self.item(row, 0).text())
                expense_ids.append(expense_id)
            self.expenses_multi_selected.emit(expense_ids)

    def get_selected_expense_id(self) -> Optional[int]:
        """Get the ID of the currently selected expense (single selection)"""
        current_row = self.currentRow()
        if current_row >= 0:
            return int(self.item(current_row, 0).text())
        return None

    def get_selected_expense_ids(self) -> List[int]:
        """Get the IDs of all currently selected expenses"""
        selected_rows = self.selectionModel().selectedRows()
        expense_ids = []
        for index in selected_rows:
            row = index.row()
            expense_id = int(self.item(row, 0).text())
            expense_ids.append(expense_id)
        return expense_ids

    def show_context_menu(self, position):
        """Show context menu for selected expenses"""
        from PySide6.QtWidgets import QMenu

        selected_ids = self.get_selected_expense_ids()
        if not selected_ids:
            return

        menu = QMenu(self)

        # Add label action
        if len(selected_ids) == 1:
            label_action = menu.addAction("🏷️ Add Label")
        else:
            label_action = menu.addAction(f"🏷️ Add Label to {len(selected_ids)} expenses")

        label_action.triggered.connect(lambda: self.label_requested.emit(selected_ids))

        # Add other actions
        menu.addSeparator()

        if len(selected_ids) == 1:
            edit_action = menu.addAction("✏️ Edit")
            edit_action.triggered.connect(lambda: self.expense_edit_requested.emit(selected_ids[0]))

        delete_action = menu.addAction(f"🗑️ Delete {len(selected_ids)} expense(s)")
        delete_action.triggered.connect(lambda: self.request_delete_multiple(selected_ids))

        # Show menu
        menu.exec(self.mapToGlobal(position))

    def request_delete_multiple(self, expense_ids: List[int]):
        """Request deletion of multiple expenses"""
        for expense_id in expense_ids:
            self.expense_delete_requested.emit(expense_id)


class ExpenseStatsWidget(QWidget):
    """Widget displaying expense statistics"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the statistics UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        
        # Title
        title_label = QLabel("Expense Statistics")
        title_label.setObjectName("expenseStatsTitle")
        font = QFont()
        font.setBold(True)
        font.setPointSize(12)
        title_label.setFont(font)
        layout.addWidget(title_label)
        
        # Stats grid
        stats_frame = QFrame()
        stats_frame.setObjectName("expenseStatsFrame")
        stats_layout = QGridLayout(stats_frame)
        
        # Create stat labels
        self.total_expenses_label = QLabel("0")
        self.total_amount_label = QLabel("₹0.00")
        self.average_amount_label = QLabel("₹0.00")
        self.this_month_label = QLabel("₹0.00")
        
        # Style stat labels
        for label in [self.total_expenses_label, self.total_amount_label, 
                     self.average_amount_label, self.this_month_label]:
            label.setObjectName("expenseStatValue")
            font = QFont()
            font.setBold(True)
            font.setPointSize(14)
            label.setFont(font)
            label.setAlignment(Qt.AlignCenter)
        
        # Add to layout
        stats_layout.addWidget(QLabel("Total Expenses:"), 0, 0)
        stats_layout.addWidget(self.total_expenses_label, 0, 1)
        
        stats_layout.addWidget(QLabel("Total Amount:"), 1, 0)
        stats_layout.addWidget(self.total_amount_label, 1, 1)
        
        stats_layout.addWidget(QLabel("Average Amount:"), 2, 0)
        stats_layout.addWidget(self.average_amount_label, 2, 1)
        
        stats_layout.addWidget(QLabel("This Month:"), 3, 0)
        stats_layout.addWidget(self.this_month_label, 3, 1)
        
        layout.addWidget(stats_frame)
        layout.addStretch()
    
    def update_stats(self, stats: Dict[str, Any]):
        """Update the statistics display"""
        self.total_expenses_label.setText(str(stats.get('total_expenses', 0)))
        self.total_amount_label.setText(f"₹{stats.get('total_amount', 0):.2f}")
        self.average_amount_label.setText(f"₹{stats.get('average_amount', 0):.2f}")
        self.this_month_label.setText(f"₹{stats.get('this_month_amount', 0):.2f}")


class CategoryManagementDialog(QDialog):
    """Dialog for managing expense categories"""
    
    categories_updated = Signal()
    
    def __init__(self, data_model: ExpenseDataModel, parent=None):
        super().__init__(parent)
        
        self.data_model = data_model
        self.setup_ui()
        self.setup_connections()
        self.load_categories()
        
        self.setModal(True)
    
    def setup_ui(self):
        """Setup the dialog UI"""
        self.setWindowTitle("Manage Categories")
        self.setMinimumSize(500, 400)
        
        layout = QVBoxLayout(self)
        
        # Add new category section
        add_frame = QGroupBox("Add New Category")
        add_layout = QFormLayout(add_frame)
        
        self.new_category_edit = QLineEdit()
        self.new_subcategory_edit = QLineEdit()
        add_button = QPushButton("Add Category")
        add_button.clicked.connect(self.add_category)
        
        add_layout.addRow("Category:", self.new_category_edit)
        add_layout.addRow("Sub-category:", self.new_subcategory_edit)
        add_layout.addRow("", add_button)
        
        layout.addWidget(add_frame)
        
        # Categories table
        self.categories_table = QTableWidget()
        self.categories_table.setColumnCount(3)
        self.categories_table.setHorizontalHeaderLabels(["Category", "Sub-category", "Active"])
        layout.addWidget(self.categories_table)
        
        # Close button
        close_button = QPushButton("Close")
        close_button.clicked.connect(self.accept)
        layout.addWidget(close_button)
    
    def setup_connections(self):
        """Setup signal connections"""
        pass
    
    def load_categories(self):
        """Load categories into the table"""
        df = self.data_model.get_categories()
        
        if df.empty:
            return
        
        self.categories_table.setRowCount(len(df))
        
        for row, (_, category) in enumerate(df.iterrows()):
            self.categories_table.setItem(row, 0, QTableWidgetItem(str(category['category'])))
            self.categories_table.setItem(row, 1, QTableWidgetItem(str(category['sub_category'])))
            
            checkbox = QCheckBox()
            checkbox.setChecked(bool(category['is_active']))
            self.categories_table.setCellWidget(row, 2, checkbox)
    
    def add_category(self):
        """Add a new category"""
        category = self.new_category_edit.text().strip()
        subcategory = self.new_subcategory_edit.text().strip()
        
        if not category or not subcategory:
            QMessageBox.warning(self, "Error", "Both category and sub-category are required")
            return
        
        if self.data_model.add_category(category, subcategory):
            self.new_category_edit.clear()
            self.new_subcategory_edit.clear()
            self.load_categories()
            self.categories_updated.emit()
            QMessageBox.information(self, "Success", "Category added successfully")
        else:
            QMessageBox.warning(self, "Error", "Failed to add category")


class LabelManagementDialog(QDialog):
    """Dialog for adding labels to expenses"""

    def __init__(self, expense_ids: List[int], data_model, parent=None):
        super().__init__(parent)
        self.expense_ids = expense_ids
        self.data_model = data_model
        self.setWindowTitle(f"Add Label to {len(expense_ids)} Expense(s)")
        self.setModal(True)
        self.resize(400, 300)
        self.setup_ui()
        self.load_existing_labels()

    def setup_ui(self):
        """Setup the label management UI"""
        layout = QVBoxLayout(self)

        # Info label
        info_text = f"Adding label to {len(self.expense_ids)} expense(s)"
        info_label = QLabel(info_text)
        info_label.setFont(QFont("Arial", 10, QFont.Bold))
        layout.addWidget(info_label)

        # Label input
        label_frame = QFrame()
        label_layout = QFormLayout(label_frame)

        self.label_edit = QLineEdit()
        self.label_edit.setPlaceholderText("Enter label name (e.g., 'Business', 'Personal', 'Tax Deductible')")
        label_layout.addRow("Label:", self.label_edit)

        # Color selection (optional)
        self.color_combo = QComboBox()
        colors = [
            ("Default", "#808080"),
            ("Red", "#f44336"),
            ("Green", "#4caf50"),
            ("Blue", "#2196f3"),
            ("Orange", "#ff9800"),
            ("Purple", "#9c27b0"),
            ("Teal", "#009688"),
            ("Pink", "#e91e63")
        ]

        for color_name, color_code in colors:
            self.color_combo.addItem(color_name, color_code)

        label_layout.addRow("Color:", self.color_combo)

        layout.addWidget(label_frame)

        # Existing labels
        existing_frame = QFrame()
        existing_layout = QVBoxLayout(existing_frame)

        existing_title = QLabel("Existing Labels:")
        existing_title.setFont(QFont("Arial", 10, QFont.Bold))
        existing_layout.addWidget(existing_title)

        self.existing_labels_list = QTableWidget()
        self.existing_labels_list.setColumnCount(3)
        self.existing_labels_list.setHorizontalHeaderLabels(["Label", "Color", "Count"])
        self.existing_labels_list.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.existing_labels_list.itemDoubleClicked.connect(self.on_existing_label_selected)
        existing_layout.addWidget(self.existing_labels_list)

        layout.addWidget(existing_frame)

        # Buttons
        button_layout = QHBoxLayout()

        apply_button = QPushButton("Apply Label")
        apply_button.clicked.connect(self.apply_label)
        button_layout.addWidget(apply_button)

        cancel_button = QPushButton("Cancel")
        cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(cancel_button)

        layout.addLayout(button_layout)

    def load_existing_labels(self):
        """Load existing labels from the database"""
        # This would load from a labels table if implemented
        # For now, show some example labels
        example_labels = [
            ("Business", "#2196f3", 15),
            ("Personal", "#4caf50", 23),
            ("Tax Deductible", "#ff9800", 8),
            ("Emergency", "#f44336", 3),
            ("Recurring", "#9c27b0", 12)
        ]

        self.existing_labels_list.setRowCount(len(example_labels))

        for row, (label, color, count) in enumerate(example_labels):
            self.existing_labels_list.setItem(row, 0, QTableWidgetItem(label))

            color_item = QTableWidgetItem()
            color_item.setBackground(QColor(color))
            color_item.setText(color)
            self.existing_labels_list.setItem(row, 1, color_item)

            self.existing_labels_list.setItem(row, 2, QTableWidgetItem(str(count)))

    def on_existing_label_selected(self, item):
        """Handle selection of existing label"""
        row = item.row()
        label_name = self.existing_labels_list.item(row, 0).text()
        self.label_edit.setText(label_name)

    def apply_label(self):
        """Apply the label to selected expenses"""
        label_text = self.label_edit.text().strip()
        if not label_text:
            QMessageBox.warning(self, "Error", "Please enter a label name")
            return

        color_code = self.color_combo.currentData()

        # Here you would implement the actual labeling logic
        # For now, just show a success message
        QMessageBox.information(
            self,
            "Success",
            f"Label '{label_text}' applied to {len(self.expense_ids)} expense(s)"
        )

        self.accept()


class ExpenseTrackerWidget(QWidget):
    """Main expense tracker widget"""

    def __init__(self, data_manager, config, parent=None):
        super().__init__(parent)

        self.data_manager = data_manager
        self.config = config
        self.expense_model = ExpenseDataModel(data_manager)

        self.setup_ui()
        self.setup_connections()
        self.refresh_data()

        # Setup auto-refresh timer
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_data)
        self.refresh_timer.start(30000)  # Refresh every 30 seconds

    def setup_ui(self):
        """Setup the main UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)  # Reduced from 20px to 10px
        layout.setSpacing(8)  # Reduced from 15px to 8px

        # Header
        self.create_header(layout)

        # Main content splitter
        splitter = QSplitter(Qt.Horizontal)

        # Left panel (table and controls)
        left_panel = self.create_left_panel()
        splitter.addWidget(left_panel)

        # Right panel (statistics and filters)
        right_panel = self.create_right_panel()
        splitter.addWidget(right_panel)

        # Set splitter proportions
        splitter.setSizes([700, 300])
        layout.addWidget(splitter)

    def create_header(self, layout):
        """Create header with title and action buttons"""
        header_frame = QFrame()
        header_frame.setObjectName("expenseHeader")
        header_layout = QHBoxLayout(header_frame)

        # Title
        title_label = QLabel("Expense Tracker")
        title_label.setObjectName("expenseTitle")
        font = QFont()
        font.setBold(True)
        font.setPointSize(16)
        title_label.setFont(font)
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        # Action buttons
        self.add_button = QPushButton("Add Expense")
        self.add_button.setObjectName("expenseAddButton")
        self.add_button.setMinimumHeight(35)
        header_layout.addWidget(self.add_button)

        self.edit_button = QPushButton("Edit")
        self.edit_button.setObjectName("expenseEditButton")
        self.edit_button.setMinimumHeight(35)
        self.edit_button.setEnabled(False)
        header_layout.addWidget(self.edit_button)

        self.delete_button = QPushButton("Delete")
        self.delete_button.setObjectName("expenseDeleteButton")
        self.delete_button.setMinimumHeight(35)
        self.delete_button.setEnabled(False)
        header_layout.addWidget(self.delete_button)

        self.categories_button = QPushButton("Manage Categories")
        self.categories_button.setObjectName("expenseCategoriesButton")
        self.categories_button.setMinimumHeight(35)
        header_layout.addWidget(self.categories_button)

        layout.addWidget(header_frame)

    def create_left_panel(self):
        """Create left panel with table and search"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(0, 0, 0, 0)

        # Search and filter bar
        search_frame = QFrame()
        search_frame.setObjectName("expenseSearchFrame")
        search_layout = QHBoxLayout(search_frame)

        # Search box
        search_label = QLabel("Search:")
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("Search expenses...")
        self.search_edit.setObjectName("expenseSearchEdit")

        # Category filter
        category_label = QLabel("Category:")
        self.category_filter = QComboBox()
        self.category_filter.addItem("All Categories")
        self.category_filter.setObjectName("expenseCategoryFilter")

        # Clear filters button
        self.clear_filters_button = QPushButton("Clear Filters")
        self.clear_filters_button.setObjectName("expenseClearFiltersButton")

        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_edit)
        search_layout.addWidget(category_label)
        search_layout.addWidget(self.category_filter)
        search_layout.addWidget(self.clear_filters_button)
        search_layout.addStretch()

        layout.addWidget(search_frame)

        # Expense table
        self.expense_table = ExpenseTableWidget()
        layout.addWidget(self.expense_table)

        return panel

    def create_right_panel(self):
        """Create right panel with statistics and quick actions"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(0, 0, 0, 0)

        # Statistics widget
        self.stats_widget = ExpenseStatsWidget()
        layout.addWidget(self.stats_widget)

        # Quick actions - more compact and visible
        actions_frame = QGroupBox("⚡ Quick Actions")
        actions_frame.setObjectName("expenseQuickActions")
        actions_frame.setMaximumHeight(200)  # Limit height for better visibility
        actions_layout = QVBoxLayout(actions_frame)
        actions_layout.setSpacing(5)  # Reduced spacing

        # Quick add buttons for common categories
        quick_food_button = QPushButton("Quick Add: Food")
        quick_food_button.setObjectName("expenseQuickButton")
        quick_food_button.clicked.connect(lambda: self.quick_add_expense("Food & Dining", "Restaurants"))
        actions_layout.addWidget(quick_food_button)

        quick_transport_button = QPushButton("Quick Add: Transport")
        quick_transport_button.setObjectName("expenseQuickButton")
        quick_transport_button.clicked.connect(lambda: self.quick_add_expense("Transportation", "Fuel"))
        actions_layout.addWidget(quick_transport_button)

        quick_shopping_button = QPushButton("Quick Add: Shopping")
        quick_shopping_button.setObjectName("expenseQuickButton")
        quick_shopping_button.clicked.connect(lambda: self.quick_add_expense("Shopping", "Clothing"))
        actions_layout.addWidget(quick_shopping_button)

        actions_layout.addStretch()
        layout.addWidget(actions_frame)

        layout.addStretch()
        return panel

    def setup_connections(self):
        """Setup signal connections"""
        # Button connections
        self.add_button.clicked.connect(self.add_expense)
        self.edit_button.clicked.connect(self.edit_expense)
        self.delete_button.clicked.connect(self.delete_expense)
        self.categories_button.clicked.connect(self.manage_categories)
        self.clear_filters_button.clicked.connect(self.clear_filters)

        # Table connections
        self.expense_table.expense_selected.connect(self.on_expense_selected)
        self.expense_table.expense_edit_requested.connect(self.edit_expense_by_id)
        self.expense_table.expense_delete_requested.connect(self.delete_expense_by_id)
        self.expense_table.expenses_multi_selected.connect(self.on_expenses_multi_selected)
        self.expense_table.label_requested.connect(self.show_label_dialog)

        # Search and filter connections
        self.search_edit.textChanged.connect(self.apply_filters)
        self.category_filter.currentTextChanged.connect(self.apply_filters)

        # Data model connections
        self.data_manager.data_changed.connect(self.on_data_changed)

    def refresh_data(self):
        """Refresh all data with error handling"""
        try:
            self.load_expenses()
            self.update_statistics()
            self.populate_category_filter()
        except Exception as e:
            print(f"Error refreshing expense data: {e}")
            # Show user-friendly message
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.warning(self, "Data Refresh Error",
                              "There was an issue refreshing the expense data. Please try again.")
            # Load empty state gracefully
            self.expense_table.setRowCount(0)

    def load_expenses(self):
        """Load expenses into the table"""
        df = self.expense_model.get_all_expenses()
        self.expense_table.populate_table(df)

    def update_statistics(self):
        """Update statistics display"""
        stats = self.expense_model.get_expense_summary()
        self.stats_widget.update_stats(stats)

    def populate_category_filter(self):
        """Populate category filter dropdown"""
        current_text = self.category_filter.currentText()
        self.category_filter.clear()
        self.category_filter.addItem("All Categories")

        categories = self.expense_model.get_category_list()
        self.category_filter.addItems(categories)

        # Restore selection if possible
        index = self.category_filter.findText(current_text)
        if index >= 0:
            self.category_filter.setCurrentIndex(index)

    def add_expense(self):
        """Add new expense"""
        dialog = ExpenseEntryDialog(self.expense_model, parent=self)
        dialog.expense_saved.connect(self.on_expense_saved)
        dialog.exec()

    def edit_expense(self):
        """Edit selected expense"""
        expense_id = self.expense_table.get_selected_expense_id()
        if expense_id:
            self.edit_expense_by_id(expense_id)

    def edit_expense_by_id(self, expense_id: int):
        """Edit expense by ID"""
        expense = self.expense_model.get_expense_by_id(expense_id)
        if expense:
            dialog = ExpenseEntryDialog(self.expense_model, expense, parent=self)
            dialog.expense_saved.connect(self.on_expense_saved)
            dialog.exec()

    def delete_expense(self):
        """Delete selected expense"""
        expense_id = self.expense_table.get_selected_expense_id()
        if not expense_id:
            return

        reply = QMessageBox.question(
            self, "Confirm Delete",
            "Are you sure you want to delete this expense?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            if self.expense_model.delete_expense(expense_id):
                self.refresh_data()
                QMessageBox.information(self, "Success", "Expense deleted successfully")
            else:
                QMessageBox.critical(self, "Error", "Failed to delete expense")

    def manage_categories(self):
        """Open category management dialog"""
        dialog = CategoryManagementDialog(self.expense_model, parent=self)
        dialog.categories_updated.connect(self.populate_category_filter)
        dialog.exec()

    def quick_add_expense(self, category: str, subcategory: str):
        """Quick add expense with predefined category"""
        expense = ExpenseRecord(
            category=category,
            sub_category=subcategory
        )
        dialog = ExpenseEntryDialog(self.expense_model, expense, parent=self)
        dialog.expense_saved.connect(self.on_expense_saved)
        dialog.exec()

    def apply_filters(self):
        """Apply search and category filters"""
        search_term = self.search_edit.text().strip()
        category_filter = self.category_filter.currentText()

        if not search_term and category_filter == "All Categories":
            # No filters, show all
            self.load_expenses()
            return

        # Get all expenses
        df = self.expense_model.get_all_expenses()

        if df.empty:
            self.expense_table.populate_table(df)
            return

        # Apply search filter
        if search_term:
            mask = (
                df['category'].str.contains(search_term, case=False, na=False) |
                df['sub_category'].str.contains(search_term, case=False, na=False) |
                df['notes'].str.contains(search_term, case=False, na=False) |
                df['transaction_mode'].str.contains(search_term, case=False, na=False)
            )
            df = df[mask]

        # Apply category filter
        if category_filter != "All Categories":
            df = df[df['category'] == category_filter]

        self.expense_table.populate_table(df)

    def clear_filters(self):
        """Clear all filters"""
        self.search_edit.clear()
        self.category_filter.setCurrentIndex(0)
        self.load_expenses()

    def on_expense_selected(self, expense_id: int):
        """Handle expense selection"""
        self.edit_button.setEnabled(True)
        self.delete_button.setEnabled(True)

    def on_expense_saved(self, expense_data: dict):
        """Handle expense saved"""
        self.refresh_data()

    def on_data_changed(self, module: str, operation: str):
        """Handle data changes"""
        if module == "expenses":
            self.refresh_data()

    def on_expenses_multi_selected(self, expense_ids: List[int]):
        """Handle multiple expenses selected"""
        # Enable/disable buttons based on multi-selection
        self.edit_button.setEnabled(len(expense_ids) == 1)  # Only enable edit for single selection
        self.delete_button.setEnabled(len(expense_ids) > 0)  # Enable delete for any selection

        # Update status or show info about selected expenses
        if len(expense_ids) > 1:
            total_amount = 0
            for expense_id in expense_ids:
                # Get expense amount (simplified - in real implementation you'd query the data)
                # For now, just show count
                pass

            # You could show a status message about selected expenses
            print(f"Selected {len(expense_ids)} expenses")

    def delete_expense_by_id(self, expense_id: int):
        """Delete expense by ID"""
        reply = QMessageBox.question(
            self, "Delete Expense",
            "Are you sure you want to delete this expense?",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            if self.expense_model.delete_expense(expense_id):
                self.refresh_data()
                QMessageBox.information(self, "Success", "Expense deleted successfully")
            else:
                QMessageBox.warning(self, "Error", "Failed to delete expense")

    def show_label_dialog(self, expense_ids: List[int]):
        """Show label management dialog for selected expenses"""
        try:
            dialog = LabelManagementDialog(expense_ids, self.expense_model, self)
            dialog.exec()
        except Exception as e:
            QMessageBox.warning(self, "Error", f"Failed to open label dialog: {str(e)}")
