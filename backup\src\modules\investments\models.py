"""
Investment Portfolio Data Models
Handles investment tracking data structure and calculations
"""

import logging
import pandas as pd
from datetime import datetime, date, timedelta
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, asdict
from enum import Enum


class InvestmentType(Enum):
    """Investment types"""
    STOCKS = "Stocks"
    BONDS = "Bonds"
    MUTUAL_FUNDS = "Mutual Funds"
    ETF = "ETF"
    CRYPTO = "Cryptocurrency"
    REAL_ESTATE = "Real Estate"
    COMMODITIES = "Commodities"
    FIXED_DEPOSIT = "Fixed Deposit"
    SAVINGS = "Savings Account"
    OTHER = "Other"


class TransactionType(Enum):
    """Transaction types"""
    BUY = "Buy"
    SELL = "Sell"
    DIVIDEND = "Dividend"
    INTEREST = "Interest"
    SPLIT = "Stock Split"
    BONUS = "Bonus Shares"


@dataclass
class Investment:
    """Data class for individual investments"""
    id: Optional[int] = None
    symbol: str = ""  # Stock symbol or investment identifier
    name: str = ""  # Full name of investment
    investment_type: str = InvestmentType.STOCKS.value
    quantity: float = 0.0
    purchase_price: float = 0.0
    current_price: float = 0.0
    purchase_date: Optional[Union[str, datetime, date]] = None
    last_updated: Optional[datetime] = None
    notes: str = ""
    
    def __post_init__(self):
        """Post-initialization processing"""
        if self.last_updated is None:
            self.last_updated = datetime.now()
        
        # Handle purchase_date conversion
        if self.purchase_date and isinstance(self.purchase_date, str):
            try:
                self.purchase_date = datetime.strptime(self.purchase_date, '%Y-%m-%d').date()
            except ValueError:
                self.purchase_date = date.today()
        elif isinstance(self.purchase_date, datetime):
            self.purchase_date = self.purchase_date.date()
        elif self.purchase_date is None:
            self.purchase_date = date.today()
    
    def get_total_investment(self) -> float:
        """Get total investment amount"""
        return self.quantity * self.purchase_price
    
    def get_current_value(self) -> float:
        """Get current market value"""
        return self.quantity * self.current_price
    
    def get_profit_loss(self) -> float:
        """Get profit/loss amount"""
        return self.get_current_value() - self.get_total_investment()
    
    def get_profit_loss_percentage(self) -> float:
        """Get profit/loss percentage"""
        if self.get_total_investment() == 0:
            return 0.0
        return (self.get_profit_loss() / self.get_total_investment()) * 100
    
    def get_days_held(self) -> int:
        """Get number of days held"""
        if not self.purchase_date:
            return 0
        return (date.today() - self.purchase_date).days
    
    def get_annualized_return(self) -> float:
        """Get annualized return percentage"""
        days_held = self.get_days_held()
        if days_held == 0:
            return 0.0
        
        years_held = days_held / 365.25
        if years_held == 0:
            return 0.0
        
        total_return = self.get_profit_loss_percentage() / 100
        return ((1 + total_return) ** (1 / years_held) - 1) * 100
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for CSV storage"""
        data = asdict(self)
        
        # Convert date objects to strings
        if isinstance(data['purchase_date'], date):
            data['purchase_date'] = data['purchase_date'].strftime('%Y-%m-%d')
        if isinstance(data['last_updated'], datetime):
            data['last_updated'] = data['last_updated'].strftime('%Y-%m-%d %H:%M:%S')
        
        # Add calculated fields
        data['total_investment'] = self.get_total_investment()
        data['current_value'] = self.get_current_value()
        data['profit_loss'] = self.get_profit_loss()
        data['profit_loss_percentage'] = self.get_profit_loss_percentage()
        data['days_held'] = self.get_days_held()
        data['annualized_return'] = self.get_annualized_return()
        
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Investment':
        """Create from dictionary"""
        # Remove calculated fields
        calc_fields = ['total_investment', 'current_value', 'profit_loss', 
                      'profit_loss_percentage', 'days_held', 'annualized_return']
        for field in calc_fields:
            data.pop(field, None)
        
        # Handle datetime strings
        if 'last_updated' in data and isinstance(data['last_updated'], str) and data['last_updated']:
            try:
                data['last_updated'] = datetime.strptime(data['last_updated'], '%Y-%m-%d %H:%M:%S')
            except ValueError:
                data['last_updated'] = None
        
        return cls(**data)
    
    def validate(self) -> List[str]:
        """Validate the investment"""
        errors = []
        
        if not self.symbol.strip():
            errors.append("Symbol is required")
        
        if not self.name.strip():
            errors.append("Name is required")
        
        if self.investment_type not in [t.value for t in InvestmentType]:
            errors.append(f"Invalid investment type: {self.investment_type}")
        
        if self.quantity < 0:
            errors.append("Quantity cannot be negative")
        
        if self.purchase_price < 0:
            errors.append("Purchase price cannot be negative")
        
        if self.current_price < 0:
            errors.append("Current price cannot be negative")
        
        return errors


class InvestmentDataModel:
    """Data model for investment portfolio management"""
    
    def __init__(self, data_manager):
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self.logger.info("="*40)
        self.logger.info("INITIALIZING INVESTMENT DATA MODEL")
        self.logger.info("="*40)
        
        try:
            self.data_manager = data_manager
            self.module_name = "investments"
            self.filename = "investments.csv"
            
            # Default columns for CSV
            self.columns = [
                'id', 'symbol', 'name', 'investment_type', 'quantity',
                'purchase_price', 'current_price', 'purchase_date',
                'last_updated', 'notes', 'total_investment', 'current_value',
                'profit_loss', 'profit_loss_percentage', 'days_held', 'annualized_return'
            ]
            
            self.logger.info("✅ InvestmentDataModel initialization SUCCESSFUL")
            
        except Exception as e:
            self.logger.error(f"❌ CRITICAL ERROR in InvestmentDataModel.__init__: {e}")
            raise
    
    def get_all_investments(self) -> pd.DataFrame:
        """Get all investments"""
        try:
            df = self.data_manager.read_csv(self.module_name, self.filename, self.columns)
            return df
        except Exception as e:
            self.logger.error(f"Error getting investments: {e}")
            return pd.DataFrame(columns=self.columns)
    
    def add_investment(self, investment: Investment) -> bool:
        """Add a new investment"""
        errors = investment.validate()
        if errors:
            self.data_manager.error_occurred.emit(f"Validation errors: {', '.join(errors)}")
            return False
        
        return self.data_manager.append_row(
            self.module_name,
            self.filename,
            investment.to_dict(),
            self.columns
        )
    
    def update_investment(self, investment_id: int, investment: Investment) -> bool:
        """Update an existing investment"""
        errors = investment.validate()
        if errors:
            self.data_manager.error_occurred.emit(f"Validation errors: {', '.join(errors)}")
            return False
        
        return self.data_manager.update_row(
            self.module_name,
            self.filename,
            investment_id,
            investment.to_dict()
        )
    
    def delete_investment(self, investment_id: int) -> bool:
        """Delete an investment"""
        return self.data_manager.delete_row(self.module_name, self.filename, investment_id)
    
    def get_investments_by_type(self, investment_type: str) -> pd.DataFrame:
        """Get investments filtered by type"""
        df = self.get_all_investments()
        if df.empty:
            return df
        return df[df['investment_type'] == investment_type]
    
    def get_portfolio_summary(self) -> Dict[str, Any]:
        """Get portfolio summary statistics"""
        df = self.get_all_investments()
        
        if df.empty:
            return {
                'total_investments': 0,
                'total_investment_amount': 0.0,
                'total_current_value': 0.0,
                'total_profit_loss': 0.0,
                'total_profit_loss_percentage': 0.0,
                'best_performer': None,
                'worst_performer': None,
                'by_type': {},
                'average_return': 0.0
            }
        
        total_investment_amount = df['total_investment'].sum()
        total_current_value = df['current_value'].sum()
        total_profit_loss = df['profit_loss'].sum()
        
        total_profit_loss_percentage = 0.0
        if total_investment_amount > 0:
            total_profit_loss_percentage = (total_profit_loss / total_investment_amount) * 100
        
        # Best and worst performers
        best_performer = None
        worst_performer = None
        
        if not df.empty:
            best_idx = df['profit_loss_percentage'].idxmax()
            worst_idx = df['profit_loss_percentage'].idxmin()
            
            best_performer = {
                'symbol': df.loc[best_idx, 'symbol'],
                'name': df.loc[best_idx, 'name'],
                'return': df.loc[best_idx, 'profit_loss_percentage']
            }
            
            worst_performer = {
                'symbol': df.loc[worst_idx, 'symbol'],
                'name': df.loc[worst_idx, 'name'],
                'return': df.loc[worst_idx, 'profit_loss_percentage']
            }
        
        # Group by investment type
        by_type = {}
        for inv_type in df['investment_type'].unique():
            type_df = df[df['investment_type'] == inv_type]
            by_type[inv_type] = {
                'count': len(type_df),
                'total_investment': type_df['total_investment'].sum(),
                'current_value': type_df['current_value'].sum(),
                'profit_loss': type_df['profit_loss'].sum()
            }
        
        # Average return (weighted by investment amount)
        if total_investment_amount > 0:
            weighted_returns = (df['profit_loss_percentage'] * df['total_investment']).sum()
            average_return = weighted_returns / total_investment_amount
        else:
            average_return = 0.0
        
        return {
            'total_investments': len(df),
            'total_investment_amount': total_investment_amount,
            'total_current_value': total_current_value,
            'total_profit_loss': total_profit_loss,
            'total_profit_loss_percentage': total_profit_loss_percentage,
            'best_performer': best_performer,
            'worst_performer': worst_performer,
            'by_type': by_type,
            'average_return': average_return
        }
    
    def update_current_prices(self, price_updates: Dict[str, float]) -> int:
        """Update current prices for multiple investments"""
        df = self.get_all_investments()
        if df.empty:
            return 0
        
        updated_count = 0
        for symbol, new_price in price_updates.items():
            matching_rows = df[df['symbol'] == symbol]
            for idx, row in matching_rows.iterrows():
                investment = Investment.from_dict(row.to_dict())
                investment.current_price = new_price
                investment.last_updated = datetime.now()
                
                if self.update_investment(row['id'], investment):
                    updated_count += 1
        
        return updated_count
