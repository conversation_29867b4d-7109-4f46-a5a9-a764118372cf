"""
Investment Portfolio UI Widgets
Contains all UI components for the investment tracking module
"""

import logging
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QFormLayout,
    QLabel, QPushButton, QLineEdit, QComboBox, QDateEdit, QTextEdit,
    QCheckBox, QFrame, QGroupBox, QScrollArea, QTabWidget,
    QProgressBar, QSpinBox, QTableWidget, QTableWidgetItem, QHeaderView,
    QAbstractItemView, QSizePolicy, QButtonGroup, QDoubleSpinBox,
    QMessageBox, QDialog, QDialogButtonBox, QSplitter
)
from PySide6.QtCore import Qt, Signal, QDate, QTimer, QSize
from PySide6.QtGui import QFont, QIcon, QPixmap, QPalette, QColor

from datetime import datetime, date, timedelta
from typing import Dict, List, Any, Optional
import pandas as pd

from .models import Investment, InvestmentDataModel, InvestmentType


class InvestmentSummaryWidget(QFrame):
    """Widget for displaying portfolio summary"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the summary UI"""
        self.setFrameStyle(QFrame.Box)
        self.setStyleSheet("""
            QFrame {
                border: 1px solid #ddd;
                border-radius: 5px;
                padding: 15px;
                background-color: #f8f9fa;
            }
        """)
        
        layout = QGridLayout(self)
        
        # Title
        title_label = QLabel("📊 Portfolio Summary")
        title_label.setFont(QFont("Arial", 14, QFont.Bold))
        layout.addWidget(title_label, 0, 0, 1, 4)
        
        # Summary metrics
        self.total_investments_label = QLabel("Total Investments: 0")
        layout.addWidget(self.total_investments_label, 1, 0)
        
        self.total_invested_label = QLabel("Total Invested: ₹0.00")
        layout.addWidget(self.total_invested_label, 1, 1)
        
        self.current_value_label = QLabel("Current Value: ₹0.00")
        layout.addWidget(self.current_value_label, 1, 2)
        
        self.profit_loss_label = QLabel("P&L: ₹0.00 (0.00%)")
        layout.addWidget(self.profit_loss_label, 1, 3)
        
        # Performance indicators
        self.best_performer_label = QLabel("Best: N/A")
        self.best_performer_label.setStyleSheet("color: #28a745;")
        layout.addWidget(self.best_performer_label, 2, 0, 1, 2)
        
        self.worst_performer_label = QLabel("Worst: N/A")
        self.worst_performer_label.setStyleSheet("color: #dc3545;")
        layout.addWidget(self.worst_performer_label, 2, 2, 1, 2)
    
    def update_summary(self, summary: Dict[str, Any]):
        """Update summary display"""
        self.total_investments_label.setText(f"Total Investments: {summary['total_investments']}")
        self.total_invested_label.setText(f"Total Invested: ₹{summary['total_investment_amount']:,.2f}")
        self.current_value_label.setText(f"Current Value: ₹{summary['total_current_value']:,.2f}")
        
        # Color code profit/loss
        pl_amount = summary['total_profit_loss']
        pl_percentage = summary['total_profit_loss_percentage']
        pl_color = "#28a745" if pl_amount >= 0 else "#dc3545"
        pl_sign = "+" if pl_amount >= 0 else ""
        
        self.profit_loss_label.setText(f"P&L: {pl_sign}₹{pl_amount:,.2f} ({pl_sign}{pl_percentage:.2f}%)")
        self.profit_loss_label.setStyleSheet(f"color: {pl_color}; font-weight: bold;")
        
        # Best and worst performers
        if summary['best_performer']:
            best = summary['best_performer']
            self.best_performer_label.setText(f"Best: {best['symbol']} (+{best['return']:.2f}%)")
        else:
            self.best_performer_label.setText("Best: N/A")
        
        if summary['worst_performer']:
            worst = summary['worst_performer']
            self.worst_performer_label.setText(f"Worst: {worst['symbol']} ({worst['return']:.2f}%)")
        else:
            self.worst_performer_label.setText("Worst: N/A")


class InvestmentTableWidget(QTableWidget):
    """Table widget for displaying investments"""
    
    investment_selected = Signal(int)  # Emits investment ID
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_table()
    
    def setup_table(self):
        """Setup the table"""
        # Define columns
        self.columns = [
            'Symbol', 'Name', 'Type', 'Quantity', 'Purchase Price',
            'Current Price', 'Total Investment', 'Current Value',
            'P&L Amount', 'P&L %', 'Days Held', 'Annual Return %'
        ]
        
        self.setColumnCount(len(self.columns))
        self.setHorizontalHeaderLabels(self.columns)
        
        # Table settings
        self.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.setSelectionMode(QAbstractItemView.SingleSelection)
        self.setAlternatingRowColors(True)
        self.setSortingEnabled(True)
        
        # Resize columns
        header = self.horizontalHeader()
        header.setStretchLastSection(True)
        for i in range(len(self.columns)):
            header.setSectionResizeMode(i, QHeaderView.ResizeToContents)
        
        # Connect selection
        self.itemSelectionChanged.connect(self.on_selection_changed)
    
    def update_investments(self, df: pd.DataFrame):
        """Update table with investment data"""
        self.setRowCount(len(df))
        
        for row_idx, (_, row) in enumerate(df.iterrows()):
            # Symbol
            self.setItem(row_idx, 0, QTableWidgetItem(str(row['symbol'])))
            
            # Name
            self.setItem(row_idx, 1, QTableWidgetItem(str(row['name'])))
            
            # Type
            self.setItem(row_idx, 2, QTableWidgetItem(str(row['investment_type'])))
            
            # Quantity
            self.setItem(row_idx, 3, QTableWidgetItem(f"{row['quantity']:.2f}"))
            
            # Purchase Price
            self.setItem(row_idx, 4, QTableWidgetItem(f"₹{row['purchase_price']:.2f}"))
            
            # Current Price
            self.setItem(row_idx, 5, QTableWidgetItem(f"₹{row['current_price']:.2f}"))
            
            # Total Investment
            self.setItem(row_idx, 6, QTableWidgetItem(f"₹{row['total_investment']:,.2f}"))
            
            # Current Value
            self.setItem(row_idx, 7, QTableWidgetItem(f"₹{row['current_value']:,.2f}"))
            
            # P&L Amount
            pl_amount = row['profit_loss']
            pl_item = QTableWidgetItem(f"₹{pl_amount:,.2f}")
            pl_item.setForeground(QColor("#28a745" if pl_amount >= 0 else "#dc3545"))
            self.setItem(row_idx, 8, pl_item)
            
            # P&L Percentage
            pl_percentage = row['profit_loss_percentage']
            pl_pct_item = QTableWidgetItem(f"{pl_percentage:.2f}%")
            pl_pct_item.setForeground(QColor("#28a745" if pl_percentage >= 0 else "#dc3545"))
            self.setItem(row_idx, 9, pl_pct_item)
            
            # Days Held
            self.setItem(row_idx, 10, QTableWidgetItem(str(int(row['days_held']))))
            
            # Annual Return
            annual_return = row['annualized_return']
            annual_item = QTableWidgetItem(f"{annual_return:.2f}%")
            annual_item.setForeground(QColor("#28a745" if annual_return >= 0 else "#dc3545"))
            self.setItem(row_idx, 11, annual_item)
            
            # Store investment ID in first column
            self.item(row_idx, 0).setData(Qt.UserRole, row['id'])
    
    def on_selection_changed(self):
        """Handle selection change"""
        current_row = self.currentRow()
        if current_row >= 0:
            item = self.item(current_row, 0)
            if item:
                investment_id = item.data(Qt.UserRole)
                if investment_id:
                    self.investment_selected.emit(investment_id)


class InvestmentEditDialog(QDialog):
    """Dialog for editing investments"""
    
    def __init__(self, investment: Investment = None, parent=None):
        super().__init__(parent)
        self.investment = investment or Investment()
        self.setup_ui()
        self.populate_fields()
    
    def setup_ui(self):
        """Setup the dialog UI"""
        self.setWindowTitle("Edit Investment")
        self.setModal(True)
        self.resize(500, 600)
        
        layout = QVBoxLayout(self)
        
        # Form layout
        form_layout = QFormLayout()
        
        # Symbol
        self.symbol_edit = QLineEdit()
        self.symbol_edit.setPlaceholderText("e.g., AAPL, RELIANCE")
        form_layout.addRow("Symbol:", self.symbol_edit)
        
        # Name
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("Full name of investment")
        form_layout.addRow("Name:", self.name_edit)
        
        # Investment Type
        self.type_combo = QComboBox()
        self.type_combo.addItems([t.value for t in InvestmentType])
        form_layout.addRow("Type:", self.type_combo)
        
        # Quantity
        self.quantity_spin = QDoubleSpinBox()
        self.quantity_spin.setRange(0, 999999)
        self.quantity_spin.setDecimals(4)
        form_layout.addRow("Quantity:", self.quantity_spin)
        
        # Purchase Price
        self.purchase_price_spin = QDoubleSpinBox()
        self.purchase_price_spin.setRange(0, 999999)
        self.purchase_price_spin.setDecimals(2)
        self.purchase_price_spin.setPrefix("₹")
        form_layout.addRow("Purchase Price:", self.purchase_price_spin)
        
        # Current Price
        self.current_price_spin = QDoubleSpinBox()
        self.current_price_spin.setRange(0, 999999)
        self.current_price_spin.setDecimals(2)
        self.current_price_spin.setPrefix("₹")
        form_layout.addRow("Current Price:", self.current_price_spin)
        
        # Purchase Date
        self.purchase_date_edit = QDateEdit()
        self.purchase_date_edit.setCalendarPopup(True)
        self.purchase_date_edit.setDate(QDate.currentDate())
        form_layout.addRow("Purchase Date:", self.purchase_date_edit)
        
        # Notes
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(100)
        form_layout.addRow("Notes:", self.notes_edit)
        
        layout.addLayout(form_layout)
        
        # Calculated fields (read-only)
        calc_group = QGroupBox("Calculated Fields")
        calc_layout = QFormLayout(calc_group)
        
        self.total_investment_label = QLabel("₹0.00")
        calc_layout.addRow("Total Investment:", self.total_investment_label)
        
        self.current_value_label = QLabel("₹0.00")
        calc_layout.addRow("Current Value:", self.current_value_label)
        
        self.profit_loss_label = QLabel("₹0.00 (0.00%)")
        calc_layout.addRow("Profit/Loss:", self.profit_loss_label)
        
        layout.addWidget(calc_group)
        
        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
        
        # Connect value changes to update calculations
        self.quantity_spin.valueChanged.connect(self.update_calculations)
        self.purchase_price_spin.valueChanged.connect(self.update_calculations)
        self.current_price_spin.valueChanged.connect(self.update_calculations)
    
    def populate_fields(self):
        """Populate fields with investment data"""
        self.symbol_edit.setText(self.investment.symbol)
        self.name_edit.setText(self.investment.name)
        self.type_combo.setCurrentText(self.investment.investment_type)
        self.quantity_spin.setValue(self.investment.quantity)
        self.purchase_price_spin.setValue(self.investment.purchase_price)
        self.current_price_spin.setValue(self.investment.current_price)
        
        if self.investment.purchase_date:
            self.purchase_date_edit.setDate(QDate.fromString(str(self.investment.purchase_date), "yyyy-MM-dd"))
        
        self.notes_edit.setPlainText(self.investment.notes)
        
        self.update_calculations()
    
    def update_calculations(self):
        """Update calculated fields"""
        quantity = self.quantity_spin.value()
        purchase_price = self.purchase_price_spin.value()
        current_price = self.current_price_spin.value()
        
        total_investment = quantity * purchase_price
        current_value = quantity * current_price
        profit_loss = current_value - total_investment
        
        profit_loss_percentage = 0.0
        if total_investment > 0:
            profit_loss_percentage = (profit_loss / total_investment) * 100
        
        self.total_investment_label.setText(f"₹{total_investment:,.2f}")
        self.current_value_label.setText(f"₹{current_value:,.2f}")
        
        pl_color = "#28a745" if profit_loss >= 0 else "#dc3545"
        pl_sign = "+" if profit_loss >= 0 else ""
        self.profit_loss_label.setText(f"{pl_sign}₹{profit_loss:,.2f} ({pl_sign}{profit_loss_percentage:.2f}%)")
        self.profit_loss_label.setStyleSheet(f"color: {pl_color}; font-weight: bold;")
    
    def get_investment(self) -> Investment:
        """Get the investment from form data"""
        self.investment.symbol = self.symbol_edit.text().strip().upper()
        self.investment.name = self.name_edit.text().strip()
        self.investment.investment_type = self.type_combo.currentText()
        self.investment.quantity = self.quantity_spin.value()
        self.investment.purchase_price = self.purchase_price_spin.value()
        self.investment.current_price = self.current_price_spin.value()
        self.investment.purchase_date = self.purchase_date_edit.date().toPython()
        self.investment.notes = self.notes_edit.toPlainText().strip()
        self.investment.last_updated = datetime.now()
        
        return self.investment


class InvestmentTrackerWidget(QWidget):
    """Main investment tracker widget"""

    def __init__(self, data_manager, config, parent=None):
        super().__init__(parent)

        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self.logger.info("="*50)
        self.logger.info("INITIALIZING INVESTMENT TRACKER WIDGET")
        self.logger.info("="*50)

        try:
            self.data_manager = data_manager
            self.config = config
            self.investment_model = InvestmentDataModel(data_manager)

            self.setup_ui()
            self.setup_connections()
            self.refresh_data()

            self.logger.info("✅ InvestmentTrackerWidget initialization SUCCESSFUL")

        except Exception as e:
            self.logger.error(f"❌ CRITICAL ERROR in InvestmentTrackerWidget.__init__: {e}")
            raise

    def setup_ui(self):
        """Setup the main UI"""
        layout = QVBoxLayout(self)

        # Header with title and add button
        header_layout = QHBoxLayout()

        title_label = QLabel("📈 Investment Portfolio Tracker")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        # Add new investment button
        add_btn = QPushButton("➕ Add Investment")
        add_btn.clicked.connect(self.add_new_investment)
        header_layout.addWidget(add_btn)

        # Update prices button
        update_btn = QPushButton("🔄 Update Prices")
        update_btn.clicked.connect(self.update_prices)
        header_layout.addWidget(update_btn)

        layout.addLayout(header_layout)

        # Portfolio summary
        self.summary_widget = InvestmentSummaryWidget()
        layout.addWidget(self.summary_widget)

        # Filter controls
        filter_layout = QHBoxLayout()

        # Type filter
        filter_layout.addWidget(QLabel("Filter by Type:"))
        self.type_filter = QComboBox()
        self.type_filter.addItems(["All"] + [t.value for t in InvestmentType])
        self.type_filter.currentTextChanged.connect(self.apply_filters)
        filter_layout.addWidget(self.type_filter)

        filter_layout.addStretch()

        # Search
        filter_layout.addWidget(QLabel("Search:"))
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("Search by symbol or name...")
        self.search_edit.textChanged.connect(self.apply_filters)
        filter_layout.addWidget(self.search_edit)

        layout.addLayout(filter_layout)

        # Investment table
        self.investment_table = InvestmentTableWidget()
        self.investment_table.investment_selected.connect(self.on_investment_selected)
        layout.addWidget(self.investment_table)

        # Action buttons
        action_layout = QHBoxLayout()

        self.edit_btn = QPushButton("✏️ Edit")
        self.edit_btn.clicked.connect(self.edit_selected_investment)
        self.edit_btn.setEnabled(False)
        action_layout.addWidget(self.edit_btn)

        self.delete_btn = QPushButton("🗑️ Delete")
        self.delete_btn.clicked.connect(self.delete_selected_investment)
        self.delete_btn.setEnabled(False)
        action_layout.addWidget(self.delete_btn)

        action_layout.addStretch()

        # Export button
        export_btn = QPushButton("📊 Export Portfolio")
        export_btn.clicked.connect(self.export_portfolio)
        action_layout.addWidget(export_btn)

        layout.addLayout(action_layout)

        self.selected_investment_id = None

    def setup_connections(self):
        """Setup signal connections"""
        pass

    def refresh_data(self):
        """Refresh investment data and update display"""
        try:
            # Get investments and apply filters
            df = self.investment_model.get_all_investments()
            filtered_df = self.apply_current_filters(df)

            # Update table
            self.investment_table.update_investments(filtered_df)

            # Update summary
            summary = self.investment_model.get_portfolio_summary()
            self.summary_widget.update_summary(summary)

        except Exception as e:
            self.logger.error(f"Error refreshing investment data: {e}")

    def apply_current_filters(self, df: pd.DataFrame) -> pd.DataFrame:
        """Apply current filter settings to dataframe"""
        if df.empty:
            return df

        filtered_df = df.copy()

        # Type filter
        type_filter = self.type_filter.currentText()
        if type_filter != "All":
            filtered_df = filtered_df[filtered_df['investment_type'] == type_filter]

        # Search filter
        search_text = self.search_edit.text().strip().lower()
        if search_text:
            mask = (
                filtered_df['symbol'].str.lower().str.contains(search_text, na=False) |
                filtered_df['name'].str.lower().str.contains(search_text, na=False)
            )
            filtered_df = filtered_df[mask]

        return filtered_df

    def apply_filters(self):
        """Apply filters and refresh display"""
        self.refresh_data()

    def add_new_investment(self):
        """Add a new investment"""
        dialog = InvestmentEditDialog(parent=self)
        if dialog.exec() == QDialog.Accepted:
            investment = dialog.get_investment()
            if self.investment_model.add_investment(investment):
                self.refresh_data()
                self.logger.info(f"Added new investment: {investment.symbol}")
            else:
                QMessageBox.warning(self, "Error", "Failed to add investment")

    def on_investment_selected(self, investment_id: int):
        """Handle investment selection"""
        self.selected_investment_id = investment_id
        self.edit_btn.setEnabled(True)
        self.delete_btn.setEnabled(True)

    def edit_selected_investment(self):
        """Edit the selected investment"""
        if not self.selected_investment_id:
            return

        # Get investment data
        df = self.investment_model.get_all_investments()
        investment_row = df[df['id'] == self.selected_investment_id]

        if investment_row.empty:
            QMessageBox.warning(self, "Error", "Investment not found")
            return

        investment = Investment.from_dict(investment_row.iloc[0].to_dict())

        dialog = InvestmentEditDialog(investment, self)
        if dialog.exec() == QDialog.Accepted:
            updated_investment = dialog.get_investment()
            if self.investment_model.update_investment(self.selected_investment_id, updated_investment):
                self.refresh_data()
                self.logger.info(f"Updated investment: {updated_investment.symbol}")
            else:
                QMessageBox.warning(self, "Error", "Failed to update investment")

    def delete_selected_investment(self):
        """Delete the selected investment"""
        if not self.selected_investment_id:
            return

        # Get investment name for confirmation
        df = self.investment_model.get_all_investments()
        investment_row = df[df['id'] == self.selected_investment_id]

        if investment_row.empty:
            QMessageBox.warning(self, "Error", "Investment not found")
            return

        investment_name = investment_row.iloc[0]['symbol']

        reply = QMessageBox.question(
            self, "Delete Investment",
            f"Are you sure you want to delete '{investment_name}'?",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            if self.investment_model.delete_investment(self.selected_investment_id):
                self.refresh_data()
                self.selected_investment_id = None
                self.edit_btn.setEnabled(False)
                self.delete_btn.setEnabled(False)
                self.logger.info(f"Deleted investment: {investment_name}")
            else:
                QMessageBox.warning(self, "Error", "Failed to delete investment")

    def update_prices(self):
        """Update current prices (placeholder for real-time data)"""
        QMessageBox.information(
            self, "Update Prices",
            "Price update functionality would integrate with financial APIs.\n"
            "For now, please update prices manually by editing each investment."
        )

    def export_portfolio(self):
        """Export portfolio data"""
        QMessageBox.information(
            self, "Export Portfolio",
            "Portfolio export functionality coming soon!\n"
            "Will support CSV, Excel, and PDF formats."
        )
