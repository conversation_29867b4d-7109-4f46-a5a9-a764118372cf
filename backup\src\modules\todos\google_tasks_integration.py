"""
Google Tasks API Integration
Provides synchronization between local todos and Google Tasks
"""

import os
import json
import logging
from datetime import datetime, date
from typing import Dict, List, Any, Optional
from pathlib import Path

try:
    from google.auth.transport.requests import Request
    from google.oauth2.credentials import Credentials
    from google_auth_oauthlib.flow import InstalledAppFlow
    from googleapiclient.discovery import build
    from googleapiclient.errors import HttpError
    GOOGLE_TASKS_AVAILABLE = True
except ImportError:
    GOOGLE_TASKS_AVAILABLE = False

from .models import TodoItem, Status, Priority


class GoogleTasksIntegration:
    """Google Tasks API integration for todo synchronization"""
    
    # If modifying these scopes, delete the file token.json.
    SCOPES = ['https://www.googleapis.com/auth/tasks']
    
    def __init__(self, data_dir: str):
        self.data_dir = Path(data_dir)
        self.logger = logging.getLogger(__name__)
        self.service = None
        self.credentials = None
        self.auth_attempted = False

        # Google API credentials
        self.client_config = {
            "installed": *********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
        }

        if not GOOGLE_TASKS_AVAILABLE:
            self.logger.warning("Google Tasks API libraries not available. Install google-api-python-client, google-auth-httplib2, and google-auth-oauthlib")
            return

        # Try to initialize with existing credentials only (no OAuth popup)
        self.try_initialize_with_existing_credentials()

    def try_initialize_with_existing_credentials(self):
        """Try to initialize with existing credentials without triggering OAuth"""
        try:
            token_file = self.data_dir / "google_tasks_token.json"

            if token_file.exists():
                creds = Credentials.from_authorized_user_file(str(token_file), self.SCOPES)

                # Only proceed if credentials are valid or can be refreshed
                if creds and creds.valid:
                    self.credentials = creds
                    self.service = build('tasks', 'v1', credentials=creds)
                    self.logger.info("✅ Google Tasks service initialized with existing valid credentials")
                elif creds and creds.expired and creds.refresh_token:
                    try:
                        creds.refresh(Request())
                        self.credentials = creds
                        self.service = build('tasks', 'v1', credentials=creds)

                        # Save refreshed credentials
                        with open(token_file, 'w') as token:
                            token.write(creds.to_json())

                        self.logger.info("✅ Google Tasks service initialized with refreshed credentials")
                    except Exception as e:
                        self.logger.warning(f"❌ Failed to refresh Google Tasks credentials: {e}")
                        self.service = None
                elif creds:
                    # Credentials exist but are invalid and can't be refreshed
                    self.logger.warning("⚠️ Google Tasks credentials exist but are invalid and cannot be refreshed")
                    self.service = None
                else:
                    self.logger.info("⚠️ Google Tasks credentials file exists but contains invalid data")
                    self.service = None
            else:
                self.logger.info("ℹ️ No Google Tasks credentials found - manual authentication required")
                self.service = None

        except Exception as e:
            self.logger.warning(f"❌ Failed to initialize Google Tasks with existing credentials: {e}")
            self.service = None

    def initialize_service(self, force_auth: bool = False):
        """Initialize Google Tasks service with optional OAuth authentication"""
        if not force_auth and self.auth_attempted:
            self.logger.info("Google Tasks authentication already attempted - use force_auth=True to retry")
            return

        self.auth_attempted = True

        try:
            creds = None
            token_file = self.data_dir / "google_tasks_token.json"

            # The file token.json stores the user's access and refresh tokens.
            if token_file.exists():
                creds = Credentials.from_authorized_user_file(str(token_file), self.SCOPES)

            # If there are no (valid) credentials available, let the user log in.
            if not creds or not creds.valid:
                if creds and creds.expired and creds.refresh_token:
                    try:
                        creds.refresh(Request())
                    except Exception as e:
                        self.logger.warning(f"Failed to refresh credentials: {e}")
                        if not force_auth:
                            self.logger.info("Skipping OAuth authentication - use manual authentication if needed")
                            self.service = None
                            return
                        creds = None

                if not creds and force_auth:
                    # Only trigger OAuth if explicitly requested
                    self.logger.info("Starting OAuth authentication flow...")

                    # Create credentials file temporarily
                    temp_creds_file = self.data_dir / "temp_credentials.json"
                    with open(temp_creds_file, 'w') as f:
                        json.dump(self.client_config, f)

                    flow = InstalledAppFlow.from_client_secrets_file(
                        str(temp_creds_file), self.SCOPES)
                    creds = flow.run_local_server(port=0)

                    # Clean up temp file
                    temp_creds_file.unlink()
                elif not creds:
                    self.logger.info("No valid credentials and OAuth not forced - Google Tasks unavailable")
                    self.service = None
                    return

                # Save the credentials for the next run
                if creds:
                    with open(token_file, 'w') as token:
                        token.write(creds.to_json())

            if creds:
                self.credentials = creds
                self.service = build('tasks', 'v1', credentials=creds)
                self.logger.info("Google Tasks service initialized successfully")
            else:
                self.service = None
                self.logger.info("Google Tasks service not available - no valid credentials")

        except Exception as e:
            self.logger.error(f"Failed to initialize Google Tasks service: {e}")
            self.service = None
    
    def is_available(self) -> bool:
        """Check if Google Tasks integration is available"""
        return GOOGLE_TASKS_AVAILABLE and self.service is not None

    def authenticate(self) -> bool:
        """Manually trigger Google Tasks authentication"""
        try:
            self.initialize_service(force_auth=True)
            return self.is_available()
        except Exception as e:
            self.logger.error(f"Authentication failed: {e}")
            return False

    def get_auth_status(self) -> Dict[str, Any]:
        """Get authentication status information"""
        token_file = self.data_dir / "google_tasks_token.json"
        credentials_exist = token_file.exists()

        # Check if credentials are valid
        credentials_valid = False
        if credentials_exist and self.credentials:
            credentials_valid = self.credentials.valid

        return {
            'libraries_available': GOOGLE_TASKS_AVAILABLE,
            'service_available': self.service is not None,
            'credentials_exist': credentials_exist,
            'credentials_valid': credentials_valid,
            'auth_attempted': self.auth_attempted,
            'is_connected': self.is_available(),
            'status_message': self._get_status_message()
        }

    def _get_status_message(self) -> str:
        """Get a human-readable status message"""
        if not GOOGLE_TASKS_AVAILABLE:
            return "❌ Google Tasks libraries not installed"

        token_file = self.data_dir / "google_tasks_token.json"
        if not token_file.exists():
            return "🔗 Ready to connect - Click 'Connect Google Tasks'"

        if self.service is not None:
            return "✅ Connected to Google Tasks"

        if self.credentials and self.credentials.expired:
            return "⚠️ Credentials expired - Reconnection needed"

        return "⚠️ Connection issue - Try reconnecting"
    
    def get_task_lists(self) -> List[Dict[str, Any]]:
        """Get all task lists from Google Tasks"""
        if not self.is_available():
            return []
        
        try:
            results = self.service.tasklists().list().execute()
            items = results.get('items', [])
            return items
        except HttpError as e:
            self.logger.error(f"Error getting task lists: {e}")
            return []
    
    def get_tasks(self, tasklist_id: str = '@default', include_completed: bool = True) -> List[Dict[str, Any]]:
        """Get tasks from a specific task list"""
        if not self.is_available():
            return []

        try:
            # Get both completed and incomplete tasks
            params = {'tasklist': tasklist_id}
            if include_completed:
                params['showCompleted'] = True
                params['showHidden'] = True

            results = self.service.tasks().list(**params).execute()
            items = results.get('items', [])
            self.logger.info(f"Retrieved {len(items)} tasks from task list {tasklist_id} (completed: {include_completed})")
            return items
        except HttpError as e:
            self.logger.error(f"Error getting tasks: {e}")
            return []
    
    def create_task(self, todo_item: TodoItem, tasklist_id: str = '@default') -> Optional[str]:
        """Create a task in Google Tasks"""
        if not self.is_available():
            return None
        
        try:
            task = {
                'title': str(todo_item.title) if todo_item.title else 'Untitled',
                'status': 'completed' if todo_item.status == Status.COMPLETED.value else 'needsAction'
            }

            # Only add notes if it's not empty/null/NaN
            if todo_item.description and str(todo_item.description).strip() and str(todo_item.description) != 'nan':
                task['notes'] = str(todo_item.description)

            if todo_item.due_date:
                # Convert date to RFC 3339 format
                if isinstance(todo_item.due_date, date):
                    task['due'] = todo_item.due_date.strftime('%Y-%m-%dT00:00:00.000Z')
                elif isinstance(todo_item.due_date, datetime):
                    task['due'] = todo_item.due_date.strftime('%Y-%m-%dT%H:%M:%S.000Z')

            result = self.service.tasks().insert(tasklist=tasklist_id, body=task).execute()
            task_id = result.get('id')

            self.logger.info(f"Created Google Task: {todo_item.title} (ID: {task_id})")
            return task_id
            
        except HttpError as e:
            self.logger.error(f"Error creating task: {e}")
            return None
    
    def update_task(self, google_task_id: str, todo_item: TodoItem, tasklist_id: str = '@default') -> bool:
        """Update a task in Google Tasks"""
        if not self.is_available():
            return False
        
        try:
            task = {
                'id': google_task_id,
                'title': str(todo_item.title) if todo_item.title else 'Untitled',
                'status': 'completed' if todo_item.status == Status.COMPLETED.value else 'needsAction'
            }

            # Only add notes if it's not empty/null/NaN
            if todo_item.description and str(todo_item.description).strip() and str(todo_item.description) != 'nan':
                task['notes'] = str(todo_item.description)

            if todo_item.due_date:
                if isinstance(todo_item.due_date, date):
                    task['due'] = todo_item.due_date.strftime('%Y-%m-%dT00:00:00.000Z')
                elif isinstance(todo_item.due_date, datetime):
                    task['due'] = todo_item.due_date.strftime('%Y-%m-%dT%H:%M:%S.000Z')

            self.service.tasks().update(tasklist=tasklist_id, task=google_task_id, body=task).execute()
            self.logger.info(f"Updated Google Task: {todo_item.title}")
            return True
            
        except HttpError as e:
            self.logger.error(f"Error updating task: {e}")
            return False
    
    def delete_task(self, google_task_id: str, tasklist_id: str = '@default') -> bool:
        """Delete a task from Google Tasks"""
        if not self.is_available():
            return False
        
        try:
            self.service.tasks().delete(tasklist=tasklist_id, task=google_task_id).execute()
            self.logger.info(f"Deleted Google Task ID: {google_task_id}")
            return True
            
        except HttpError as e:
            self.logger.error(f"Error deleting task: {e}")
            return False
    
    def sync_from_google(self, tasklist_id: str = '@default') -> List[TodoItem]:
        """Sync tasks from Google Tasks to local format"""
        if not self.is_available():
            return []
        
        google_tasks = self.get_tasks(tasklist_id)
        local_todos = []
        
        for task in google_tasks:
            try:
                # Convert Google Task to TodoItem
                todo_item = TodoItem(
                    title=task.get('title', 'Untitled'),
                    description=task.get('notes', ''),
                    status=Status.COMPLETED.value if task.get('status') == 'completed' else Status.PENDING.value,
                    priority=Priority.MEDIUM.value,  # Default priority
                    google_task_id=task.get('id')
                )
                
                # Parse due date
                if 'due' in task:
                    try:
                        due_str = task['due']
                        # Remove timezone info for simplicity
                        if 'T' in due_str:
                            due_str = due_str.split('T')[0]
                        todo_item.due_date = datetime.strptime(due_str, '%Y-%m-%d').date()
                    except ValueError:
                        pass
                
                local_todos.append(todo_item)
                
            except Exception as e:
                self.logger.warning(f"Error converting Google Task: {e}")
        
        return local_todos
    
    def sync_to_google(self, todo_items: List[TodoItem], tasklist_id: str = '@default') -> Dict[str, str]:
        """Sync local todos to Google Tasks"""
        if not self.is_available():
            return {}
        
        sync_results = {}
        
        for todo_item in todo_items:
            try:
                if hasattr(todo_item, 'google_task_id') and todo_item.google_task_id:
                    # Update existing task
                    success = self.update_task(todo_item.google_task_id, todo_item, tasklist_id)
                    sync_results[str(todo_item.id)] = 'updated' if success else 'failed'
                else:
                    # Create new task
                    google_task_id = self.create_task(todo_item, tasklist_id)
                    if google_task_id:
                        todo_item.google_task_id = google_task_id
                        sync_results[str(todo_item.id)] = 'created'
                    else:
                        sync_results[str(todo_item.id)] = 'failed'
                        
            except Exception as e:
                self.logger.error(f"Error syncing todo {todo_item.title}: {e}")
                sync_results[str(todo_item.id)] = 'failed'
        
        return sync_results
