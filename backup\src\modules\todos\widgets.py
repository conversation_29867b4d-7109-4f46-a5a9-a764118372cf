"""
To-Do List UI Widgets
Contains all UI components for the todo list module
"""

import logging
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QFormLayout,
    QLabel, QPushButton, QLineEdit, QComboBox, QDateEdit, QTextEdit,
    QCheckBox, QFrame, QGroupBox, QScrollArea, QTabWidget,
    QProgressBar, QSpinBox, QTableWidget, QTableWidgetItem, QHeaderView,
    QAbstractItemView, QSizePolicy, QButtonGroup, QDoubleSpinBox,
    QMessageBox, QDialog, QDialogButtonBox, QSplitter, QProgressDialog
)
from PySide6.QtCore import Qt, Signal, QDate, QTimer, QSize
from PySide6.QtGui import QFont, QIcon, QPixmap, QPalette, QColor

from datetime import datetime, date, timedelta
from typing import Dict, List, Any, Optional
import pandas as pd

from .models import TodoItem, TodoDataModel, Priority, Status, Category


class TodoItemWidget(QFrame):
    """Widget for displaying a single todo item"""
    
    item_updated = Signal(int)  # Emits todo ID when updated
    item_deleted = Signal(int)  # Emits todo ID when deleted
    
    def __init__(self, todo_item: TodoItem, parent=None):
        super().__init__(parent)
        self.todo_item = todo_item
        self.setup_ui()
        self.update_display()
    
    def setup_ui(self):
        """Setup the UI for todo item"""
        self.setFrameStyle(QFrame.Box)
        self.setStyleSheet("""
            QFrame {
                border: 1px solid #ddd;
                border-radius: 5px;
                margin: 2px;
                padding: 5px;
            }
        """)
        
        layout = QVBoxLayout(self)
        
        # Header with title and controls
        header_layout = QHBoxLayout()
        
        # Checkbox for completion
        self.completed_checkbox = QCheckBox()
        self.completed_checkbox.setChecked(self.todo_item.status == Status.COMPLETED.value)
        self.completed_checkbox.toggled.connect(self.toggle_completion)
        header_layout.addWidget(self.completed_checkbox)
        
        # Title
        self.title_label = QLabel(self.todo_item.title)
        self.title_label.setFont(QFont("Arial", 12, QFont.Bold))
        header_layout.addWidget(self.title_label)
        
        header_layout.addStretch()
        
        # Priority badge
        self.priority_label = QLabel(self.todo_item.priority)
        self.priority_label.setStyleSheet(self.get_priority_style())
        header_layout.addWidget(self.priority_label)
        
        # Edit button
        edit_btn = QPushButton("Edit")
        edit_btn.clicked.connect(self.edit_item)
        header_layout.addWidget(edit_btn)
        
        # Delete button
        delete_btn = QPushButton("Delete")
        delete_btn.clicked.connect(self.delete_item)
        header_layout.addWidget(delete_btn)
        
        layout.addLayout(header_layout)
        
        # Description
        if self.todo_item.description:
            desc_label = QLabel(self.todo_item.description)
            desc_label.setWordWrap(True)
            layout.addWidget(desc_label)
        
        # Details row
        details_layout = QHBoxLayout()
        
        # Category
        cat_label = QLabel(f"Category: {self.todo_item.category}")
        details_layout.addWidget(cat_label)
        
        # Due date
        if self.todo_item.due_date:
            due_label = QLabel(f"Due: {self.todo_item.due_date}")
            if self.todo_item.is_overdue():
                due_label.setStyleSheet("color: red; font-weight: bold;")
            details_layout.addWidget(due_label)
        
        details_layout.addStretch()
        layout.addLayout(details_layout)
    
    def get_priority_style(self) -> str:
        """Get CSS style for priority badge"""
        styles = {
            Priority.LOW.value: "background-color: #28a745; color: white; padding: 2px 8px; border-radius: 3px;",
            Priority.MEDIUM.value: "background-color: #ffc107; color: black; padding: 2px 8px; border-radius: 3px;",
            Priority.HIGH.value: "background-color: #fd7e14; color: white; padding: 2px 8px; border-radius: 3px;",
            Priority.URGENT.value: "background-color: #dc3545; color: white; padding: 2px 8px; border-radius: 3px;"
        }
        return styles.get(self.todo_item.priority, styles[Priority.MEDIUM.value])
    
    def update_display(self):
        """Update the display based on current item state"""
        if self.todo_item.status == Status.COMPLETED.value:
            self.setStyleSheet("""
                QFrame {
                    border: 1px solid #ddd;
                    border-radius: 5px;
                    margin: 2px;
                    padding: 5px;
                    background-color: #f8f9fa;
                    opacity: 0.7;
                }
            """)
            self.title_label.setStyleSheet("text-decoration: line-through; color: #6c757d;")
        else:
            self.setStyleSheet("""
                QFrame {
                    border: 1px solid #ddd;
                    border-radius: 5px;
                    margin: 2px;
                    padding: 5px;
                    background-color: white;
                }
            """)
            self.title_label.setStyleSheet("text-decoration: none; color: black;")
    
    def toggle_completion(self, checked: bool):
        """Toggle completion status"""
        if checked:
            self.todo_item.mark_completed()
        else:
            self.todo_item.status = Status.PENDING.value
            self.todo_item.completed_at = None
            self.todo_item.updated_at = datetime.now()
        
        self.update_display()
        self.item_updated.emit(self.todo_item.id)
    
    def edit_item(self):
        """Open edit dialog"""
        dialog = TodoEditDialog(self.todo_item, self)
        if dialog.exec() == QDialog.Accepted:
            self.todo_item = dialog.get_todo_item()
            self.setup_ui()
            self.update_display()
            self.item_updated.emit(self.todo_item.id)
    
    def delete_item(self):
        """Delete the item"""
        reply = QMessageBox.question(
            self, "Delete Todo", 
            f"Are you sure you want to delete '{self.todo_item.title}'?",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.item_deleted.emit(self.todo_item.id)


class TodoEditDialog(QDialog):
    """Dialog for editing todo items"""
    
    def __init__(self, todo_item: TodoItem = None, parent=None):
        super().__init__(parent)
        self.todo_item = todo_item or TodoItem()
        self.setup_ui()
        self.populate_fields()
    
    def setup_ui(self):
        """Setup the dialog UI"""
        self.setWindowTitle("Edit Todo Item")
        self.setModal(True)
        self.resize(500, 600)
        
        layout = QVBoxLayout(self)
        
        # Form layout
        form_layout = QFormLayout()
        
        # Title
        self.title_edit = QLineEdit()
        form_layout.addRow("Title:", self.title_edit)
        
        # Description
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(100)
        form_layout.addRow("Description:", self.description_edit)
        
        # Category
        self.category_combo = QComboBox()
        self.category_combo.addItems([c.value for c in Category])
        form_layout.addRow("Category:", self.category_combo)
        
        # Priority
        self.priority_combo = QComboBox()
        self.priority_combo.addItems([p.value for p in Priority])
        form_layout.addRow("Priority:", self.priority_combo)
        
        # Status
        self.status_combo = QComboBox()
        self.status_combo.addItems([s.value for s in Status])
        form_layout.addRow("Status:", self.status_combo)
        
        # Due date
        self.due_date_edit = QDateEdit()
        self.due_date_edit.setCalendarPopup(True)
        self.due_date_edit.setDate(QDate.currentDate())
        self.due_date_checkbox = QCheckBox("Set due date")
        due_layout = QHBoxLayout()
        due_layout.addWidget(self.due_date_checkbox)
        due_layout.addWidget(self.due_date_edit)
        form_layout.addRow("Due Date:", due_layout)
        
        # Estimated hours
        self.estimated_hours_spin = QDoubleSpinBox()
        self.estimated_hours_spin.setRange(0, 999)
        self.estimated_hours_spin.setSuffix(" hours")
        form_layout.addRow("Estimated Hours:", self.estimated_hours_spin)
        
        # Actual hours
        self.actual_hours_spin = QDoubleSpinBox()
        self.actual_hours_spin.setRange(0, 999)
        self.actual_hours_spin.setSuffix(" hours")
        form_layout.addRow("Actual Hours:", self.actual_hours_spin)
        
        # Tags
        self.tags_edit = QLineEdit()
        self.tags_edit.setPlaceholderText("Comma-separated tags")
        form_layout.addRow("Tags:", self.tags_edit)
        
        # Notes
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(100)
        form_layout.addRow("Notes:", self.notes_edit)
        
        layout.addLayout(form_layout)
        
        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
        
        # Connect due date checkbox
        self.due_date_checkbox.toggled.connect(self.due_date_edit.setEnabled)
        self.due_date_edit.setEnabled(False)
    
    def populate_fields(self):
        """Populate fields with todo item data"""
        self.title_edit.setText(self.todo_item.title)
        self.description_edit.setPlainText(self.todo_item.description)
        self.category_combo.setCurrentText(self.todo_item.category)
        self.priority_combo.setCurrentText(self.todo_item.priority)
        self.status_combo.setCurrentText(self.todo_item.status)
        
        if self.todo_item.due_date:
            self.due_date_checkbox.setChecked(True)
            self.due_date_edit.setEnabled(True)
            self.due_date_edit.setDate(QDate.fromString(str(self.todo_item.due_date), "yyyy-MM-dd"))
        
        self.estimated_hours_spin.setValue(self.todo_item.estimated_hours)
        self.actual_hours_spin.setValue(self.todo_item.actual_hours)
        self.tags_edit.setText(self.todo_item.tags)
        self.notes_edit.setPlainText(self.todo_item.notes)
    
    def get_todo_item(self) -> TodoItem:
        """Get the todo item from form data"""
        self.todo_item.title = self.title_edit.text().strip()
        self.todo_item.description = self.description_edit.toPlainText().strip()
        self.todo_item.category = self.category_combo.currentText()
        self.todo_item.priority = self.priority_combo.currentText()
        self.todo_item.status = self.status_combo.currentText()
        
        if self.due_date_checkbox.isChecked():
            self.todo_item.due_date = self.due_date_edit.date().toPython()
        else:
            self.todo_item.due_date = None
        
        self.todo_item.estimated_hours = self.estimated_hours_spin.value()
        self.todo_item.actual_hours = self.actual_hours_spin.value()
        self.todo_item.tags = self.tags_edit.text().strip()
        self.todo_item.notes = self.notes_edit.toPlainText().strip()
        self.todo_item.updated_at = datetime.now()
        
        return self.todo_item


class TodoTrackerWidget(QWidget):
    """Main todo tracker widget"""

    def __init__(self, data_manager, config, parent=None):
        super().__init__(parent)

        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self.logger.info("="*50)
        self.logger.info("INITIALIZING TODO TRACKER WIDGET")
        self.logger.info("="*50)

        try:
            self.data_manager = data_manager
            self.config = config
            self.todo_model = TodoDataModel(data_manager)

            self.setup_ui()
            self.setup_connections()
            self.refresh_data()

            self.logger.info("✅ TodoTrackerWidget initialization SUCCESSFUL")

        except Exception as e:
            self.logger.error(f"❌ CRITICAL ERROR in TodoTrackerWidget.__init__: {e}")
            raise

    def setup_ui(self):
        """Setup the main UI"""
        layout = QVBoxLayout(self)

        # Header with title and add button
        header_layout = QHBoxLayout()

        title_label = QLabel("📝 To-Do List Manager")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        header_layout.addWidget(title_label)

        # Google Tasks status indicator
        status = self.todo_model.get_google_tasks_status()
        status_label = QLabel(status.get('status_message', 'Unknown status'))
        status_label.setObjectName('google_tasks_status')
        status_label.setStyleSheet("""
            QLabel {
                padding: 5px 10px;
                border-radius: 3px;
                font-weight: bold;
                background-color: #f0f0f0;
                border: 1px solid #ddd;
            }
        """)
        header_layout.addWidget(status_label)

        header_layout.addStretch()

        # Google Tasks sync buttons
        if self.todo_model.is_google_tasks_available():
            sync_from_btn = QPushButton("⬇️ Sync from Google")
            sync_from_btn.setToolTip("Import tasks from Google Tasks")
            sync_from_btn.clicked.connect(self.sync_from_google_tasks)
            header_layout.addWidget(sync_from_btn)

            sync_to_btn = QPushButton("⬆️ Sync to Google")
            sync_to_btn.setToolTip("Export tasks to Google Tasks")
            sync_to_btn.clicked.connect(self.sync_to_google_tasks)
            header_layout.addWidget(sync_to_btn)

            full_sync_btn = QPushButton("🔄 Full Sync")
            full_sync_btn.setToolTip("Bidirectional sync with Google Tasks")
            full_sync_btn.clicked.connect(self.full_sync_google_tasks)
            header_layout.addWidget(full_sync_btn)
        else:
            # Show authentication button if Google Tasks is not available
            if status.get('libraries_available', False):
                auth_btn = QPushButton("🔗 Connect Google Tasks")
                auth_btn.setToolTip("Authenticate with Google Tasks to enable synchronization")
                auth_btn.clicked.connect(self.authenticate_google_tasks)
                header_layout.addWidget(auth_btn)
            else:
                info_btn = QPushButton("ℹ️ Google Tasks Info")
                info_btn.setToolTip("Information about Google Tasks integration")
                info_btn.clicked.connect(self.show_google_tasks_info)
                header_layout.addWidget(info_btn)

        # Add new todo button
        add_btn = QPushButton("➕ Add New Todo")
        add_btn.clicked.connect(self.add_new_todo)
        header_layout.addWidget(add_btn)

        layout.addLayout(header_layout)

        # Statistics panel
        self.stats_widget = self.create_stats_widget()
        layout.addWidget(self.stats_widget)

        # Filter controls
        filter_layout = QHBoxLayout()

        # Status filter
        filter_layout.addWidget(QLabel("Filter by Status:"))
        self.status_filter = QComboBox()
        self.status_filter.addItems(["All", "Pending", "In Progress", "Completed"])
        self.status_filter.currentTextChanged.connect(self.apply_filters)
        filter_layout.addWidget(self.status_filter)

        # Priority filter
        filter_layout.addWidget(QLabel("Priority:"))
        self.priority_filter = QComboBox()
        self.priority_filter.addItems(["All"] + [p.value for p in Priority])
        self.priority_filter.currentTextChanged.connect(self.apply_filters)
        filter_layout.addWidget(self.priority_filter)

        # Category filter
        filter_layout.addWidget(QLabel("Category:"))
        self.category_filter = QComboBox()
        self.category_filter.addItems(["All"] + [c.value for c in Category])
        self.category_filter.currentTextChanged.connect(self.apply_filters)
        filter_layout.addWidget(self.category_filter)

        filter_layout.addStretch()

        # Show overdue checkbox
        self.show_overdue_only = QCheckBox("Show Overdue Only")
        self.show_overdue_only.toggled.connect(self.apply_filters)
        filter_layout.addWidget(self.show_overdue_only)

        layout.addLayout(filter_layout)

        # Todo list area
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        self.todo_container = QWidget()
        self.todo_layout = QVBoxLayout(self.todo_container)
        self.todo_layout.addStretch()

        self.scroll_area.setWidget(self.todo_container)
        layout.addWidget(self.scroll_area)

    def create_stats_widget(self) -> QWidget:
        """Create statistics widget"""
        stats_frame = QFrame()
        stats_frame.setFrameStyle(QFrame.Box)
        stats_frame.setStyleSheet("""
            QFrame {
                border: 1px solid #ddd;
                border-radius: 5px;
                padding: 10px;
                background-color: #f8f9fa;
            }
        """)

        layout = QHBoxLayout(stats_frame)

        # Total todos
        self.total_label = QLabel("Total: 0")
        self.total_label.setFont(QFont("Arial", 12, QFont.Bold))
        layout.addWidget(self.total_label)

        # Completed
        self.completed_label = QLabel("Completed: 0")
        self.completed_label.setStyleSheet("color: #28a745;")
        layout.addWidget(self.completed_label)

        # Pending
        self.pending_label = QLabel("Pending: 0")
        self.pending_label.setStyleSheet("color: #ffc107;")
        layout.addWidget(self.pending_label)

        # In Progress
        self.in_progress_label = QLabel("In Progress: 0")
        self.in_progress_label.setStyleSheet("color: #17a2b8;")
        layout.addWidget(self.in_progress_label)

        # Overdue
        self.overdue_label = QLabel("Overdue: 0")
        self.overdue_label.setStyleSheet("color: #dc3545;")
        layout.addWidget(self.overdue_label)

        # Completion rate
        self.completion_rate_label = QLabel("Completion Rate: 0%")
        layout.addWidget(self.completion_rate_label)

        layout.addStretch()

        return stats_frame

    def setup_connections(self):
        """Setup signal connections"""
        pass

    def refresh_data(self):
        """Refresh todo data and update display"""
        try:
            # Clear existing todos
            for i in reversed(range(self.todo_layout.count() - 1)):  # -1 to keep the stretch
                child = self.todo_layout.itemAt(i).widget()
                if child:
                    child.setParent(None)

            # Get todos and apply filters
            df = self.todo_model.get_all_todos()
            filtered_df = self.apply_current_filters(df)

            # Create todo widgets
            for _, row in filtered_df.iterrows():
                todo_item = TodoItem.from_dict(row.to_dict())
                todo_widget = TodoItemWidget(todo_item)
                todo_widget.item_updated.connect(self.on_todo_updated)
                todo_widget.item_deleted.connect(self.on_todo_deleted)

                # Insert before the stretch
                self.todo_layout.insertWidget(self.todo_layout.count() - 1, todo_widget)

            # Update statistics
            self.update_statistics()

            # Update Google Tasks status if status label exists
            self.update_google_tasks_status()

        except Exception as e:
            self.logger.error(f"Error refreshing todo data: {e}")

    def update_google_tasks_status(self):
        """Update the Google Tasks status display"""
        try:
            # Find the status label and update it
            for i in range(self.layout().itemAt(0).layout().count()):
                widget = self.layout().itemAt(0).layout().itemAt(i).widget()
                if isinstance(widget, QLabel) and hasattr(widget, 'objectName') and widget.objectName() == 'google_tasks_status':
                    status = self.todo_model.get_google_tasks_status()
                    widget.setText(status.get('status_message', 'Unknown status'))
                    break
        except Exception as e:
            self.logger.debug(f"Could not update Google Tasks status: {e}")

    def apply_current_filters(self, df: pd.DataFrame) -> pd.DataFrame:
        """Apply current filter settings to dataframe"""
        if df.empty:
            return df

        filtered_df = df.copy()

        # Status filter
        status_filter = self.status_filter.currentText()
        if status_filter != "All":
            filtered_df = filtered_df[filtered_df['status'] == status_filter]

        # Priority filter
        priority_filter = self.priority_filter.currentText()
        if priority_filter != "All":
            filtered_df = filtered_df[filtered_df['priority'] == priority_filter]

        # Category filter
        category_filter = self.category_filter.currentText()
        if category_filter != "All":
            filtered_df = filtered_df[filtered_df['category'] == category_filter]

        # Overdue filter
        if self.show_overdue_only.isChecked():
            today = date.today().strftime('%Y-%m-%d')
            filtered_df = filtered_df[
                (filtered_df['due_date'].notna()) &
                (filtered_df['due_date'] != '') &
                (filtered_df['due_date'] < today) &
                (filtered_df['status'] != Status.COMPLETED.value)
            ]

        return filtered_df

    def apply_filters(self):
        """Apply filters and refresh display"""
        self.refresh_data()

    def update_statistics(self):
        """Update statistics display"""
        stats = self.todo_model.get_statistics()

        self.total_label.setText(f"Total: {stats['total_todos']}")
        self.completed_label.setText(f"Completed: {stats['completed']}")
        self.pending_label.setText(f"Pending: {stats['pending']}")
        self.in_progress_label.setText(f"In Progress: {stats['in_progress']}")
        self.overdue_label.setText(f"Overdue: {stats['overdue']}")
        self.completion_rate_label.setText(f"Completion Rate: {stats['completion_rate']:.1f}%")

    def add_new_todo(self):
        """Add a new todo item"""
        dialog = TodoEditDialog(parent=self)
        if dialog.exec() == QDialog.Accepted:
            todo_item = dialog.get_todo_item()
            if self.todo_model.add_todo(todo_item):
                self.refresh_data()
                self.logger.info(f"Added new todo: {todo_item.title}")
            else:
                QMessageBox.warning(self, "Error", "Failed to add todo item")

    def on_todo_updated(self, todo_id: int):
        """Handle todo item update"""
        # Find the widget and update the database
        for i in range(self.todo_layout.count() - 1):
            widget = self.todo_layout.itemAt(i).widget()
            if isinstance(widget, TodoItemWidget) and widget.todo_item.id == todo_id:
                if self.todo_model.update_todo(todo_id, widget.todo_item):
                    self.update_statistics()
                    self.logger.info(f"Updated todo: {widget.todo_item.title}")
                else:
                    QMessageBox.warning(self, "Error", "Failed to update todo item")
                break

    def on_todo_deleted(self, todo_id: int):
        """Handle todo item deletion"""
        if self.todo_model.delete_todo(todo_id):
            self.refresh_data()
            self.logger.info(f"Deleted todo ID: {todo_id}")
        else:
            QMessageBox.warning(self, "Error", "Failed to delete todo item")

    # Google Tasks Synchronization Methods
    def sync_from_google_tasks(self):
        """Sync todos from Google Tasks"""
        if not self.todo_model.is_google_tasks_available():
            QMessageBox.warning(self, "Google Tasks", "Google Tasks integration is not available.\n\nPlease install required packages:\npip install google-api-python-client google-auth-httplib2 google-auth-oauthlib")
            return

        try:
            # Show progress dialog
            progress = QProgressDialog("Syncing from Google Tasks...", "Cancel", 0, 0, self)
            progress.setWindowModality(Qt.WindowModal)
            progress.show()

            # Perform sync
            added_count = self.todo_model.sync_from_google_tasks()

            progress.close()

            # Show result
            if added_count > 0:
                QMessageBox.information(self, "Sync Complete", f"Successfully imported {added_count} tasks from Google Tasks!")
                self.refresh_data()
            else:
                QMessageBox.information(self, "Sync Complete", "No new tasks found in Google Tasks.")

            # Always refresh to update status
            self.update_google_tasks_status()

        except Exception as e:
            QMessageBox.critical(self, "Sync Error", f"Failed to sync from Google Tasks:\n{str(e)}")
            self.logger.error(f"Error syncing from Google Tasks: {e}")

    def sync_to_google_tasks(self):
        """Sync todos to Google Tasks"""
        if not self.todo_model.is_google_tasks_available():
            QMessageBox.warning(self, "Google Tasks", "Google Tasks integration is not available.\n\nPlease install required packages:\npip install google-api-python-client google-auth-httplib2 google-auth-oauthlib")
            return

        try:
            # Show progress dialog
            progress = QProgressDialog("Syncing to Google Tasks...", "Cancel", 0, 0, self)
            progress.setWindowModality(Qt.WindowModal)
            progress.show()

            # Perform sync
            results = self.todo_model.sync_to_google_tasks()

            progress.close()

            # Show result
            if results:
                success_count = sum(1 for status in results.values() if status in ['created', 'updated'])
                failed_count = sum(1 for status in results.values() if status == 'failed')

                message = f"Sync completed!\n\nSuccessful: {success_count}\nFailed: {failed_count}"
                QMessageBox.information(self, "Sync Complete", message)
                self.refresh_data()
            else:
                QMessageBox.information(self, "Sync Complete", "No todos to sync.")

            # Always refresh status
            self.update_google_tasks_status()

        except Exception as e:
            QMessageBox.critical(self, "Sync Error", f"Failed to sync to Google Tasks:\n{str(e)}")
            self.logger.error(f"Error syncing to Google Tasks: {e}")

    def full_sync_google_tasks(self):
        """Perform full bidirectional sync with Google Tasks"""
        if not self.todo_model.is_google_tasks_available():
            QMessageBox.warning(self, "Google Tasks", "Google Tasks integration is not available.\n\nPlease install required packages:\npip install google-api-python-client google-auth-httplib2 google-auth-oauthlib")
            return

        # Confirm with user
        reply = QMessageBox.question(
            self,
            "Full Sync",
            "This will perform a bidirectional sync with Google Tasks.\n\nThis may create duplicate tasks if not managed properly.\n\nContinue?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        try:
            # Show progress dialog
            progress = QProgressDialog("Performing full sync with Google Tasks...", "Cancel", 0, 0, self)
            progress.setWindowModality(Qt.WindowModal)
            progress.show()

            # Perform full sync
            results = self.todo_model.full_sync_google_tasks()

            progress.close()

            # Show result
            if 'error' in results:
                QMessageBox.critical(self, "Sync Error", f"Full sync failed:\n{results['error']}")
            else:
                message = f"Full sync completed!\n\nFrom Google: {results['from_google']} tasks\nTo Google: {results['to_google']} tasks"
                QMessageBox.information(self, "Sync Complete", message)
                self.refresh_data()

            # Always refresh status
            self.update_google_tasks_status()

        except Exception as e:
            QMessageBox.critical(self, "Sync Error", f"Failed to perform full sync:\n{str(e)}")
            self.logger.error(f"Error in full sync: {e}")

    def authenticate_google_tasks(self):
        """Authenticate with Google Tasks"""
        try:
            # Show info dialog first
            reply = QMessageBox.question(
                self,
                "Google Tasks Authentication",
                "This will open a web browser for Google authentication.\n\n"
                "You'll need to:\n"
                "1. Sign in to your Google account\n"
                "2. Grant permission to access Google Tasks\n"
                "3. Complete the authorization process\n\n"
                "Continue?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )

            if reply != QMessageBox.Yes:
                return

            # Show progress dialog
            progress = QProgressDialog("Authenticating with Google Tasks...", "Cancel", 0, 0, self)
            progress.setWindowModality(Qt.WindowModal)
            progress.show()

            # Attempt authentication
            success = self.todo_model.authenticate_google_tasks()

            progress.close()

            if success:
                QMessageBox.information(
                    self,
                    "Authentication Successful",
                    "Successfully connected to Google Tasks!\n\n"
                    "You can now use the sync features to synchronize your todos."
                )
                # Refresh the UI to show sync buttons and updated status
                self.setup_ui()
                # Also refresh data to show any synced tasks
                self.refresh_data()
            else:
                QMessageBox.warning(
                    self,
                    "Authentication Failed",
                    "Failed to authenticate with Google Tasks.\n\n"
                    "This might be due to:\n"
                    "• Network connectivity issues\n"
                    "• Google account access restrictions\n"
                    "• App verification requirements\n\n"
                    "Please try again later or check your internet connection."
                )

        except Exception as e:
            QMessageBox.critical(
                self,
                "Authentication Error",
                f"An error occurred during authentication:\n{str(e)}"
            )
            self.logger.error(f"Error during Google Tasks authentication: {e}")

    def show_google_tasks_info(self):
        """Show information about Google Tasks integration"""
        status = self.todo_model.get_google_tasks_status()

        if not status.get('libraries_available', False):
            message = (
                "Google Tasks Integration\n\n"
                "❌ Required libraries not installed\n\n"
                "To enable Google Tasks synchronization, install:\n"
                "pip install google-api-python-client google-auth-httplib2 google-auth-oauthlib\n\n"
                "After installation, restart the application."
            )
        else:
            message = (
                "Google Tasks Integration\n\n"
                "✅ Libraries installed\n"
                f"🔐 Credentials: {'✅ Available' if status.get('credentials_exist') else '❌ Not found'}\n"
                f"🔗 Service: {'✅ Connected' if status.get('available') else '❌ Not connected'}\n\n"
                "Click 'Connect Google Tasks' to authenticate and enable synchronization."
            )

        QMessageBox.information(self, "Google Tasks Integration", message)
