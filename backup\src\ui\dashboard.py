"""
Dashboard Widget Module
Main dashboard showing overview of all modules
"""

from PySide6.QtWidgets import (
    QWidget, Q<PERSON>oxLayout, QHBoxLayout, QGridLayout, QLabel, 
    QFrame, QPushButton, QScrollArea, QGroupBox, QProgressBar
)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QFont, QPalette

from ..core.config import AppConfig
from ..core.data_manager import DataManager


class StatCard(QFrame):
    """Statistics card widget for dashboard"""
    
    def __init__(self, title: str, value: str, subtitle: str = "", parent=None):
        super().__init__(parent)
        
        self.setObjectName("statCard")
        self.setMinimumHeight(120)
        self.setMaximumHeight(120)
        
        # Create layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(5)
        
        # Title
        title_label = QLabel(title)
        title_label.setObjectName("statCardTitle")
        font = QFont()
        font.setPointSize(10)
        title_label.setFont(font)
        layout.addWidget(title_label)
        
        # Value
        self.value_label = QLabel(value)
        self.value_label.setObjectName("statCardValue")
        font = QFont()
        font.setPointSize(24)
        font.setBold(True)
        self.value_label.setFont(font)
        self.value_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.value_label)
        
        # Subtitle
        if subtitle:
            subtitle_label = QLabel(subtitle)
            subtitle_label.setObjectName("statCardSubtitle")
            font = QFont()
            font.setPointSize(9)
            subtitle_label.setFont(font)
            subtitle_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(subtitle_label)
        
        layout.addStretch()
    
    def update_value(self, value: str):
        """Update the card value"""
        self.value_label.setText(value)


class QuickActionCard(QFrame):
    """Quick action card for dashboard"""
    
    def __init__(self, title: str, description: str, button_text: str, parent=None):
        super().__init__(parent)
        
        self.setObjectName("quickActionCard")
        self.setMinimumHeight(100)
        
        # Create layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)
        
        # Title
        title_label = QLabel(title)
        title_label.setObjectName("quickActionTitle")
        font = QFont()
        font.setPointSize(12)
        font.setBold(True)
        title_label.setFont(font)
        layout.addWidget(title_label)
        
        # Description
        desc_label = QLabel(description)
        desc_label.setObjectName("quickActionDescription")
        desc_label.setWordWrap(True)
        layout.addWidget(desc_label)
        
        # Button
        self.action_button = QPushButton(button_text)
        self.action_button.setObjectName("quickActionButton")
        self.action_button.setMinimumHeight(35)
        layout.addWidget(self.action_button)


class DashboardWidget(QWidget):
    """Main dashboard widget"""
    
    def __init__(self, data_manager: DataManager, config: AppConfig, parent=None):
        super().__init__(parent)
        
        self.data_manager = data_manager
        self.config = config
        
        self.setup_ui()
        self.setup_refresh_timer()
        self.refresh_data()
    
    def setup_ui(self):
        """Setup the dashboard UI"""
        # Create main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # Create header
        self.create_header(main_layout)
        
        # Create scroll area for content
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setObjectName("dashboardScrollArea")
        
        # Create content widget
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(20)
        
        # Create statistics section
        self.create_statistics_section(content_layout)
        
        # Create quick actions section
        self.create_quick_actions_section(content_layout)
        
        # Create recent activity section
        self.create_recent_activity_section(content_layout)
        
        # Add stretch
        content_layout.addStretch()
        
        scroll_area.setWidget(content_widget)
        main_layout.addWidget(scroll_area)
    
    def create_header(self, layout):
        """Create dashboard header"""
        header_frame = QFrame()
        header_frame.setObjectName("dashboardHeader")
        
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(0, 0, 0, 0)
        
        # Welcome message
        welcome_label = QLabel("Welcome to Your Personal Finance Dashboard")
        welcome_label.setObjectName("dashboardWelcome")
        font = QFont()
        font.setPointSize(18)
        font.setBold(True)
        welcome_label.setFont(font)
        header_layout.addWidget(welcome_label)

        header_layout.addStretch()

        # Refresh button
        self.refresh_button = QPushButton("🔄 Refresh")
        self.refresh_button.setObjectName("refreshButton")
        self.refresh_button.clicked.connect(self.refresh_dashboard)
        header_layout.addWidget(self.refresh_button)
        
        header_layout.addStretch()
        
        # Refresh button
        refresh_button = QPushButton("Refresh")
        refresh_button.setObjectName("refreshButton")
        refresh_button.clicked.connect(self.refresh_data)
        header_layout.addWidget(refresh_button)
        
        layout.addWidget(header_frame)
    
    def create_statistics_section(self, layout):
        """Create statistics cards section"""
        # Section title
        stats_title = QLabel("Overview")
        stats_title.setObjectName("sectionTitle")
        font = QFont()
        font.setPointSize(14)
        font.setBold(True)
        stats_title.setFont(font)
        layout.addWidget(stats_title)
        
        # Statistics grid
        stats_frame = QFrame()
        stats_frame.setObjectName("statsFrame")
        stats_layout = QGridLayout(stats_frame)
        stats_layout.setSpacing(15)
        
        # Create stat cards
        self.stat_cards = {}
        
        # Total Expenses
        self.stat_cards['expenses'] = StatCard(
            "Total Expenses", "₹0", "This Month"
        )
        stats_layout.addWidget(self.stat_cards['expenses'], 0, 0)
        
        # Total Income
        self.stat_cards['income'] = StatCard(
            "Total Income", "₹0", "This Month"
        )
        stats_layout.addWidget(self.stat_cards['income'], 0, 1)
        
        # Savings
        self.stat_cards['savings'] = StatCard(
            "Savings", "₹0", "This Month"
        )
        stats_layout.addWidget(self.stat_cards['savings'], 0, 2)
        
        # Habits Completed
        self.stat_cards['habits'] = StatCard(
            "Habits", "0%", "Completion Rate"
        )
        stats_layout.addWidget(self.stat_cards['habits'], 1, 0)
        
        # Attendance
        self.stat_cards['attendance'] = StatCard(
            "Attendance", "0%", "Current Semester"
        )
        stats_layout.addWidget(self.stat_cards['attendance'], 1, 1)
        
        # Active Tasks
        self.stat_cards['tasks'] = StatCard(
            "Active Tasks", "0", "Pending"
        )
        stats_layout.addWidget(self.stat_cards['tasks'], 1, 2)
        
        layout.addWidget(stats_frame)
    
    def create_quick_actions_section(self, layout):
        """Create quick actions section"""
        # Section title
        actions_title = QLabel("Quick Actions")
        actions_title.setObjectName("sectionTitle")
        font = QFont()
        font.setPointSize(14)
        font.setBold(True)
        actions_title.setFont(font)
        layout.addWidget(actions_title)
        
        # Quick actions grid
        actions_frame = QFrame()
        actions_frame.setObjectName("actionsFrame")
        actions_layout = QGridLayout(actions_frame)
        actions_layout.setSpacing(15)
        
        # Create action cards
        self.action_cards = {}
        
        # Add Expense
        self.action_cards['add_expense'] = QuickActionCard(
            "Add Expense", 
            "Quickly record a new expense transaction",
            "Add Expense"
        )
        actions_layout.addWidget(self.action_cards['add_expense'], 0, 0)
        
        # Update Income Goal
        self.action_cards['update_income'] = QuickActionCard(
            "Update Income",
            "Record today's income progress",
            "Update Income"
        )
        actions_layout.addWidget(self.action_cards['update_income'], 0, 1)
        
        # Mark Habits
        self.action_cards['mark_habits'] = QuickActionCard(
            "Mark Habits",
            "Update today's habit completion",
            "Mark Habits"
        )
        actions_layout.addWidget(self.action_cards['mark_habits'], 1, 0)
        
        # Add Task
        self.action_cards['add_task'] = QuickActionCard(
            "Add Task",
            "Create a new task or reminder",
            "Add Task"
        )
        actions_layout.addWidget(self.action_cards['add_task'], 1, 1)
        
        layout.addWidget(actions_frame)
    
    def create_recent_activity_section(self, layout):
        """Create recent activity section"""
        # Section title
        activity_title = QLabel("Recent Activity")
        activity_title.setObjectName("sectionTitle")
        font = QFont()
        font.setPointSize(14)
        font.setBold(True)
        activity_title.setFont(font)
        layout.addWidget(activity_title)
        
        # Activity frame
        activity_frame = QFrame()
        activity_frame.setObjectName("activityFrame")
        activity_frame.setMinimumHeight(200)
        
        activity_layout = QVBoxLayout(activity_frame)
        activity_layout.setContentsMargins(15, 15, 15, 15)
        
        # Placeholder for recent activity
        self.activity_label = QLabel("No recent activity")
        self.activity_label.setAlignment(Qt.AlignCenter)
        activity_layout.addWidget(self.activity_label)
        
        layout.addWidget(activity_frame)
    
    def setup_refresh_timer(self):
        """Setup automatic refresh timer"""
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_data)
        self.refresh_timer.start(60000)  # Refresh every minute
    
    def refresh_data(self):
        """Refresh dashboard data"""
        try:
            # Get expense summary
            expense_summary = self.get_module_summary("expenses")

            # Update stat cards with real data
            if expense_summary:
                self.stat_cards['expenses'].update_value(f"₹{expense_summary.get('total_amount', 0):.0f}")
            else:
                self.stat_cards['expenses'].update_value("₹0")

            # Get income summary
            income_summary = self.get_module_summary("income")

            if income_summary:
                self.stat_cards['income'].update_value(f"₹{income_summary.get('total_earned', 0):.0f}")
                # Calculate savings (income - expenses)
                total_income = income_summary.get('total_earned', 0)
                total_expenses = expense_summary.get('total_amount', 0) if expense_summary else 0
                savings = total_income - total_expenses
                self.stat_cards['savings'].update_value(f"₹{savings:.0f}")
            else:
                self.stat_cards['income'].update_value("₹0")
                self.stat_cards['savings'].update_value("₹0")

            # Get habit summary
            habit_summary = self.get_module_summary("habits")

            if habit_summary:
                self.stat_cards['habits'].update_value(f"{habit_summary.get('today_completion_rate', 0):.0f}%")
            else:
                self.stat_cards['habits'].update_value("0%")

            # Get attendance summary
            attendance_summary = self.get_module_summary("attendance")

            if attendance_summary:
                self.stat_cards['attendance'].update_value(f"{attendance_summary.get('overall_percentage', 0):.0f}%")
            else:
                self.stat_cards['attendance'].update_value("0%")

            # Placeholder data for other modules
            self.stat_cards['tasks'].update_value("5")

            # Update activity
            self.activity_label.setText("Dashboard refreshed successfully")

        except Exception as e:
            print(f"Error refreshing dashboard data: {e}")
            # Set default values to prevent crashes
            for key in self.stat_cards:
                if key == 'expenses' or key == 'income' or key == 'savings':
                    self.stat_cards[key].update_value("₹0")
                elif key == 'habits' or key == 'attendance':
                    self.stat_cards[key].update_value("0%")
                else:
                    self.stat_cards[key].update_value("0")

            self.activity_label.setText(f"Error refreshing data: {e}")
    
    def get_module_summary(self, module: str):
        """Get summary data for a specific module"""
        try:
            if module == "expenses":
                # Import here to avoid circular imports
                from ..modules.expenses.models import ExpenseDataModel
                expense_model = ExpenseDataModel(self.data_manager)
                return expense_model.get_expense_summary()
            elif module == "income":
                # Import here to avoid circular imports
                from ..modules.income.models import IncomeDataModel
                income_model = IncomeDataModel(self.data_manager)
                return income_model.get_income_summary()
            elif module == "habits":
                # Import here to avoid circular imports
                from ..modules.habits.models import HabitDataModel
                habit_model = HabitDataModel(self.data_manager)
                return habit_model.get_habit_summary()
            elif module == "attendance":
                # Import simplified attendance model to avoid recursion
                from ..modules.attendance.simple_models import SimpleAttendanceDataManager
                attendance_model = SimpleAttendanceDataManager(str(self.data_manager.data_dir))
                return attendance_model.get_summary()

            return self.data_manager.get_module_summary(module)
        except Exception as e:
            print(f"Error getting summary for module {module}: {e}")
            # Return empty summary to prevent crashes
            return {}

    def refresh_dashboard(self):
        """Refresh dashboard with latest data from all modules"""
        try:
            # Update financial overview with real data
            self.update_financial_overview()

            # Update recent activity
            self.update_recent_activity()

            # Update module summaries
            self.update_module_summaries()

        except Exception as e:
            print(f"Error refreshing dashboard: {e}")

    def update_financial_overview(self):
        """Update financial overview with real data"""
        try:
            total_income = 0.0
            total_expenses = 0.0
            total_investments = 0.0

            # Get expense data
            try:
                expense_df = self.data_manager.read_csv('expenses', 'expenses.csv',
                                                      ['id', 'amount', 'category', 'description', 'date'])
                if not expense_df.empty:
                    total_expenses = expense_df['amount'].sum()
            except:
                pass

            # Get income data
            try:
                income_df = self.data_manager.read_csv('income', 'income_records.csv',
                                                     ['id', 'amount', 'source', 'description', 'date'])
                if not income_df.empty:
                    total_income = income_df['amount'].sum()
            except:
                pass

            # Get investment data
            try:
                investment_df = self.data_manager.read_csv('investments', 'investments.csv',
                                                         ['id', 'current_value', 'total_investment'])
                if not investment_df.empty:
                    total_investments = investment_df['current_value'].sum()
            except:
                pass

            # Calculate savings
            total_savings = total_income - total_expenses

            # Update stat cards
            if hasattr(self, 'stat_cards'):
                self.stat_cards['expenses'].update_value(f"₹{total_expenses:,.2f}")
                self.stat_cards['income'].update_value(f"₹{total_income:,.2f}")
                self.stat_cards['savings'].update_value(f"₹{total_savings:,.2f}")
                if 'investments' in self.stat_cards:
                    self.stat_cards['investments'].update_value(f"₹{total_investments:,.2f}")

        except Exception as e:
            print(f"Error updating financial overview: {e}")

    def update_recent_activity(self):
        """Update recent activity section"""
        try:
            recent_items = []

            # Get recent expenses
            try:
                expense_df = self.data_manager.read_csv('expenses', 'expenses.csv',
                                                      ['id', 'amount', 'category', 'description', 'date'])
                if not expense_df.empty:
                    recent_expenses = expense_df.tail(3)
                    for _, row in recent_expenses.iterrows():
                        recent_items.append(f"💸 {row['description']} - ₹{row['amount']:.2f}")
            except:
                pass

            # Get recent income
            try:
                income_df = self.data_manager.read_csv('income', 'income_records.csv',
                                                     ['id', 'amount', 'source', 'description', 'date'])
                if not income_df.empty:
                    recent_income = income_df.tail(2)
                    for _, row in recent_income.iterrows():
                        recent_items.append(f"💰 {row['description']} - ₹{row['amount']:.2f}")
            except:
                pass

            # Get recent todos
            try:
                todos_df = self.data_manager.read_csv('todos', 'todo_items.csv',
                                                    ['id', 'title', 'status'])
                if not todos_df.empty:
                    recent_todos = todos_df[todos_df['status'] == 'Completed'].tail(2)
                    for _, row in recent_todos.iterrows():
                        recent_items.append(f"✅ Completed: {row['title']}")
            except:
                pass

            # Update recent activity display if it exists
            if hasattr(self, 'recent_activity_list') and recent_items:
                # Clear existing items
                for i in reversed(range(self.recent_activity_list.count())):
                    self.recent_activity_list.takeItem(i)

                # Add new items
                for item in recent_items[-5:]:  # Show last 5 items
                    self.recent_activity_list.addItem(item)

        except Exception as e:
            print(f"Error updating recent activity: {e}")

    def update_module_summaries(self):
        """Update module summary cards"""
        try:
            modules = ['expenses', 'income', 'habits', 'todos', 'investments', 'budget']

            for module in modules:
                try:
                    summary = self.get_module_summary(module)
                    # Update module-specific displays based on summary data
                    # This would be expanded based on specific module summary formats
                except Exception as e:
                    print(f"Error updating summary for {module}: {e}")

        except Exception as e:
            print(f"Error updating module summaries: {e}")
