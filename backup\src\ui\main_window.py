"""
Main Window Module
The primary application window with collapsible sidebar navigation
"""

import logging
from PySide6.QtWidgets import (
    QMainWindow, QWidget, QHBoxLayout, QVBoxLayout, QStackedWidget,
    QSplitter, QFrame, QLabel, QPushButton, QToolButton, QStatusBar,
    QMenuBar, QMenu, QMessageBox, QApplication, QDialog
)
from PySide6.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, Signal
from PySide6.QtGui import QAction, QIcon, QFont, QPixmap

from ..core.config import AppConfig, SettingsManager
from ..core.data_manager import DataManager
from .sidebar import Sidebar
from .dashboard import DashboardWidget
from .global_search import GlobalSearchDialog
from .styles import StyleManager
from ..modules.expenses.widgets import ExpenseTrackerWidget
from ..modules.income.widgets import IncomeTrackerWidget
from ..modules.habits.widgets import HabitTrackerWidget
from ..modules.attendance.widgets import AttendanceTrackerWidget
from ..modules.todos.widgets import TodoTrackerWidget
from ..modules.investments.widgets import InvestmentTrackerWidget
from ..modules.budget.widgets import BudgetPlannerWidget


class MainWindow(QMainWindow):
    """Main application window with sidebar navigation"""
    
    # Signals
    module_changed = Signal(str)
    
    def __init__(self, data_manager: DataManager, config: AppConfig):
        super().__init__()

        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self.logger.info("="*60)
        self.logger.info("INITIALIZING MAIN WINDOW")
        self.logger.info("="*60)

        self.data_manager = data_manager
        self.config = config
        self.settings_manager = SettingsManager()
        self.style_manager = StyleManager(QApplication.instance())
        self.logger.debug("Core components initialized")

        # Initialize UI
        self.logger.info("Setting up UI components...")
        self.setup_ui()
        self.setup_menu_bar()
        self.setup_status_bar()
        self.setup_connections()
        self.apply_theme()
        self.logger.info("UI setup complete")

        # Restore window state
        self.logger.debug("Restoring window state...")
        self.restore_window_state()

        # Setup auto-save timer
        self.logger.debug("Setting up auto-save...")
        self.setup_auto_save()

        self.logger.info("MainWindow initialization complete")
    
    def setup_ui(self):
        """Setup the main UI layout"""
        # Set window properties
        self.setWindowTitle(f"{self.config.app_name} v{self.config.app_version}")
        self.setMinimumSize(800, 600)
        self.resize(self.config.window_width, self.config.window_height)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create main layout
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # Create splitter for resizable sidebar
        self.splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(self.splitter)
        
        # Create sidebar
        self.sidebar = Sidebar(self.config)
        self.splitter.addWidget(self.sidebar)
        
        # Create content area
        self.content_widget = QStackedWidget()
        self.splitter.addWidget(self.content_widget)
        
        # Set splitter proportions
        self.splitter.setSizes([self.config.sidebar_width, 
                               self.config.window_width - self.config.sidebar_width])
        self.splitter.setCollapsible(0, True)
        self.splitter.setCollapsible(1, False)
        
        # Create content pages
        self.setup_content_pages()
    
    def setup_content_pages(self):
        """Setup all content pages"""
        self.logger.info("Setting up content pages...")

        try:
            # Dashboard (default page)
            self.logger.debug("Creating Dashboard widget...")
            self.dashboard = DashboardWidget(self.data_manager, self.config)
            self.content_widget.addWidget(self.dashboard)
            self.logger.debug("Dashboard widget created successfully")

            # Expense Tracker
            self.logger.debug("Creating Expense Tracker widget...")
            self.expense_tracker = ExpenseTrackerWidget(self.data_manager, self.config)
            self.content_widget.addWidget(self.expense_tracker)
            self.logger.debug("Expense Tracker widget created successfully")

            # Income Tracker
            self.logger.debug("Creating Income Tracker widget...")
            self.income_tracker = IncomeTrackerWidget(self.data_manager, self.config)
            self.content_widget.addWidget(self.income_tracker)
            self.logger.debug("Income Tracker widget created successfully")

            # Habit Tracker
            self.logger.debug("Creating Habit Tracker widget...")
            self.habit_tracker = HabitTrackerWidget(self.data_manager, self.config)
            self.content_widget.addWidget(self.habit_tracker)
            self.logger.debug("Habit Tracker widget created successfully")

            # Attendance Tracker - RE-ENABLED WITH SIMPLIFIED IMPLEMENTATION
            self.logger.debug("Creating Simplified Attendance Tracker widget...")
            try:
                from modules.attendance.simple_widgets import SimpleAttendanceTrackerWidget
                self.attendance_tracker = SimpleAttendanceTrackerWidget(self.data_manager, self.config)
                self.content_widget.addWidget(self.attendance_tracker)
                self.logger.debug("Simplified Attendance Tracker widget created successfully")
            except Exception as e:
                self.logger.error(f"Failed to create Simplified Attendance Tracker: {e}")
                self.attendance_tracker = None

            # To-Do List Module
            self.logger.debug("Creating To-Do List widget...")
            try:
                self.todo_tracker = TodoTrackerWidget(self.data_manager, self.config)
                self.content_widget.addWidget(self.todo_tracker)
                self.logger.debug("To-Do List widget created successfully")
            except Exception as e:
                self.logger.error(f"Failed to create To-Do List widget: {e}")
                self.todo_tracker = None

            # Investment Tracker Module
            self.logger.debug("Creating Investment Tracker widget...")
            try:
                self.investment_tracker = InvestmentTrackerWidget(self.data_manager, self.config)
                self.content_widget.addWidget(self.investment_tracker)
                self.logger.debug("Investment Tracker widget created successfully")
            except Exception as e:
                self.logger.error(f"Failed to create Investment Tracker widget: {e}")
                self.investment_tracker = None

            # Budget Planner Module
            self.logger.debug("Creating Budget Planner widget...")
            try:
                self.budget_planner = BudgetPlannerWidget(self.data_manager, self.config)
                self.content_widget.addWidget(self.budget_planner)
                self.logger.debug("Budget Planner widget created successfully")
            except Exception as e:
                self.logger.error(f"Failed to create Budget Planner widget: {e}")
                self.budget_planner = None

        except Exception as e:
            self.logger.error(f"Critical error in setup_content_pages: {e}")
            raise

        # Module widgets mapping
        self.module_widgets = {
            'dashboard': self.dashboard,
            'expenses': self.expense_tracker,
            'income': self.income_tracker,
            'habits': self.habit_tracker,
            'attendance': self.attendance_tracker,  # Will be None if failed to load
            'todos': self.todo_tracker,  # Will be None if failed to load
            'investments': self.investment_tracker,  # Will be None if failed to load
            'budget': self.budget_planner  # Will be None if failed to load
        }

        # Log module status
        for module_name, widget in self.module_widgets.items():
            status = "✅ LOADED" if widget is not None else "❌ NOT LOADED"
            self.logger.info(f"Module '{module_name}': {status}")

        # Set dashboard as default
        self.content_widget.setCurrentWidget(self.dashboard)
        self.logger.info("Content pages setup complete")
    
    def setup_menu_bar(self):
        """Setup the menu bar"""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu("&File")

        # New action
        new_action = QAction("&New Entry", self)
        new_action.setShortcut("Ctrl+N")
        new_action.triggered.connect(self.new_entry)
        file_menu.addAction(new_action)

        file_menu.addSeparator()

        # Import/Export actions
        import_action = QAction("&Import Data", self)
        import_action.triggered.connect(self.import_data)
        file_menu.addAction(import_action)

        export_action = QAction("&Export Data", self)
        export_action.triggered.connect(self.export_data)
        file_menu.addAction(export_action)
        
        file_menu.addSeparator()
        
        # Backup actions
        backup_action = QAction("Create &Backup", self)
        backup_action.triggered.connect(self.create_backup)
        file_menu.addAction(backup_action)
        
        file_menu.addSeparator()
        
        # Exit action
        exit_action = QAction("E&xit", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # View menu
        view_menu = menubar.addMenu("&View")
        
        # Theme actions
        theme_menu = view_menu.addMenu("&Theme")
        
        light_theme_action = QAction("&Light", self)
        light_theme_action.triggered.connect(lambda: self.change_theme("light"))
        theme_menu.addAction(light_theme_action)
        
        dark_theme_action = QAction("&Dark", self)
        dark_theme_action.triggered.connect(lambda: self.change_theme("dark"))
        theme_menu.addAction(dark_theme_action)
        
        view_menu.addSeparator()
        
        # Sidebar toggle
        toggle_sidebar_action = QAction("Toggle &Sidebar", self)
        toggle_sidebar_action.setShortcut("Ctrl+B")
        toggle_sidebar_action.triggered.connect(self.toggle_sidebar)
        view_menu.addAction(toggle_sidebar_action)
        
        # Tools menu
        tools_menu = menubar.addMenu("&Tools")

        # Global Search action
        search_action = QAction("&Global Search", self)
        search_action.setShortcut("Ctrl+F")
        search_action.triggered.connect(self.show_global_search)
        tools_menu.addAction(search_action)

        tools_menu.addSeparator()

        # Settings action
        settings_action = QAction("&Settings", self)
        settings_action.triggered.connect(self.show_settings)
        tools_menu.addAction(settings_action)
        
        # Help menu
        help_menu = menubar.addMenu("&Help")
        
        # About action
        about_action = QAction("&About", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def setup_status_bar(self):
        """Setup the status bar"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # Add status labels
        self.status_label = QLabel("Ready")
        self.status_bar.addWidget(self.status_label)
        
        # Add permanent widgets
        self.records_label = QLabel("Records: 0")
        self.status_bar.addPermanentWidget(self.records_label)
        
        self.auto_save_label = QLabel("Auto-save: ON")
        self.status_bar.addPermanentWidget(self.auto_save_label)
    
    def setup_connections(self):
        """Setup signal connections with enhanced error handling"""
        try:
            # Sidebar connections
            if hasattr(self.sidebar, 'module_selected'):
                self.sidebar.module_selected.connect(self.switch_module)
                self.logger.debug("Sidebar module_selected signal connected")

            if hasattr(self.sidebar, 'sidebar_toggled'):
                self.sidebar.sidebar_toggled.connect(self.on_sidebar_toggled)
                self.logger.debug("Sidebar toggle signal connected")

            # Data manager connections
            if hasattr(self.data_manager, 'data_changed'):
                self.data_manager.data_changed.connect(self.on_data_changed)
                self.logger.debug("Data manager data_changed signal connected")

            if hasattr(self.data_manager, 'error_occurred'):
                self.data_manager.error_occurred.connect(self.show_error)
                self.logger.debug("Data manager error_occurred signal connected")

            self.logger.info("✅ All signal connections established successfully")

        except Exception as e:
            self.logger.error(f"❌ Failed to setup signal connections: {e}")
            self.show_error(f"Failed to setup application connections: {str(e)}")
    
    def setup_auto_save(self):
        """Setup auto-save timer"""
        self.auto_save_timer = QTimer()
        self.auto_save_timer.timeout.connect(self.auto_save)
        self.auto_save_timer.start(self.config.auto_save_interval * 1000)  # Convert to milliseconds
    
    def apply_theme(self):
        """Apply the current theme"""
        self.style_manager.apply_theme(self.config.theme)
    
    def switch_module(self, module_name: str):
        """Switch to a different module"""
        self.logger.debug(f"switch_module called with: {module_name}")

        # Remove the forced disable - we're going to fix the module
        # if module_name == "attendance":
        #     self.logger.info(f"🚨 ATTENDANCE MODULE CLICKED - Showing disabled dialog")
        #     self.show_attendance_disabled_dialog()
        #     return

        if module_name in self.module_widgets:
            widget = self.module_widgets[module_name]
            self.logger.debug(f"Widget for {module_name}: {widget}")

            if widget is not None:
                self.content_widget.setCurrentWidget(widget)
                self.module_changed.emit(module_name)
                self.status_label.setText(f"Switched to {module_name.title()}")
                self.logger.debug(f"Successfully switched to {module_name}")
            else:
                # Handle specific disabled modules with detailed feedback
                self.logger.debug(f"Module {module_name} is disabled (widget is None)")
                if module_name == "attendance":
                    self.show_attendance_disabled_dialog()
                else:
                    # Generic message for other modules
                    self.status_label.setText(f"{module_name.title()} module coming soon...")
        else:
            self.logger.warning(f"Module {module_name} not found in module_widgets")
            self.status_label.setText(f"Module {module_name} not found")
    
    def toggle_sidebar(self):
        """Toggle sidebar visibility"""
        self.sidebar.toggle_sidebar()
    
    def on_sidebar_toggled(self, collapsed: bool):
        """Handle sidebar toggle"""
        if collapsed:
            self.splitter.setSizes([self.config.sidebar_collapsed_width, 
                                   self.width() - self.config.sidebar_collapsed_width])
        else:
            self.splitter.setSizes([self.config.sidebar_width, 
                                   self.width() - self.config.sidebar_width])
    
    def on_data_changed(self, module: str, operation: str):
        """Handle data changes"""
        self.status_label.setText(f"Data {operation} in {module}")
        # Update record count if needed
        self.update_record_count()
    
    def update_record_count(self):
        """Update the record count in status bar"""
        # This will be implemented when modules are added
        pass
    
    def show_error(self, error_message: str, title: str = "Error", details: str = None):
        """Show enhanced error message to user with logging"""
        self.logger.error(f"User error displayed: {error_message}")

        # Create enhanced error dialog
        msg_box = QMessageBox(self)
        msg_box.setIcon(QMessageBox.Critical)
        msg_box.setWindowTitle(title)
        msg_box.setText(error_message)

        # Add details if provided
        if details:
            msg_box.setDetailedText(details)

        # Add helpful buttons
        msg_box.setStandardButtons(QMessageBox.Ok | QMessageBox.Help)
        msg_box.setDefaultButton(QMessageBox.Ok)

        # Show status bar message
        self.status_label.setText(f"❌ Error: {error_message}")

        # Handle help button
        result = msg_box.exec()
        if result == QMessageBox.Help:
            self.show_help_dialog()

    def show_warning(self, message: str, title: str = "Warning"):
        """Show warning message to user"""
        self.logger.warning(f"User warning displayed: {message}")
        QMessageBox.warning(self, title, message)
        self.status_label.setText(f"⚠️ Warning: {message}")

    def show_info(self, message: str, title: str = "Information"):
        """Show information message to user"""
        self.logger.info(f"User info displayed: {message}")
        QMessageBox.information(self, title, message)
        self.status_label.setText(f"ℹ️ {message}")

    def show_success(self, message: str):
        """Show success message in status bar"""
        self.logger.info(f"Success: {message}")
        self.status_label.setText(f"✅ {message}")

    def show_attendance_disabled_dialog(self):
        """Show dialog explaining why attendance module is disabled"""
        dialog_text = """
        <h3>⚠️ Attendance Module Temporarily Disabled</h3>

        <p><b>The Attendance Tracker module is currently disabled due to technical issues.</b></p>

        <p><b>Issue Details:</b></p>
        <ul>
        <li>Pandas DataFrame operations are causing infinite recursion</li>
        <li>This prevents the module from loading properly</li>
        <li>The issue affects data reading and writing operations</li>
        </ul>

        <p><b>Current Status:</b></p>
        <ul>
        <li>🔧 The development team is working on a fix</li>
        <li>📊 Your existing attendance data is safe and preserved</li>
        <li>🚀 The module will be restored in the next update</li>
        </ul>

        <p><b>Alternative Solutions:</b></p>
        <ul>
        <li>Use the other modules (Expenses, Income, Habits, etc.) which are fully functional</li>
        <li>Check back after the next application update</li>
        <li>Contact support if you need urgent access to attendance data</li>
        </ul>

        <p><i>We apologize for the inconvenience and appreciate your patience.</i></p>
        """

        msg_box = QMessageBox(self)
        msg_box.setIcon(QMessageBox.Warning)
        msg_box.setWindowTitle("Attendance Module - Temporarily Disabled")
        msg_box.setText(dialog_text)
        msg_box.setStandardButtons(QMessageBox.Ok)
        msg_box.exec()

        # Update status bar
        self.status_label.setText("⚠️ Attendance module is temporarily disabled due to technical issues")

    def show_help_dialog(self):
        """Show help dialog with troubleshooting information"""
        help_text = """
        <h3>Troubleshooting Guide</h3>
        <p><b>Common Issues:</b></p>
        <ul>
        <li><b>Data not saving:</b> Check file permissions in the data directory</li>
        <li><b>Module not loading:</b> Check the logs for detailed error information</li>
        <li><b>Performance issues:</b> Try restarting the application</li>
        <li><b>Display problems:</b> Check your screen resolution and scaling</li>
        </ul>

        <p><b>Log Files:</b></p>
        <p>Detailed logs are saved to: <code>logs/app_debug.log</code></p>

        <p><b>Data Location:</b></p>
        <p>Your data is stored in: <code>data/</code> directory</p>

        <p><b>Backup:</b></p>
        <p>Automatic backups are created in: <code>data/.backups/</code></p>
        """

        help_dialog = QMessageBox(self)
        help_dialog.setIcon(QMessageBox.Information)
        help_dialog.setWindowTitle("Help & Troubleshooting")
        help_dialog.setText(help_text)
        help_dialog.setStandardButtons(QMessageBox.Ok)
        help_dialog.exec()
    
    def change_theme(self, theme: str):
        """Change application theme"""
        self.config.theme = theme
        self.apply_theme()
        self.status_label.setText(f"Theme changed to {theme}")
    
    def new_entry(self):
        """Create new entry in current module"""
        # This will be implemented when modules are added
        self.status_label.setText("New entry functionality coming soon...")
    
    def import_data(self):
        """Import data from file"""
        # This will be implemented later
        self.status_label.setText("Import functionality coming soon...")
    
    def export_data(self):
        """Export data to file"""
        # This will be implemented later
        self.status_label.setText("Export functionality coming soon...")
    
    def create_backup(self):
        """Create data backup"""
        if self.data_manager.backup_data():
            QMessageBox.information(self, "Backup", "Backup created successfully!")
            self.status_label.setText("Backup created successfully")
        else:
            QMessageBox.warning(self, "Backup", "Failed to create backup!")
    
    def show_global_search(self):
        """Show global search dialog"""
        try:
            search_dialog = GlobalSearchDialog(self.data_manager, self)
            search_dialog.exec()
        except Exception as e:
            self.logger.error(f"Error opening global search: {e}")
            self.show_error("Failed to open global search dialog")

    def show_settings(self):
        """Show settings dialog"""
        try:
            from .settings_dialog import SettingsDialog

            settings_dialog = SettingsDialog(self.config, self.data_manager, self)
            settings_dialog.settings_changed.connect(self.on_settings_changed)

            if settings_dialog.exec() == QDialog.Accepted:
                self.status_label.setText("Settings saved successfully")

        except Exception as e:
            self.logger.error(f"Error opening settings dialog: {e}")
            self.show_error("Failed to open settings dialog")

    def on_settings_changed(self):
        """Handle settings changes"""
        try:
            # Reload config
            self.config = AppConfig.load_from_file()

            # Apply theme changes if needed
            self.style_manager.apply_theme(self.config.theme)

            # Update other components as needed
            self.status_label.setText("Settings applied successfully")

        except Exception as e:
            self.logger.error(f"Error applying settings: {e}")
            self.show_error("Failed to apply settings changes")

    def show_about(self):
        """Show about dialog"""
        QMessageBox.about(self, "About", 
                         f"{self.config.app_name} v{self.config.app_version}\n\n"
                         "A comprehensive personal finance management application\n"
                         "built with PySide6.")
    
    def auto_save(self):
        """Perform auto-save"""
        # Auto-save is handled by the data manager
        self.status_label.setText("Auto-saved")
    
    def save_window_state(self):
        """Save window state"""
        if self.config.remember_window_state:
            self.settings_manager.save_window_geometry(self.saveGeometry())
            self.settings_manager.save_window_state(self.saveState())
    
    def restore_window_state(self):
        """Restore window state"""
        if self.config.remember_window_state:
            geometry = self.settings_manager.load_window_geometry()
            if geometry:
                self.restoreGeometry(geometry)
            
            state = self.settings_manager.load_window_state()
            if state:
                self.restoreState(state)
    
    def closeEvent(self, event):
        """Handle window close event"""
        # Save window state
        self.save_window_state()
        
        # Save configuration
        self.config.save_to_file()
        
        # Accept the close event
        event.accept()
