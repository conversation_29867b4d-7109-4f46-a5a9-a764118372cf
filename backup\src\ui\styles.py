"""
Style Manager Module
Handles application theming and styling
"""

from typing import Dict


class StyleManager:
    """Manages application styles and themes"""

    def __init__(self, app=None):
        self.app = app
        self.themes = {
            "dark": self._get_dark_theme(),
            "light": self._get_light_theme()
        }

    def get_stylesheet(self, theme: str = "dark") -> str:
        """Get stylesheet for specified theme"""
        return self.themes.get(theme, self.themes["dark"])

    def apply_theme(self, theme: str = "dark"):
        """Apply theme to the application"""
        if self.app:
            stylesheet = self.get_stylesheet(theme)
            self.app.setStyleSheet(stylesheet)
    
    def _get_dark_theme(self) -> str:
        """Get dark theme stylesheet"""
        return """
        /* Main Window */
        QMainWindow {
            background-color: #1e1e1e;
            color: #ffffff;
        }
        
        /* Sidebar */
        #sidebar {
            background-color: #252526;
            border-right: 1px solid #3e3e42;
        }
        
        #sidebarHeader {
            background-color: #2d2d30;
            border-bottom: 1px solid #3e3e42;
        }
        
        #sidebarTitle {
            color: #ffffff;
            font-weight: bold;
        }
        
        #toggleButton {
            background-color: #3e3e42;
            border: none;
            border-radius: 4px;
            color: #ffffff;
            font-size: 14px;
        }
        
        #toggleButton:hover {
            background-color: #4e4e52;
        }
        
        #sidebarButton {
            background-color: transparent;
            border: none;
            color: #cccccc;
            text-align: left;
            padding: 10px 15px;
            border-radius: 4px;
            font-size: 11px;
        }
        
        #sidebarButton:hover {
            background-color: #3e3e42;
            color: #ffffff;
        }
        
        #sidebarButton:checked {
            background-color: #0e639c;
            color: #ffffff;
        }
        
        #sidebarFooter {
            border-top: 1px solid #3e3e42;
        }
        
        /* Dashboard */
        #dashboardScrollArea {
            background-color: #1e1e1e;
            border: none;
        }
        
        #dashboardHeader {
            background-color: transparent;
        }
        
        #dashboardWelcome {
            color: #ffffff;
        }
        
        #refreshButton {
            background-color: #0e639c;
            border: none;
            color: #ffffff;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 11px;
        }
        
        #refreshButton:hover {
            background-color: #1177bb;
        }
        
        #sectionTitle {
            color: #ffffff;
            margin-bottom: 10px;
        }
        
        /* Stat Cards */
        #statCard {
            background-color: #252526;
            border: 1px solid #3e3e42;
            border-radius: 8px;
        }
        
        #statCard:hover {
            border-color: #0e639c;
        }
        
        #statCardTitle {
            color: #cccccc;
        }
        
        #statCardValue {
            color: #ffffff;
        }
        
        #statCardSubtitle {
            color: #999999;
        }
        
        /* Quick Action Cards */
        #quickActionCard {
            background-color: #252526;
            border: 1px solid #3e3e42;
            border-radius: 8px;
        }
        
        #quickActionCard:hover {
            border-color: #0e639c;
        }
        
        #quickActionTitle {
            color: #ffffff;
        }
        
        #quickActionDescription {
            color: #cccccc;
        }
        
        #quickActionButton {
            background-color: #0e639c;
            border: none;
            color: #ffffff;
            border-radius: 4px;
            font-size: 11px;
        }
        
        #quickActionButton:hover {
            background-color: #1177bb;
        }

        /* Enhanced Quick Action Buttons */
        #expenseQuickButton, #incomeQuickButton, #habitQuickButton {
            background-color: #0e639c;
            border: none;
            color: #ffffff;
            border-radius: 6px;
            padding: 8px 12px;
            font-size: 12px;
            font-weight: bold;
            min-height: 32px;
        }

        #expenseQuickButton:hover, #incomeQuickButton:hover, #habitQuickButton:hover {
            background-color: #1177bb;
            transform: translateY(-1px);
        }

        /* Compact layouts for better information density */
        QGroupBox {
            font-weight: bold;
            padding-top: 8px;
        }

        QGroupBox::title {
            subcontrol-origin: margin;
            left: 8px;
            padding: 0 4px 0 4px;
        }
        
        /* Activity Frame */
        #activityFrame {
            background-color: #252526;
            border: 1px solid #3e3e42;
            border-radius: 8px;
        }
        
        /* Status Bar */
        QStatusBar {
            background-color: #2d2d30;
            border-top: 1px solid #3e3e42;
            color: #cccccc;
        }
        
        /* Menu Bar */
        QMenuBar {
            background-color: #2d2d30;
            color: #ffffff;
            border-bottom: 1px solid #3e3e42;
        }
        
        QMenuBar::item {
            background-color: transparent;
            padding: 4px 8px;
        }
        
        QMenuBar::item:selected {
            background-color: #3e3e42;
        }
        
        QMenu {
            background-color: #2d2d30;
            color: #ffffff;
            border: 1px solid #3e3e42;
        }
        
        QMenu::item {
            padding: 4px 20px;
        }
        
        QMenu::item:selected {
            background-color: #0e639c;
        }
        
        /* Scroll Bars */
        QScrollBar:vertical {
            background-color: #2d2d30;
            width: 12px;
            border-radius: 6px;
        }
        
        QScrollBar::handle:vertical {
            background-color: #555555;
            border-radius: 6px;
            min-height: 20px;
        }
        
        QScrollBar::handle:vertical:hover {
            background-color: #666666;
        }
        
        QScrollBar::add-line:vertical,
        QScrollBar::sub-line:vertical {
            border: none;
            background: none;
        }
        
        /* Splitter */
        QSplitter::handle {
            background-color: #3e3e42;
        }
        
        QSplitter::handle:hover {
            background-color: #0e639c;
        }

        /* Expense Tracker Styles */
        #expenseHeader {
            background-color: transparent;
            border-bottom: 1px solid #3e3e42;
            padding-bottom: 10px;
        }

        #expenseTitle {
            color: #ffffff;
        }

        #expenseAddButton, #expenseEditButton, #expenseDeleteButton,
        #expenseCategoriesButton, #expenseClearFiltersButton {
            background-color: #0e639c;
            border: none;
            color: #ffffff;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 11px;
        }

        #expenseAddButton:hover, #expenseEditButton:hover,
        #expenseDeleteButton:hover, #expenseCategoriesButton:hover,
        #expenseClearFiltersButton:hover {
            background-color: #1177bb;
        }

        #expenseEditButton:disabled, #expenseDeleteButton:disabled {
            background-color: #555555;
            color: #999999;
        }

        #expenseSearchFrame {
            background-color: #252526;
            border: 1px solid #3e3e42;
            border-radius: 4px;
            padding: 10px;
        }

        #expenseSearchEdit, #expenseCategoryFilter {
            background-color: #1e1e1e;
            border: 1px solid #3e3e42;
            color: #ffffff;
            padding: 5px;
            border-radius: 3px;
        }

        #expenseTable {
            background-color: #1e1e1e;
            alternate-background-color: #252526;
            color: #ffffff;
            gridline-color: #3e3e42;
            selection-background-color: #0e639c;
        }

        #expenseTable::item {
            padding: 5px;
            border: none;
        }

        #expenseTable::item:selected {
            background-color: #0e639c;
        }

        #expenseStatsFrame {
            background-color: #252526;
            border: 1px solid #3e3e42;
            border-radius: 8px;
            padding: 15px;
        }

        #expenseStatsTitle {
            color: #ffffff;
        }

        #expenseStatValue {
            color: #0e639c;
        }

        #expenseQuickActions {
            background-color: #252526;
            border: 1px solid #3e3e42;
            border-radius: 8px;
        }

        #expenseQuickButton {
            background-color: #3e3e42;
            border: none;
            color: #ffffff;
            padding: 8px;
            border-radius: 4px;
            text-align: left;
        }

        #expenseQuickButton:hover {
            background-color: #4e4e52;
        }

        /* Expense Dialog Styles */
        #expenseFormFrame {
            background-color: #252526;
            border: 1px solid #3e3e42;
            border-radius: 8px;
            padding: 15px;
        }

        #expenseDateEdit, #expenseTypeCombo, #expenseCategoryCombo,
        #expenseSubcategoryCombo, #expenseTransactionModeCombo,
        #expenseAmountSpinbox, #expenseNotesEdit {
            background-color: #1e1e1e;
            border: 1px solid #3e3e42;
            color: #ffffff;
            padding: 5px;
            border-radius: 3px;
        }

        #expenseDialogButtonBox QPushButton {
            background-color: #0e639c;
            border: none;
            color: #ffffff;
            padding: 8px 16px;
            border-radius: 4px;
            min-width: 80px;
        }

        #expenseDialogButtonBox QPushButton:hover {
            background-color: #1177bb;
        }

        /* Income Tracker Styles */
        #incomeHeader {
            background-color: transparent;
            border-bottom: 1px solid #3e3e42;
            padding-bottom: 10px;
        }

        #incomeTitle {
            color: #ffffff;
        }

        #incomeUpdateTodayButton, #incomeQuickEntryButton,
        #incomeGoalSettingsButton, #incomeAddGoalButton {
            background-color: #0e639c;
            border: none;
            color: #ffffff;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 11px;
        }

        #incomeUpdateTodayButton:hover, #incomeQuickEntryButton:hover,
        #incomeGoalSettingsButton:hover, #incomeAddGoalButton:hover {
            background-color: #1177bb;
        }

        #incomeTabWidget::pane {
            border: 1px solid #3e3e42;
            background-color: #252526;
        }

        #incomeTabWidget::tab-bar {
            alignment: left;
        }

        #incomeTabWidget QTabBar::tab {
            background-color: #2d2d30;
            color: #cccccc;
            padding: 8px 16px;
            border: 1px solid #3e3e42;
            border-bottom: none;
        }

        #incomeTabWidget QTabBar::tab:selected {
            background-color: #0e639c;
            color: #ffffff;
        }

        #incomeTabWidget QTabBar::tab:hover {
            background-color: #3e3e42;
        }

        #incomeProgressFrame, #incomeStatsFrame, #incomeBreakdownFrame,
        #incomeNotesFrame, #incomeDailyFrame {
            background-color: #252526;
            border: 1px solid #3e3e42;
            border-radius: 8px;
            padding: 15px;
        }

        #incomeProgressTitle, #incomeStatsTitle {
            color: #ffffff;
        }

        #incomeGoalLabel, #incomeEarnedLabel, #incomeRemainingLabel {
            color: #cccccc;
        }

        #incomeMainProgressBar, #incomeWeeklyProgressBar, #incomeDayProgress {
            background-color: #1e1e1e;
            border: 1px solid #3e3e42;
            border-radius: 4px;
            text-align: center;
        }

        #incomeMainProgressBar::chunk, #incomeWeeklyProgressBar::chunk,
        #incomeDayProgress::chunk {
            background-color: #0e639c;
            border-radius: 3px;
        }

        #incomeMainStatusLabel, #incomeDayStatus {
            font-weight: bold;
        }

        #incomeStatValue, #incomeSourceAmount, #incomeTotalAmount,
        #incomeExtraAmount, #incomeCurrentGoalLabel {
            color: #0e639c;
            font-weight: bold;
        }

        #incomeQuickActions {
            background-color: #252526;
            border: 1px solid #3e3e42;
            border-radius: 8px;
        }

        #incomeQuickSourceButton {
            background-color: #3e3e42;
            border: none;
            color: #ffffff;
            padding: 8px;
            border-radius: 4px;
            text-align: left;
        }

        #incomeQuickSourceButton:hover {
            background-color: #4e4e52;
        }

        #incomeWeekNavButton {
            background-color: #3e3e42;
            border: none;
            color: #ffffff;
            padding: 6px 12px;
            border-radius: 4px;
        }

        #incomeWeekNavButton:hover {
            background-color: #4e4e52;
        }

        #incomeWeekLabel {
            color: #ffffff;
        }

        #incomeDayWidget {
            background-color: #2d2d30;
            border: 1px solid #3e3e42;
            border-radius: 4px;
            padding: 8px;
            margin: 2px 0;
        }

        #incomeDayInfo {
            color: #cccccc;
            font-size: 10px;
        }

        /* Income Dialog Styles */
        #incomeFormFrame {
            background-color: #252526;
            border: 1px solid #3e3e42;
            border-radius: 8px;
            padding: 15px;
        }

        #incomeDateEdit, #incomeGoalSpinbox, #incomeZomatoSpinbox,
        #incomeSwiggySpinbox, #incomeShadowFaxSpinbox, #incomeOtherSpinbox,
        #incomeNotesEdit {
            background-color: #1e1e1e;
            border: 1px solid #3e3e42;
            color: #ffffff;
            padding: 5px;
            border-radius: 3px;
        }

        #incomeSectionLabel {
            color: #0e639c;
            font-weight: bold;
        }

        #incomeTotalLabel {
            color: #00aa00;
            font-weight: bold;
        }

        #incomeProgressBar {
            background-color: #1e1e1e;
            border: 1px solid #3e3e42;
            border-radius: 4px;
        }

        #incomeProgressBar::chunk {
            background-color: #0e639c;
            border-radius: 3px;
        }

        #incomeStatusLabel {
            font-weight: bold;
        }

        #incomeDialogButtonBox QPushButton {
            background-color: #0e639c;
            border: none;
            color: #ffffff;
            padding: 8px 16px;
            border-radius: 4px;
            min-width: 80px;
        }

        #incomeDialogButtonBox QPushButton:hover {
            background-color: #1177bb;
        }

        /* Habit Tracker Styles */
        #habitHeader {
            background-color: transparent;
            border-bottom: 1px solid #3e3e42;
            padding-bottom: 10px;
        }

        #habitTitle {
            color: #ffffff;
        }

        #habitDateLabel {
            color: #cccccc;
            font-size: 12px;
        }

        #habitNavButton, #habitTodayButton, #habitManageButton, #habitAddButton {
            background-color: #0e639c;
            border: none;
            color: #ffffff;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 11px;
        }

        #habitNavButton:hover, #habitTodayButton:hover,
        #habitManageButton:hover, #habitAddButton:hover {
            background-color: #1177bb;
        }

        #habitNavButton:disabled {
            background-color: #555555;
            color: #999999;
        }

        #habitScrollArea {
            background-color: transparent;
            border: none;
        }

        #habitGridWidget {
            background-color: transparent;
        }

        #habitCard {
            background-color: #252526;
            border: 1px solid #3e3e42;
            border-radius: 8px;
            padding: 10px;
        }

        #habitCard:hover {
            border-color: #0e639c;
        }

        #habitIcon {
            color: #ffffff;
        }

        #habitName {
            color: #ffffff;
            font-weight: bold;
        }

        #habitProgress {
            color: #cccccc;
            font-size: 10px;
        }

        #habitCheckBox {
            spacing: 5px;
        }

        #habitCheckBox::indicator {
            width: 20px;
            height: 20px;
            border-radius: 3px;
            border: 2px solid #3e3e42;
            background-color: #1e1e1e;
        }

        #habitCheckBox::indicator:checked {
            background-color: #0e639c;
            border-color: #0e639c;
        }

        #habitCheckBox::indicator:checked:hover {
            background-color: #1177bb;
        }

        #habitProgressFrame, #habitStatsFrame {
            background-color: #252526;
            border: 1px solid #3e3e42;
            border-radius: 8px;
            padding: 15px;
        }

        #habitProgressTitle, #habitStatsTitle {
            color: #ffffff;
        }

        #habitCompletionLabel, #habitMessageLabel {
            color: #cccccc;
        }

        #habitMainProgressBar {
            background-color: #1e1e1e;
            border: 1px solid #3e3e42;
            border-radius: 4px;
            text-align: center;
        }

        #habitMainProgressBar::chunk {
            background-color: #0e639c;
            border-radius: 3px;
        }

        #habitStatValue {
            color: #0e639c;
            font-weight: bold;
        }

        #habitManagementItem {
            background-color: #2d2d30;
            border: 1px solid #3e3e42;
            border-radius: 4px;
            padding: 10px;
            margin: 2px 0;
        }

        #habitManagementName {
            color: #ffffff;
            font-weight: bold;
        }

        #habitManagementDetails {
            color: #cccccc;
            font-size: 10px;
        }

        /* Attendance Tracker Styles */
        #attendanceHeader {
            background-color: transparent;
            border-bottom: 1px solid #3e3e42;
            padding-bottom: 10px;
        }

        #attendanceTitle {
            color: #ffffff;
        }

        #attendanceTodayButton, #attendanceSemesterButton, #attendanceActionButton,
        #attendanceNavButton {
            background-color: #0e639c;
            border: none;
            color: #ffffff;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 11px;
        }

        #attendanceTodayButton:hover, #attendanceSemesterButton:hover,
        #attendanceActionButton:hover, #attendanceNavButton:hover {
            background-color: #1177bb;
        }

        #attendanceTabWidget::pane {
            border: 1px solid #3e3e42;
            background-color: #252526;
        }

        #attendanceTabWidget QTabBar::tab {
            background-color: #2d2d30;
            color: #cccccc;
            padding: 8px 16px;
            border: 1px solid #3e3e42;
            border-bottom: none;
        }

        #attendanceTabWidget QTabBar::tab:selected {
            background-color: #0e639c;
            color: #ffffff;
        }

        #attendanceDateLabel {
            color: #ffffff;
            font-weight: bold;
        }

        #attendanceDayLabel {
            color: #cccccc;
        }

        #attendancePeriodsFrame, #attendanceActionsFrame, #attendanceSummaryFrame,
        #attendanceNotesFrame, #attendanceStatsFrame, #attendanceSemesterFrame,
        #attendanceProgressFrame {
            background-color: #252526;
            border: 1px solid #3e3e42;
            border-radius: 8px;
            padding: 15px;
        }

        #periodButton {
            min-width: 60px;
            min-height: 40px;
            border-radius: 4px;
            font-weight: bold;
        }

        #attendanceSummaryValue, #attendanceStatValue, #attendanceSemesterValue {
            color: #0e639c;
            font-weight: bold;
        }

        #attendancePercentageValue {
            font-weight: bold;
            font-size: 12px;
        }

        #attendanceOverallPercentage {
            font-weight: bold;
            font-size: 16px;
        }

        #attendanceThresholdStatus {
            font-weight: bold;
        }

        #attendanceProgressBar {
            background-color: #1e1e1e;
            border: 1px solid #3e3e42;
            border-radius: 4px;
            text-align: center;
        }

        #attendanceProgressBar::chunk {
            background-color: #0e639c;
            border-radius: 3px;
        }

        #attendanceStatsTitle {
            color: #ffffff;
        }

        #attendanceProgressLabel {
            color: #cccccc;
        }

        #attendanceNotesEdit {
            background-color: #1e1e1e;
            border: 1px solid #3e3e42;
            color: #ffffff;
            padding: 5px;
            border-radius: 3px;
        }
        """
    
    def _get_light_theme(self) -> str:
        """Get light theme stylesheet"""
        return """
        /* Main Window */
        QMainWindow {
            background-color: #ffffff;
            color: #000000;
        }
        
        /* Sidebar */
        #sidebar {
            background-color: #f3f3f3;
            border-right: 1px solid #d0d0d0;
        }
        
        #sidebarHeader {
            background-color: #e8e8e8;
            border-bottom: 1px solid #d0d0d0;
        }
        
        #sidebarTitle {
            color: #000000;
            font-weight: bold;
        }
        
        #toggleButton {
            background-color: #d0d0d0;
            border: none;
            border-radius: 4px;
            color: #000000;
            font-size: 14px;
        }
        
        #toggleButton:hover {
            background-color: #c0c0c0;
        }
        
        #sidebarButton {
            background-color: transparent;
            border: none;
            color: #333333;
            text-align: left;
            padding: 10px 15px;
            border-radius: 4px;
            font-size: 11px;
        }
        
        #sidebarButton:hover {
            background-color: #e0e0e0;
            color: #000000;
        }
        
        #sidebarButton:checked {
            background-color: #0078d4;
            color: #ffffff;
        }
        
        #sidebarFooter {
            border-top: 1px solid #d0d0d0;
        }
        
        /* Dashboard */
        #dashboardScrollArea {
            background-color: #ffffff;
            border: none;
        }
        
        #dashboardHeader {
            background-color: transparent;
        }
        
        #dashboardWelcome {
            color: #000000;
        }
        
        #refreshButton {
            background-color: #0078d4;
            border: none;
            color: #ffffff;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 11px;
        }
        
        #refreshButton:hover {
            background-color: #106ebe;
        }
        
        #sectionTitle {
            color: #000000;
            margin-bottom: 10px;
        }
        
        /* Stat Cards */
        #statCard {
            background-color: #f9f9f9;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        
        #statCard:hover {
            border-color: #0078d4;
        }
        
        #statCardTitle {
            color: #666666;
        }
        
        #statCardValue {
            color: #000000;
        }
        
        #statCardSubtitle {
            color: #888888;
        }
        
        /* Quick Action Cards */
        #quickActionCard {
            background-color: #f9f9f9;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        
        #quickActionCard:hover {
            border-color: #0078d4;
        }
        
        #quickActionTitle {
            color: #000000;
        }
        
        #quickActionDescription {
            color: #666666;
        }
        
        #quickActionButton {
            background-color: #0078d4;
            border: none;
            color: #ffffff;
            border-radius: 4px;
            font-size: 11px;
        }
        
        #quickActionButton:hover {
            background-color: #106ebe;
        }

        /* Enhanced Quick Action Buttons */
        #expenseQuickButton, #incomeQuickButton, #habitQuickButton {
            background-color: #0078d4;
            border: none;
            color: #ffffff;
            border-radius: 6px;
            padding: 8px 12px;
            font-size: 12px;
            font-weight: bold;
            min-height: 32px;
        }

        #expenseQuickButton:hover, #incomeQuickButton:hover, #habitQuickButton:hover {
            background-color: #106ebe;
            transform: translateY(-1px);
        }

        /* Compact layouts for better information density */
        QGroupBox {
            font-weight: bold;
            padding-top: 8px;
        }

        QGroupBox::title {
            subcontrol-origin: margin;
            left: 8px;
            padding: 0 4px 0 4px;
        }
        
        /* Activity Frame */
        #activityFrame {
            background-color: #f9f9f9;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        
        /* Status Bar */
        QStatusBar {
            background-color: #f0f0f0;
            border-top: 1px solid #d0d0d0;
            color: #333333;
        }
        
        /* Menu Bar */
        QMenuBar {
            background-color: #f0f0f0;
            color: #000000;
            border-bottom: 1px solid #d0d0d0;
        }
        
        QMenuBar::item {
            background-color: transparent;
            padding: 4px 8px;
        }
        
        QMenuBar::item:selected {
            background-color: #e0e0e0;
        }
        
        QMenu {
            background-color: #ffffff;
            color: #000000;
            border: 1px solid #d0d0d0;
        }
        
        QMenu::item {
            padding: 4px 20px;
        }
        
        QMenu::item:selected {
            background-color: #0078d4;
            color: #ffffff;
        }
        
        /* Scroll Bars */
        QScrollBar:vertical {
            background-color: #f0f0f0;
            width: 12px;
            border-radius: 6px;
        }
        
        QScrollBar::handle:vertical {
            background-color: #c0c0c0;
            border-radius: 6px;
            min-height: 20px;
        }
        
        QScrollBar::handle:vertical:hover {
            background-color: #a0a0a0;
        }
        
        QScrollBar::add-line:vertical,
        QScrollBar::sub-line:vertical {
            border: none;
            background: none;
        }
        
        /* Splitter */
        QSplitter::handle {
            background-color: #d0d0d0;
        }
        
        QSplitter::handle:hover {
            background-color: #0078d4;
        }

        /* Expense Tracker Styles */
        #expenseHeader {
            background-color: transparent;
            border-bottom: 1px solid #d0d0d0;
            padding-bottom: 10px;
        }

        #expenseTitle {
            color: #000000;
        }

        #expenseAddButton, #expenseEditButton, #expenseDeleteButton,
        #expenseCategoriesButton, #expenseClearFiltersButton {
            background-color: #0078d4;
            border: none;
            color: #ffffff;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 11px;
        }

        #expenseAddButton:hover, #expenseEditButton:hover,
        #expenseDeleteButton:hover, #expenseCategoriesButton:hover,
        #expenseClearFiltersButton:hover {
            background-color: #106ebe;
        }

        #expenseEditButton:disabled, #expenseDeleteButton:disabled {
            background-color: #cccccc;
            color: #666666;
        }

        #expenseSearchFrame {
            background-color: #f9f9f9;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            padding: 10px;
        }

        #expenseSearchEdit, #expenseCategoryFilter {
            background-color: #ffffff;
            border: 1px solid #d0d0d0;
            color: #000000;
            padding: 5px;
            border-radius: 3px;
        }

        #expenseTable {
            background-color: #ffffff;
            alternate-background-color: #f9f9f9;
            color: #000000;
            gridline-color: #e0e0e0;
            selection-background-color: #0078d4;
        }

        #expenseTable::item {
            padding: 5px;
            border: none;
        }

        #expenseTable::item:selected {
            background-color: #0078d4;
            color: #ffffff;
        }

        #expenseStatsFrame {
            background-color: #f9f9f9;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
        }

        #expenseStatsTitle {
            color: #000000;
        }

        #expenseStatValue {
            color: #0078d4;
        }

        #expenseQuickActions {
            background-color: #f9f9f9;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }

        #expenseQuickButton {
            background-color: #e0e0e0;
            border: none;
            color: #000000;
            padding: 8px;
            border-radius: 4px;
            text-align: left;
        }

        #expenseQuickButton:hover {
            background-color: #d0d0d0;
        }

        /* Expense Dialog Styles */
        #expenseFormFrame {
            background-color: #f9f9f9;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
        }

        #expenseDateEdit, #expenseTypeCombo, #expenseCategoryCombo,
        #expenseSubcategoryCombo, #expenseTransactionModeCombo,
        #expenseAmountSpinbox, #expenseNotesEdit {
            background-color: #ffffff;
            border: 1px solid #d0d0d0;
            color: #000000;
            padding: 5px;
            border-radius: 3px;
        }

        #expenseDialogButtonBox QPushButton {
            background-color: #0078d4;
            border: none;
            color: #ffffff;
            padding: 8px 16px;
            border-radius: 4px;
            min-width: 80px;
        }

        #expenseDialogButtonBox QPushButton:hover {
            background-color: #106ebe;
        }

        /* Income Tracker Styles */
        #incomeHeader {
            background-color: transparent;
            border-bottom: 1px solid #d0d0d0;
            padding-bottom: 10px;
        }

        #incomeTitle {
            color: #000000;
        }

        #incomeUpdateTodayButton, #incomeQuickEntryButton,
        #incomeGoalSettingsButton, #incomeAddGoalButton {
            background-color: #0078d4;
            border: none;
            color: #ffffff;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 11px;
        }

        #incomeUpdateTodayButton:hover, #incomeQuickEntryButton:hover,
        #incomeGoalSettingsButton:hover, #incomeAddGoalButton:hover {
            background-color: #106ebe;
        }

        #incomeTabWidget::pane {
            border: 1px solid #d0d0d0;
            background-color: #f9f9f9;
        }

        #incomeTabWidget::tab-bar {
            alignment: left;
        }

        #incomeTabWidget QTabBar::tab {
            background-color: #f0f0f0;
            color: #333333;
            padding: 8px 16px;
            border: 1px solid #d0d0d0;
            border-bottom: none;
        }

        #incomeTabWidget QTabBar::tab:selected {
            background-color: #0078d4;
            color: #ffffff;
        }

        #incomeTabWidget QTabBar::tab:hover {
            background-color: #e0e0e0;
        }

        #incomeProgressFrame, #incomeStatsFrame, #incomeBreakdownFrame,
        #incomeNotesFrame, #incomeDailyFrame {
            background-color: #f9f9f9;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
        }

        #incomeProgressTitle, #incomeStatsTitle {
            color: #000000;
        }

        #incomeGoalLabel, #incomeEarnedLabel, #incomeRemainingLabel {
            color: #666666;
        }

        #incomeMainProgressBar, #incomeWeeklyProgressBar, #incomeDayProgress {
            background-color: #ffffff;
            border: 1px solid #d0d0d0;
            border-radius: 4px;
            text-align: center;
        }

        #incomeMainProgressBar::chunk, #incomeWeeklyProgressBar::chunk,
        #incomeDayProgress::chunk {
            background-color: #0078d4;
            border-radius: 3px;
        }

        #incomeMainStatusLabel, #incomeDayStatus {
            font-weight: bold;
        }

        #incomeStatValue, #incomeSourceAmount, #incomeTotalAmount,
        #incomeExtraAmount, #incomeCurrentGoalLabel {
            color: #0078d4;
            font-weight: bold;
        }

        #incomeQuickActions {
            background-color: #f9f9f9;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }

        #incomeQuickSourceButton {
            background-color: #e0e0e0;
            border: none;
            color: #000000;
            padding: 8px;
            border-radius: 4px;
            text-align: left;
        }

        #incomeQuickSourceButton:hover {
            background-color: #d0d0d0;
        }

        #incomeWeekNavButton {
            background-color: #e0e0e0;
            border: none;
            color: #000000;
            padding: 6px 12px;
            border-radius: 4px;
        }

        #incomeWeekNavButton:hover {
            background-color: #d0d0d0;
        }

        #incomeWeekLabel {
            color: #000000;
        }

        #incomeDayWidget {
            background-color: #ffffff;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            padding: 8px;
            margin: 2px 0;
        }

        #incomeDayInfo {
            color: #666666;
            font-size: 10px;
        }

        /* Income Dialog Styles */
        #incomeFormFrame {
            background-color: #f9f9f9;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
        }

        #incomeDateEdit, #incomeGoalSpinbox, #incomeZomatoSpinbox,
        #incomeSwiggySpinbox, #incomeShadowFaxSpinbox, #incomeOtherSpinbox,
        #incomeNotesEdit {
            background-color: #ffffff;
            border: 1px solid #d0d0d0;
            color: #000000;
            padding: 5px;
            border-radius: 3px;
        }

        #incomeSectionLabel {
            color: #0078d4;
            font-weight: bold;
        }

        #incomeTotalLabel {
            color: #00aa00;
            font-weight: bold;
        }

        #incomeProgressBar {
            background-color: #ffffff;
            border: 1px solid #d0d0d0;
            border-radius: 4px;
        }

        #incomeProgressBar::chunk {
            background-color: #0078d4;
            border-radius: 3px;
        }

        #incomeStatusLabel {
            font-weight: bold;
        }

        #incomeDialogButtonBox QPushButton {
            background-color: #0078d4;
            border: none;
            color: #ffffff;
            padding: 8px 16px;
            border-radius: 4px;
            min-width: 80px;
        }

        #incomeDialogButtonBox QPushButton:hover {
            background-color: #106ebe;
        }

        /* Habit Tracker Styles */
        #habitHeader {
            background-color: transparent;
            border-bottom: 1px solid #d0d0d0;
            padding-bottom: 10px;
        }

        #habitTitle {
            color: #000000;
        }

        #habitDateLabel {
            color: #666666;
            font-size: 12px;
        }

        #habitNavButton, #habitTodayButton, #habitManageButton, #habitAddButton {
            background-color: #0078d4;
            border: none;
            color: #ffffff;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 11px;
        }

        #habitNavButton:hover, #habitTodayButton:hover,
        #habitManageButton:hover, #habitAddButton:hover {
            background-color: #106ebe;
        }

        #habitNavButton:disabled {
            background-color: #cccccc;
            color: #666666;
        }

        #habitScrollArea {
            background-color: transparent;
            border: none;
        }

        #habitGridWidget {
            background-color: transparent;
        }

        #habitCard {
            background-color: #f9f9f9;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 10px;
        }

        #habitCard:hover {
            border-color: #0078d4;
        }

        #habitIcon {
            color: #000000;
        }

        #habitName {
            color: #000000;
            font-weight: bold;
        }

        #habitProgress {
            color: #666666;
            font-size: 10px;
        }

        #habitCheckBox {
            spacing: 5px;
        }

        #habitCheckBox::indicator {
            width: 20px;
            height: 20px;
            border-radius: 3px;
            border: 2px solid #d0d0d0;
            background-color: #ffffff;
        }

        #habitCheckBox::indicator:checked {
            background-color: #0078d4;
            border-color: #0078d4;
        }

        #habitCheckBox::indicator:checked:hover {
            background-color: #106ebe;
        }

        #habitProgressFrame, #habitStatsFrame {
            background-color: #f9f9f9;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
        }

        #habitProgressTitle, #habitStatsTitle {
            color: #000000;
        }

        #habitCompletionLabel, #habitMessageLabel {
            color: #666666;
        }

        #habitMainProgressBar {
            background-color: #ffffff;
            border: 1px solid #d0d0d0;
            border-radius: 4px;
            text-align: center;
        }

        #habitMainProgressBar::chunk {
            background-color: #0078d4;
            border-radius: 3px;
        }

        #habitStatValue {
            color: #0078d4;
            font-weight: bold;
        }

        #habitManagementItem {
            background-color: #ffffff;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            padding: 10px;
            margin: 2px 0;
        }

        #habitManagementName {
            color: #000000;
            font-weight: bold;
        }

        #habitManagementDetails {
            color: #666666;
            font-size: 10px;
        }

        /* Attendance Tracker Styles */
        #attendanceHeader {
            background-color: transparent;
            border-bottom: 1px solid #d0d0d0;
            padding-bottom: 10px;
        }

        #attendanceTitle {
            color: #000000;
        }

        #attendanceTodayButton, #attendanceSemesterButton, #attendanceActionButton,
        #attendanceNavButton {
            background-color: #0078d4;
            border: none;
            color: #ffffff;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 11px;
        }

        #attendanceTodayButton:hover, #attendanceSemesterButton:hover,
        #attendanceActionButton:hover, #attendanceNavButton:hover {
            background-color: #106ebe;
        }

        #attendanceTabWidget::pane {
            border: 1px solid #d0d0d0;
            background-color: #f9f9f9;
        }

        #attendanceTabWidget QTabBar::tab {
            background-color: #f0f0f0;
            color: #333333;
            padding: 8px 16px;
            border: 1px solid #d0d0d0;
            border-bottom: none;
        }

        #attendanceTabWidget QTabBar::tab:selected {
            background-color: #0078d4;
            color: #ffffff;
        }

        #attendanceDateLabel {
            color: #000000;
            font-weight: bold;
        }

        #attendanceDayLabel {
            color: #666666;
        }

        #attendancePeriodsFrame, #attendanceActionsFrame, #attendanceSummaryFrame,
        #attendanceNotesFrame, #attendanceStatsFrame, #attendanceSemesterFrame,
        #attendanceProgressFrame {
            background-color: #f9f9f9;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
        }

        #periodButton {
            min-width: 60px;
            min-height: 40px;
            border-radius: 4px;
            font-weight: bold;
        }

        #attendanceSummaryValue, #attendanceStatValue, #attendanceSemesterValue {
            color: #0078d4;
            font-weight: bold;
        }

        #attendancePercentageValue {
            font-weight: bold;
            font-size: 12px;
        }

        #attendanceOverallPercentage {
            font-weight: bold;
            font-size: 16px;
        }

        #attendanceThresholdStatus {
            font-weight: bold;
        }

        #attendanceProgressBar {
            background-color: #ffffff;
            border: 1px solid #d0d0d0;
            border-radius: 4px;
            text-align: center;
        }

        #attendanceProgressBar::chunk {
            background-color: #0078d4;
            border-radius: 3px;
        }

        #attendanceStatsTitle {
            color: #000000;
        }

        #attendanceProgressLabel {
            color: #666666;
        }

        #attendanceNotesEdit {
            background-color: #ffffff;
            border: 1px solid #d0d0d0;
            color: #000000;
            padding: 5px;
            border-radius: 3px;
        }
        """
