"""
Confidence Booster System
Provides multiple ways to improve categorization confidence levels
"""

import json
import re
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime

from ..core.logger import get_logger


@dataclass
class CustomRule:
    """Custom categorization rule for confidence boosting"""
    name: str
    category: str
    sub_category: str
    keywords: List[str]
    patterns: List[str]
    confidence_boost: float
    created_at: str
    is_active: bool = True


@dataclass
class MerchantMapping:
    """Merchant-specific mapping for high confidence categorization"""
    merchant_name: str
    category: str
    sub_category: str
    confidence: float
    transaction_mode: str = "Auto"
    notes: str = ""
    created_at: str = ""


class ConfidenceBooster:
    """
    System for improving categorization confidence through various methods
    """
    
    def __init__(self, config_dir: str = "bank_analyzer_config"):
        self.logger = get_logger(__name__)
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        
        # Configuration files
        self.custom_rules_file = self.config_dir / "custom_rules.json"
        self.merchant_mappings_file = self.config_dir / "merchant_mappings.json"
        self.learning_data_file = self.config_dir / "learning_data.json"
        
        # Load existing configurations
        self.custom_rules = self._load_custom_rules()
        self.merchant_mappings = self._load_merchant_mappings()
        self.learning_data = self._load_learning_data()
    
    def _load_custom_rules(self) -> List[CustomRule]:
        """Load custom categorization rules"""
        if not self.custom_rules_file.exists():
            return []
        
        try:
            with open(self.custom_rules_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            return [CustomRule(**rule) for rule in data]
        except Exception as e:
            self.logger.error(f"Error loading custom rules: {str(e)}")
            return []
    
    def _load_merchant_mappings(self) -> List[MerchantMapping]:
        """Load merchant-specific mappings"""
        if not self.merchant_mappings_file.exists():
            return self._create_default_merchant_mappings()
        
        try:
            with open(self.merchant_mappings_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            return [MerchantMapping(**mapping) for mapping in data]
        except Exception as e:
            self.logger.error(f"Error loading merchant mappings: {str(e)}")
            return self._create_default_merchant_mappings()
    
    def _load_learning_data(self) -> Dict[str, Any]:
        """Load learning data from user corrections"""
        if not self.learning_data_file.exists():
            return {"corrections": [], "patterns": {}, "last_updated": ""}
        
        try:
            with open(self.learning_data_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            self.logger.error(f"Error loading learning data: {str(e)}")
            return {"corrections": [], "patterns": {}, "last_updated": ""}
    
    def _create_default_merchant_mappings(self) -> List[MerchantMapping]:
        """Create default merchant mappings for common Indian merchants"""
        default_mappings = [
            # Food Delivery
            MerchantMapping("ZOMATO", "Food & Dining", "Delivery", 0.95, "UPI", "Food delivery app"),
            MerchantMapping("SWIGGY", "Food & Dining", "Delivery", 0.95, "UPI", "Food delivery app"),
            MerchantMapping("UBER EATS", "Food & Dining", "Delivery", 0.90, "UPI", "Food delivery app"),
            
            # Transportation
            MerchantMapping("UBER", "Transportation", "Taxi/Uber", 0.95, "UPI", "Ride sharing"),
            MerchantMapping("OLA", "Transportation", "Taxi/Uber", 0.95, "UPI", "Ride sharing"),
            MerchantMapping("RAPIDO", "Transportation", "Taxi/Uber", 0.90, "UPI", "Bike taxi"),
            
            # E-commerce
            MerchantMapping("AMAZON", "Shopping", "Electronics", 0.85, "Credit Card", "E-commerce"),
            MerchantMapping("FLIPKART", "Shopping", "Electronics", 0.85, "Credit Card", "E-commerce"),
            MerchantMapping("MYNTRA", "Shopping", "Clothing", 0.90, "Credit Card", "Fashion e-commerce"),
            
            # Groceries
            MerchantMapping("BIG BAZAAR", "Food & Dining", "Groceries", 0.90, "Debit Card", "Retail chain"),
            MerchantMapping("DMART", "Food & Dining", "Groceries", 0.90, "Debit Card", "Retail chain"),
            MerchantMapping("RELIANCE FRESH", "Food & Dining", "Groceries", 0.90, "Debit Card", "Grocery store"),
            MerchantMapping("MORE", "Food & Dining", "Groceries", 0.85, "Debit Card", "Grocery store"),
            
            # Fuel
            MerchantMapping("INDIAN OIL", "Transportation", "Fuel", 0.95, "Debit Card", "Petrol pump"),
            MerchantMapping("BHARAT PETROLEUM", "Transportation", "Fuel", 0.95, "Debit Card", "Petrol pump"),
            MerchantMapping("HP", "Transportation", "Fuel", 0.90, "Debit Card", "Petrol pump"),
            MerchantMapping("SHELL", "Transportation", "Fuel", 0.90, "Credit Card", "Petrol pump"),
            
            # Utilities
            MerchantMapping("AIRTEL", "Bills & Utilities", "Internet", 0.90, "Net Banking", "Telecom"),
            MerchantMapping("JIO", "Bills & Utilities", "Internet", 0.90, "UPI", "Telecom"),
            MerchantMapping("BSNL", "Bills & Utilities", "Internet", 0.85, "Net Banking", "Telecom"),
            
            # Healthcare
            MerchantMapping("APOLLO PHARMACY", "Healthcare", "Pharmacy", 0.95, "Debit Card", "Pharmacy chain"),
            MerchantMapping("MEDPLUS", "Healthcare", "Pharmacy", 0.90, "Debit Card", "Pharmacy chain"),
            MerchantMapping("1MG", "Healthcare", "Pharmacy", 0.90, "UPI", "Online pharmacy"),
            
            # Fast Food
            MerchantMapping("MCDONALD", "Food & Dining", "Fast Food", 0.95, "Credit Card", "Fast food chain"),
            MerchantMapping("KFC", "Food & Dining", "Fast Food", 0.95, "Credit Card", "Fast food chain"),
            MerchantMapping("PIZZA HUT", "Food & Dining", "Fast Food", 0.90, "Credit Card", "Pizza chain"),
            MerchantMapping("DOMINOS", "Food & Dining", "Fast Food", 0.90, "Credit Card", "Pizza chain"),
        ]
        
        # Add creation timestamp
        for mapping in default_mappings:
            mapping.created_at = datetime.now().isoformat()
        
        # Save default mappings
        self._save_merchant_mappings()
        
        return default_mappings
    
    def add_custom_rule(self, name: str, category: str, sub_category: str, 
                       keywords: List[str], patterns: List[str] = None, 
                       confidence_boost: float = 0.3) -> bool:
        """Add a custom categorization rule"""
        try:
            if patterns is None:
                patterns = []
            
            rule = CustomRule(
                name=name,
                category=category,
                sub_category=sub_category,
                keywords=keywords,
                patterns=patterns,
                confidence_boost=confidence_boost,
                created_at=datetime.now().isoformat()
            )
            
            self.custom_rules.append(rule)
            self._save_custom_rules()
            
            self.logger.info(f"Added custom rule: {name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error adding custom rule: {str(e)}")
            return False
    
    def add_merchant_mapping(self, merchant_name: str, category: str, sub_category: str,
                           confidence: float = 0.9, transaction_mode: str = "Auto",
                           notes: str = "") -> bool:
        """Add a merchant-specific mapping"""
        try:
            mapping = MerchantMapping(
                merchant_name=merchant_name.upper(),
                category=category,
                sub_category=sub_category,
                confidence=confidence,
                transaction_mode=transaction_mode,
                notes=notes,
                created_at=datetime.now().isoformat()
            )
            
            # Remove existing mapping for same merchant
            self.merchant_mappings = [m for m in self.merchant_mappings 
                                    if m.merchant_name != merchant_name.upper()]
            
            self.merchant_mappings.append(mapping)
            self._save_merchant_mappings()
            
            self.logger.info(f"Added merchant mapping: {merchant_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error adding merchant mapping: {str(e)}")
            return False
    
    def learn_from_correction(self, original_description: str, original_category: str,
                            original_sub_category: str, corrected_category: str,
                            corrected_sub_category: str, confidence: float) -> None:
        """Learn from user corrections to improve future categorization"""
        try:
            correction = {
                "description": original_description,
                "original": {"category": original_category, "sub_category": original_sub_category},
                "corrected": {"category": corrected_category, "sub_category": corrected_sub_category},
                "confidence": confidence,
                "timestamp": datetime.now().isoformat()
            }
            
            self.learning_data["corrections"].append(correction)
            
            # Extract patterns from corrections
            self._extract_learning_patterns()
            
            # Save learning data
            self._save_learning_data()
            
            self.logger.info(f"Learned from correction: {original_description[:50]}...")
            
        except Exception as e:
            self.logger.error(f"Error learning from correction: {str(e)}")
    
    def _extract_learning_patterns(self) -> None:
        """Extract patterns from user corrections"""
        patterns = {}
        
        for correction in self.learning_data["corrections"]:
            corrected_key = f"{correction['corrected']['category']}/{correction['corrected']['sub_category']}"
            
            if corrected_key not in patterns:
                patterns[corrected_key] = {"keywords": [], "count": 0}
            
            # Extract keywords from description
            description = correction["description"].lower()
            words = re.findall(r'\b\w{3,}\b', description)
            
            patterns[corrected_key]["keywords"].extend(words)
            patterns[corrected_key]["count"] += 1
        
        # Keep only frequent patterns
        for key in patterns:
            word_counts = {}
            for word in patterns[key]["keywords"]:
                word_counts[word] = word_counts.get(word, 0) + 1
            
            # Keep words that appear in at least 20% of corrections for this category
            min_frequency = max(1, patterns[key]["count"] * 0.2)
            frequent_words = [word for word, count in word_counts.items() if count >= min_frequency]
            
            patterns[key]["keywords"] = frequent_words
        
        self.learning_data["patterns"] = patterns
        self.learning_data["last_updated"] = datetime.now().isoformat()
    
    def get_confidence_boost(self, description: str, category: str, sub_category: str) -> float:
        """Get confidence boost for a transaction based on learned patterns"""
        boost = 0.0
        
        # Check custom rules
        for rule in self.custom_rules:
            if not rule.is_active:
                continue
            
            if rule.category == category and rule.sub_category == sub_category:
                description_lower = description.lower()
                
                # Check keywords
                keyword_matches = sum(1 for keyword in rule.keywords 
                                    if keyword.lower() in description_lower)
                
                # Check patterns
                pattern_matches = 0
                for pattern in rule.patterns:
                    try:
                        if re.search(pattern, description_lower, re.IGNORECASE):
                            pattern_matches += 1
                    except re.error:
                        continue
                
                if keyword_matches > 0 or pattern_matches > 0:
                    boost += rule.confidence_boost
        
        # Check learned patterns
        pattern_key = f"{category}/{sub_category}"
        if pattern_key in self.learning_data["patterns"]:
            learned_keywords = self.learning_data["patterns"][pattern_key]["keywords"]
            description_lower = description.lower()
            
            matches = sum(1 for keyword in learned_keywords if keyword in description_lower)
            if matches > 0:
                boost += min(0.2, matches * 0.05)  # Up to 0.2 boost from learned patterns
        
        return min(boost, 0.5)  # Cap total boost at 0.5
    
    def find_merchant_mapping(self, description: str) -> Optional[MerchantMapping]:
        """Find merchant mapping for a transaction description"""
        description_upper = description.upper()
        
        for mapping in self.merchant_mappings:
            if mapping.merchant_name in description_upper:
                return mapping
        
        return None
    
    def _save_custom_rules(self) -> None:
        """Save custom rules to file"""
        try:
            data = [asdict(rule) for rule in self.custom_rules]
            with open(self.custom_rules_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"Error saving custom rules: {str(e)}")
    
    def _save_merchant_mappings(self) -> None:
        """Save merchant mappings to file"""
        try:
            data = [asdict(mapping) for mapping in self.merchant_mappings]
            with open(self.merchant_mappings_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"Error saving merchant mappings: {str(e)}")
    
    def _save_learning_data(self) -> None:
        """Save learning data to file"""
        try:
            with open(self.learning_data_file, 'w', encoding='utf-8') as f:
                json.dump(self.learning_data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"Error saving learning data: {str(e)}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get statistics about confidence boosting"""
        return {
            "custom_rules": len([r for r in self.custom_rules if r.is_active]),
            "merchant_mappings": len(self.merchant_mappings),
            "learned_corrections": len(self.learning_data["corrections"]),
            "learned_patterns": len(self.learning_data["patterns"]),
            "last_updated": self.learning_data.get("last_updated", "Never")
        }
