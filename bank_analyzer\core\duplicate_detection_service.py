"""
Duplicate Transaction Detection Service
Handles detection and separation of duplicate transactions when loading session data
"""

import hashlib
from typing import List, Dict, Set, Tuple, Optional
from dataclasses import dataclass
from datetime import datetime

from .logger import get_logger
from ..models.transaction import RawTransaction
from ..ml.data_preparation import TransactionDataPreparator


@dataclass
class DuplicateDetectionResult:
    """Result of duplicate detection analysis"""
    new_transactions: List[RawTransaction]
    duplicate_transactions: List[RawTransaction]
    duplicate_details: List[Dict[str, any]]
    total_new: int
    total_duplicates: int
    detection_summary: str


@dataclass
class TrainedTransactionSignature:
    """Signature of a trained transaction for duplicate detection"""
    hash_id: str
    normalized_description: str
    transaction_type: str
    source_session_id: str
    training_session_id: str


class DuplicateDetectionService:
    """
    Service for detecting duplicate transactions against trained data
    """
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.data_preparator = TransactionDataPreparator()
        
    def detect_duplicates(self, new_transactions: List[RawTransaction], 
                         trained_signatures: Set[str]) -> DuplicateDetectionResult:
        """
        Detect duplicate transactions by comparing against trained data
        
        Args:
            new_transactions: List of new transactions to check
            trained_signatures: Set of hash IDs from all trained transactions
            
        Returns:
            DuplicateDetectionResult with separated transactions
        """
        try:
            new_txns = []
            duplicate_txns = []
            duplicate_details = []
            
            for txn in new_transactions:
                # Generate hash ID for this transaction
                normalized = self.data_preparator.normalize_description(txn.description)
                txn_type = self._get_transaction_type(txn)
                hash_id = self.data_preparator.generate_context_aware_hash_id(normalized, txn_type)
                
                if hash_id in trained_signatures:
                    # This is a duplicate
                    duplicate_txns.append(txn)
                    duplicate_details.append({
                        'transaction': txn,
                        'hash_id': hash_id,
                        'normalized_description': normalized,
                        'transaction_type': txn_type,
                        'reason': 'Exact match with trained data'
                    })
                else:
                    # This is a new transaction
                    new_txns.append(txn)
            
            # Create summary
            summary = self._create_detection_summary(len(new_txns), len(duplicate_txns))
            
            return DuplicateDetectionResult(
                new_transactions=new_txns,
                duplicate_transactions=duplicate_txns,
                duplicate_details=duplicate_details,
                total_new=len(new_txns),
                total_duplicates=len(duplicate_txns),
                detection_summary=summary
            )
            
        except Exception as e:
            self.logger.error(f"Error detecting duplicates: {str(e)}", exc_info=True)
            # Return all as new transactions if detection fails
            return DuplicateDetectionResult(
                new_transactions=new_transactions,
                duplicate_transactions=[],
                duplicate_details=[],
                total_new=len(new_transactions),
                total_duplicates=0,
                detection_summary="Duplicate detection failed - all transactions treated as new"
            )
    
    def detect_duplicates_with_similarity(self, new_transactions: List[RawTransaction],
                                        trained_signatures: Set[str],
                                        similarity_threshold: float = 0.85) -> DuplicateDetectionResult:
        """
        Enhanced duplicate detection with similarity matching for near-duplicates
        
        Args:
            new_transactions: List of new transactions to check
            trained_signatures: Set of hash IDs from all trained transactions
            similarity_threshold: Threshold for similarity matching (0.0-1.0)
            
        Returns:
            DuplicateDetectionResult with separated transactions
        """
        try:
            new_txns = []
            duplicate_txns = []
            duplicate_details = []
            
            for txn in new_transactions:
                # Generate hash ID for this transaction
                normalized = self.data_preparator.normalize_description(txn.description)
                txn_type = self._get_transaction_type(txn)
                hash_id = self.data_preparator.generate_context_aware_hash_id(normalized, txn_type)
                
                is_duplicate = False
                duplicate_reason = ""
                
                # Check for exact match first
                if hash_id in trained_signatures:
                    is_duplicate = True
                    duplicate_reason = "Exact hash match with trained data"
                else:
                    # Check for similarity-based duplicates
                    similarity_match = self._find_similar_trained_transaction(
                        txn, trained_signatures, similarity_threshold
                    )
                    if similarity_match:
                        is_duplicate = True
                        duplicate_reason = f"Similar to trained transaction (similarity: {similarity_match['similarity']:.2f})"
                
                if is_duplicate:
                    duplicate_txns.append(txn)
                    duplicate_details.append({
                        'transaction': txn,
                        'hash_id': hash_id,
                        'normalized_description': normalized,
                        'transaction_type': txn_type,
                        'reason': duplicate_reason
                    })
                else:
                    new_txns.append(txn)
            
            # Create summary
            summary = self._create_detection_summary(len(new_txns), len(duplicate_txns))
            
            return DuplicateDetectionResult(
                new_transactions=new_txns,
                duplicate_transactions=duplicate_txns,
                duplicate_details=duplicate_details,
                total_new=len(new_txns),
                total_duplicates=len(duplicate_txns),
                detection_summary=summary
            )
            
        except Exception as e:
            self.logger.error(f"Error in enhanced duplicate detection: {str(e)}", exc_info=True)
            # Return all as new transactions if detection fails
            return DuplicateDetectionResult(
                new_transactions=new_transactions,
                duplicate_transactions=[],
                duplicate_details=[],
                total_new=len(new_transactions),
                total_duplicates=0,
                detection_summary="Enhanced duplicate detection failed - all transactions treated as new"
            )
    
    def _get_transaction_type(self, transaction: RawTransaction) -> str:
        """Get normalized transaction type"""
        if hasattr(transaction, 'transaction_type') and transaction.transaction_type:
            txn_type = transaction.transaction_type.lower()
            if 'debit' in txn_type or transaction.amount < 0:
                return 'debit'
            elif 'credit' in txn_type or transaction.amount > 0:
                return 'credit'
        
        # Fallback to amount-based detection
        return 'debit' if transaction.amount < 0 else 'credit'
    
    def _find_similar_trained_transaction(self, transaction: RawTransaction,
                                        trained_signatures: Set[str],
                                        threshold: float) -> Optional[Dict[str, any]]:
        """
        Find similar trained transactions using description similarity
        
        Args:
            transaction: Transaction to check
            trained_signatures: Set of trained transaction hash IDs
            threshold: Similarity threshold
            
        Returns:
            Dictionary with similarity info if found, None otherwise
        """
        # This is a simplified implementation
        # In a full implementation, you would need access to the actual trained transaction descriptions
        # For now, we'll just do basic word overlap checking
        
        try:
            txn_words = set(transaction.description.lower().split())
            
            # This would need to be enhanced to actually compare against trained descriptions
            # For now, return None to indicate no similarity matching
            return None
            
        except Exception as e:
            self.logger.error(f"Error in similarity matching: {str(e)}")
            return None
    
    def _create_detection_summary(self, new_count: int, duplicate_count: int) -> str:
        """Create a human-readable summary of detection results"""
        total = new_count + duplicate_count
        if total == 0:
            return "No transactions to process"
        
        duplicate_percentage = (duplicate_count / total) * 100 if total > 0 else 0
        
        summary = f"Processed {total} transactions: "
        summary += f"{new_count} new ({100-duplicate_percentage:.1f}%), "
        summary += f"{duplicate_count} duplicates ({duplicate_percentage:.1f}%)"
        
        if duplicate_count > 0:
            summary += f"\n{duplicate_count} transactions were already trained and will be shown separately."
        
        return summary
