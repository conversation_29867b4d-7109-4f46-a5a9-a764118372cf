"""
AI Categorization Coordinator - Enhanced AI-Assisted Labeling System

This module coordinates the enhanced AI categorization system with intelligent routing,
merchant mapping, cost optimization, and training data integration.

Key Features:
- Intelligent transaction routing (AI vs Manual vs Cached)
- Merchant pattern learning and caching
- Cost optimization with budget protection
- Training data integration
- Transaction type and amount context
- Batch processing with priority handling
"""

from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime
import time

from ..models.transaction import RawTransaction, ProcessedTransaction
from ..core.logger import get_logger
from .enhanced_merchant_mapper import EnhancedMerchantMapper
from .smart_transaction_router import SmartTransactionRouter, RoutingDecision
from .sambanova_categorizer import SambaNovaTransactionCategorizer
from .training_data_manager import TrainingDataManager
from .transaction_filter import SmartTransactionFilter
from .transaction_context_analyzer import TransactionContextAnalyzer


@dataclass
class CategorizationResult:
    """Result of AI categorization process"""
    total_transactions: int
    processed_transactions: List[ProcessedTransaction]
    merchant_cache_used: int
    ai_categorized: int
    manual_required: int
    skipped: int
    total_cost: float
    cost_saved: float
    processing_time: float
    learned_patterns: int
    errors: List[str]


@dataclass
class CostEstimate:
    """Cost estimation for categorization"""
    total_transactions: int
    merchant_cache_available: int
    ai_suitable: int
    manual_required: int
    estimated_ai_cost: float
    estimated_total_cost: float
    cost_savings_from_cache: float
    budget_sufficient: bool
    recommendation: str


class AICategorizationCoordinator:
    """
    Coordinates the enhanced AI categorization system with intelligent routing,
    merchant caching, and cost optimization
    """
    
    def __init__(self, data_dir: str = "bank_analyzer_config"):
        self.logger = get_logger(__name__)
        self.data_dir = data_dir

        # Load configuration first
        self.config = self._load_config()

        # Initialize components with config
        self.merchant_mapper = EnhancedMerchantMapper(f"{data_dir}/merchant_cache")
        self.transaction_filter = SmartTransactionFilter(data_dir)
        self.context_analyzer = TransactionContextAnalyzer()
        self.router = SmartTransactionRouter(self.merchant_mapper, self.transaction_filter, data_dir)
        self.ai_categorizer = SambaNovaTransactionCategorizer(f"{data_dir}/ml_data")
        self.training_manager = TrainingDataManager(f"{data_dir}/ml_data")

        # Initialize session statistics
        self.session_stats = {
            'total_sessions': 0,
            'total_transactions_processed': 0,
            'total_cost': 0.0,
            'total_cost_saved': 0.0,
            'patterns_learned': 0
        }

        self.logger.info(f"AI Coordinator initialized with budget: ${self.config['max_budget_per_session']}")

    def _load_config(self):
        """Load configuration from file or use defaults"""
        import json
        from pathlib import Path

        config_file = Path(self.data_dir) / "ai_coordinator_config.json"

        # Default configuration
        default_config = {
            'max_budget_per_session': 1.0,  # $1 per session
            'enable_learning': True,
            'auto_add_to_training': True,
            'min_confidence_for_learning': 0.7,
            'batch_size': 25,
            'enable_merchant_learning': True
        }

        if config_file.exists():
            try:
                with open(config_file, 'r') as f:
                    loaded_config = json.load(f)

                # Update defaults with loaded config
                default_config.update(loaded_config)
                self.logger.info(f"Loaded AI coordinator config from {config_file}")

            except Exception as e:
                self.logger.error(f"Error loading AI coordinator config: {str(e)}")
        else:
            self.logger.info("Using default AI coordinator configuration")

        return default_config
    
    def estimate_categorization_cost(self, transactions: List[RawTransaction]) -> CostEstimate:
        """
        Estimate cost and provide recommendation for categorizing transactions
        
        Args:
            transactions: List of transactions to estimate
            
        Returns:
            CostEstimate with detailed cost breakdown
        """
        self.logger.info(f"Estimating cost for {len(transactions)} transactions")
        
        # Route transactions to get breakdown
        routing_results = self.router.route_batch(transactions, self.config['max_budget_per_session'])
        
        merchant_cache_count = len(routing_results['merchant_cache'])
        ai_count = len(routing_results['ai_categorization'])
        manual_count = len(routing_results['manual_labeling'])
        
        # Calculate costs
        ai_cost_per_transaction = 0.02  # Estimated
        estimated_ai_cost = ai_count * ai_cost_per_transaction
        cost_savings = merchant_cache_count * ai_cost_per_transaction
        
        budget_sufficient = estimated_ai_cost <= self.config['max_budget_per_session']
        
        # Generate recommendation
        if budget_sufficient:
            if merchant_cache_count > ai_count:
                recommendation = f"Excellent! {merchant_cache_count} transactions can use cached patterns, saving ${cost_savings:.2f}"
            else:
                recommendation = f"Good to proceed. Estimated cost: ${estimated_ai_cost:.2f}"
        else:
            recommendation = f"Budget exceeded. Consider processing in batches or increasing budget limit."
        
        return CostEstimate(
            total_transactions=len(transactions),
            merchant_cache_available=merchant_cache_count,
            ai_suitable=ai_count,
            manual_required=manual_count,
            estimated_ai_cost=estimated_ai_cost,
            estimated_total_cost=estimated_ai_cost,
            cost_savings_from_cache=cost_savings,
            budget_sufficient=budget_sufficient,
            recommendation=recommendation
        )
    
    def categorize_transactions(self, transactions: List[RawTransaction],
                              budget_limit: Optional[float] = None,
                              progress_callback=None) -> CategorizationResult:
        """
        Categorize transactions using the enhanced AI system
        
        Args:
            transactions: List of transactions to categorize
            budget_limit: Optional budget limit override
            
        Returns:
            CategorizationResult with comprehensive results
        """
        start_time = time.time()
        budget = budget_limit or self.config['max_budget_per_session']

        # Progress callback helper
        def update_progress(step, percent, details=""):
            if progress_callback:
                progress_callback(step, percent, details)

        self.logger.info(f"Starting enhanced AI categorization of {len(transactions)} transactions")
        self.session_stats['total_sessions'] += 1

        update_progress("Analyzing transaction routing", 15, f"Processing {len(transactions)} transactions")

        # Route transactions
        routing_results = self.router.route_batch(transactions, budget)

        # Report routing results
        cache_count = len(routing_results['merchant_cache'])
        ai_count = len(routing_results['ai_categorization'])
        manual_count = len(routing_results['manual_labeling'])

        update_progress("Routing completed", 25,
                       f"Cache: {cache_count}, AI: {ai_count}, Manual: {manual_count}")
        
        processed_transactions = []
        errors = []
        total_cost = 0.0
        learned_patterns = 0
        
        # Process merchant cache transactions (instant, no cost)
        update_progress("Processing cached transactions", 30, f"Processing {cache_count} cached transactions")

        for i, (transaction, routing_result) in enumerate(routing_results['merchant_cache']):
            try:
                processed = self._process_with_merchant_cache(transaction, routing_result)
                processed_transactions.append(processed)

                # Update progress for cache processing
                if i % 10 == 0 or i == cache_count - 1:
                    progress = 30 + (i + 1) * 15 // max(1, cache_count)
                    update_progress("Processing cached transactions", progress,
                                  f"Processed {i + 1}/{cache_count}: {transaction.description[:30]}...")

            except Exception as e:
                errors.append(f"Merchant cache error for {transaction.description[:50]}: {str(e)}")

        # Process AI categorization transactions
        ai_transactions = [t for t, r in routing_results['ai_categorization']]
        if ai_transactions:
            update_progress("Starting AI categorization", 45, f"Processing {len(ai_transactions)} transactions with AI")

            try:
                ai_results = self._process_with_ai(ai_transactions, update_progress)
                processed_transactions.extend(ai_results)

                update_progress("Learning from AI results", 75, "Analyzing patterns and updating cache")

                # Learn from AI results
                if self.config['enable_learning']:
                    learned_patterns += self._learn_from_ai_results(ai_transactions, ai_results)
                
                # Calculate actual cost
                total_cost = len(ai_transactions) * 0.02  # Estimated cost per transaction
                
            except Exception as e:
                errors.append(f"AI categorization error: {str(e)}")
                # Fallback: move AI transactions to manual
                for transaction, routing_result in routing_results['ai_categorization']:
                    routing_results['manual_labeling'].append((transaction, routing_result))
        
        # Process manual labeling transactions (create with default categories)
        for transaction, routing_result in routing_results['manual_labeling']:
            try:
                processed = self._process_for_manual_labeling(transaction, routing_result)
                processed_transactions.append(processed)
            except Exception as e:
                errors.append(f"Manual processing error for {transaction.description[:50]}: {str(e)}")
        
        # Update statistics
        self.session_stats['total_transactions_processed'] += len(processed_transactions)
        self.session_stats['total_cost'] += total_cost
        self.session_stats['total_cost_saved'] += len(routing_results['merchant_cache']) * 0.02
        self.session_stats['patterns_learned'] += learned_patterns
        
        processing_time = time.time() - start_time
        
        result = CategorizationResult(
            total_transactions=len(transactions),
            processed_transactions=processed_transactions,
            merchant_cache_used=len(routing_results['merchant_cache']),
            ai_categorized=len(routing_results['ai_categorization']),
            manual_required=len(routing_results['manual_labeling']),
            skipped=len(routing_results['skipped']),
            total_cost=total_cost,
            cost_saved=len(routing_results['merchant_cache']) * 0.02,
            processing_time=processing_time,
            learned_patterns=learned_patterns,
            errors=errors
        )
        
        self.logger.info(f"Categorization completed in {processing_time:.2f}s: "
                        f"{result.merchant_cache_used} cached, "
                        f"{result.ai_categorized} AI, "
                        f"{result.manual_required} manual, "
                        f"${result.total_cost:.2f} cost, "
                        f"${result.cost_saved:.2f} saved")
        
        return result
    
    def _process_with_merchant_cache(self, transaction: RawTransaction, 
                                   routing_result) -> ProcessedTransaction:
        """Process transaction using merchant cache"""
        suggestion = routing_result.merchant_suggestion
        
        processed = ProcessedTransaction()
        processed.update_from_raw(transaction)
        processed.category = suggestion['category']
        processed.sub_category = suggestion['sub_category']
        processed.confidence_score = suggestion['confidence']
        processed.notes = f"Merchant cache: {suggestion['pattern']}"
        
        return processed
    
    def _process_with_ai(self, transactions: List[RawTransaction],
                        progress_callback=None) -> List[ProcessedTransaction]:
        """Process transactions using AI categorization with context enhancement"""

        def update_progress(step, percent, details=""):
            if progress_callback:
                progress_callback(step, percent, details)

        update_progress("Analyzing transaction context", 50, f"Analyzing {len(transactions)} transactions")

        # Analyze context for each transaction
        enhanced_transactions = []
        for i, transaction in enumerate(transactions):
            context = self.context_analyzer.analyze_transaction_context(transaction, transactions)

            # Update progress for context analysis
            if i % 5 == 0 or i == len(transactions) - 1:
                progress = 50 + (i + 1) * 10 // len(transactions)
                update_progress("Analyzing context", progress,
                              f"Analyzed {i + 1}/{len(transactions)}: {transaction.description[:30]}...")
            hints = self.context_analyzer.get_categorization_hints(context)

            # Add context information to transaction for AI processing
            transaction.context_hints = hints
            transaction.context_summary = self.context_analyzer.get_context_summary(context)
            enhanced_transactions.append(transaction)

        update_progress("Processing with AI", 60, f"Sending {len(enhanced_transactions)} transactions to AI")

        # Process with AI using enhanced context
        processed_transactions = self.ai_categorizer.categorize_batch(enhanced_transactions)

        update_progress("Applying context adjustments", 70, "Enhancing AI results with context")

        # Apply context-based confidence adjustments
        for i, processed in enumerate(processed_transactions):
            if hasattr(enhanced_transactions[i], 'context_hints'):
                hints = enhanced_transactions[i].context_hints

                # Apply confidence modifiers
                confidence_adjustments = hints.get('confidence_modifiers', {})
                for modifier, adjustment in confidence_adjustments.items():
                    processed.confidence_score = max(0.0, min(1.0, processed.confidence_score + adjustment))

                # Add context information to notes
                if hasattr(enhanced_transactions[i], 'context_summary'):
                    processed.notes += f" | Context: {enhanced_transactions[i].context_summary}"

                # Store context hints for review
                processed.context_hints = hints

        return processed_transactions
    
    def _process_for_manual_labeling(self, transaction: RawTransaction, 
                                   routing_result) -> ProcessedTransaction:
        """Process transaction for manual labeling"""
        processed = ProcessedTransaction()
        processed.update_from_raw(transaction)
        processed.category = "Other"
        processed.sub_category = "Needs Review"
        processed.confidence_score = 0.1
        processed.notes = f"Manual required: {routing_result.reason}"
        
        # Add merchant suggestion if available
        if routing_result.merchant_suggestion:
            suggestion = routing_result.merchant_suggestion
            processed.suggested_categories = [{
                'category': suggestion['category'],
                'sub_category': suggestion['sub_category'],
                'confidence': suggestion['confidence'],
                'source': 'merchant_cache_low_confidence'
            }]
        
        return processed
    
    def _learn_from_ai_results(self, transactions: List[RawTransaction], 
                             results: List[ProcessedTransaction]) -> int:
        """Learn merchant patterns from AI categorization results"""
        learned_count = 0
        
        for transaction, result in zip(transactions, results):
            if (result.confidence_score >= self.config['min_confidence_for_learning'] and
                self.config['enable_merchant_learning']):
                
                success = self.merchant_mapper.learn_from_ai_categorization(
                    transaction, result.category, result.sub_category, 
                    result.confidence_score, "ai"
                )
                
                if success:
                    learned_count += 1
                
                # Add to training data if configured
                if self.config['auto_add_to_training']:
                    try:
                        self.training_manager.add_ai_categorization_to_training(
                            transaction, result.category, result.sub_category, 
                            result.confidence_score
                        )
                    except Exception as e:
                        self.logger.warning(f"Error adding to training data: {str(e)}")
        
        return learned_count

    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        merchant_stats = self.merchant_mapper.get_merchant_statistics()
        routing_stats = self.router.get_routing_statistics()

        return {
            'coordinator_stats': self.session_stats,
            'merchant_mapping': merchant_stats,
            'routing_stats': routing_stats,
            'ai_categorizer_available': self.ai_categorizer.is_available(),
            'configuration': self.config,
            'budget_status': {
                'max_budget_per_session': self.config['max_budget_per_session'],
                'total_spent': self.session_stats['total_cost'],
                'total_saved': self.session_stats['total_cost_saved'],
                'efficiency_ratio': self.session_stats['total_cost_saved'] / max(0.01, self.session_stats['total_cost'])
            }
        }

    def cleanup_expired_patterns(self) -> Dict[str, int]:
        """Cleanup expired merchant patterns"""
        removed_count = self.merchant_mapper.cleanup_expired_patterns()
        return {'removed_patterns': removed_count}

    def export_merchant_patterns(self, file_path: str) -> bool:
        """Export merchant patterns to file"""
        return self.merchant_mapper.export_patterns(file_path)

    def import_merchant_patterns(self, file_path: str, merge: bool = True) -> Tuple[bool, str, Dict[str, int]]:
        """Import merchant patterns from file"""
        return self.merchant_mapper.import_patterns(file_path, merge)

    def reset_system(self, confirm: bool = False) -> Dict[str, bool]:
        """Reset the entire system (requires confirmation)"""
        if not confirm:
            return {'reset': False, 'reason': 'Confirmation required'}

        results = {}

        # Reset merchant patterns
        results['merchant_patterns'] = self.merchant_mapper.reset_patterns(confirm=True)

        # Reset routing statistics
        self.router.reset_statistics()
        results['routing_stats'] = True

        # Reset session statistics
        self.session_stats = {
            'total_sessions': 0,
            'total_transactions_processed': 0,
            'total_cost': 0.0,
            'total_cost_saved': 0.0,
            'patterns_learned': 0
        }
        results['session_stats'] = True

        self.logger.info("AI Categorization Coordinator system reset completed")
        return results

    def update_configuration(self, config_updates: Dict[str, Any]):
        """Update system configuration"""
        self.config.update(config_updates)

        # Update router configuration if relevant
        router_config = {k: v for k, v in config_updates.items()
                        if k in ['max_ai_transactions_per_batch', 'high_amount_threshold']}
        if router_config:
            self.router.update_config(router_config)

        self.logger.info(f"Configuration updated: {config_updates}")

    def get_manual_labeling_candidates(self, min_amount: float = 0.0,
                                     limit: int = 100) -> List[Dict[str, Any]]:
        """Get transactions that would benefit most from manual labeling"""
        # This would integrate with the training data manager to find
        # transactions that need manual review
        try:
            candidates = []
            # Implementation would depend on training data manager capabilities
            return candidates
        except Exception as e:
            self.logger.error(f"Error getting manual labeling candidates: {str(e)}")
            return []

    def get_ai_suggestions_for_manual_review(self, transactions: List[RawTransaction]) -> Dict[str, Dict[str, Any]]:
        """Get AI suggestions for transactions pending manual review"""
        suggestions = {}

        for transaction in transactions:
            try:
                # Check merchant cache first
                merchant_suggestion = self.merchant_mapper.get_categorization_suggestion(transaction)

                if merchant_suggestion:
                    suggestions[transaction.description] = {
                        'category': merchant_suggestion['category'],
                        'sub_category': merchant_suggestion['sub_category'],
                        'confidence': merchant_suggestion['confidence'],
                        'source': 'merchant_cache',
                        'pattern': merchant_suggestion.get('pattern', ''),
                        'transaction_count': merchant_suggestion.get('transaction_count', 0)
                    }
                else:
                    # Could add AI-based suggestions here if budget allows
                    suggestions[transaction.description] = {
                        'category': 'Other',
                        'sub_category': 'Needs Review',
                        'confidence': 0.1,
                        'source': 'default',
                        'pattern': '',
                        'transaction_count': 0
                    }
            except Exception as e:
                self.logger.warning(f"Error getting suggestion for {transaction.description[:50]}: {str(e)}")

        return suggestions
