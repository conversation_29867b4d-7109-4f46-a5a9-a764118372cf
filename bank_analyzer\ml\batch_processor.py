"""
Intelligent batch processing for SambaNova AI categorization
Optimizes API usage with cost estimation and budget protection
"""

import time
import math
from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime

from ..models.transaction import RawTransaction, ProcessedTransaction
from ..core.logger import get_logger
from .sambanova_categorizer import SambaNovaTransactionCategorizer
from .sambanova_cost_tracker import get_cost_tracker
from .transaction_filter import SmartTransactionFilter, FilterResult


@dataclass
class BatchConfig:
    """Configuration for batch processing"""
    min_batch_size: int = 20
    max_batch_size: int = 30
    cost_estimation_buffer: float = 0.10  # 10% buffer for cost estimation
    max_daily_budget_usage: float = 0.90  # Don't use more than 90% of daily budget
    priority_threshold: int = 7  # Process high priority transactions first
    
    # Token estimation parameters
    avg_tokens_per_transaction: int = 80
    max_tokens_per_transaction: int = 150
    
    # Timing parameters
    batch_delay_seconds: float = 1.0  # Delay between batches
    retry_delay_seconds: float = 2.0


@dataclass
class BatchResult:
    """Result of batch processing"""
    total_transactions: int
    processed_successfully: int
    failed_transactions: int
    skipped_transactions: int
    total_cost: float
    processing_time: float
    batches_processed: int
    budget_exhausted: bool
    error_messages: List[str]


@dataclass
class CostEstimation:
    """Cost estimation for a batch"""
    estimated_tokens: int
    estimated_cost: float
    can_afford: bool
    remaining_budget: float
    confidence: float  # 0.0-1.0 confidence in estimation


class IntelligentBatchProcessor:
    """
    Intelligent batch processor for SambaNova AI categorization
    Optimizes API usage within budget constraints
    """
    
    def __init__(self, config: BatchConfig = None):
        self.logger = get_logger(__name__)
        self.config = config or BatchConfig()
        
        # Initialize components
        self.categorizer = SambaNovaTransactionCategorizer()
        self.transaction_filter = SmartTransactionFilter()
        self.cost_tracker = get_cost_tracker()
        
        # Statistics
        self.stats = {
            "total_batches_processed": 0,
            "total_transactions_processed": 0,
            "total_cost_incurred": 0.0,
            "average_batch_size": 0.0,
            "average_cost_per_transaction": 0.0,
            "budget_stops": 0,
            "estimation_accuracy": []
        }
        
        self.logger.info(f"Batch processor initialized (batch size: {self.config.min_batch_size}-{self.config.max_batch_size})")
    
    def process_transactions(self, transactions: List[RawTransaction]) -> BatchResult:
        """
        Process transactions in optimized batches
        
        Args:
            transactions: List of raw transactions to process
            
        Returns:
            BatchResult with processing summary
        """
        start_time = time.time()
        
        # Filter transactions first
        self.logger.info(f"Filtering {len(transactions)} transactions...")
        filtered_result = self.transaction_filter.filter_batch(transactions)
        
        ai_suitable = filtered_result['priority_order']  # Already sorted by priority
        
        self.logger.info(f"Filtered results: {len(ai_suitable)} AI suitable, "
                        f"{len(filtered_result['manual_required'])} manual, "
                        f"{len(filtered_result['excluded'])} excluded")
        
        if not ai_suitable:
            return BatchResult(
                total_transactions=len(transactions),
                processed_successfully=0,
                failed_transactions=0,
                skipped_transactions=len(transactions),
                total_cost=0.0,
                processing_time=time.time() - start_time,
                batches_processed=0,
                budget_exhausted=False,
                error_messages=["No transactions suitable for AI processing"]
            )
        
        # Process in batches
        result = self._process_in_batches(ai_suitable)
        result.total_transactions = len(transactions)
        result.processing_time = time.time() - start_time
        
        # Update statistics
        self._update_stats(result)
        
        return result
    
    def _process_in_batches(self, transactions: List[RawTransaction]) -> BatchResult:
        """Process transactions in optimized batches"""
        result = BatchResult(
            total_transactions=len(transactions),
            processed_successfully=0,
            failed_transactions=0,
            skipped_transactions=0,
            total_cost=0.0,
            processing_time=0.0,
            batches_processed=0,
            budget_exhausted=False,
            error_messages=[]
        )
        
        remaining_transactions = transactions.copy()
        
        while remaining_transactions:
            # Check budget before processing batch
            if not self._can_continue_processing():
                result.budget_exhausted = True
                result.skipped_transactions = len(remaining_transactions)
                result.error_messages.append("Budget limit reached")
                break
            
            # Determine optimal batch size
            batch_size = self._calculate_optimal_batch_size(remaining_transactions)
            current_batch = remaining_transactions[:batch_size]
            
            # Estimate cost for this batch
            cost_estimation = self._estimate_batch_cost(current_batch)
            
            if not cost_estimation.can_afford:
                # Try smaller batch
                smaller_batch_size = max(1, batch_size // 2)
                current_batch = remaining_transactions[:smaller_batch_size]
                cost_estimation = self._estimate_batch_cost(current_batch)
                
                if not cost_estimation.can_afford:
                    result.budget_exhausted = True
                    result.skipped_transactions = len(remaining_transactions)
                    result.error_messages.append(f"Cannot afford even single transaction (estimated cost: ${cost_estimation.estimated_cost:.6f})")
                    break
            
            # Process the batch
            self.logger.info(f"Processing batch of {len(current_batch)} transactions "
                           f"(estimated cost: ${cost_estimation.estimated_cost:.6f})")
            
            batch_result = self._process_single_batch(current_batch, cost_estimation)
            
            # Update results
            result.processed_successfully += batch_result['successful']
            result.failed_transactions += batch_result['failed']
            result.total_cost += batch_result['cost']
            result.batches_processed += 1
            
            if batch_result['errors']:
                result.error_messages.extend(batch_result['errors'])
            
            # Remove processed transactions
            remaining_transactions = remaining_transactions[len(current_batch):]
            
            # Add delay between batches to respect rate limits
            if remaining_transactions:
                time.sleep(self.config.batch_delay_seconds)
        
        return result
    
    def _calculate_optimal_batch_size(self, transactions: List[RawTransaction]) -> int:
        """Calculate optimal batch size based on current conditions"""
        # Start with configured batch size
        optimal_size = self.config.max_batch_size
        
        # Adjust based on remaining budget
        remaining_budget = self.cost_tracker.get_remaining_budget()['daily_remaining']
        
        if remaining_budget < 0.20:  # Less than $0.20 remaining
            optimal_size = min(optimal_size, 5)
        elif remaining_budget < 0.50:  # Less than $0.50 remaining
            optimal_size = min(optimal_size, 15)
        
        # Adjust based on transaction complexity
        high_priority_count = sum(1 for txn in transactions[:optimal_size] 
                                if self._get_transaction_priority(txn) >= self.config.priority_threshold)
        
        if high_priority_count > optimal_size * 0.8:  # Mostly high priority
            optimal_size = min(optimal_size, 20)  # Smaller batches for high priority
        
        # Ensure minimum batch size
        optimal_size = max(self.config.min_batch_size, optimal_size)
        
        # Don't exceed available transactions
        optimal_size = min(optimal_size, len(transactions))
        
        return optimal_size
    
    def _estimate_batch_cost(self, transactions: List[RawTransaction]) -> CostEstimation:
        """Estimate cost for processing a batch of transactions"""
        total_estimated_tokens = 0
        
        for transaction in transactions:
            # Estimate tokens based on description length
            description_length = len(transaction.description)
            estimated_tokens = min(
                self.config.max_tokens_per_transaction,
                max(30, int(description_length * 1.5))  # Rough estimation
            )
            total_estimated_tokens += estimated_tokens
        
        # Add buffer for few-shot examples and response tokens
        total_estimated_tokens = int(total_estimated_tokens * 1.3)
        
        # Calculate estimated cost
        estimated_cost = self.cost_tracker.calculate_cost(
            total_estimated_tokens // 2,  # Input tokens
            total_estimated_tokens // 2   # Output tokens
        )['total_cost']
        
        # Add buffer
        estimated_cost *= (1 + self.config.cost_estimation_buffer)
        
        # Check if we can afford it
        remaining_budget = self.cost_tracker.get_remaining_budget()['daily_remaining']
        can_afford = estimated_cost <= remaining_budget
        
        # Calculate confidence based on historical accuracy
        confidence = self._calculate_estimation_confidence()
        
        return CostEstimation(
            estimated_tokens=total_estimated_tokens,
            estimated_cost=estimated_cost,
            can_afford=can_afford,
            remaining_budget=remaining_budget,
            confidence=confidence
        )
    
    def _process_single_batch(self, transactions: List[RawTransaction], 
                            cost_estimation: CostEstimation) -> Dict[str, Any]:
        """Process a single batch of transactions"""
        batch_start_time = time.time()
        successful = 0
        failed = 0
        total_cost = 0.0
        errors = []
        
        for transaction in transactions:
            try:
                # Process individual transaction
                processed = self.categorizer.categorize_transaction(transaction)
                
                if processed:
                    successful += 1
                    # Extract cost from notes if available
                    if hasattr(processed, 'notes') and 'SambaNova' in processed.notes:
                        # Parse cost from notes like "SambaNova ($0.000015)"
                        import re
                        cost_match = re.search(r'\$(\d+\.\d+)', processed.notes)
                        if cost_match:
                            total_cost += float(cost_match.group(1))
                else:
                    failed += 1
                    errors.append(f"Failed to process transaction: {transaction.description[:50]}")
                
            except Exception as e:
                failed += 1
                errors.append(f"Error processing transaction: {str(e)}")
        
        processing_time = time.time() - batch_start_time
        
        # Update estimation accuracy
        if cost_estimation.estimated_cost > 0:
            accuracy = 1.0 - abs(total_cost - cost_estimation.estimated_cost) / cost_estimation.estimated_cost
            self.stats["estimation_accuracy"].append(max(0.0, min(1.0, accuracy)))
        
        self.logger.info(f"Batch completed: {successful} successful, {failed} failed, "
                        f"${total_cost:.6f} cost, {processing_time:.2f}s")
        
        return {
            'successful': successful,
            'failed': failed,
            'cost': total_cost,
            'processing_time': processing_time,
            'errors': errors
        }

    def _can_continue_processing(self) -> bool:
        """Check if we can continue processing based on budget"""
        remaining_budget = self.cost_tracker.get_remaining_budget()

        # Check daily budget
        daily_usage_pct = (remaining_budget['daily_budget'] - remaining_budget['daily_remaining']) / remaining_budget['daily_budget']
        if daily_usage_pct >= self.config.max_daily_budget_usage:
            return False

        # Check total budget
        if remaining_budget['total_remaining'] <= 0.05:  # Less than $0.05 remaining
            return False

        return True

    def _get_transaction_priority(self, transaction: RawTransaction) -> int:
        """Get priority score for a transaction"""
        filter_result = self.transaction_filter.filter_transaction(transaction)
        return filter_result.priority_score

    def _calculate_estimation_confidence(self) -> float:
        """Calculate confidence in cost estimation based on historical accuracy"""
        if not self.stats["estimation_accuracy"]:
            return 0.7  # Default confidence

        recent_accuracy = self.stats["estimation_accuracy"][-10:]  # Last 10 estimations
        return sum(recent_accuracy) / len(recent_accuracy)

    def _update_stats(self, result: BatchResult):
        """Update processing statistics"""
        self.stats["total_batches_processed"] += result.batches_processed
        self.stats["total_transactions_processed"] += result.processed_successfully
        self.stats["total_cost_incurred"] += result.total_cost

        if result.budget_exhausted:
            self.stats["budget_stops"] += 1

        # Update averages
        if self.stats["total_batches_processed"] > 0:
            self.stats["average_batch_size"] = (
                self.stats["total_transactions_processed"] / self.stats["total_batches_processed"]
            )

        if self.stats["total_transactions_processed"] > 0:
            self.stats["average_cost_per_transaction"] = (
                self.stats["total_cost_incurred"] / self.stats["total_transactions_processed"]
            )

    def get_processing_stats(self) -> Dict[str, Any]:
        """Get comprehensive processing statistics"""
        estimation_confidence = self._calculate_estimation_confidence()

        return {
            **self.stats,
            "current_estimation_confidence": estimation_confidence,
            "filter_stats": self.transaction_filter.get_stats(),
            "cost_tracker_stats": self.cost_tracker.get_usage_stats(),
            "config": {
                "min_batch_size": self.config.min_batch_size,
                "max_batch_size": self.config.max_batch_size,
                "max_daily_budget_usage": self.config.max_daily_budget_usage,
                "priority_threshold": self.config.priority_threshold
            }
        }

    def estimate_processing_cost(self, transactions: List[RawTransaction]) -> Dict[str, Any]:
        """Estimate cost and feasibility for processing a list of transactions"""
        # Filter transactions first
        filtered_result = self.transaction_filter.filter_batch(transactions)
        ai_suitable = filtered_result['ai_suitable']

        if not ai_suitable:
            return {
                "total_transactions": len(transactions),
                "ai_suitable": 0,
                "estimated_cost": 0.0,
                "estimated_batches": 0,
                "can_afford": True,
                "budget_sufficient": True,
                "processing_time_estimate": 0.0
            }

        # Estimate cost for AI suitable transactions
        cost_estimation = self._estimate_batch_cost(ai_suitable)

        # Calculate number of batches needed
        estimated_batches = math.ceil(len(ai_suitable) / self.config.max_batch_size)

        # Estimate processing time
        estimated_time = (
            estimated_batches * self.config.batch_delay_seconds +
            len(ai_suitable) * 1.5  # Rough estimate of 1.5 seconds per transaction
        )

        # Check budget sufficiency
        remaining_budget = self.cost_tracker.get_remaining_budget()
        budget_sufficient = cost_estimation.estimated_cost <= remaining_budget['daily_remaining']

        return {
            "total_transactions": len(transactions),
            "ai_suitable": len(ai_suitable),
            "manual_required": len(filtered_result['manual_required']),
            "excluded": len(filtered_result['excluded']),
            "estimated_cost": cost_estimation.estimated_cost,
            "estimated_tokens": cost_estimation.estimated_tokens,
            "estimated_batches": estimated_batches,
            "can_afford": cost_estimation.can_afford,
            "budget_sufficient": budget_sufficient,
            "remaining_budget": remaining_budget['daily_remaining'],
            "processing_time_estimate": estimated_time,
            "estimation_confidence": cost_estimation.confidence
        }

    def reset_stats(self):
        """Reset processing statistics"""
        self.stats = {
            "total_batches_processed": 0,
            "total_transactions_processed": 0,
            "total_cost_incurred": 0.0,
            "average_batch_size": 0.0,
            "average_cost_per_transaction": 0.0,
            "budget_stops": 0,
            "estimation_accuracy": []
        }
        self.transaction_filter.reset_stats()
        self.logger.info("Batch processor statistics reset")
