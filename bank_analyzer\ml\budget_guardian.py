"""
Budget protection and pre-flight cost estimation for SambaNova AI
Ensures strict adherence to the $5 total budget limit with comprehensive protection
"""

import json
import time
from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
from pathlib import Path

from ..models.transaction import RawTransaction
from ..core.logger import get_logger
from .sambanova_cost_tracker import get_cost_tracker
from .transaction_filter import SmartTransactionFilter
from .transaction_prioritizer import TransactionPrioritizer


@dataclass
class BudgetStatus:
    """Current budget status"""
    total_budget: float
    daily_budget: float
    total_used: float
    daily_used: float
    total_remaining: float
    daily_remaining: float
    total_usage_pct: float
    daily_usage_pct: float
    can_continue: bool
    warning_level: str  # "safe", "warning", "critical", "exhausted"


@dataclass
class CostEstimate:
    """Pre-flight cost estimation"""
    estimated_cost: float
    estimated_tokens: int
    transaction_count: int
    can_afford: bool
    budget_after: float
    confidence: float
    estimation_method: str
    breakdown: Dict[str, float]


@dataclass
class BudgetAlert:
    """Budget alert information"""
    level: str  # "warning", "critical", "limit"
    message: str
    current_usage: float
    limit: float
    percentage: float
    timestamp: datetime
    action_required: bool


class BudgetGuardian:
    """
    Comprehensive budget protection system for SambaNova AI usage
    Provides pre-flight cost estimation and strict budget enforcement
    """
    
    def __init__(self, config_dir: str = "bank_analyzer_config"):
        self.logger = get_logger(__name__)
        self.config_dir = Path(config_dir)
        
        # Initialize components
        self.cost_tracker = get_cost_tracker()
        self.transaction_filter = SmartTransactionFilter()
        self.prioritizer = TransactionPrioritizer()
        
        # Budget limits and thresholds
        self.total_budget_limit = 5.0  # $5 total limit
        self.daily_budget_limit = 1.0  # $1 daily limit
        
        # Warning thresholds
        self.warning_threshold = 0.70  # 70% usage warning
        self.critical_threshold = 0.85  # 85% usage critical
        self.emergency_threshold = 0.95  # 95% usage emergency stop
        
        # Safety margins
        self.safety_margin = 0.10  # 10% safety margin for estimates
        self.minimum_reserve = 0.25  # Always keep $0.25 in reserve
        
        # Cost estimation parameters
        self.avg_cost_per_transaction = 0.000015  # Based on testing
        self.token_cost_input = 0.10 / 1_000_000  # $0.10 per million input tokens
        self.token_cost_output = 0.20 / 1_000_000  # $0.20 per million output tokens
        
        # Alert history
        self.alerts = []
        self._load_alerts()
        
        # Statistics
        self.stats = {
            "total_estimates": 0,
            "estimates_approved": 0,
            "estimates_rejected": 0,
            "budget_stops": 0,
            "warnings_issued": 0,
            "critical_alerts": 0
        }
        
        self.logger.info(f"Budget guardian initialized (${self.total_budget_limit} total, ${self.daily_budget_limit} daily)")
    
    def get_budget_status(self) -> BudgetStatus:
        """Get current budget status"""
        usage_stats = self.cost_tracker.get_usage_stats()
        
        total_used = usage_stats["total_cost"]
        daily_used = usage_stats["daily_cost"]
        total_remaining = max(0, self.total_budget_limit - total_used)
        daily_remaining = max(0, self.daily_budget_limit - daily_used)
        
        total_usage_pct = (total_used / self.total_budget_limit) * 100
        daily_usage_pct = (daily_used / self.daily_budget_limit) * 100
        
        # Determine warning level
        max_usage_pct = max(total_usage_pct, daily_usage_pct)
        if max_usage_pct >= self.emergency_threshold * 100:
            warning_level = "exhausted"
            can_continue = False
        elif max_usage_pct >= self.critical_threshold * 100:
            warning_level = "critical"
            can_continue = total_remaining > self.minimum_reserve and daily_remaining > 0.05
        elif max_usage_pct >= self.warning_threshold * 100:
            warning_level = "warning"
            can_continue = True
        else:
            warning_level = "safe"
            can_continue = True
        
        return BudgetStatus(
            total_budget=self.total_budget_limit,
            daily_budget=self.daily_budget_limit,
            total_used=total_used,
            daily_used=daily_used,
            total_remaining=total_remaining,
            daily_remaining=daily_remaining,
            total_usage_pct=total_usage_pct,
            daily_usage_pct=daily_usage_pct,
            can_continue=can_continue,
            warning_level=warning_level
        )
    
    def estimate_cost(self, transactions: List[RawTransaction], 
                     use_prioritization: bool = True) -> CostEstimate:
        """
        Estimate cost for processing a list of transactions
        
        Args:
            transactions: List of transactions to estimate
            use_prioritization: Whether to use prioritization for more accurate estimates
            
        Returns:
            CostEstimate with detailed breakdown
        """
        self.stats["total_estimates"] += 1
        
        # Filter transactions to get AI-suitable ones
        filtered = self.transaction_filter.filter_batch(transactions)
        ai_suitable = filtered['ai_suitable']
        
        if not ai_suitable:
            return CostEstimate(
                estimated_cost=0.0,
                estimated_tokens=0,
                transaction_count=0,
                can_afford=True,
                budget_after=self.get_budget_status().total_remaining,
                confidence=1.0,
                estimation_method="no_ai_suitable",
                breakdown={"ai_suitable": 0, "filtered_out": len(transactions)}
            )
        
        # Use prioritization if requested
        if use_prioritization:
            prioritized = self.prioritizer.prioritize_batch(ai_suitable)
            # Focus on high and medium priority transactions
            high_priority = [txn for txn, score in prioritized if score.total_score >= 7]
            medium_priority = [txn for txn, score in prioritized if 5 <= score.total_score < 7]
            
            # Estimate based on priority
            processing_candidates = high_priority + medium_priority[:10]  # Limit medium priority
        else:
            processing_candidates = ai_suitable
        
        # Estimate tokens and cost
        total_tokens = 0
        for transaction in processing_candidates:
            # Estimate tokens based on description length and complexity
            desc_length = len(transaction.description)
            base_tokens = max(30, min(150, desc_length * 1.2))
            
            # Add tokens for few-shot examples (if budget allows)
            if self.get_budget_status().daily_remaining > 0.50:
                base_tokens += 60  # Additional tokens for examples
            
            total_tokens += int(base_tokens)
        
        # Calculate cost (assume 50/50 input/output split)
        input_tokens = total_tokens // 2
        output_tokens = total_tokens - input_tokens
        
        estimated_cost = (
            input_tokens * self.token_cost_input +
            output_tokens * self.token_cost_output
        )
        
        # Add safety margin
        estimated_cost *= (1 + self.safety_margin)
        
        # Check if we can afford it
        budget_status = self.get_budget_status()
        can_afford = (
            estimated_cost <= budget_status.daily_remaining and
            estimated_cost <= budget_status.total_remaining - self.minimum_reserve
        )
        
        # Calculate confidence based on historical accuracy
        confidence = self._calculate_estimation_confidence()
        
        # Create breakdown
        breakdown = {
            "total_transactions": len(transactions),
            "ai_suitable": len(ai_suitable),
            "processing_candidates": len(processing_candidates),
            "estimated_tokens": total_tokens,
            "input_tokens": input_tokens,
            "output_tokens": output_tokens,
            "base_cost": estimated_cost / (1 + self.safety_margin),
            "safety_margin": estimated_cost * self.safety_margin / (1 + self.safety_margin),
            "daily_remaining": budget_status.daily_remaining,
            "total_remaining": budget_status.total_remaining
        }
        
        return CostEstimate(
            estimated_cost=estimated_cost,
            estimated_tokens=total_tokens,
            transaction_count=len(processing_candidates),
            can_afford=can_afford,
            budget_after=budget_status.total_remaining - estimated_cost,
            confidence=confidence,
            estimation_method="prioritized" if use_prioritization else "filtered",
            breakdown=breakdown
        )
    
    def approve_processing(self, cost_estimate: CostEstimate) -> Tuple[bool, str]:
        """
        Approve or reject processing based on cost estimate and budget status
        
        Args:
            cost_estimate: Cost estimate from estimate_cost()
            
        Returns:
            Tuple of (approved, reason)
        """
        budget_status = self.get_budget_status()
        
        # Check if we can continue at all
        if not budget_status.can_continue:
            self.stats["estimates_rejected"] += 1
            self.stats["budget_stops"] += 1
            return False, f"Budget exhausted ({budget_status.warning_level})"
        
        # Check if we can afford this specific request
        if not cost_estimate.can_afford:
            self.stats["estimates_rejected"] += 1
            reason = f"Cannot afford ${cost_estimate.estimated_cost:.6f} (remaining: ${budget_status.daily_remaining:.6f})"
            return False, reason
        
        # Check if this would put us over critical threshold
        projected_usage = (budget_status.total_used + cost_estimate.estimated_cost) / self.total_budget_limit
        if projected_usage > self.critical_threshold:
            self.stats["estimates_rejected"] += 1
            self._create_alert("critical", f"Processing would exceed critical threshold ({projected_usage*100:.1f}%)")
            return False, f"Would exceed critical budget threshold ({projected_usage*100:.1f}%)"
        
        # Check minimum reserve
        if cost_estimate.budget_after < self.minimum_reserve:
            self.stats["estimates_rejected"] += 1
            return False, f"Would leave insufficient reserve (${cost_estimate.budget_after:.6f} < ${self.minimum_reserve:.2f})"
        
        # Issue warnings if needed
        if projected_usage > self.warning_threshold:
            self._create_alert("warning", f"Approaching budget limit ({projected_usage*100:.1f}%)")
        
        self.stats["estimates_approved"] += 1
        return True, f"Approved: ${cost_estimate.estimated_cost:.6f} for {cost_estimate.transaction_count} transactions"
    
    def get_processing_recommendation(self, transactions: List[RawTransaction]) -> Dict[str, Any]:
        """
        Get comprehensive recommendation for processing transactions
        
        Args:
            transactions: List of transactions to analyze
            
        Returns:
            Dictionary with processing recommendations
        """
        # Get cost estimate
        cost_estimate = self.estimate_cost(transactions, use_prioritization=True)
        
        # Get approval status
        approved, reason = self.approve_processing(cost_estimate)
        
        # Get budget status
        budget_status = self.get_budget_status()
        
        # Calculate how many transactions we can afford
        if approved:
            affordable_count = cost_estimate.transaction_count
        else:
            # Calculate reduced batch size
            max_affordable_cost = min(
                budget_status.daily_remaining * 0.8,  # 80% of remaining daily
                budget_status.total_remaining - self.minimum_reserve
            )
            affordable_count = max(0, int(max_affordable_cost / self.avg_cost_per_transaction))
        
        return {
            "approved": approved,
            "reason": reason,
            "cost_estimate": {
                "total_cost": cost_estimate.estimated_cost,
                "transaction_count": cost_estimate.transaction_count,
                "confidence": cost_estimate.confidence,
                "can_afford": cost_estimate.can_afford
            },
            "budget_status": {
                "warning_level": budget_status.warning_level,
                "total_remaining": budget_status.total_remaining,
                "daily_remaining": budget_status.daily_remaining,
                "usage_percentage": budget_status.total_usage_pct
            },
            "recommendations": {
                "max_affordable_transactions": affordable_count,
                "suggested_batch_size": min(affordable_count, 20),
                "should_prioritize": budget_status.warning_level in ["warning", "critical"],
                "should_delay": budget_status.warning_level == "exhausted"
            },
            "alternatives": {
                "process_high_priority_only": affordable_count > 0,
                "wait_for_daily_reset": budget_status.daily_remaining < 0.10,
                "manual_categorization": not approved
            }
        }

    def _calculate_estimation_confidence(self) -> float:
        """Calculate confidence in cost estimation based on historical accuracy"""
        # Simple confidence calculation - could be enhanced with historical data
        base_confidence = 0.75

        # Adjust based on budget status
        budget_status = self.get_budget_status()
        if budget_status.warning_level == "safe":
            return min(0.95, base_confidence + 0.15)
        elif budget_status.warning_level == "warning":
            return base_confidence
        else:
            return max(0.60, base_confidence - 0.15)

    def _create_alert(self, level: str, message: str):
        """Create and log a budget alert"""
        budget_status = self.get_budget_status()

        alert = BudgetAlert(
            level=level,
            message=message,
            current_usage=budget_status.total_used,
            limit=self.total_budget_limit,
            percentage=budget_status.total_usage_pct,
            timestamp=datetime.now(),
            action_required=level in ["critical", "limit"]
        )

        self.alerts.append(alert)
        self._save_alerts()

        # Update statistics
        if level == "warning":
            self.stats["warnings_issued"] += 1
        elif level in ["critical", "limit"]:
            self.stats["critical_alerts"] += 1

        # Log the alert
        if level == "critical":
            self.logger.error(f"CRITICAL BUDGET ALERT: {message}")
        elif level == "warning":
            self.logger.warning(f"Budget warning: {message}")
        else:
            self.logger.info(f"Budget alert: {message}")

    def _load_alerts(self):
        """Load alert history from file"""
        try:
            alerts_file = self.config_dir / "budget_alerts.json"
            if alerts_file.exists():
                with open(alerts_file, 'r') as f:
                    alert_data = json.load(f)

                self.alerts = []
                for data in alert_data:
                    alert = BudgetAlert(
                        level=data["level"],
                        message=data["message"],
                        current_usage=data["current_usage"],
                        limit=data["limit"],
                        percentage=data["percentage"],
                        timestamp=datetime.fromisoformat(data["timestamp"]),
                        action_required=data.get("action_required", False)
                    )
                    self.alerts.append(alert)
        except Exception as e:
            self.logger.error(f"Error loading alerts: {str(e)}")
            self.alerts = []

    def _save_alerts(self):
        """Save alert history to file"""
        try:
            alerts_file = self.config_dir / "budget_alerts.json"

            # Keep only recent alerts (last 100)
            recent_alerts = self.alerts[-100:]

            alert_data = []
            for alert in recent_alerts:
                alert_data.append({
                    "level": alert.level,
                    "message": alert.message,
                    "current_usage": alert.current_usage,
                    "limit": alert.limit,
                    "percentage": alert.percentage,
                    "timestamp": alert.timestamp.isoformat(),
                    "action_required": alert.action_required
                })

            with open(alerts_file, 'w') as f:
                json.dump(alert_data, f, indent=2)

        except Exception as e:
            self.logger.error(f"Error saving alerts: {str(e)}")

    def get_recent_alerts(self, hours: int = 24) -> List[BudgetAlert]:
        """Get recent alerts within specified hours"""
        cutoff = datetime.now() - timedelta(hours=hours)
        return [alert for alert in self.alerts if alert.timestamp > cutoff]

    def get_guardian_stats(self) -> Dict[str, Any]:
        """Get comprehensive budget guardian statistics"""
        budget_status = self.get_budget_status()
        recent_alerts = self.get_recent_alerts()

        return {
            **self.stats,
            "budget_status": {
                "total_budget": budget_status.total_budget,
                "daily_budget": budget_status.daily_budget,
                "total_used": budget_status.total_used,
                "daily_used": budget_status.daily_used,
                "total_remaining": budget_status.total_remaining,
                "daily_remaining": budget_status.daily_remaining,
                "warning_level": budget_status.warning_level,
                "can_continue": budget_status.can_continue
            },
            "thresholds": {
                "warning_threshold": self.warning_threshold * 100,
                "critical_threshold": self.critical_threshold * 100,
                "emergency_threshold": self.emergency_threshold * 100,
                "safety_margin": self.safety_margin * 100,
                "minimum_reserve": self.minimum_reserve
            },
            "recent_alerts": len(recent_alerts),
            "approval_rate": (self.stats["estimates_approved"] / max(1, self.stats["total_estimates"])) * 100,
            "cost_estimation": {
                "avg_cost_per_transaction": self.avg_cost_per_transaction,
                "token_cost_input": self.token_cost_input * 1_000_000,
                "token_cost_output": self.token_cost_output * 1_000_000
            }
        }

    def reset_stats(self):
        """Reset guardian statistics"""
        self.stats = {
            "total_estimates": 0,
            "estimates_approved": 0,
            "estimates_rejected": 0,
            "budget_stops": 0,
            "warnings_issued": 0,
            "critical_alerts": 0
        }
        self.logger.info("Budget guardian statistics reset")

    def emergency_stop(self, reason: str = "Manual emergency stop"):
        """Emergency stop all processing"""
        self._create_alert("limit", f"EMERGENCY STOP: {reason}")
        self.logger.critical(f"EMERGENCY BUDGET STOP: {reason}")

        # Could implement additional emergency measures here
        # such as disabling the SambaNova client

    def get_budget_forecast(self, days: int = 7) -> Dict[str, Any]:
        """Get budget usage forecast"""
        current_daily_rate = self.get_budget_status().daily_used

        # Simple linear projection
        projected_weekly_cost = current_daily_rate * days
        projected_total_after_period = self.get_budget_status().total_used + projected_weekly_cost

        days_until_exhaustion = float('inf')
        if current_daily_rate > 0:
            remaining_budget = self.get_budget_status().total_remaining
            days_until_exhaustion = remaining_budget / current_daily_rate

        return {
            "current_daily_rate": current_daily_rate,
            "projected_cost_next_7_days": projected_weekly_cost,
            "projected_total_after_7_days": projected_total_after_period,
            "days_until_budget_exhaustion": min(days_until_exhaustion, 999),
            "sustainable_daily_rate": self.get_budget_status().total_remaining / 30,  # 30 days
            "forecast_confidence": "low" if current_daily_rate == 0 else "medium"
        }
