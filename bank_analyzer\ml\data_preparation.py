"""
Data preparation module for ML-based transaction categorization
Extracts unique transaction descriptions and prepares datasets for training
"""

import pandas as pd
import re
import hashlib
from pathlib import Path
from typing import List, Dict, Set, Optional, Tuple, Any
from datetime import datetime, date
from dataclasses import dataclass, field
import logging

from ..models.transaction import RawTransaction, ProcessedTransaction
from ..core.logger import get_logger


def detect_transaction_type(amount: float) -> str:
    """
    Detect transaction type based on amount

    Args:
        amount: Transaction amount

    Returns:
        "debit" for negative amounts (money going out), "credit" for positive amounts (money coming in)
    """
    return "debit" if amount < 0 else "credit"


def get_category_type_for_transaction(transaction_type: str) -> str:
    """
    Get the appropriate category type for a transaction type

    Args:
        transaction_type: "debit" or "credit"

    Returns:
        "expense" for debit transactions, "income" for credit transactions
    """
    return "expense" if transaction_type == "debit" else "income"


def is_debit_transaction(amount: float) -> bool:
    """Check if transaction is a debit (money going out)"""
    return amount < 0


def is_credit_transaction(amount: float) -> bool:
    """Check if transaction is a credit (money coming in)"""
    return amount > 0


@dataclass
class UniqueTransaction:
    """Represents a unique transaction description with metadata"""
    description: str
    normalized_description: str
    hash_id: str
    frequency: int = 1
    first_seen: Optional[date] = None
    last_seen: Optional[date] = None
    amount_range: Tuple[float, float] = field(default_factory=lambda: (0.0, 0.0))
    sample_amounts: List[float] = field(default_factory=list)

    # Transaction type information for context-aware categorization
    transaction_types: Set[str] = field(default_factory=set)  # Set of 'debit', 'credit'
    debit_frequency: int = 0  # How many times seen as debit
    credit_frequency: int = 0  # How many times seen as credit

    # Categorization info
    category: Optional[str] = None
    sub_category: Optional[str] = None
    confidence: float = 0.0
    is_manually_labeled: bool = False
    labeled_by: str = "system"
    labeled_at: Optional[datetime] = None

    # Source tracking
    source_files: Set[str] = field(default_factory=set)
    bank_names: Set[str] = field(default_factory=set)

    def get_predominant_transaction_type(self) -> str:
        """
        Get the predominant transaction type for this unique transaction

        Returns:
            "debit" if more debit transactions, "credit" if more credit transactions,
            "mixed" if equal or if no transaction type data available
        """
        # First check frequency-based determination
        if self.debit_frequency > self.credit_frequency:
            return "debit"
        elif self.credit_frequency > self.debit_frequency:
            return "credit"

        # If frequencies are equal or both zero, use amount range to determine type
        if self.amount_range and len(self.amount_range) == 2:
            min_amount, max_amount = self.amount_range

            # If all amounts are negative, it's likely a debit transaction
            if max_amount < 0:
                return "debit"
            # If all amounts are positive, it's likely a credit transaction
            elif min_amount > 0:
                return "credit"
            # If amounts span both positive and negative, check which is more common
            elif abs(min_amount) > max_amount:
                return "debit"  # Larger negative amounts suggest predominantly debit
            elif max_amount > abs(min_amount):
                return "credit"  # Larger positive amounts suggest predominantly credit

        # If we still can't determine, check if we have any frequency data
        if self.debit_frequency > 0 and self.credit_frequency == 0:
            return "debit"
        elif self.credit_frequency > 0 and self.debit_frequency == 0:
            return "credit"

        # Default to mixed if we can't determine
        return "mixed"

    def get_suggested_category_type(self) -> str:
        """
        Get the suggested category type based on predominant transaction type

        Returns:
            "expense" for predominantly debit transactions,
            "income" for predominantly credit transactions,
            "both" for mixed transactions
        """
        predominant_type = self.get_predominant_transaction_type()
        if predominant_type == "debit":
            return "expense"
        elif predominant_type == "credit":
            return "income"
        else:
            return "both"

    def is_predominantly_debit(self) -> bool:
        """Check if this transaction is predominantly a debit transaction"""
        return self.get_predominant_transaction_type() == "debit"

    def is_predominantly_credit(self) -> bool:
        """Check if this transaction is predominantly a credit transaction"""
        return self.get_predominant_transaction_type() == "credit"

    def infer_transaction_type_from_amount(self) -> str:
        """
        Infer transaction type purely from amount range when frequency data is unreliable
        This is a fallback method for cases where frequency data might be incorrect

        Returns:
            "debit", "credit", or "mixed" based on amount range analysis
        """
        if not self.amount_range or len(self.amount_range) != 2:
            return "mixed"

        min_amount, max_amount = self.amount_range

        # If all amounts are negative, it's a debit transaction
        if max_amount < 0:
            return "debit"
        # If all amounts are positive, it's a credit transaction
        elif min_amount > 0:
            return "credit"
        # If amounts span both positive and negative, it's mixed
        else:
            return "mixed"


class TransactionDataPreparator:
    """
    Prepares transaction data for ML training and categorization
    Extracts unique descriptions, normalizes text, and manages training datasets
    """
    
    def __init__(self, data_dir: str = "bank_analyzer_config/ml_data"):
        self.logger = get_logger(__name__)
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # File paths
        self.unique_transactions_file = self.data_dir / "unique_transactions.csv"
        self.training_data_file = self.data_dir / "training_data.csv"
        self.normalization_rules_file = self.data_dir / "normalization_rules.json"
        
        # Text normalization patterns
        self.normalization_patterns = self._load_normalization_patterns()
    
    def _load_normalization_patterns(self) -> List[Dict[str, str]]:
        """Load text normalization patterns"""
        return [
            # Remove transaction IDs and reference numbers
            {"pattern": r"\b\d{10,}\b", "replacement": ""},
            {"pattern": r"\bREF\s*:?\s*\w+", "replacement": ""},
            {"pattern": r"\bTXN\s*:?\s*\w+", "replacement": ""},
            {"pattern": r"\bUTR\s*:?\s*\w+", "replacement": ""},
            
            # Remove dates and times
            {"pattern": r"\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b", "replacement": ""},
            {"pattern": r"\b\d{1,2}:\d{2}(:\d{2})?\s*(AM|PM)?\b", "replacement": ""},
            
            # Remove amounts and currency symbols
            {"pattern": r"₹\s*[\d,]+\.?\d*", "replacement": ""},
            {"pattern": r"INR\s*[\d,]+\.?\d*", "replacement": ""},
            {"pattern": r"RS\.?\s*[\d,]+\.?\d*", "replacement": ""},
            
            # Normalize common merchant patterns
            {"pattern": r"\bPVT\.?\s*LTD\.?", "replacement": ""},
            {"pattern": r"\bLIMITED\b", "replacement": ""},
            {"pattern": r"\bLTD\.?\b", "replacement": ""},
            {"pattern": r"\bPVT\.?\b", "replacement": ""},
            
            # Remove extra whitespace
            {"pattern": r"\s+", "replacement": " "},
        ]
    
    def normalize_description(self, description: str) -> str:
        """
        Normalize transaction description for better matching
        
        Args:
            description: Raw transaction description
            
        Returns:
            Normalized description
        """
        if not description:
            return ""
        
        normalized = description.upper().strip()
        
        # Apply normalization patterns
        for pattern_rule in self.normalization_patterns:
            normalized = re.sub(
                pattern_rule["pattern"], 
                pattern_rule["replacement"], 
                normalized, 
                flags=re.IGNORECASE
            )
        
        # Final cleanup
        normalized = re.sub(r'\s+', ' ', normalized).strip()
        
        return normalized

    def _determine_transaction_type(self, transaction: 'RawTransaction') -> str:
        """
        Determine if transaction is debit or credit based on amount and type

        Args:
            transaction: Raw transaction object

        Returns:
            'debit' or 'credit'
        """
        # Check if transaction has explicit type
        if hasattr(transaction, 'transaction_type') and transaction.transaction_type:
            txn_type = transaction.transaction_type.lower()
            if 'debit' in txn_type or 'dr' in txn_type:
                return 'debit'
            elif 'credit' in txn_type or 'cr' in txn_type:
                return 'credit'

        # Fallback to amount sign (negative = debit, positive = credit)
        if transaction.amount < 0:
            return 'debit'
        else:
            return 'credit'

    def generate_hash_id(self, normalized_description: str) -> str:
        """Generate unique hash ID for normalized description"""
        return hashlib.md5(normalized_description.encode()).hexdigest()[:12]

    def generate_context_aware_hash_id(self, normalized_description: str, transaction_type: str) -> str:
        """
        Generate context-aware hash ID that includes transaction type
        This allows same merchant to have different categories for debit vs credit

        Args:
            normalized_description: Normalized transaction description
            transaction_type: 'debit' or 'credit'

        Returns:
            Context-aware hash ID
        """
        context_string = f"{normalized_description}|{transaction_type}"
        return hashlib.md5(context_string.encode()).hexdigest()[:12]
    
    def extract_unique_transactions(self, transactions: List[RawTransaction]) -> Dict[str, UniqueTransaction]:
        """
        Extract unique transaction descriptions from a list of transactions
        
        Args:
            transactions: List of raw transactions
            
        Returns:
            Dictionary of unique transactions keyed by hash_id
        """
        unique_transactions = {}
        
        for transaction in transactions:
            if not transaction.description:
                continue
            
            # Normalize description
            normalized = self.normalize_description(transaction.description)
            if not normalized:
                continue
            
            # Generate hash ID
            hash_id = self.generate_hash_id(normalized)
            
            if hash_id in unique_transactions:
                # Update existing unique transaction
                unique_txn = unique_transactions[hash_id]
                unique_txn.frequency += 1
                unique_txn.last_seen = max(unique_txn.last_seen or transaction.date, transaction.date)

                # Update transaction type tracking
                txn_type = self._determine_transaction_type(transaction)
                unique_txn.transaction_types.add(txn_type)
                if txn_type == 'debit':
                    unique_txn.debit_frequency += 1
                elif txn_type == 'credit':
                    unique_txn.credit_frequency += 1

                # Update amount range
                amount = float(abs(transaction.amount))
                unique_txn.sample_amounts.append(amount)
                unique_txn.amount_range = (
                    min(unique_txn.amount_range[0], amount),
                    max(unique_txn.amount_range[1], amount)
                )

                # Update source tracking
                if transaction.source_file:
                    unique_txn.source_files.add(transaction.source_file)
                if transaction.bank_name:
                    unique_txn.bank_names.add(transaction.bank_name)
            else:
                # Create new unique transaction
                amount = float(abs(transaction.amount))
                txn_type = self._determine_transaction_type(transaction)

                unique_transactions[hash_id] = UniqueTransaction(
                    description=transaction.description,
                    normalized_description=normalized,
                    hash_id=hash_id,
                    frequency=1,
                    first_seen=transaction.date,
                    last_seen=transaction.date,
                    amount_range=(amount, amount),
                    sample_amounts=[amount],
                    transaction_types={txn_type},
                    debit_frequency=1 if txn_type == 'debit' else 0,
                    credit_frequency=1 if txn_type == 'credit' else 0,
                    source_files={transaction.source_file} if transaction.source_file else set(),
                    bank_names={transaction.bank_name} if transaction.bank_name else set()
                )
        
        self.logger.info(f"Extracted {len(unique_transactions)} unique transaction descriptions from {len(transactions)} transactions")
        return unique_transactions

    def extract_context_aware_unique_transactions(self, transactions: List['RawTransaction']) -> Dict[str, UniqueTransaction]:
        """
        Extract unique transactions with context-aware categorization (separate debit/credit)

        Args:
            transactions: List of raw transactions

        Returns:
            Dictionary of context-aware unique transactions keyed by context-aware hash_id
        """
        unique_transactions = {}

        for transaction in transactions:
            if not transaction.description:
                continue

            # Normalize description
            normalized = self.normalize_description(transaction.description)
            if not normalized:
                continue

            # Determine transaction type
            txn_type = self._determine_transaction_type(transaction)

            # Generate context-aware hash ID
            hash_id = self.generate_context_aware_hash_id(normalized, txn_type)

            if hash_id in unique_transactions:
                # Update existing unique transaction
                unique_txn = unique_transactions[hash_id]
                unique_txn.frequency += 1
                unique_txn.last_seen = max(unique_txn.last_seen or transaction.date, transaction.date)

                # Update transaction type tracking (should be consistent for context-aware)
                unique_txn.transaction_types.add(txn_type)
                if txn_type == 'debit':
                    unique_txn.debit_frequency += 1
                elif txn_type == 'credit':
                    unique_txn.credit_frequency += 1

                # Update amount range
                amount = float(abs(transaction.amount))
                unique_txn.sample_amounts.append(amount)
                unique_txn.amount_range = (
                    min(unique_txn.amount_range[0], amount),
                    max(unique_txn.amount_range[1], amount)
                )

                # Update source tracking
                if transaction.source_file:
                    unique_txn.source_files.add(transaction.source_file)
                if transaction.bank_name:
                    unique_txn.bank_names.add(transaction.bank_name)
            else:
                # Create new context-aware unique transaction
                amount = float(abs(transaction.amount))

                unique_transactions[hash_id] = UniqueTransaction(
                    description=f"{transaction.description} [{txn_type.upper()}]",  # Add type indicator
                    normalized_description=f"{normalized} [{txn_type.upper()}]",
                    hash_id=hash_id,
                    frequency=1,
                    first_seen=transaction.date,
                    last_seen=transaction.date,
                    amount_range=(amount, amount),
                    sample_amounts=[amount],
                    transaction_types={txn_type},
                    debit_frequency=1 if txn_type == 'debit' else 0,
                    credit_frequency=1 if txn_type == 'credit' else 0,
                    source_files={transaction.source_file} if transaction.source_file else set(),
                    bank_names={transaction.bank_name} if transaction.bank_name else set()
                )

        self.logger.info(f"Extracted {len(unique_transactions)} context-aware unique transaction descriptions from {len(transactions)} transactions")
        return unique_transactions
    
    def load_unique_transactions(self) -> Dict[str, UniqueTransaction]:
        """Load unique transactions from storage"""
        import time
        start_time = time.time()
        unique_transactions = {}

        if self.unique_transactions_file.exists():
            try:
                # Use chunked reading for large files
                chunk_size = 10000
                df_chunks = pd.read_csv(self.unique_transactions_file, chunksize=chunk_size)

                total_rows = 0
                for chunk_df in df_chunks:
                    for _, row in chunk_df.iterrows():
                        # Validate essential fields before creating transaction
                        description = row.get('description', '')
                        hash_id = row.get('hash_id', '')

                        # Skip rows with missing essential data
                        if pd.isna(description) or not str(description).strip():
                            self.logger.warning(f"Skipping row with empty description: {row.to_dict()}")
                            continue

                        if pd.isna(hash_id) or not str(hash_id).strip():
                            self.logger.warning(f"Skipping row with empty hash_id: {description}")
                            continue

                        # Parse transaction types
                        transaction_types = set()
                        if pd.notna(row.get('transaction_types')):
                            transaction_types = set(str(row['transaction_types']).split(';'))

                        # Clean and validate description
                        description = str(description).strip()
                        normalized_description = row.get('normalized_description', '')
                        if pd.isna(normalized_description) or not str(normalized_description).strip():
                            normalized_description = self._normalize_description(description)

                        unique_txn = UniqueTransaction(
                            description=description,
                            normalized_description=str(normalized_description).strip(),
                            hash_id=str(hash_id).strip(),
                            frequency=max(1, int(row.get('frequency', 1))),  # Ensure frequency is at least 1
                            first_seen=pd.to_datetime(row['first_seen']).date() if pd.notna(row['first_seen']) else None,
                            last_seen=pd.to_datetime(row['last_seen']).date() if pd.notna(row['last_seen']) else None,
                            amount_range=(row.get('min_amount', 0.0), row.get('max_amount', 0.0)),
                            transaction_types=transaction_types,
                            debit_frequency=max(0, int(row.get('debit_frequency', 0))),
                            credit_frequency=max(0, int(row.get('credit_frequency', 0))),
                            category=row.get('category') if pd.notna(row.get('category')) else None,
                            sub_category=row.get('sub_category') if pd.notna(row.get('sub_category')) else None,
                            confidence=float(row.get('confidence', 0.0)),
                            is_manually_labeled=str(row.get('is_manually_labeled', 'False')).lower() in ('true', '1', 'yes'),
                            labeled_by=str(row.get('labeled_by', 'system')),
                            labeled_at=pd.to_datetime(row['labeled_at']) if pd.notna(row.get('labeled_at')) else None,
                            source_files=set(str(row.get('source_files', '')).split(';')) if row.get('source_files') else set(),
                            bank_names=set(str(row.get('bank_names', '')).split(';')) if row.get('bank_names') else set()
                        )

                        # Parse sample amounts
                        if pd.notna(row.get('sample_amounts')):
                            try:
                                unique_txn.sample_amounts = [float(x) for x in str(row['sample_amounts']).split(';') if x]
                            except:
                                unique_txn.sample_amounts = []

                        unique_transactions[unique_txn.hash_id] = unique_txn
                        total_rows += 1

                load_time = time.time() - start_time
                self.logger.info(f"Loaded {len(unique_transactions)} unique transactions from storage in {load_time:.3f} seconds")

            except Exception as e:
                self.logger.error(f"Error loading unique transactions: {str(e)}")

        return unique_transactions


    
    def save_unique_transactions(self, unique_transactions: Dict[str, UniqueTransaction]) -> bool:
        """
        Save unique transactions to storage with test data protection

        Args:
            unique_transactions: Dictionary of unique transactions

        Returns:
            True if saved successfully
        """
        try:
            # Filter out test data before saving
            filtered_transactions = self._filter_test_data(unique_transactions)

            if len(filtered_transactions) != len(unique_transactions):
                removed_count = len(unique_transactions) - len(filtered_transactions)
                self.logger.warning(f"Filtered out {removed_count} test transactions before saving")

            # Convert to DataFrame
            data = []
            for unique_txn in filtered_transactions.values():
                data.append({
                    'hash_id': unique_txn.hash_id,
                    'description': unique_txn.description,
                    'normalized_description': unique_txn.normalized_description,
                    'frequency': unique_txn.frequency,
                    'first_seen': unique_txn.first_seen,
                    'last_seen': unique_txn.last_seen,
                    'min_amount': unique_txn.amount_range[0],
                    'max_amount': unique_txn.amount_range[1],
                    'sample_amounts': ';'.join(map(str, unique_txn.sample_amounts[:10])),  # Limit to 10 samples
                    'transaction_types': ';'.join(unique_txn.transaction_types),
                    'debit_frequency': unique_txn.debit_frequency,
                    'credit_frequency': unique_txn.credit_frequency,
                    'category': unique_txn.category,
                    'sub_category': unique_txn.sub_category,
                    'confidence': unique_txn.confidence,
                    'is_manually_labeled': unique_txn.is_manually_labeled,
                    'labeled_by': unique_txn.labeled_by,
                    'labeled_at': unique_txn.labeled_at,
                    'source_files': ';'.join(unique_txn.source_files),
                    'bank_names': ';'.join(unique_txn.bank_names)
                })

            df = pd.DataFrame(data)
            df.to_csv(self.unique_transactions_file, index=False)

            # Update cache with filtered data
            self._unique_transactions_cache = filtered_transactions
            self._cache_loaded = True

            self.logger.info(f"Saved {len(filtered_transactions)} unique transactions to storage")
            return True

        except Exception as e:
            self.logger.error(f"Error saving unique transactions: {str(e)}")
            return False

    def _filter_test_data(self, transactions: Dict[str, UniqueTransaction]) -> Dict[str, UniqueTransaction]:
        """Filter out test data and low-quality training imports from transactions before saving"""
        test_patterns = [
            "SALARY DEPOSIT COMPANY ABC",
            "MORTGAGE PAYMENT BANK XYZ",
            "GROCERY STORE PURCHASE",
            "GAS STATION FUEL",
            "GROCERY STORE",
            "GAS STATION",
            "SALARY DEPOSIT",
            "ATM WITHDRAWAL",
            "PAYMENT TO JOHN SMITH",
            "TRANSFER TO MARY JOHNSON",
            "RESTAURANT DINING",
            "ELECTRIC BILL PAYMENT",
            "ONLINE PURCHASE AMAZON",
            "COMPANY ABC",
            "BANK XYZ",
            "JOHN SMITH",
            "MARY JOHNSON"
        ]

        filtered = {}
        for hash_id, txn in transactions.items():
            # Skip if description matches test patterns
            if txn.description in test_patterns:
                self.logger.warning(f"Filtering out test transaction: {txn.description}")
                continue

            # Skip if source files are empty or contain 'nan' (test data indicator)
            if not txn.source_files or any('nan' in str(f) for f in txn.source_files):
                self.logger.warning(f"Filtering out transaction with invalid source: {txn.description}")
                continue

            # Skip if labeled by test systems
            if txn.labeled_by in ['ai_ai', 'test', 'auto_test']:
                self.logger.warning(f"Filtering out transaction labeled by test system: {txn.description}")
                continue

            # Skip low-frequency training imports that might pollute the dataset
            if (txn.frequency == 1 and
                txn.labeled_by in ['auto_sambanova', 'system'] and
                not txn.is_manually_labeled and
                any('training' in str(f).lower() or 'import' in str(f).lower()
                    for f in txn.source_files)):
                self.logger.warning(f"Filtering out low-frequency training import: {txn.description}")
                continue

            filtered[hash_id] = txn

        return filtered
    
    def merge_unique_transactions(self, new_transactions: Dict[str, UniqueTransaction]) -> Dict[str, UniqueTransaction]:
        """
        Merge new unique transactions with existing ones
        
        Args:
            new_transactions: New unique transactions to merge
            
        Returns:
            Merged unique transactions
        """
        existing_transactions = self.load_unique_transactions()
        
        for hash_id, new_txn in new_transactions.items():
            if hash_id in existing_transactions:
                # Merge with existing
                existing_txn = existing_transactions[hash_id]
                existing_txn.frequency += new_txn.frequency
                existing_txn.last_seen = max(existing_txn.last_seen or new_txn.last_seen, new_txn.last_seen or existing_txn.last_seen)

                # Merge transaction types
                existing_txn.transaction_types.update(new_txn.transaction_types)
                existing_txn.debit_frequency += new_txn.debit_frequency
                existing_txn.credit_frequency += new_txn.credit_frequency

                # Update amount range
                existing_txn.amount_range = (
                    min(existing_txn.amount_range[0], new_txn.amount_range[0]),
                    max(existing_txn.amount_range[1], new_txn.amount_range[1])
                )

                # Merge sample amounts (keep most recent 20)
                existing_txn.sample_amounts.extend(new_txn.sample_amounts)
                existing_txn.sample_amounts = existing_txn.sample_amounts[-20:]

                # Merge source tracking
                existing_txn.source_files.update(new_txn.source_files)
                existing_txn.bank_names.update(new_txn.bank_names)
                
                # Keep manual labels if they exist
                if not existing_txn.is_manually_labeled and new_txn.is_manually_labeled:
                    existing_txn.category = new_txn.category
                    existing_txn.sub_category = new_txn.sub_category
                    existing_txn.confidence = new_txn.confidence
                    existing_txn.is_manually_labeled = new_txn.is_manually_labeled
                    existing_txn.labeled_by = new_txn.labeled_by
                    existing_txn.labeled_at = new_txn.labeled_at
            else:
                # Add new transaction
                existing_transactions[hash_id] = new_txn
        
        return existing_transactions
    
    def get_unlabeled_transactions(self) -> List[UniqueTransaction]:
        """Get list of unique transactions that haven't been manually labeled"""
        unique_transactions = self.load_unique_transactions()
        
        unlabeled = [
            txn for txn in unique_transactions.values()
            if not txn.is_manually_labeled
        ]
        
        # Sort by frequency (most common first)
        unlabeled.sort(key=lambda x: x.frequency, reverse=True)
        
        return unlabeled
    
    def get_training_data(self) -> pd.DataFrame:
        """
        Get labeled training data for ML model
        
        Returns:
            DataFrame with columns: description, normalized_description, category, sub_category, confidence
        """
        unique_transactions = self.load_unique_transactions()
        
        training_data = []
        for txn in unique_transactions.values():
            if txn.category and txn.sub_category:
                # Determine primary transaction type
                primary_type = 'mixed'
                if txn.debit_frequency > 0 and txn.credit_frequency == 0:
                    primary_type = 'debit'
                elif txn.credit_frequency > 0 and txn.debit_frequency == 0:
                    primary_type = 'credit'
                elif txn.debit_frequency > txn.credit_frequency:
                    primary_type = 'mostly_debit'
                elif txn.credit_frequency > txn.debit_frequency:
                    primary_type = 'mostly_credit'

                training_data.append({
                    'hash_id': txn.hash_id,
                    'description': txn.description,
                    'normalized_description': txn.normalized_description,
                    'category': txn.category,
                    'sub_category': txn.sub_category,
                    'confidence': txn.confidence,
                    'is_manually_labeled': txn.is_manually_labeled,
                    'frequency': txn.frequency,
                    'min_amount': txn.amount_range[0],
                    'max_amount': txn.amount_range[1],
                    'transaction_type': primary_type,
                    'debit_frequency': txn.debit_frequency,
                    'credit_frequency': txn.credit_frequency,
                    'debit_ratio': txn.debit_frequency / txn.frequency if txn.frequency > 0 else 0,
                    'credit_ratio': txn.credit_frequency / txn.frequency if txn.frequency > 0 else 0
                })
        
        return pd.DataFrame(training_data)

    def fix_transaction_frequencies(self) -> int:
        """
        Fix transaction frequencies for existing data that doesn't have proper debit/credit tracking
        This method infers transaction types from descriptions and updates frequencies

        Returns:
            Number of transactions updated
        """
        unique_transactions = self.load_unique_transactions()
        updated_count = 0

        # Common expense keywords (these should be debit transactions)
        expense_keywords = [
            'zomato', 'swiggy', 'uber', 'ola', 'amazon', 'flipkart', 'myntra', 'ajio',
            'big bazaar', 'dmart', 'reliance', 'grocery', 'supermarket', 'mall',
            'restaurant', 'cafe', 'coffee', 'pizza', 'food', 'dining', 'hotel',
            'petrol', 'diesel', 'fuel', 'gas', 'oil', 'hp', 'indian oil', 'bharat petroleum',
            'pharmacy', 'medical', 'hospital', 'doctor', 'clinic', 'apollo', 'medicine',
            'electricity', 'water', 'gas bill', 'mobile', 'internet', 'broadband',
            'shopping', 'purchase', 'payment', 'debit', 'withdrawal', 'atm',
            'movie', 'cinema', 'entertainment', 'netflix', 'spotify', 'subscription'
        ]

        # Common income keywords (these should be credit transactions)
        income_keywords = [
            'salary', 'wage', 'income', 'credit', 'deposit', 'transfer in',
            'refund', 'cashback', 'reward', 'bonus', 'dividend', 'interest',
            'freelance', 'consulting', 'business', 'rental', 'rent received'
        ]

        for transaction in unique_transactions.values():
            # Skip if already has proper frequency data
            if transaction.debit_frequency > 0 or transaction.credit_frequency > 0:
                continue

            description_lower = transaction.description.lower()

            # Check if it's likely an expense transaction
            is_expense = any(keyword in description_lower for keyword in expense_keywords)

            # Check if it's likely an income transaction
            is_income = any(keyword in description_lower for keyword in income_keywords)

            if is_expense and not is_income:
                # This is likely a debit/expense transaction
                transaction.debit_frequency = transaction.frequency
                transaction.credit_frequency = 0
                transaction.transaction_types = {'debit'}
                updated_count += 1
                self.logger.debug(f"Updated '{transaction.description}' as debit transaction")

            elif is_income and not is_expense:
                # This is likely a credit/income transaction
                transaction.debit_frequency = 0
                transaction.credit_frequency = transaction.frequency
                transaction.transaction_types = {'credit'}
                updated_count += 1
                self.logger.debug(f"Updated '{transaction.description}' as credit transaction")

            else:
                # Default to debit for most transactions (expenses are more common)
                # unless the description clearly suggests income
                if any(word in description_lower for word in ['salary', 'income', 'credit', 'deposit']):
                    transaction.debit_frequency = 0
                    transaction.credit_frequency = transaction.frequency
                    transaction.transaction_types = {'credit'}
                else:
                    transaction.debit_frequency = transaction.frequency
                    transaction.credit_frequency = 0
                    transaction.transaction_types = {'debit'}
                updated_count += 1
                self.logger.debug(f"Updated '{transaction.description}' as default type")

        if updated_count > 0:
            # Save the updated transactions
            self.save_unique_transactions(unique_transactions)
            self.logger.info(f"Fixed transaction frequencies for {updated_count} transactions")

        return updated_count
    
    def update_transaction_label(self, hash_id: str, category: str, sub_category: str, 
                               confidence: float = 1.0, labeled_by: str = "user") -> bool:
        """
        Update the label for a unique transaction
        
        Args:
            hash_id: Hash ID of the transaction
            category: Category label
            sub_category: Sub-category label
            confidence: Confidence score
            labeled_by: Who labeled this transaction
            
        Returns:
            True if updated successfully
        """
        unique_transactions = self.load_unique_transactions()
        
        if hash_id not in unique_transactions:
            self.logger.error(f"Transaction with hash_id {hash_id} not found")
            return False
        
        txn = unique_transactions[hash_id]
        txn.category = category
        txn.sub_category = sub_category
        txn.confidence = confidence
        txn.is_manually_labeled = True
        txn.labeled_by = labeled_by
        txn.labeled_at = datetime.now()
        
        return self.save_unique_transactions(unique_transactions)
