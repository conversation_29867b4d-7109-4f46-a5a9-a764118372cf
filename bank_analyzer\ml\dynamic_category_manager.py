"""
Dynamic category management for SambaNova AI categorization
Automatically creates and manages new categories from AI responses
"""

import json
import re
from typing import Dict, List, Optional, Tuple, Set
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path

from ..core.logger import get_logger
from .category_manager import CategoryManager, Category


@dataclass
class CategoryCreationResult:
    """Result of category creation attempt"""
    success: bool
    category_id: Optional[str]
    category_name: str
    subcategory_name: str
    reason: str
    was_existing: bool = False


@dataclass
class CategoryValidationResult:
    """Result of category validation"""
    is_valid: bool
    normalized_category: str
    normalized_subcategory: str
    suggested_parent: Optional[str]
    confidence: float
    issues: List[str]


class DynamicCategoryManager:
    """
    Manages automatic creation and validation of categories from AI responses
    Ensures new categories follow existing structure and naming conventions
    """
    
    def __init__(self, config_dir: str = "bank_analyzer_config"):
        self.logger = get_logger(__name__)
        self.config_dir = Path(config_dir)
        
        # Initialize category manager
        self.category_manager = CategoryManager()
        
        # Load existing categories and patterns
        self._load_existing_categories()
        
        # Category mapping rules
        self.category_mappings = {
            # Common AI category variations to standard categories
            'food': 'Food & Dining',
            'dining': 'Food & Dining',
            'restaurant': 'Food & Dining',
            'grocery': 'Food & Dining',
            'groceries': 'Food & Dining',
            'shopping': 'Shopping',
            'retail': 'Shopping',
            'store': 'Shopping',
            'transport': 'Transportation',
            'transportation': 'Transportation',
            'travel': 'Transportation',
            'gas': 'Transportation',
            'fuel': 'Transportation',
            'entertainment': 'Entertainment',
            'recreation': 'Entertainment',
            'fun': 'Entertainment',
            'bills': 'Bills & Utilities',
            'utilities': 'Bills & Utilities',
            'utility': 'Bills & Utilities',
            'health': 'Healthcare',
            'healthcare': 'Healthcare',
            'medical': 'Healthcare',
            'education': 'Education',
            'learning': 'Education',
            'business': 'Business',
            'work': 'Business',
            'personal': 'Personal Care',
            'care': 'Personal Care',
            'gifts': 'Gifts & Donations',
            'donation': 'Gifts & Donations',
            'charity': 'Gifts & Donations',
            'other': 'Other',
            'miscellaneous': 'Other',
            'misc': 'Other'
        }
        
        # Subcategory normalization patterns
        self.subcategory_patterns = {
            'atm': 'ATM Withdrawals',
            'withdrawal': 'ATM Withdrawals',
            'cash': 'ATM Withdrawals',
            'deposit': 'Deposits',
            'transfer': 'Transfers',
            'payment': 'Payments',
            'fee': 'Fees',
            'charge': 'Fees',
            'interest': 'Interest',
            'dividend': 'Dividends',
            'salary': 'Salary',
            'wage': 'Salary',
            'bonus': 'Bonus',
            'tip': 'Tips',
            'refund': 'Refunds',
            'return': 'Refunds'
        }
        
        # Track new categories created
        self.new_categories_log = []
        self._load_new_categories_log()
        
        self.logger.info("Dynamic category manager initialized")
    
    def _load_existing_categories(self):
        """Load existing category structure"""
        try:
            self.existing_categories = set()
            self.existing_subcategories = {}  # category -> set of subcategories

            # Load main categories
            main_categories = self.category_manager.get_main_categories()
            for category in main_categories:
                self.existing_categories.add(category.name)
                self.existing_subcategories[category.name] = set()

                # Load subcategories for this main category
                subcategories = self.category_manager.get_subcategories(category.name)
                for subcategory in subcategories:
                    self.existing_subcategories[category.name].add(subcategory.name)

            self.logger.info(f"Loaded {len(self.existing_categories)} main categories")

        except Exception as e:
            self.logger.error(f"Error loading existing categories: {str(e)}")
            self.existing_categories = set()
            self.existing_subcategories = {}
    
    def _load_new_categories_log(self):
        """Load log of newly created categories"""
        try:
            log_file = self.config_dir / "new_categories_log.json"
            if log_file.exists():
                with open(log_file, 'r') as f:
                    self.new_categories_log = json.load(f)
        except Exception as e:
            self.logger.error(f"Error loading new categories log: {str(e)}")
            self.new_categories_log = []
    
    def _save_new_categories_log(self):
        """Save log of newly created categories"""
        try:
            log_file = self.config_dir / "new_categories_log.json"
            with open(log_file, 'w') as f:
                json.dump(self.new_categories_log, f, indent=2)
        except Exception as e:
            self.logger.error(f"Error saving new categories log: {str(e)}")
    
    def validate_and_normalize_category(self, category: str, subcategory: str) -> CategoryValidationResult:
        """
        Validate and normalize category/subcategory from AI response
        
        Args:
            category: Category name from AI
            subcategory: Subcategory name from AI
            
        Returns:
            CategoryValidationResult with validation details
        """
        issues = []
        
        # Normalize category name
        normalized_category = self._normalize_category_name(category)
        normalized_subcategory = self._normalize_subcategory_name(subcategory)
        
        # Check if category exists
        if normalized_category in self.existing_categories:
            # Category exists, check subcategory
            if normalized_category in self.existing_subcategories:
                if normalized_subcategory in self.existing_subcategories[normalized_category]:
                    # Both exist - perfect match
                    return CategoryValidationResult(
                        is_valid=True,
                        normalized_category=normalized_category,
                        normalized_subcategory=normalized_subcategory,
                        suggested_parent=None,
                        confidence=1.0,
                        issues=[]
                    )
                else:
                    # Category exists but subcategory is new
                    return CategoryValidationResult(
                        is_valid=True,
                        normalized_category=normalized_category,
                        normalized_subcategory=normalized_subcategory,
                        suggested_parent=normalized_category,
                        confidence=0.8,
                        issues=[f"New subcategory '{normalized_subcategory}' for existing category '{normalized_category}'"]
                    )
        
        # Category doesn't exist - try to map to existing
        mapped_category = self._find_best_category_match(normalized_category)
        if mapped_category:
            issues.append(f"Mapped '{category}' to existing category '{mapped_category}'")
            return CategoryValidationResult(
                is_valid=True,
                normalized_category=mapped_category,
                normalized_subcategory=normalized_subcategory,
                suggested_parent=mapped_category,
                confidence=0.7,
                issues=issues
            )
        
        # Completely new category
        issues.append(f"New category '{normalized_category}' will be created")
        return CategoryValidationResult(
            is_valid=True,
            normalized_category=normalized_category,
            normalized_subcategory=normalized_subcategory,
            suggested_parent=None,
            confidence=0.6,
            issues=issues
        )
    
    def _normalize_category_name(self, category: str) -> str:
        """Normalize category name to standard format"""
        # Clean and normalize
        normalized = category.strip().lower()
        
        # Check mappings
        if normalized in self.category_mappings:
            return self.category_mappings[normalized]
        
        # Apply title case
        normalized = ' '.join(word.capitalize() for word in normalized.split())
        
        # Handle special cases
        if 'and' in normalized.lower():
            normalized = normalized.replace(' And ', ' & ')
        
        return normalized
    
    def _normalize_subcategory_name(self, subcategory: str) -> str:
        """Normalize subcategory name to standard format"""
        # Clean and normalize
        normalized = subcategory.strip().lower()
        
        # Check patterns
        for pattern, replacement in self.subcategory_patterns.items():
            if pattern in normalized:
                return replacement
        
        # Apply title case
        normalized = ' '.join(word.capitalize() for word in normalized.split())
        
        return normalized
    
    def _find_best_category_match(self, category: str) -> Optional[str]:
        """Find best matching existing category"""
        category_lower = category.lower()
        
        # Direct mapping check
        if category_lower in self.category_mappings:
            return self.category_mappings[category_lower]
        
        # Fuzzy matching
        best_match = None
        best_score = 0.0
        
        for existing_category in self.existing_categories:
            score = self._calculate_similarity(category_lower, existing_category.lower())
            if score > best_score and score > 0.6:  # Minimum similarity threshold
                best_score = score
                best_match = existing_category
        
        return best_match
    
    def _calculate_similarity(self, str1: str, str2: str) -> float:
        """Calculate similarity between two strings"""
        # Simple word overlap similarity
        words1 = set(str1.split())
        words2 = set(str2.split())
        
        if not words1 or not words2:
            return 0.0
        
        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))
        
        return intersection / union if union > 0 else 0.0
    
    def create_category_if_needed(self, category: str, subcategory: str) -> CategoryCreationResult:
        """
        Create category/subcategory if it doesn't exist
        
        Args:
            category: Category name from AI
            subcategory: Subcategory name from AI
            
        Returns:
            CategoryCreationResult with creation details
        """
        # Validate and normalize first
        validation = self.validate_and_normalize_category(category, subcategory)
        
        if not validation.is_valid:
            return CategoryCreationResult(
                success=False,
                category_id=None,
                category_name=validation.normalized_category,
                subcategory_name=validation.normalized_subcategory,
                reason=f"Validation failed: {', '.join(validation.issues)}"
            )
        
        # Check if category already exists
        if (validation.normalized_category in self.existing_categories and 
            validation.normalized_category in self.existing_subcategories and
            validation.normalized_subcategory in self.existing_subcategories[validation.normalized_category]):
            
            return CategoryCreationResult(
                success=True,
                category_id=None,
                category_name=validation.normalized_category,
                subcategory_name=validation.normalized_subcategory,
                reason="Category and subcategory already exist",
                was_existing=True
            )
        
        try:
            # Create main category if needed
            if validation.normalized_category not in self.existing_categories:
                main_category_id = self.category_manager.create_category(
                    name=validation.normalized_category,
                    description=f"Auto-created category from AI response",
                    category_type="expense"  # Default to expense
                )
                
                if main_category_id:
                    self.existing_categories.add(validation.normalized_category)
                    self.existing_subcategories[validation.normalized_category] = set()
                    
                    # Log the creation
                    self._log_category_creation(validation.normalized_category, "", "main_category")
                    
                    self.logger.info(f"Created main category: {validation.normalized_category}")
            
            # Create subcategory
            subcategory_id = self.category_manager.create_category(
                name=validation.normalized_subcategory,
                parent_name=validation.normalized_category,
                description=f"Auto-created subcategory from AI response"
            )
            
            if subcategory_id:
                self.existing_subcategories[validation.normalized_category].add(validation.normalized_subcategory)
                
                # Log the creation
                self._log_category_creation(validation.normalized_category, validation.normalized_subcategory, "subcategory")
                
                self.logger.info(f"Created subcategory: {validation.normalized_category}/{validation.normalized_subcategory}")
                
                return CategoryCreationResult(
                    success=True,
                    category_id=subcategory_id,
                    category_name=validation.normalized_category,
                    subcategory_name=validation.normalized_subcategory,
                    reason="Successfully created new subcategory"
                )
            else:
                return CategoryCreationResult(
                    success=False,
                    category_id=None,
                    category_name=validation.normalized_category,
                    subcategory_name=validation.normalized_subcategory,
                    reason="Failed to create subcategory"
                )
                
        except Exception as e:
            self.logger.error(f"Error creating category: {str(e)}")
            return CategoryCreationResult(
                success=False,
                category_id=None,
                category_name=validation.normalized_category,
                subcategory_name=validation.normalized_subcategory,
                reason=f"Error: {str(e)}"
            )

    def _log_category_creation(self, category: str, subcategory: str, creation_type: str):
        """Log creation of new category"""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "category": category,
            "subcategory": subcategory,
            "type": creation_type,
            "source": "ai_response"
        }

        self.new_categories_log.append(log_entry)
        self._save_new_categories_log()

    def get_new_categories_summary(self) -> Dict[str, any]:
        """Get summary of newly created categories"""
        if not self.new_categories_log:
            return {
                "total_new_categories": 0,
                "main_categories": 0,
                "subcategories": 0,
                "recent_creations": []
            }

        main_categories = sum(1 for entry in self.new_categories_log if entry["type"] == "main_category")
        subcategories = sum(1 for entry in self.new_categories_log if entry["type"] == "subcategory")

        # Get recent creations (last 10)
        recent_creations = self.new_categories_log[-10:]

        return {
            "total_new_categories": len(self.new_categories_log),
            "main_categories": main_categories,
            "subcategories": subcategories,
            "recent_creations": recent_creations,
            "creation_rate": len(self.new_categories_log) / max(1, len(self.existing_categories))
        }

    def validate_ai_response(self, ai_category: str, ai_subcategory: str) -> Tuple[str, str, bool]:
        """
        Validate and potentially create categories from AI response

        Args:
            ai_category: Category from AI response
            ai_subcategory: Subcategory from AI response

        Returns:
            Tuple of (final_category, final_subcategory, was_created)
        """
        # Validate and normalize
        validation = self.validate_and_normalize_category(ai_category, ai_subcategory)

        if not validation.is_valid:
            # Fall back to "Other" category
            self.logger.warning(f"Invalid AI category response: {ai_category}/{ai_subcategory}")
            return "Other", "Miscellaneous", False

        # Try to create if needed
        creation_result = self.create_category_if_needed(
            validation.normalized_category,
            validation.normalized_subcategory
        )

        if creation_result.success:
            return (
                creation_result.category_name,
                creation_result.subcategory_name,
                not creation_result.was_existing
            )
        else:
            # Fall back to "Other" category
            self.logger.error(f"Failed to create category: {creation_result.reason}")
            return "Other", "Miscellaneous", False

    def get_category_statistics(self) -> Dict[str, any]:
        """Get comprehensive category statistics"""
        total_categories = len(self.existing_categories)
        total_subcategories = sum(len(subs) for subs in self.existing_subcategories.values())

        # Calculate category distribution
        category_sizes = {
            cat: len(subs) for cat, subs in self.existing_subcategories.items()
        }

        return {
            "total_main_categories": total_categories,
            "total_subcategories": total_subcategories,
            "average_subcategories_per_category": total_subcategories / max(1, total_categories),
            "category_sizes": category_sizes,
            "largest_category": max(category_sizes.items(), key=lambda x: x[1]) if category_sizes else None,
            "new_categories_summary": self.get_new_categories_summary(),
            "mapping_rules_count": len(self.category_mappings),
            "subcategory_patterns_count": len(self.subcategory_patterns)
        }

    def export_category_mappings(self) -> Dict[str, any]:
        """Export current category mappings for review"""
        return {
            "category_mappings": self.category_mappings,
            "subcategory_patterns": self.subcategory_patterns,
            "existing_categories": list(self.existing_categories),
            "existing_subcategories": {
                cat: list(subs) for cat, subs in self.existing_subcategories.items()
            },
            "new_categories_log": self.new_categories_log,
            "export_timestamp": datetime.now().isoformat()
        }

    def clear_new_categories_log(self):
        """Clear the log of newly created categories"""
        self.new_categories_log = []
        self._save_new_categories_log()
        self.logger.info("New categories log cleared")
