"""
Enhanced Merchant Mapping System for AI-Assisted Transaction Categorization

This module provides intelligent merchant-to-category mapping that learns from AI categorizations
and stores persistent mappings to avoid future API calls for similar transactions.

Key Features:
- Automatic merchant pattern extraction from transaction descriptions
- Persistent storage of AI categorization results as merchant mappings
- Intelligent pattern matching for future transactions
- Cost optimization through merchant-based caching
- Integration with existing confidence booster and training data systems
"""

import json
import re
import hashlib
from typing import Dict, List, Optional, Tuple, Any, Set
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from pathlib import Path
from collections import defaultdict

from ..models.transaction import RawTransaction, ProcessedTransaction
from ..core.logger import get_logger
from ..core.confidence_booster import MerchantMapping


@dataclass
class MerchantPattern:
    """Represents a learned merchant pattern from AI categorizations"""
    pattern: str  # The merchant pattern (e.g., "ZOMATO", "ATM-")
    category: str
    sub_category: str
    confidence: float
    transaction_count: int  # Number of transactions that contributed to this pattern
    last_seen: datetime
    created_at: datetime
    pattern_type: str  # "exact", "prefix", "suffix", "contains", "regex"
    transaction_types: Set[str]  # Set of transaction types (DEBIT, CREDIT)
    amount_ranges: List[Tuple[float, float]]  # List of (min, max) amount ranges
    source: str  # "ai", "manual", "hybrid"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'pattern': self.pattern,
            'category': self.category,
            'sub_category': self.sub_category,
            'confidence': self.confidence,
            'transaction_count': self.transaction_count,
            'last_seen': self.last_seen.isoformat(),
            'created_at': self.created_at.isoformat(),
            'pattern_type': self.pattern_type,
            'transaction_types': list(self.transaction_types),
            'amount_ranges': self.amount_ranges,
            'source': self.source
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MerchantPattern':
        """Create from dictionary"""
        return cls(
            pattern=data['pattern'],
            category=data['category'],
            sub_category=data['sub_category'],
            confidence=data['confidence'],
            transaction_count=data['transaction_count'],
            last_seen=datetime.fromisoformat(data['last_seen']),
            created_at=datetime.fromisoformat(data['created_at']),
            pattern_type=data['pattern_type'],
            transaction_types=set(data['transaction_types']),
            amount_ranges=data['amount_ranges'],
            source=data['source']
        )


@dataclass
class MerchantMatchResult:
    """Result of merchant pattern matching"""
    matched: bool
    pattern: Optional[MerchantPattern]
    confidence: float
    match_type: str  # "exact", "pattern", "fuzzy"
    match_score: float  # 0.0-1.0 similarity score
    reason: str


@dataclass
class MerchantLearningStats:
    """Statistics for merchant learning system"""
    total_patterns: int = 0
    ai_learned_patterns: int = 0
    manual_patterns: int = 0
    hybrid_patterns: int = 0
    total_matches: int = 0
    cache_hits: int = 0
    cache_misses: int = 0
    patterns_created_today: int = 0
    last_updated: Optional[datetime] = None


class EnhancedMerchantMapper:
    """
    Enhanced merchant mapping system that learns from AI categorizations
    and provides intelligent caching to minimize API costs
    """
    
    def __init__(self, data_dir: str = "bank_analyzer_config/merchant_cache"):
        self.logger = get_logger(__name__)
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # File paths
        self.patterns_file = self.data_dir / "merchant_patterns.json"
        self.stats_file = self.data_dir / "merchant_stats.json"
        self.learning_log_file = self.data_dir / "learning_log.json"
        
        # Load existing data
        self.patterns: Dict[str, MerchantPattern] = self._load_patterns()
        self.stats = self._load_stats()
        self.learning_log: List[Dict[str, Any]] = self._load_learning_log()
        
        # Pattern matching configuration
        self.min_confidence_threshold = 0.7
        self.min_transaction_count = 2  # Minimum transactions to trust a pattern
        self.pattern_expiry_days = 90  # Days after which unused patterns expire
        
        # Common merchant patterns for Indian banking
        self.common_patterns = {
            'atm': {'pattern': r'ATM-?\d*', 'type': 'regex'},
            'upi': {'pattern': r'UPI-.*', 'type': 'regex'},
            'neft': {'pattern': r'NEFT.*', 'type': 'regex'},
            'imps': {'pattern': r'IMPS.*', 'type': 'regex'},
            'pos': {'pattern': r'POS.*', 'type': 'regex'},
            'online': {'pattern': r'ONLINE.*', 'type': 'regex'}
        }
        
        # Advanced persistence features
        self.backup_dir = self.data_dir / "backups"
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        self.auto_backup_enabled = True
        self.backup_interval_hours = 24
        self.max_backups = 30  # Keep 30 days of backups

        # Cross-session sharing
        self.shared_patterns_file = self.data_dir / "shared_patterns.json"
        self.session_id = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Performance optimization
        self._pattern_index = {}  # Fast lookup index
        self._rebuild_pattern_index()

        # Auto-backup on startup
        if self.auto_backup_enabled:
            self._create_automatic_backup()

        self.logger.info(f"Enhanced merchant mapper initialized with {len(self.patterns)} patterns")
    
    def find_merchant_match(self, transaction: RawTransaction) -> MerchantMatchResult:
        """
        Find merchant pattern match for a transaction
        
        Args:
            transaction: Raw transaction to match
            
        Returns:
            MerchantMatchResult with match details
        """
        description = transaction.description.upper().strip()
        transaction_type = transaction.transaction_type.upper()
        amount = float(abs(transaction.amount))
        
        # Try exact pattern matches first
        for pattern_key, pattern in self.patterns.items():
            if self._matches_pattern(description, pattern, transaction_type, amount):
                # Update last seen
                pattern.last_seen = datetime.now()
                self._save_patterns()
                
                # Update stats
                self.stats.cache_hits += 1
                self.stats.total_matches += 1
                
                return MerchantMatchResult(
                    matched=True,
                    pattern=pattern,
                    confidence=pattern.confidence,
                    match_type="exact" if pattern.pattern_type == "exact" else "pattern",
                    match_score=1.0,
                    reason=f"Matched pattern: {pattern.pattern}"
                )
        
        # No match found
        self.stats.cache_misses += 1
        return MerchantMatchResult(
            matched=False,
            pattern=None,
            confidence=0.0,
            match_type="none",
            match_score=0.0,
            reason="No merchant pattern matched"
        )
    
    def learn_from_ai_categorization(self, transaction: RawTransaction, 
                                   category: str, sub_category: str, 
                                   confidence: float, source: str = "ai") -> bool:
        """
        Learn merchant pattern from AI categorization result
        
        Args:
            transaction: The transaction that was categorized
            category: AI-determined category
            sub_category: AI-determined sub-category
            confidence: AI confidence score
            source: Source of categorization ("ai", "manual", "hybrid")
            
        Returns:
            True if pattern was learned/updated
        """
        if confidence < self.min_confidence_threshold:
            return False
        
        # Extract merchant pattern from description
        merchant_pattern = self._extract_merchant_pattern(transaction.description)
        if not merchant_pattern:
            return False
        
        pattern_key = self._generate_pattern_key(merchant_pattern, category, sub_category)
        
        # Check if pattern already exists
        if pattern_key in self.patterns:
            # Update existing pattern
            existing = self.patterns[pattern_key]
            existing.transaction_count += 1
            existing.last_seen = datetime.now()
            existing.confidence = (existing.confidence + confidence) / 2  # Average confidence
            existing.transaction_types.add(transaction.transaction_type.upper())
            
            # Update amount range
            amount = float(abs(transaction.amount))
            self._update_amount_range(existing, amount)
            
        else:
            # Create new pattern
            new_pattern = MerchantPattern(
                pattern=merchant_pattern,
                category=category,
                sub_category=sub_category,
                confidence=confidence,
                transaction_count=1,
                last_seen=datetime.now(),
                created_at=datetime.now(),
                pattern_type=self._determine_pattern_type(merchant_pattern),
                transaction_types={transaction.transaction_type.upper()},
                amount_ranges=[(float(abs(transaction.amount)), float(abs(transaction.amount)))],
                source=source
            )
            
            self.patterns[pattern_key] = new_pattern
            
            # Update stats
            if source == "ai":
                self.stats.ai_learned_patterns += 1
            elif source == "manual":
                self.stats.manual_patterns += 1
            else:
                self.stats.hybrid_patterns += 1
            
            self.stats.patterns_created_today += 1
        
        # Log learning event
        self._log_learning_event(transaction, category, sub_category, confidence, source)
        
        # Save data
        self._save_patterns()
        self._save_stats()
        
        return True

    def _matches_pattern(self, description: str, pattern: MerchantPattern,
                        transaction_type: str, amount: float) -> bool:
        """Check if description matches a merchant pattern"""
        # Check transaction type compatibility
        if pattern.transaction_types and transaction_type not in pattern.transaction_types:
            return False

        # Check amount range compatibility
        if pattern.amount_ranges:
            amount_match = any(min_amt <= amount <= max_amt for min_amt, max_amt in pattern.amount_ranges)
            if not amount_match:
                return False

        # Check pattern match based on type
        if pattern.pattern_type == "exact":
            return pattern.pattern in description
        elif pattern.pattern_type == "prefix":
            return description.startswith(pattern.pattern)
        elif pattern.pattern_type == "suffix":
            return description.endswith(pattern.pattern)
        elif pattern.pattern_type == "contains":
            return pattern.pattern in description
        elif pattern.pattern_type == "regex":
            try:
                return bool(re.search(pattern.pattern, description, re.IGNORECASE))
            except re.error:
                return False

        return False

    def _extract_merchant_pattern(self, description: str) -> Optional[str]:
        """Extract merchant pattern from transaction description"""
        description = description.upper().strip()

        # Common patterns for Indian banking
        patterns = [
            # UPI patterns
            r'UPI-([A-Z0-9]+)',
            # ATM patterns
            r'(ATM-?\d*)',
            # POS patterns
            r'POS\s+([A-Z0-9\s]+)',
            # Online patterns
            r'ONLINE\s+([A-Z0-9\s]+)',
            # NEFT/IMPS patterns
            r'(NEFT|IMPS)\s+([A-Z0-9\s]+)',
            # Direct merchant names
            r'^([A-Z]{3,})',  # Words with 3+ characters at start
        ]

        for pattern in patterns:
            match = re.search(pattern, description)
            if match:
                if len(match.groups()) > 1:
                    return match.group(2).strip()[:20]  # Limit length
                else:
                    return match.group(1).strip()[:20]

        # Fallback: use first significant word
        words = description.split()
        for word in words:
            if len(word) >= 3 and word.isalpha():
                return word[:20]

        return None

    def _determine_pattern_type(self, pattern: str) -> str:
        """Determine the type of pattern"""
        if any(char in pattern for char in ['*', '+', '?', '[', ']', '(', ')', '|']):
            return "regex"
        elif pattern.startswith('^'):
            return "prefix"
        elif pattern.endswith('$'):
            return "suffix"
        else:
            return "contains"

    def _generate_pattern_key(self, pattern: str, category: str, sub_category: str) -> str:
        """Generate unique key for pattern"""
        key_string = f"{pattern}|{category}|{sub_category}"
        return hashlib.md5(key_string.encode()).hexdigest()[:16]

    def _update_amount_range(self, pattern: MerchantPattern, amount: float):
        """Update amount range for pattern"""
        if not pattern.amount_ranges:
            pattern.amount_ranges = [(amount, amount)]
            return

        # Find if amount fits in existing range
        for i, (min_amt, max_amt) in enumerate(pattern.amount_ranges):
            if min_amt <= amount <= max_amt:
                return  # Already in range
            elif abs(amount - min_amt) <= max_amt * 0.1 or abs(amount - max_amt) <= max_amt * 0.1:
                # Extend existing range
                pattern.amount_ranges[i] = (min(min_amt, amount), max(max_amt, amount))
                return

        # Add new range if we have less than 3 ranges
        if len(pattern.amount_ranges) < 3:
            pattern.amount_ranges.append((amount, amount))

    def _log_learning_event(self, transaction: RawTransaction, category: str,
                           sub_category: str, confidence: float, source: str):
        """Log learning event for analysis"""
        event = {
            'timestamp': datetime.now().isoformat(),
            'description': transaction.description[:100],  # Truncate for privacy
            'amount': float(abs(transaction.amount)),
            'transaction_type': transaction.transaction_type,
            'category': category,
            'sub_category': sub_category,
            'confidence': confidence,
            'source': source
        }

        self.learning_log.append(event)

        # Keep only last 1000 events
        if len(self.learning_log) > 1000:
            self.learning_log = self.learning_log[-1000:]

        self._save_learning_log()

    def get_categorization_suggestion(self, transaction: RawTransaction) -> Optional[Dict[str, Any]]:
        """Get categorization suggestion based on merchant patterns"""
        match_result = self.find_merchant_match(transaction)

        if match_result.matched and match_result.pattern:
            pattern = match_result.pattern

            # Only suggest if pattern has enough confidence and transaction count
            if (pattern.confidence >= self.min_confidence_threshold and
                pattern.transaction_count >= self.min_transaction_count):

                return {
                    'category': pattern.category,
                    'sub_category': pattern.sub_category,
                    'confidence': pattern.confidence,
                    'source': 'merchant_cache',
                    'pattern': pattern.pattern,
                    'transaction_count': pattern.transaction_count,
                    'match_type': match_result.match_type
                }

        return None

    def cleanup_expired_patterns(self) -> int:
        """Remove expired patterns and return count of removed patterns"""
        cutoff_date = datetime.now() - timedelta(days=self.pattern_expiry_days)
        initial_count = len(self.patterns)

        # Remove expired patterns
        expired_keys = [
            key for key, pattern in self.patterns.items()
            if pattern.last_seen < cutoff_date and pattern.transaction_count < self.min_transaction_count
        ]

        for key in expired_keys:
            del self.patterns[key]

        if expired_keys:
            self._save_patterns()
            self.logger.info(f"Cleaned up {len(expired_keys)} expired merchant patterns")

        return len(expired_keys)

    def get_merchant_statistics(self) -> Dict[str, Any]:
        """Get comprehensive merchant mapping statistics"""
        now = datetime.now()
        today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)

        # Update current stats
        self.stats.total_patterns = len(self.patterns)
        self.stats.last_updated = now

        # Calculate additional stats
        active_patterns = sum(1 for p in self.patterns.values()
                            if p.last_seen > now - timedelta(days=30))

        high_confidence_patterns = sum(1 for p in self.patterns.values()
                                     if p.confidence >= 0.8)

        category_distribution = defaultdict(int)
        for pattern in self.patterns.values():
            category_distribution[pattern.category] += 1

        return {
            'total_patterns': self.stats.total_patterns,
            'active_patterns': active_patterns,
            'high_confidence_patterns': high_confidence_patterns,
            'ai_learned': self.stats.ai_learned_patterns,
            'manual_learned': self.stats.manual_patterns,
            'hybrid_learned': self.stats.hybrid_patterns,
            'total_matches': self.stats.total_matches,
            'cache_hits': self.stats.cache_hits,
            'cache_misses': self.stats.cache_misses,
            'cache_hit_rate': self.stats.cache_hits / max(1, self.stats.cache_hits + self.stats.cache_misses),
            'patterns_created_today': self.stats.patterns_created_today,
            'category_distribution': dict(category_distribution),
            'last_updated': self.stats.last_updated.isoformat() if self.stats.last_updated else None
        }

    def _load_patterns(self) -> Dict[str, MerchantPattern]:
        """Load merchant patterns from file"""
        try:
            if self.patterns_file.exists():
                with open(self.patterns_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                patterns = {}
                for key, pattern_data in data.items():
                    try:
                        patterns[key] = MerchantPattern.from_dict(pattern_data)
                    except Exception as e:
                        self.logger.warning(f"Error loading pattern {key}: {str(e)}")

                self.logger.info(f"Loaded {len(patterns)} merchant patterns")
                return patterns
        except Exception as e:
            self.logger.error(f"Error loading merchant patterns: {str(e)}")

        return {}

    def _save_patterns(self):
        """Save merchant patterns to file"""
        try:
            data = {key: pattern.to_dict() for key, pattern in self.patterns.items()}
            with open(self.patterns_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"Error saving merchant patterns: {str(e)}")

    def _load_stats(self) -> MerchantLearningStats:
        """Load merchant learning statistics"""
        try:
            if self.stats_file.exists():
                with open(self.stats_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                stats = MerchantLearningStats()
                for key, value in data.items():
                    if hasattr(stats, key):
                        if key == 'last_updated' and value:
                            setattr(stats, key, datetime.fromisoformat(value))
                        else:
                            setattr(stats, key, value)

                return stats
        except Exception as e:
            self.logger.error(f"Error loading merchant stats: {str(e)}")

        return MerchantLearningStats()

    def _save_stats(self):
        """Save merchant learning statistics"""
        try:
            data = asdict(self.stats)
            if data['last_updated']:
                data['last_updated'] = data['last_updated'].isoformat()

            with open(self.stats_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"Error saving merchant stats: {str(e)}")

    def _load_learning_log(self) -> List[Dict[str, Any]]:
        """Load learning log"""
        try:
            if self.learning_log_file.exists():
                with open(self.learning_log_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            self.logger.error(f"Error loading learning log: {str(e)}")

        return []

    def _save_learning_log(self):
        """Save learning log"""
        try:
            with open(self.learning_log_file, 'w', encoding='utf-8') as f:
                json.dump(self.learning_log, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"Error saving learning log: {str(e)}")

    def export_patterns(self, file_path: str, format: str = "json") -> bool:
        """Export merchant patterns to file"""
        try:
            export_data = {
                'export_timestamp': datetime.now().isoformat(),
                'total_patterns': len(self.patterns),
                'patterns': {key: pattern.to_dict() for key, pattern in self.patterns.items()},
                'statistics': asdict(self.stats)
            }

            if format.lower() == "json":
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, indent=2, ensure_ascii=False)
            else:
                self.logger.error(f"Unsupported export format: {format}")
                return False

            self.logger.info(f"Exported {len(self.patterns)} patterns to {file_path}")
            return True

        except Exception as e:
            self.logger.error(f"Error exporting patterns: {str(e)}")
            return False

    def import_patterns(self, file_path: str, merge: bool = True) -> Tuple[bool, str, Dict[str, int]]:
        """Import merchant patterns from file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            imported_patterns = data.get('patterns', {})
            stats = {'imported': 0, 'updated': 0, 'skipped': 0}

            for key, pattern_data in imported_patterns.items():
                try:
                    pattern = MerchantPattern.from_dict(pattern_data)

                    if key in self.patterns:
                        if merge:
                            # Update existing pattern
                            existing = self.patterns[key]
                            existing.transaction_count += pattern.transaction_count
                            existing.confidence = max(existing.confidence, pattern.confidence)
                            existing.last_seen = max(existing.last_seen, pattern.last_seen)
                            stats['updated'] += 1
                        else:
                            stats['skipped'] += 1
                    else:
                        self.patterns[key] = pattern
                        stats['imported'] += 1

                except Exception as e:
                    self.logger.warning(f"Error importing pattern {key}: {str(e)}")
                    stats['skipped'] += 1

            if stats['imported'] > 0 or stats['updated'] > 0:
                self._save_patterns()

            message = f"Import completed: {stats['imported']} new, {stats['updated']} updated, {stats['skipped']} skipped"
            self.logger.info(message)
            return True, message, stats

        except Exception as e:
            error_msg = f"Error importing patterns: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg, {}

    def reset_patterns(self, confirm: bool = False) -> bool:
        """Reset all merchant patterns (requires confirmation)"""
        if not confirm:
            return False

        try:
            self.patterns.clear()
            self.stats = MerchantLearningStats()
            self.learning_log.clear()

            self._save_patterns()
            self._save_stats()
            self._save_learning_log()

            self.logger.info("All merchant patterns reset")
            return True

        except Exception as e:
            self.logger.error(f"Error resetting patterns: {str(e)}")
            return False

    def _rebuild_pattern_index(self):
        """Rebuild pattern index for fast lookups"""
        self._pattern_index = {}
        for pattern_key, pattern in self.patterns.items():
            # Index by pattern text for fast matching
            pattern_text = pattern.pattern.upper()
            if pattern_text not in self._pattern_index:
                self._pattern_index[pattern_text] = []
            self._pattern_index[pattern_text].append(pattern_key)

    def _create_automatic_backup(self):
        """Create automatic backup of patterns"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = self.backup_dir / f"patterns_backup_{timestamp}.json"

            backup_data = {
                'timestamp': timestamp,
                'session_id': self.session_id,
                'pattern_count': len(self.patterns),
                'patterns': {key: pattern.to_dict() for key, pattern in self.patterns.items()},
                'statistics': {
                    'total_patterns': self.stats.total_patterns,
                    'ai_learned': self.stats.ai_learned_patterns,
                    'manual_learned': self.stats.manual_patterns,
                    'cache_hits': self.stats.cache_hits,
                    'cache_misses': self.stats.cache_misses
                }
            }

            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, indent=2, ensure_ascii=False)

            # Cleanup old backups
            self._cleanup_old_backups()

            self.logger.info(f"Created automatic backup: {backup_file}")
            return str(backup_file)

        except Exception as e:
            self.logger.error(f"Error creating automatic backup: {str(e)}")
            return None

    def _cleanup_old_backups(self):
        """Remove old backup files beyond the retention limit"""
        try:
            backup_files = list(self.backup_dir.glob("patterns_backup_*.json"))
            backup_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)

            # Remove files beyond the limit
            for old_backup in backup_files[self.max_backups:]:
                old_backup.unlink()
                self.logger.debug(f"Removed old backup: {old_backup}")

        except Exception as e:
            self.logger.error(f"Error cleaning up old backups: {str(e)}")

    def create_manual_backup(self, description: str = "") -> Optional[str]:
        """Create a manual backup with optional description"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = self.backup_dir / f"patterns_manual_{timestamp}.json"

            backup_data = {
                'timestamp': timestamp,
                'session_id': self.session_id,
                'backup_type': 'manual',
                'description': description,
                'pattern_count': len(self.patterns),
                'patterns': {key: pattern.to_dict() for key, pattern in self.patterns.items()},
                'statistics': {
                    'total_patterns': self.stats.total_patterns,
                    'ai_learned': self.stats.ai_learned_patterns,
                    'manual_learned': self.stats.manual_patterns,
                    'cache_hits': self.stats.cache_hits,
                    'cache_misses': self.stats.cache_misses
                }
            }

            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, indent=2, ensure_ascii=False)

            self.logger.info(f"Created manual backup: {backup_file}")
            return str(backup_file)

        except Exception as e:
            self.logger.error(f"Error creating manual backup: {str(e)}")
            return None

    def restore_from_backup(self, backup_file: str, merge: bool = False) -> Tuple[bool, str, Dict[str, int]]:
        """Restore patterns from backup file"""
        try:
            with open(backup_file, 'r', encoding='utf-8') as f:
                backup_data = json.load(f)

            backup_patterns = backup_data.get('patterns', {})
            stats = {'restored': 0, 'updated': 0, 'skipped': 0}

            if not merge:
                # Full restore - replace all patterns
                self.patterns.clear()
                self.stats = MerchantLearningStats()

            for key, pattern_data in backup_patterns.items():
                try:
                    pattern = MerchantPattern.from_dict(pattern_data)

                    if key in self.patterns and merge:
                        # Merge with existing pattern
                        existing = self.patterns[key]
                        existing.transaction_count += pattern.transaction_count
                        existing.confidence = max(existing.confidence, pattern.confidence)
                        existing.last_seen = max(existing.last_seen, pattern.last_seen)
                        stats['updated'] += 1
                    else:
                        self.patterns[key] = pattern
                        stats['restored'] += 1

                except Exception as e:
                    self.logger.warning(f"Error restoring pattern {key}: {str(e)}")
                    stats['skipped'] += 1

            # Rebuild index and save
            self._rebuild_pattern_index()
            self._save_patterns()

            # Restore statistics if full restore
            if not merge and 'statistics' in backup_data:
                backup_stats = backup_data['statistics']
                self.stats.total_patterns = len(self.patterns)
                self.stats.ai_learned_patterns = backup_stats.get('ai_learned', 0)
                self.stats.manual_patterns = backup_stats.get('manual_learned', 0)
                self.stats.cache_hits = backup_stats.get('cache_hits', 0)
                self.stats.cache_misses = backup_stats.get('cache_misses', 0)
                self._save_stats()

            message = f"Restore completed: {stats['restored']} restored, {stats['updated']} updated, {stats['skipped']} skipped"
            self.logger.info(message)
            return True, message, stats

        except Exception as e:
            error_msg = f"Error restoring from backup: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg, {}

    def list_backups(self) -> List[Dict[str, Any]]:
        """List available backup files with metadata"""
        backups = []

        try:
            backup_files = list(self.backup_dir.glob("patterns_*.json"))

            for backup_file in backup_files:
                try:
                    with open(backup_file, 'r', encoding='utf-8') as f:
                        backup_data = json.load(f)

                    backup_info = {
                        'file_path': str(backup_file),
                        'file_name': backup_file.name,
                        'timestamp': backup_data.get('timestamp', 'Unknown'),
                        'backup_type': backup_data.get('backup_type', 'automatic'),
                        'description': backup_data.get('description', ''),
                        'pattern_count': backup_data.get('pattern_count', 0),
                        'file_size': backup_file.stat().st_size,
                        'created_date': datetime.fromtimestamp(backup_file.stat().st_mtime)
                    }

                    backups.append(backup_info)

                except Exception as e:
                    self.logger.warning(f"Error reading backup metadata from {backup_file}: {str(e)}")

            # Sort by creation date (newest first)
            backups.sort(key=lambda x: x['created_date'], reverse=True)

        except Exception as e:
            self.logger.error(f"Error listing backups: {str(e)}")

        return backups

    def share_patterns_to_global(self, pattern_keys: List[str] = None) -> bool:
        """Share patterns to global shared patterns file"""
        try:
            # Load existing shared patterns
            shared_patterns = {}
            if self.shared_patterns_file.exists():
                with open(self.shared_patterns_file, 'r', encoding='utf-8') as f:
                    shared_data = json.load(f)
                    shared_patterns = shared_data.get('patterns', {})

            # Select patterns to share
            patterns_to_share = {}
            if pattern_keys:
                # Share specific patterns
                for key in pattern_keys:
                    if key in self.patterns:
                        patterns_to_share[key] = self.patterns[key]
            else:
                # Share high-confidence patterns with multiple transactions
                for key, pattern in self.patterns.items():
                    if (pattern.confidence >= 0.8 and
                        pattern.transaction_count >= 3 and
                        pattern.source in ['ai', 'hybrid']):
                        patterns_to_share[key] = pattern

            # Merge with existing shared patterns
            shared_count = 0
            for key, pattern in patterns_to_share.items():
                if key not in shared_patterns:
                    shared_patterns[key] = pattern.to_dict()
                    shared_count += 1
                else:
                    # Update if this pattern has higher confidence or more transactions
                    existing = MerchantPattern.from_dict(shared_patterns[key])
                    if (pattern.confidence > existing.confidence or
                        pattern.transaction_count > existing.transaction_count):
                        shared_patterns[key] = pattern.to_dict()
                        shared_count += 1

            # Save shared patterns
            shared_data = {
                'last_updated': datetime.now().isoformat(),
                'updated_by_session': self.session_id,
                'pattern_count': len(shared_patterns),
                'patterns': shared_patterns
            }

            with open(self.shared_patterns_file, 'w', encoding='utf-8') as f:
                json.dump(shared_data, f, indent=2, ensure_ascii=False)

            self.logger.info(f"Shared {shared_count} patterns to global repository")
            return True

        except Exception as e:
            self.logger.error(f"Error sharing patterns to global: {str(e)}")
            return False

    def import_shared_patterns(self, min_confidence: float = 0.7) -> Tuple[bool, str, Dict[str, int]]:
        """Import patterns from global shared patterns file"""
        try:
            if not self.shared_patterns_file.exists():
                return True, "No shared patterns file found", {'imported': 0}

            with open(self.shared_patterns_file, 'r', encoding='utf-8') as f:
                shared_data = json.load(f)

            shared_patterns = shared_data.get('patterns', {})
            stats = {'imported': 0, 'updated': 0, 'skipped': 0}

            for key, pattern_data in shared_patterns.items():
                try:
                    pattern = MerchantPattern.from_dict(pattern_data)

                    # Only import high-quality patterns
                    if pattern.confidence < min_confidence:
                        stats['skipped'] += 1
                        continue

                    if key not in self.patterns:
                        # Import new pattern
                        pattern.source = 'shared'  # Mark as shared
                        self.patterns[key] = pattern
                        stats['imported'] += 1
                    else:
                        # Update if shared pattern is better
                        existing = self.patterns[key]
                        if (pattern.confidence > existing.confidence or
                            pattern.transaction_count > existing.transaction_count):
                            # Merge the patterns
                            existing.confidence = max(existing.confidence, pattern.confidence)
                            existing.transaction_count += pattern.transaction_count
                            existing.last_seen = max(existing.last_seen, pattern.last_seen)
                            stats['updated'] += 1
                        else:
                            stats['skipped'] += 1

                except Exception as e:
                    self.logger.warning(f"Error importing shared pattern {key}: {str(e)}")
                    stats['skipped'] += 1

            if stats['imported'] > 0 or stats['updated'] > 0:
                self._rebuild_pattern_index()
                self._save_patterns()

            message = f"Import completed: {stats['imported']} imported, {stats['updated']} updated, {stats['skipped']} skipped"
            self.logger.info(message)
            return True, message, stats

        except Exception as e:
            error_msg = f"Error importing shared patterns: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg, {}

    def get_pattern_quality_report(self) -> Dict[str, Any]:
        """Get detailed quality report of patterns"""
        report = {
            'total_patterns': len(self.patterns),
            'quality_distribution': {
                'high_quality': 0,      # confidence >= 0.8, transactions >= 3
                'medium_quality': 0,    # confidence >= 0.6, transactions >= 2
                'low_quality': 0,       # below medium quality
                'untested': 0          # only 1 transaction
            },
            'source_distribution': defaultdict(int),
            'pattern_type_distribution': defaultdict(int),
            'age_distribution': {
                'recent': 0,    # last seen within 7 days
                'active': 0,    # last seen within 30 days
                'stale': 0,     # last seen > 30 days ago
                'old': 0        # last seen > 90 days ago
            },
            'recommendations': []
        }

        now = datetime.now()

        for pattern in self.patterns.values():
            # Quality assessment
            if pattern.confidence >= 0.8 and pattern.transaction_count >= 3:
                report['quality_distribution']['high_quality'] += 1
            elif pattern.confidence >= 0.6 and pattern.transaction_count >= 2:
                report['quality_distribution']['medium_quality'] += 1
            elif pattern.transaction_count == 1:
                report['quality_distribution']['untested'] += 1
            else:
                report['quality_distribution']['low_quality'] += 1

            # Source distribution
            report['source_distribution'][pattern.source] += 1

            # Pattern type distribution
            report['pattern_type_distribution'][pattern.pattern_type] += 1

            # Age assessment
            days_since_seen = (now - pattern.last_seen).days
            if days_since_seen <= 7:
                report['age_distribution']['recent'] += 1
            elif days_since_seen <= 30:
                report['age_distribution']['active'] += 1
            elif days_since_seen <= 90:
                report['age_distribution']['stale'] += 1
            else:
                report['age_distribution']['old'] += 1

        # Generate recommendations
        if report['quality_distribution']['low_quality'] > 0:
            report['recommendations'].append(
                f"Consider reviewing {report['quality_distribution']['low_quality']} low-quality patterns"
            )

        if report['age_distribution']['old'] > 0:
            report['recommendations'].append(
                f"Consider cleaning up {report['age_distribution']['old']} old patterns (>90 days)"
            )

        if report['quality_distribution']['untested'] > 10:
            report['recommendations'].append(
                f"{report['quality_distribution']['untested']} patterns need more transactions for validation"
            )

        return report
