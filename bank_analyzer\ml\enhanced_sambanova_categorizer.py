"""
Enhanced SambaNova AI categorizer with intelligent filtering, batch processing,
dynamic category management, and budget protection
"""

import time
from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime

from ..models.transaction import RawTransaction, ProcessedTransaction
from ..core.logger import get_logger
from .sambanova_categorizer import SambaNovaTransactionCategorizer, SambaNovaPrediction
from .transaction_filter import SmartTransactionFilter
from .batch_processor import IntelligentBatchProcessor, BatchConfig
from .dynamic_category_manager import DynamicCategoryManager
from .transaction_prioritizer import TransactionPrioritizer
from .budget_guardian import BudgetGuardian


@dataclass
class EnhancedCategorizationResult:
    """Result of enhanced categorization process"""
    total_transactions: int
    processed_successfully: int
    failed_transactions: int
    skipped_transactions: int
    manual_required: int
    excluded: int
    new_categories_created: int
    total_cost: float
    processing_time: float
    budget_status: str
    recommendations: List[str]
    detailed_results: List[Dict[str, Any]]


@dataclass
class EnhancedConfig:
    """Configuration for enhanced categorizer"""
    # Budget settings
    enable_budget_protection: bool = True
    max_daily_cost: float = 1.0
    safety_margin: float = 0.10
    
    # Filtering settings
    enable_smart_filtering: bool = True
    min_ai_confidence: float = 0.6
    
    # Batch processing settings
    enable_batch_processing: bool = True
    batch_size_min: int = 20
    batch_size_max: int = 30
    
    # Prioritization settings
    enable_prioritization: bool = True
    high_priority_threshold: int = 8
    
    # Category management
    enable_dynamic_categories: bool = True
    auto_create_categories: bool = True


class EnhancedSambaNovaCategorizor:
    """
    Enhanced SambaNova categorizer with intelligent filtering, batch processing,
    dynamic category management, and comprehensive budget protection
    """
    
    def __init__(self, config: EnhancedConfig = None):
        self.logger = get_logger(__name__)
        self.config = config or EnhancedConfig()
        
        # Initialize core components
        self.base_categorizer = SambaNovaTransactionCategorizer()
        
        # Initialize enhancement components
        if self.config.enable_smart_filtering:
            self.transaction_filter = SmartTransactionFilter()
        
        if self.config.enable_batch_processing:
            batch_config = BatchConfig(
                min_batch_size=self.config.batch_size_min,
                max_batch_size=self.config.batch_size_max,
                max_daily_budget_usage=0.90
            )
            self.batch_processor = IntelligentBatchProcessor(batch_config)
        
        if self.config.enable_dynamic_categories:
            self.category_manager = DynamicCategoryManager()
        
        if self.config.enable_prioritization:
            self.prioritizer = TransactionPrioritizer()
        
        if self.config.enable_budget_protection:
            self.budget_guardian = BudgetGuardian()
        
        # Statistics
        self.stats = {
            "total_sessions": 0,
            "total_transactions_processed": 0,
            "total_cost_incurred": 0.0,
            "categories_created": 0,
            "budget_stops": 0,
            "filtering_efficiency": 0.0,
            "average_processing_time": 0.0
        }
        
        self.logger.info("Enhanced SambaNova categorizer initialized")
    
    def categorize_transactions(self, transactions: List[RawTransaction]) -> EnhancedCategorizationResult:
        """
        Categorize transactions using enhanced AI system with all optimizations
        
        Args:
            transactions: List of raw transactions to categorize
            
        Returns:
            EnhancedCategorizationResult with comprehensive results
        """
        start_time = time.time()
        self.stats["total_sessions"] += 1
        
        self.logger.info(f"Starting enhanced categorization of {len(transactions)} transactions")
        
        # Step 1: Budget protection check
        if self.config.enable_budget_protection:
            budget_check = self._check_budget_before_processing(transactions)
            if not budget_check["can_proceed"]:
                return self._create_budget_limited_result(transactions, budget_check)
        
        # Step 2: Smart filtering
        if self.config.enable_smart_filtering:
            filtered_result = self.transaction_filter.filter_batch(transactions)
            ai_suitable = filtered_result['priority_order']
            manual_required = filtered_result['manual_required']
            excluded = filtered_result['excluded']
        else:
            ai_suitable = transactions
            manual_required = []
            excluded = []
        
        self.logger.info(f"Filtering results: {len(ai_suitable)} AI suitable, "
                        f"{len(manual_required)} manual, {len(excluded)} excluded")
        
        # Step 3: Prioritization
        if self.config.enable_prioritization and ai_suitable:
            prioritized = self.prioritizer.prioritize_batch(ai_suitable)
            # Focus on high priority transactions first
            high_priority = [txn for txn, score in prioritized if score.total_score >= self.config.high_priority_threshold]
            other_priority = [txn for txn, score in prioritized if score.total_score < self.config.high_priority_threshold]
            ai_suitable = high_priority + other_priority[:20]  # Limit others
        
        # Step 4: Batch processing with budget monitoring
        if self.config.enable_batch_processing and ai_suitable:
            batch_result = self.batch_processor.process_transactions(ai_suitable)
            processed_results = self._extract_processed_transactions(batch_result)
        else:
            processed_results = self._process_individually(ai_suitable)
        
        # Step 5: Dynamic category management
        if self.config.enable_dynamic_categories:
            processed_results = self._apply_dynamic_category_management(processed_results)
        
        # Step 6: Compile final results
        processing_time = time.time() - start_time
        result = self._compile_final_result(
            transactions, processed_results, manual_required, excluded,
            processing_time
        )
        
        # Update statistics
        self._update_stats(result)
        
        self.logger.info(f"Enhanced categorization completed in {processing_time:.2f}s: "
                        f"{result.processed_successfully} processed, "
                        f"${result.total_cost:.6f} cost")
        
        return result
    
    def _check_budget_before_processing(self, transactions: List[RawTransaction]) -> Dict[str, Any]:
        """Check budget status before processing"""
        recommendation = self.budget_guardian.get_processing_recommendation(transactions)
        
        return {
            "can_proceed": recommendation["approved"],
            "reason": recommendation["reason"],
            "max_affordable": recommendation["recommendations"]["max_affordable_transactions"],
            "budget_status": recommendation["budget_status"]["warning_level"],
            "alternatives": recommendation["alternatives"]
        }
    
    def _create_budget_limited_result(self, transactions: List[RawTransaction], 
                                    budget_check: Dict[str, Any]) -> EnhancedCategorizationResult:
        """Create result when budget limits prevent processing"""
        return EnhancedCategorizationResult(
            total_transactions=len(transactions),
            processed_successfully=0,
            failed_transactions=0,
            skipped_transactions=len(transactions),
            manual_required=len(transactions),
            excluded=0,
            new_categories_created=0,
            total_cost=0.0,
            processing_time=0.0,
            budget_status=budget_check["budget_status"],
            recommendations=[
                f"Budget limit reached: {budget_check['reason']}",
                "Consider manual categorization or wait for daily budget reset",
                f"Maximum affordable transactions: {budget_check['max_affordable']}"
            ],
            detailed_results=[]
        )
    
    def _extract_processed_transactions(self, batch_result) -> List[ProcessedTransaction]:
        """Extract processed transactions from batch result"""
        # This would need to be implemented based on the actual batch processor output
        # For now, return empty list as placeholder
        return []
    
    def _process_individually(self, transactions: List[RawTransaction]) -> List[ProcessedTransaction]:
        """Process transactions individually when batch processing is disabled"""
        processed = []
        
        for transaction in transactions:
            try:
                result = self.base_categorizer.categorize_transaction(transaction)
                if result:
                    processed.append(result)
            except Exception as e:
                self.logger.error(f"Error processing transaction individually: {str(e)}")
        
        return processed
    
    def _apply_dynamic_category_management(self, processed_transactions: List[ProcessedTransaction]) -> List[ProcessedTransaction]:
        """Apply dynamic category management to processed transactions"""
        categories_created = 0
        
        for processed in processed_transactions:
            try:
                # Validate and potentially create categories
                final_category, final_subcategory, was_created = self.category_manager.validate_ai_response(
                    processed.category, processed.sub_category
                )
                
                # Update the processed transaction
                processed.category = final_category
                processed.sub_category = final_subcategory
                
                if was_created:
                    categories_created += 1
                    processed.notes += " [New category created]"
                    
            except Exception as e:
                self.logger.error(f"Error in dynamic category management: {str(e)}")
        
        self.stats["categories_created"] += categories_created
        return processed_transactions
    
    def _compile_final_result(self, original_transactions: List[RawTransaction],
                            processed_results: List[ProcessedTransaction],
                            manual_required: List[RawTransaction],
                            excluded: List[RawTransaction],
                            processing_time: float) -> EnhancedCategorizationResult:
        """Compile final categorization result"""
        
        # Calculate costs and statistics
        total_cost = 0.0
        for processed in processed_results:
            if hasattr(processed, 'notes') and 'SambaNova' in processed.notes:
                # Extract cost from notes
                import re
                cost_match = re.search(r'\$(\d+\.\d+)', processed.notes)
                if cost_match:
                    total_cost += float(cost_match.group(1))
        
        # Get budget status
        budget_status = "safe"
        if self.config.enable_budget_protection:
            status = self.budget_guardian.get_budget_status()
            budget_status = status.warning_level
        
        # Generate recommendations
        recommendations = self._generate_recommendations(
            len(original_transactions), len(processed_results),
            len(manual_required), total_cost, budget_status
        )
        
        # Create detailed results
        detailed_results = []
        for processed in processed_results:
            detailed_results.append({
                "description": processed.description[:50],
                "category": processed.category,
                "subcategory": processed.sub_category,
                "confidence": processed.confidence_score,
                "amount": processed.amount,
                "notes": processed.notes
            })
        
        return EnhancedCategorizationResult(
            total_transactions=len(original_transactions),
            processed_successfully=len(processed_results),
            failed_transactions=len(original_transactions) - len(processed_results) - len(manual_required) - len(excluded),
            skipped_transactions=0,
            manual_required=len(manual_required),
            excluded=len(excluded),
            new_categories_created=self.stats["categories_created"],
            total_cost=total_cost,
            processing_time=processing_time,
            budget_status=budget_status,
            recommendations=recommendations,
            detailed_results=detailed_results
        )

    def _generate_recommendations(self, total: int, processed: int, manual: int,
                                cost: float, budget_status: str) -> List[str]:
        """Generate recommendations based on processing results"""
        recommendations = []

        # Processing efficiency recommendations
        efficiency = (processed / total) * 100 if total > 0 else 0
        if efficiency < 50:
            recommendations.append(f"Low AI processing efficiency ({efficiency:.1f}%). Consider reviewing transaction filtering.")
        elif efficiency > 80:
            recommendations.append(f"High AI processing efficiency ({efficiency:.1f}%). System is working well.")

        # Budget recommendations
        if budget_status == "critical":
            recommendations.append("CRITICAL: Approaching budget limit. Consider manual categorization for remaining transactions.")
        elif budget_status == "warning":
            recommendations.append("WARNING: Budget usage is high. Monitor costs carefully.")
        elif budget_status == "safe":
            recommendations.append("Budget usage is within safe limits.")

        # Manual categorization recommendations
        if manual > 0:
            recommendations.append(f"{manual} transactions require manual categorization due to specific identifiers.")

        # Cost efficiency recommendations
        if processed > 0:
            cost_per_transaction = cost / processed
            if cost_per_transaction > 0.00005:  # $0.00005 per transaction
                recommendations.append(f"High cost per transaction (${cost_per_transaction:.6f}). Consider optimizing batch sizes.")

        return recommendations

    def _update_stats(self, result: EnhancedCategorizationResult):
        """Update processing statistics"""
        self.stats["total_transactions_processed"] += result.processed_successfully
        self.stats["total_cost_incurred"] += result.total_cost

        if result.budget_status in ["critical", "exhausted"]:
            self.stats["budget_stops"] += 1

        # Update filtering efficiency
        if result.total_transactions > 0:
            ai_suitable = result.processed_successfully + result.failed_transactions
            efficiency = (ai_suitable / result.total_transactions) * 100

            # Running average
            sessions = self.stats["total_sessions"]
            current_avg = self.stats["filtering_efficiency"]
            self.stats["filtering_efficiency"] = (current_avg * (sessions - 1) + efficiency) / sessions

        # Update average processing time
        sessions = self.stats["total_sessions"]
        current_avg = self.stats["average_processing_time"]
        self.stats["average_processing_time"] = (current_avg * (sessions - 1) + result.processing_time) / sessions

    def get_comprehensive_stats(self) -> Dict[str, Any]:
        """Get comprehensive statistics from all components"""
        stats = {
            "enhanced_categorizer": self.stats,
            "base_categorizer": self.base_categorizer.get_stats() if hasattr(self.base_categorizer, 'get_stats') else {}
        }

        if self.config.enable_smart_filtering:
            stats["transaction_filter"] = self.transaction_filter.get_stats()

        if self.config.enable_batch_processing:
            stats["batch_processor"] = self.batch_processor.get_processing_stats()

        if self.config.enable_dynamic_categories:
            stats["category_manager"] = self.category_manager.get_category_statistics()

        if self.config.enable_prioritization:
            stats["prioritizer"] = self.prioritizer.get_stats()

        if self.config.enable_budget_protection:
            stats["budget_guardian"] = self.budget_guardian.get_guardian_stats()

        return stats

    def get_system_health(self) -> Dict[str, Any]:
        """Get overall system health status"""
        health = {
            "overall_status": "healthy",
            "components": {},
            "warnings": [],
            "recommendations": []
        }

        # Check budget health
        if self.config.enable_budget_protection:
            budget_status = self.budget_guardian.get_budget_status()
            health["components"]["budget"] = {
                "status": budget_status.warning_level,
                "remaining": budget_status.total_remaining,
                "usage_pct": budget_status.total_usage_pct
            }

            if budget_status.warning_level in ["critical", "exhausted"]:
                health["overall_status"] = "critical"
                health["warnings"].append(f"Budget status: {budget_status.warning_level}")

        # Check processing efficiency
        if self.stats["filtering_efficiency"] < 50:
            health["warnings"].append("Low filtering efficiency")
            health["recommendations"].append("Review transaction filtering patterns")

        # Check cost efficiency
        if self.stats["total_transactions_processed"] > 0:
            avg_cost = self.stats["total_cost_incurred"] / self.stats["total_transactions_processed"]
            if avg_cost > 0.00005:
                health["warnings"].append("High cost per transaction")
                health["recommendations"].append("Optimize batch processing settings")

        # Overall status determination
        if health["warnings"]:
            if health["overall_status"] != "critical":
                health["overall_status"] = "warning"

        return health

    def estimate_processing_cost(self, transactions: List[RawTransaction]) -> Dict[str, Any]:
        """Estimate cost for processing transactions"""
        if self.config.enable_budget_protection:
            return self.budget_guardian.get_processing_recommendation(transactions)
        else:
            # Simple estimation without budget protection
            ai_suitable_count = len(transactions)
            if self.config.enable_smart_filtering:
                filtered = self.transaction_filter.filter_batch(transactions)
                ai_suitable_count = len(filtered['ai_suitable'])

            estimated_cost = ai_suitable_count * 0.000015  # Average cost per transaction

            return {
                "approved": True,
                "reason": "Budget protection disabled",
                "cost_estimate": {
                    "total_cost": estimated_cost,
                    "transaction_count": ai_suitable_count,
                    "confidence": 0.7,
                    "can_afford": True
                },
                "recommendations": {
                    "max_affordable_transactions": ai_suitable_count,
                    "suggested_batch_size": min(ai_suitable_count, 30)
                }
            }

    def reset_all_stats(self):
        """Reset all component statistics"""
        self.stats = {
            "total_sessions": 0,
            "total_transactions_processed": 0,
            "total_cost_incurred": 0.0,
            "categories_created": 0,
            "budget_stops": 0,
            "filtering_efficiency": 0.0,
            "average_processing_time": 0.0
        }

        # Reset component stats
        if self.config.enable_smart_filtering:
            self.transaction_filter.reset_stats()

        if self.config.enable_batch_processing:
            self.batch_processor.reset_stats()

        if self.config.enable_prioritization:
            self.prioritizer.reset_stats()

        if self.config.enable_budget_protection:
            self.budget_guardian.reset_stats()

        self.logger.info("All enhanced categorizer statistics reset")
