"""
Hybrid ML Categorizer that integrates SambaNova AI with existing ML workflow
Enhances rather than replaces the existing ML capabilities
"""

from typing import List, Dict, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime, date
from decimal import Decimal
from enum import Enum

from ..models.transaction import RawTransaction, ProcessedTransaction
from ..core.logger import get_logger
from .integrated_categorizer import IntegratedCategorizer, IntegratedPrediction
from .enhanced_sambanova_categorizer import EnhancedSambaNovaCategorizor, EnhancedConfig
from .ml_categorizer import MLTransactionCategorizer, MLPrediction
from .training_data_manager import TrainingDataManager
from .data_preparation import UniqueTransaction, TransactionDataPreparator
from .model_trainer import ModelTrainer


class OperationMode(Enum):
    """Operation modes for the hybrid categorizer"""
    AI_FIRST = "ai_first"           # Use SambaNova AI first, fall back to local ML/rules
    ML_FIRST = "ml_first"           # Use local ML first, use AI for uncertain cases
    MANUAL_ONLY = "manual_only"     # Disable AI, use only local ML and manual labeling
    HYBRID = "hybrid"               # Intelligently combine all approaches


class DataSource(Enum):
    """Data sources for ML training"""
    MANUAL = "manual"               # Manually labeled (highest quality)
    SAMBANOVA = "sambanova"         # SambaNova AI labeled (medium quality)
    RULES = "rules"                 # Rule-based (baseline quality)


@dataclass
class HybridPrediction:
    """Enhanced prediction with ML integration details"""
    category: str
    sub_category: str
    confidence: float
    source: str
    data_source: DataSource
    should_add_to_training: bool
    ml_confidence: Optional[float] = None
    ai_confidence: Optional[float] = None
    rule_confidence: Optional[float] = None
    uncertainty_score: float = 0.0
    recommendation: str = ""


@dataclass
class HybridConfig:
    """Configuration for hybrid ML categorizer"""
    operation_mode: OperationMode = OperationMode.HYBRID
    
    # Confidence thresholds
    ml_confidence_threshold: float = 0.7
    ai_confidence_threshold: float = 0.8
    uncertainty_threshold: float = 0.5
    
    # Training data settings
    auto_add_ai_labels: bool = True
    auto_add_rule_labels: bool = False
    min_confidence_for_training: float = 0.6
    
    # Budget settings for AI usage
    use_ai_for_uncertain_only: bool = True
    max_ai_transactions_per_session: int = 50
    
    # Data source weights for training
    manual_weight: float = 1.0
    sambanova_weight: float = 0.8
    rules_weight: float = 0.5


class HybridMLCategorizer:
    """
    Hybrid ML categorizer that integrates SambaNova AI with existing ML workflow
    Enhances local ML capabilities while maintaining budget efficiency
    """
    
    def __init__(self, config: HybridConfig = None):
        self.logger = get_logger(__name__)
        self.config = config or HybridConfig()
        
        # Initialize existing ML components
        self.integrated_categorizer = IntegratedCategorizer()
        self.ml_categorizer = MLTransactionCategorizer()
        self.training_manager = TrainingDataManager()
        self.data_preparator = TransactionDataPreparator()
        self.model_trainer = ModelTrainer()
        
        # Initialize enhanced AI categorizer
        ai_config = EnhancedConfig(
            enable_budget_protection=True,
            enable_smart_filtering=True,
            enable_prioritization=True,
            max_daily_cost=0.50 if self.config.use_ai_for_uncertain_only else 1.0
        )
        self.ai_categorizer = EnhancedSambaNovaCategorizor(ai_config)
        
        # Statistics
        self.stats = {
            "total_categorized": 0,
            "ml_used": 0,
            "ai_used": 0,
            "rules_used": 0,
            "hybrid_used": 0,
            "added_to_training": 0,
            "uncertain_cases": 0
        }
        
        self.logger.info(f"Hybrid ML categorizer initialized in {self.config.operation_mode.value} mode")
    
    def categorize_transaction(self, transaction: RawTransaction) -> Tuple[ProcessedTransaction, HybridPrediction]:
        """
        Categorize a single transaction using hybrid approach
        
        Args:
            transaction: Raw transaction to categorize
            
        Returns:
            Tuple of (processed_transaction, hybrid_prediction)
        """
        self.stats["total_categorized"] += 1
        
        if self.config.operation_mode == OperationMode.MANUAL_ONLY:
            return self._categorize_manual_only(transaction)
        elif self.config.operation_mode == OperationMode.AI_FIRST:
            return self._categorize_ai_first(transaction)
        elif self.config.operation_mode == OperationMode.ML_FIRST:
            return self._categorize_ml_first(transaction)
        else:  # HYBRID mode
            return self._categorize_hybrid(transaction)
    
    def _categorize_manual_only(self, transaction: RawTransaction) -> Tuple[ProcessedTransaction, HybridPrediction]:
        """Categorize using only local ML and rules"""
        # Use existing integrated categorizer without AI
        processed = self.integrated_categorizer.categorize_transaction(transaction)

        # Extract source from notes
        source = "rules"  # Default
        if "ml" in processed.notes.lower():
            source = "ml"
        elif "sambanova" in processed.notes.lower():
            source = "sambanova"

        hybrid_pred = HybridPrediction(
            category=processed.category,
            sub_category=processed.sub_category,
            confidence=processed.confidence_score,
            source=source,
            data_source=DataSource.MANUAL if source == "ml" else DataSource.RULES,
            should_add_to_training=False,  # Don't auto-add in manual mode
            ml_confidence=processed.confidence_score if source == "ml" else None,
            rule_confidence=processed.confidence_score if source == "rules" else None,
            recommendation="Manual review recommended"
        )

        self._update_source_stats(source)
        return processed, hybrid_pred
    
    def _categorize_ai_first(self, transaction: RawTransaction) -> Tuple[ProcessedTransaction, HybridPrediction]:
        """Categorize using AI first, fall back to local ML/rules"""
        # Check budget first
        budget_check = self.ai_categorizer.estimate_processing_cost([transaction])
        
        if budget_check['approved']:
            # Use AI categorization
            ai_result = self.ai_categorizer.categorize_transactions([transaction])
            
            if ai_result.processed_successfully > 0 and ai_result.detailed_results:
                detail = ai_result.detailed_results[0]
                
                processed = ProcessedTransaction()
                processed.original_description = transaction.description
                processed.amount = Decimal(str(transaction.amount))
                processed.date = datetime.strptime(transaction.date, "%Y-%m-%d").date() if isinstance(transaction.date, str) else transaction.date
                processed.category = detail['category']
                processed.sub_category = detail['subcategory']
                processed.confidence_score = detail['confidence']
                processed.notes = f"SambaNova AI ({detail['notes']})"
                
                hybrid_pred = HybridPrediction(
                    category=detail['category'],
                    sub_category=detail['subcategory'],
                    confidence=detail['confidence'],
                    source="sambanova",
                    data_source=DataSource.SAMBANOVA,
                    should_add_to_training=self.config.auto_add_ai_labels and detail['confidence'] >= self.config.min_confidence_for_training,
                    ai_confidence=detail['confidence'],
                    recommendation="High-quality AI categorization"
                )
                
                self.stats["ai_used"] += 1
                
                # Add to training data if configured
                if hybrid_pred.should_add_to_training:
                    self._add_to_training_data(transaction, hybrid_pred, DataSource.SAMBANOVA)
                
                return processed, hybrid_pred
        
        # Fall back to local ML/rules
        return self._categorize_manual_only(transaction)
    
    def _categorize_ml_first(self, transaction: RawTransaction) -> Tuple[ProcessedTransaction, HybridPrediction]:
        """Categorize using local ML first, use AI for uncertain cases"""
        # Try local ML first
        processed = self.integrated_categorizer.categorize_transaction(transaction)

        # Extract source and confidence from processed transaction
        source = "rules"  # Default
        if "ml" in processed.notes.lower():
            source = "ml"
        elif "sambanova" in processed.notes.lower():
            source = "sambanova"

        ml_confidence = processed.confidence_score if source == "ml" else 0.0

        if ml_confidence >= self.config.ml_confidence_threshold:
            # ML is confident, use its prediction
            hybrid_pred = HybridPrediction(
                category=processed.category,
                sub_category=processed.sub_category,
                confidence=processed.confidence_score,
                source=source,
                data_source=DataSource.MANUAL if source == "ml" else DataSource.RULES,
                should_add_to_training=False,
                ml_confidence=ml_confidence,
                rule_confidence=processed.confidence_score if source == "rules" else None,
                recommendation="Confident ML prediction"
            )

            self._update_source_stats(source)
            return processed, hybrid_pred

        else:
            # ML is uncertain, try AI if budget allows
            self.stats["uncertain_cases"] += 1

            budget_check = self.ai_categorizer.estimate_processing_cost([transaction])

            if budget_check['approved']:
                # Use AI for uncertain case
                return self._categorize_ai_first(transaction)
            else:
                # Budget exhausted, use ML prediction anyway
                hybrid_pred = HybridPrediction(
                    category=processed.category,
                    sub_category=processed.sub_category,
                    confidence=processed.confidence_score,
                    source=source,
                    data_source=DataSource.MANUAL if source == "ml" else DataSource.RULES,
                    should_add_to_training=False,
                    ml_confidence=ml_confidence,
                    uncertainty_score=1.0 - ml_confidence,
                    recommendation="Low confidence - manual review recommended"
                )

                self._update_source_stats(source)
                return processed, hybrid_pred

    def _categorize_hybrid(self, transaction: RawTransaction) -> Tuple[ProcessedTransaction, HybridPrediction]:
        """Intelligent hybrid categorization combining all approaches"""
        # Get predictions from all available sources
        processed = self.integrated_categorizer.categorize_transaction(transaction)

        # Extract source and confidence from processed transaction
        source = "rules"  # Default
        if "ml" in processed.notes.lower():
            source = "ml"
        elif "sambanova" in processed.notes.lower():
            source = "sambanova"

        ml_confidence = processed.confidence_score if source == "ml" else 0.0
        rule_confidence = processed.confidence_score if source == "rules" else 0.0

        # Calculate uncertainty score
        uncertainty = 1.0 - max(ml_confidence, rule_confidence)

        # Decide whether to use AI based on uncertainty and budget
        should_use_ai = (
            uncertainty > self.config.uncertainty_threshold and
            self.ai_categorizer.estimate_processing_cost([transaction])['approved']
        )

        if should_use_ai:
            # Get AI prediction for comparison
            ai_result = self.ai_categorizer.categorize_transactions([transaction])

            if ai_result.processed_successfully > 0 and ai_result.detailed_results:
                detail = ai_result.detailed_results[0]
                ai_confidence = detail['confidence']

                # Choose best prediction based on confidence
                if ai_confidence > max(ml_confidence, rule_confidence) + 0.1:  # AI is significantly better
                    processed = ProcessedTransaction()
                    processed.original_description = transaction.description
                    processed.amount = Decimal(str(transaction.amount))
                    processed.date = datetime.strptime(transaction.date, "%Y-%m-%d").date() if isinstance(transaction.date, str) else transaction.date
                    processed.category = detail['category']
                    processed.sub_category = detail['subcategory']
                    processed.confidence_score = ai_confidence
                    processed.notes = f"Hybrid (AI): {detail['notes']}"

                    hybrid_pred = HybridPrediction(
                        category=detail['category'],
                        sub_category=detail['subcategory'],
                        confidence=ai_confidence,
                        source="hybrid_ai",
                        data_source=DataSource.SAMBANOVA,
                        should_add_to_training=self.config.auto_add_ai_labels and ai_confidence >= self.config.min_confidence_for_training,
                        ml_confidence=ml_confidence,
                        ai_confidence=ai_confidence,
                        rule_confidence=rule_confidence,
                        uncertainty_score=uncertainty,
                        recommendation="AI provided better confidence than local ML"
                    )

                    self.stats["hybrid_used"] += 1

                    if hybrid_pred.should_add_to_training:
                        self._add_to_training_data(transaction, hybrid_pred, DataSource.SAMBANOVA)

                    return processed, hybrid_pred

        # Use local ML/rules prediction
        processed.notes = f"Hybrid (Local): {processed.notes}"

        hybrid_pred = HybridPrediction(
            category=processed.category,
            sub_category=processed.sub_category,
            confidence=processed.confidence_score,
            source=f"hybrid_{source}",
            data_source=DataSource.MANUAL if source == "ml" else DataSource.RULES,
            should_add_to_training=False,
            ml_confidence=ml_confidence,
            rule_confidence=rule_confidence,
            uncertainty_score=uncertainty,
            recommendation="Local ML/rules used" + (" - consider manual review" if uncertainty > 0.5 else "")
        )

        self.stats["hybrid_used"] += 1
        return processed, hybrid_pred

    def _update_source_stats(self, source: str):
        """Update statistics for source usage"""
        if source == "ml":
            self.stats["ml_used"] += 1
        elif source == "rules":
            self.stats["rules_used"] += 1
        elif source == "sambanova":
            self.stats["ai_used"] += 1
        else:
            self.stats["rules_used"] += 1  # Default fallback



    def _add_to_training_data(self, transaction: RawTransaction, prediction: HybridPrediction, source: DataSource):
        """Add high-quality predictions to training data"""
        try:
            # Create unique transaction for training
            unique_txn = UniqueTransaction(
                description=transaction.description,
                amount=float(transaction.amount),
                category=prediction.category,
                sub_category=prediction.sub_category,
                confidence=prediction.confidence,
                source=source.value,
                labeled_date=datetime.now().isoformat(),
                notes=f"Auto-added from {source.value} with confidence {prediction.confidence:.2f}"
            )

            # Add to training manager
            self.training_manager.add_labeled_transaction(unique_txn)
            self.stats["added_to_training"] += 1

            self.logger.info(f"Added {source.value} prediction to training data: {transaction.description[:50]}")

        except Exception as e:
            self.logger.error(f"Failed to add prediction to training data: {str(e)}")

    def categorize_batch(self, transactions: List[RawTransaction]) -> List[ProcessedTransaction]:
        """Categorize a batch of transactions using hybrid approach"""
        processed_transactions = []

        for transaction in transactions:
            try:
                processed, hybrid_pred = self.categorize_transaction(transaction)
                processed_transactions.append(processed)
            except Exception as e:
                self.logger.error(f"Error categorizing transaction: {str(e)}")
                # Create fallback processed transaction
                fallback = ProcessedTransaction()
                fallback.original_description = transaction.description
                fallback.amount = Decimal(str(transaction.amount))
                fallback.date = datetime.strptime(transaction.date, "%Y-%m-%d").date() if isinstance(transaction.date, str) else transaction.date
                fallback.category = "Other"
                fallback.sub_category = "Uncategorized"
                fallback.confidence_score = 0.0
                fallback.notes = f"Error: {str(e)}"
                processed_transactions.append(fallback)

        return processed_transactions

    def get_ai_suggestions_for_manual_labeling(self, transactions: List[RawTransaction]) -> Dict[str, Dict[str, Any]]:
        """Get AI suggestions for manual labeling interface"""
        suggestions = {}

        # Validate input transactions
        valid_transactions = []
        for txn in transactions:
            if txn and hasattr(txn, 'description') and txn.description and txn.description.strip():
                valid_transactions.append(txn)
            else:
                self.logger.warning(f"Skipping invalid transaction for AI suggestions: {txn}")

        if not valid_transactions:
            self.logger.warning("No valid transactions for AI suggestions")
            return suggestions

        # Check budget for AI suggestions
        budget_check = self.ai_categorizer.estimate_processing_cost(valid_transactions)

        if not budget_check['approved']:
            self.logger.warning("Budget insufficient for AI suggestions")
            return suggestions

        # Get AI predictions for manual review
        ai_result = self.ai_categorizer.categorize_transactions(valid_transactions)

        if not ai_result or not hasattr(ai_result, 'detailed_results'):
            self.logger.warning("No AI results received")
            return suggestions

        for i, detail in enumerate(ai_result.detailed_results):
            if i < len(valid_transactions) and detail:
                txn = valid_transactions[i]
                # Validate AI response
                if (detail.get('category') and detail.get('subcategory') and
                    txn.description and txn.description.strip()):
                    suggestions[txn.description] = {
                        'category': detail['category'],
                        'subcategory': detail['subcategory'],
                        'confidence': detail.get('confidence', 0.5),
                        'source': 'sambanova_ai',
                        'notes': detail.get('notes', ''),
                        'recommendation': 'AI suggestion - please review and confirm'
                    }
                else:
                    self.logger.warning(f"Invalid AI response for transaction: {txn.description[:50]}")

        return suggestions

    def train_model_with_hybrid_data(self, include_sources: List[DataSource] = None) -> Dict[str, Any]:
        """Train local ML model using hybrid data sources"""
        if include_sources is None:
            include_sources = [DataSource.MANUAL, DataSource.SAMBANOVA]

        # Get training data from specified sources
        training_data = []
        source_weights = {}

        for source in include_sources:
            if source == DataSource.MANUAL:
                manual_data = self.training_manager.get_labeled_transactions(source_filter="manual")
                training_data.extend(manual_data)
                source_weights.update({txn.description: self.config.manual_weight for txn in manual_data})

            elif source == DataSource.SAMBANOVA:
                ai_data = self.training_manager.get_labeled_transactions(source_filter="sambanova")
                training_data.extend(ai_data)
                source_weights.update({txn.description: self.config.sambanova_weight for txn in ai_data})

            elif source == DataSource.RULES:
                rule_data = self.training_manager.get_labeled_transactions(source_filter="rules")
                training_data.extend(rule_data)
                source_weights.update({txn.description: self.config.rules_weight for txn in rule_data})

        if not training_data:
            return {"success": False, "error": "No training data available"}

        # Train model with weighted data
        try:
            result = self.model_trainer.train_model(
                training_data=training_data,
                sample_weights=source_weights
            )

            result["data_sources"] = [source.value for source in include_sources]
            result["total_samples"] = len(training_data)
            result["source_breakdown"] = {
                source.value: len([txn for txn in training_data if txn.source == source.value])
                for source in include_sources
            }

            return result

        except Exception as e:
            self.logger.error(f"Model training failed: {str(e)}")
            return {"success": False, "error": str(e)}

    def get_uncertain_transactions(self, min_uncertainty: float = 0.5) -> List[Dict[str, Any]]:
        """Get transactions where local ML has low confidence for AI enhancement"""
        try:
            # Get recent transactions from training data
            all_transactions = self.training_manager.get_unique_transactions()
            uncertain = []

            for txn in all_transactions:
                if not txn.category:  # Unlabeled
                    # Get ML prediction
                    raw_txn = RawTransaction(
                        description=txn.description,
                        amount=txn.amount,
                        date=datetime.now().strftime("%Y-%m-%d")
                    )

                    integrated_pred = self.integrated_categorizer.categorize_transaction(raw_txn)
                    ml_confidence = integrated_pred.ml_prediction.confidence if integrated_pred.ml_prediction else 0.0

                    uncertainty = 1.0 - ml_confidence

                    if uncertainty >= min_uncertainty:
                        uncertain.append({
                            'transaction': txn,
                            'ml_prediction': {
                                'category': integrated_pred.category,
                                'subcategory': integrated_pred.sub_category,
                                'confidence': ml_confidence
                            },
                            'uncertainty_score': uncertainty,
                            'recommendation': 'Consider AI categorization or manual review'
                        })

            return sorted(uncertain, key=lambda x: x['uncertainty_score'], reverse=True)

        except Exception as e:
            self.logger.error(f"Error getting uncertain transactions: {str(e)}")
            return []

    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status including ML integration"""
        base_status = self.integrated_categorizer.get_system_status()

        # Add hybrid-specific status
        hybrid_status = {
            "hybrid_ml_features": {
                "operation_mode": self.config.operation_mode.value,
                "ml_confidence_threshold": self.config.ml_confidence_threshold,
                "ai_confidence_threshold": self.config.ai_confidence_threshold,
                "auto_add_ai_labels": self.config.auto_add_ai_labels,
                "use_ai_for_uncertain_only": self.config.use_ai_for_uncertain_only
            },
            "hybrid_statistics": self.stats,
            "training_data_sources": {
                "manual_weight": self.config.manual_weight,
                "sambanova_weight": self.config.sambanova_weight,
                "rules_weight": self.config.rules_weight
            },
            "ai_integration": {
                "budget_status": self._get_ai_budget_status(),
                "system_health": self._get_ai_system_health()
            }
        }

        # Merge with base status
        base_status.update(hybrid_status)
        return base_status

    def switch_operation_mode(self, new_mode: OperationMode):
        """Switch operation mode dynamically"""
        old_mode = self.config.operation_mode
        self.config.operation_mode = new_mode

        self.logger.info(f"Switched operation mode from {old_mode.value} to {new_mode.value}")

        # Adjust AI budget based on mode
        if new_mode == OperationMode.MANUAL_ONLY:
            # Disable AI usage
            pass
        elif new_mode == OperationMode.AI_FIRST:
            # Increase AI budget allowance
            self.ai_categorizer.config.max_daily_cost = 1.0
        elif new_mode in [OperationMode.ML_FIRST, OperationMode.HYBRID]:
            # Conservative AI usage
            self.ai_categorizer.config.max_daily_cost = 0.5

    def get_training_data_quality_report(self) -> Dict[str, Any]:
        """Generate report on training data quality from different sources"""
        try:
            all_data = self.training_manager.get_labeled_transactions()

            source_breakdown = {}
            confidence_stats = {}

            for txn in all_data:
                source = getattr(txn, 'source', 'unknown')
                confidence = getattr(txn, 'confidence', 0.0)

                if source not in source_breakdown:
                    source_breakdown[source] = 0
                    confidence_stats[source] = []

                source_breakdown[source] += 1
                if confidence > 0:
                    confidence_stats[source].append(confidence)

            # Calculate average confidence by source
            avg_confidence = {}
            for source, confidences in confidence_stats.items():
                if confidences:
                    avg_confidence[source] = sum(confidences) / len(confidences)
                else:
                    avg_confidence[source] = 0.0

            return {
                "total_labeled_transactions": len(all_data),
                "source_breakdown": source_breakdown,
                "average_confidence_by_source": avg_confidence,
                "data_quality_score": sum(avg_confidence.values()) / len(avg_confidence) if avg_confidence else 0.0,
                "recommendations": self._generate_data_quality_recommendations(source_breakdown, avg_confidence)
            }

        except Exception as e:
            self.logger.error(f"Error generating training data quality report: {str(e)}")
            return {"error": str(e)}

    def _generate_data_quality_recommendations(self, source_breakdown: Dict[str, int],
                                             avg_confidence: Dict[str, float]) -> List[str]:
        """Generate recommendations for improving training data quality"""
        recommendations = []

        total_samples = sum(source_breakdown.values())
        manual_ratio = source_breakdown.get('manual', 0) / max(1, total_samples)
        ai_ratio = source_breakdown.get('sambanova', 0) / max(1, total_samples)

        if manual_ratio < 0.3:
            recommendations.append("Consider increasing manual labeling for higher quality training data")

        if ai_ratio > 0.7:
            recommendations.append("High reliance on AI labels - consider manual review of AI predictions")

        if avg_confidence.get('sambanova', 0) < 0.7:
            recommendations.append("AI confidence is low - consider using AI only for high-confidence predictions")

        if total_samples < 100:
            recommendations.append("Training dataset is small - consider collecting more labeled data")

        return recommendations

    def reset_stats(self):
        """Reset hybrid categorizer statistics"""
        self.stats = {
            "total_categorized": 0,
            "ml_used": 0,
            "ai_used": 0,
            "rules_used": 0,
            "hybrid_used": 0,
            "added_to_training": 0,
            "uncertain_cases": 0
        }
        self.logger.info("Hybrid categorizer statistics reset")

    def _get_ai_budget_status(self):
        """Safely get AI budget status"""
        try:
            if hasattr(self.ai_categorizer, 'get_budget_status'):
                return self.ai_categorizer.get_budget_status()
            elif hasattr(self.ai_categorizer, 'budget_guardian'):
                return self.ai_categorizer.budget_guardian.get_budget_status().__dict__
            else:
                return {
                    "total_remaining": 5.0,
                    "total_usage_pct": 0.0,
                    "warning_level": "safe",
                    "status": "Budget status not available"
                }
        except Exception as e:
            self.logger.debug(f"Error getting AI budget status: {str(e)}")
            return {
                "total_remaining": 5.0,
                "total_usage_pct": 0.0,
                "warning_level": "unknown",
                "error": str(e)
            }

    def _get_ai_system_health(self):
        """Safely get AI system health"""
        try:
            if hasattr(self.ai_categorizer, 'get_system_health'):
                return self.ai_categorizer.get_system_health()
            else:
                return {
                    "status": "operational",
                    "features_enabled": 5,
                    "last_check": "unknown",
                    "note": "System health not available"
                }
        except Exception as e:
            self.logger.debug(f"Error getting AI system health: {str(e)}")
            return {
                "status": "unknown",
                "error": str(e)
            }
