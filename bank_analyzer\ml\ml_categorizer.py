"""
Machine Learning-based transaction categorizer
Uses TF-IDF + SVM for text classification with confidence scoring
"""

import pandas as pd
import numpy as np
import joblib
import re
from pathlib import Path
from typing import List, Dict, Tuple, Optional, Any
from datetime import datetime
from dataclasses import dataclass
import logging

try:
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.svm import SVC
    from sklearn.pipeline import Pipeline
    from sklearn.model_selection import train_test_split, cross_val_score
    from sklearn.metrics import classification_report, accuracy_score, confusion_matrix
    from sklearn.preprocessing import LabelEncoder
    import nltk
    from nltk.corpus import stopwords
    from nltk.tokenize import word_tokenize
    from nltk.stem import PorterStemmer
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False

from ..models.transaction import RawTransaction, ProcessedTransaction
from ..core.logger import get_logger
from .data_preparation import TransactionDataPreparator


@dataclass
class MLPrediction:
    """ML prediction result"""
    category: str
    sub_category: str
    confidence: float
    model_version: str
    predicted_at: datetime
    feature_importance: Dict[str, float] = None


@dataclass
class ModelMetrics:
    """Model performance metrics"""
    accuracy: float
    precision: float
    recall: float
    f1_score: float
    training_samples: int
    model_version: str
    trained_at: datetime


class TextPreprocessor:
    """Text preprocessing for ML features"""
    
    def __init__(self):
        self.stemmer = PorterStemmer()
        self.stop_words = set()
        
        # Initialize NLTK data if available
        if SKLEARN_AVAILABLE:
            try:
                # Try to download NLTK data with timeout and error handling
                import socket
                socket.setdefaulttimeout(10)  # 10 second timeout

                nltk.download('stopwords', quiet=True)
                nltk.download('punkt', quiet=True)
                self.stop_words = set(stopwords.words('english'))

            except Exception as e:
                # If NLTK download fails, use a basic English stopwords list
                print(f"Warning: NLTK download failed ({e}), using basic stopwords list")
                self.stop_words = {
                    'a', 'an', 'and', 'are', 'as', 'at', 'be', 'by', 'for', 'from',
                    'has', 'he', 'in', 'is', 'it', 'its', 'of', 'on', 'that', 'the',
                    'to', 'was', 'will', 'with', 'i', 'me', 'my', 'we', 'our', 'you',
                    'your', 'this', 'these', 'they', 'them', 'their', 'have', 'had'
                }
    
    def preprocess_text(self, text: str) -> str:
        """
        Preprocess text for ML features
        
        Args:
            text: Raw text
            
        Returns:
            Preprocessed text
        """
        if not text:
            return ""
        
        # Convert to lowercase
        text = text.lower()
        
        # Remove special characters but keep spaces
        text = re.sub(r'[^a-zA-Z\s]', ' ', text)
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text).strip()
        
        if SKLEARN_AVAILABLE and self.stop_words:
            try:
                # Try NLTK tokenization first
                tokens = word_tokenize(text)
                tokens = [self.stemmer.stem(token) for token in tokens if token not in self.stop_words and len(token) > 2]
                text = ' '.join(tokens)
            except:
                # Fallback to simple tokenization if NLTK fails
                try:
                    tokens = text.split()
                    tokens = [self.stemmer.stem(token) for token in tokens if token not in self.stop_words and len(token) > 2]
                    text = ' '.join(tokens)
                except:
                    # If even stemming fails, just remove stopwords
                    tokens = [token for token in text.split() if token not in self.stop_words and len(token) > 2]
                    text = ' '.join(tokens)
        
        return text


class MLTransactionCategorizer:
    """
    Machine Learning-based transaction categorizer
    Uses TF-IDF vectorization with SVM classifier
    """
    
    def __init__(self, model_dir: str = "bank_analyzer_config/ml_models"):
        self.logger = get_logger(__name__)
        self.model_dir = Path(model_dir)
        self.model_dir.mkdir(parents=True, exist_ok=True)
        
        # Model files
        self.category_model_file = self.model_dir / "category_model.joblib"
        self.subcategory_model_file = self.model_dir / "subcategory_model.joblib"
        self.label_encoders_file = self.model_dir / "label_encoders.joblib"
        self.metrics_file = self.model_dir / "model_metrics.json"
        
        # Model components
        self.category_model: Optional[Pipeline] = None
        self.subcategory_models: Dict[str, Pipeline] = {}
        self.category_encoder: Optional[LabelEncoder] = None
        self.subcategory_encoders: Dict[str, LabelEncoder] = {}
        
        # Text preprocessor
        self.text_preprocessor = TextPreprocessor()
        
        # Data preparator
        self.data_preparator = TransactionDataPreparator()
        
        # Model metadata
        self.model_version = "1.0"
        self.is_trained = False
        
        # Check if sklearn is available
        if not SKLEARN_AVAILABLE:
            self.logger.warning("scikit-learn not available. ML categorization will be disabled.")
        
        # Load existing models
        self._load_models()
    
    def _create_tfidf_pipeline(self, max_features: int = 5000) -> Pipeline:
        """Create TF-IDF + SVM pipeline"""
        if not SKLEARN_AVAILABLE:
            raise ImportError("scikit-learn is required for ML categorization")
        
        return Pipeline([
            ('tfidf', TfidfVectorizer(
                max_features=max_features,
                ngram_range=(1, 2),  # Use unigrams and bigrams
                min_df=2,  # Ignore terms that appear in less than 2 documents
                max_df=0.8,  # Ignore terms that appear in more than 80% of documents
                stop_words='english'
            )),
            ('svm', SVC(
                kernel='linear',
                probability=True,  # Enable probability estimates
                random_state=42
            ))
        ])
    
    def prepare_training_data(self) -> Tuple[pd.DataFrame, bool]:
        """
        Prepare training data from labeled transactions

        Returns:
            Tuple of (training_data_df, has_sufficient_data)
        """
        training_data = self.data_preparator.get_training_data()

        if training_data.empty:
            self.logger.warning("No labeled training data available")
            return training_data, False

        # Check if we have sufficient data for training
        min_samples_per_category = 3
        category_counts = training_data['category'].value_counts()
        insufficient_categories = category_counts[category_counts < min_samples_per_category]

        if len(insufficient_categories) > 0:
            self.logger.warning(f"Insufficient training data for categories: {insufficient_categories.index.tolist()}")

        # Keep categories with sufficient data
        sufficient_categories = category_counts[category_counts >= min_samples_per_category].index
        training_data = training_data[training_data['category'].isin(sufficient_categories)]

        has_sufficient_data = len(training_data) >= 10 and len(sufficient_categories) >= 2

        self.logger.info(f"Training data: {len(training_data)} samples, {len(sufficient_categories)} categories")
        self.logger.info(f"Category distribution: {training_data['category'].value_counts().to_dict()}")
        return training_data, has_sufficient_data

    def train_models(self, force_retrain: bool = False) -> Dict[str, Any]:
        """
        Train ML models on labeled data
        
        Args:
            force_retrain: Force retraining even if models exist
            
        Returns:
            Training results and metrics
        """
        if not SKLEARN_AVAILABLE:
            return {"error": "scikit-learn not available"}
        
        result = {
            "success": False,
            "category_model_trained": False,
            "subcategory_models_trained": 0,
            "metrics": {},
            "errors": []
        }
        
        try:
            # Prepare training data
            training_data, has_sufficient_data = self.prepare_training_data()
            
            if not has_sufficient_data:
                result["errors"].append("Insufficient training data")
                return result
            
            # Preprocess text features
            training_data['processed_text'] = training_data['normalized_description'].apply(
                self.text_preprocessor.preprocess_text
            )
            
            X = training_data['processed_text'].values
            y_category = training_data['category'].values
            
            # Train category model
            self.logger.info("Training category classification model...")
            
            # Encode category labels
            self.category_encoder = LabelEncoder()
            y_category_encoded = self.category_encoder.fit_transform(y_category)
            
            # Create and train category model
            self.category_model = self._create_tfidf_pipeline()
            
            # Handle small datasets with proper cross-validation
            if len(X) < 20:  # Small dataset
                # Use cross-validation for small datasets
                from sklearn.model_selection import cross_val_score
                import warnings

                # Suppress sklearn warnings for small datasets
                with warnings.catch_warnings():
                    warnings.filterwarnings("ignore", category=UserWarning)
                    warnings.filterwarnings("ignore", message=".*ill-defined.*")
                    warnings.filterwarnings("ignore", message=".*least populated class.*")

                    # Train on full dataset for small data
                    self.category_model.fit(X, y_category_encoded)

                    # Use cross-validation for evaluation (with minimum 2 folds)
                    cv_folds = min(3, len(np.unique(y_category_encoded)))
                    if cv_folds >= 2:
                        cv_scores = cross_val_score(self.category_model, X, y_category_encoded,
                                                  cv=cv_folds, scoring='accuracy')
                        category_accuracy = np.mean(cv_scores)
                    else:
                        # Fallback for very small datasets
                        category_accuracy = 1.0  # Perfect score for single class
            else:
                # Standard train-test split for larger datasets
                X_train, X_test, y_train, y_test = train_test_split(
                    X, y_category_encoded, test_size=0.2, random_state=42, stratify=y_category_encoded
                )

                # Train category model
                self.category_model.fit(X_train, y_train)

                # Evaluate category model
                y_pred = self.category_model.predict(X_test)
                category_accuracy = accuracy_score(y_test, y_pred)
            
            result["category_model_trained"] = True
            result["metrics"]["category_accuracy"] = category_accuracy
            
            self.logger.info(f"Category model trained with accuracy: {category_accuracy:.3f}")
            
            # Train subcategory models for each category
            self.subcategory_models = {}
            self.subcategory_encoders = {}
            
            for category in training_data['category'].unique():
                category_data = training_data[training_data['category'] == category]
                
                if len(category_data) < 3:  # Need at least 3 samples
                    continue
                
                subcategory_counts = category_data['sub_category'].value_counts()
                if len(subcategory_counts) < 2:  # Need at least 2 different subcategories
                    continue
                
                try:
                    X_sub = category_data['processed_text'].values
                    y_sub = category_data['sub_category'].values

                    # Encode subcategory labels
                    sub_encoder = LabelEncoder()
                    y_sub_encoded = sub_encoder.fit_transform(y_sub)

                    # Create and train subcategory model with warning suppression
                    sub_model = self._create_tfidf_pipeline(max_features=2000)

                    # Suppress sklearn warnings for small subcategory datasets
                    import warnings
                    with warnings.catch_warnings():
                        warnings.filterwarnings("ignore", category=UserWarning)
                        warnings.filterwarnings("ignore", message=".*ill-defined.*")
                        warnings.filterwarnings("ignore", message=".*least populated class.*")

                        sub_model.fit(X_sub, y_sub_encoded)

                    self.subcategory_models[category] = sub_model
                    self.subcategory_encoders[category] = sub_encoder

                    result["subcategory_models_trained"] += 1

                    self.logger.info(f"Trained subcategory model for {category}")

                except Exception as e:
                    self.logger.error(f"Error training subcategory model for {category}: {str(e)}")
            
            # Save models
            self._save_models()
            
            # Update metadata
            self.is_trained = True
            self.model_version = f"1.0_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            result["success"] = True
            result["model_version"] = self.model_version
            
            self.logger.info("ML models training completed successfully")
            
        except Exception as e:
            self.logger.error(f"Error training ML models: {str(e)}")
            result["errors"].append(str(e))
        
        return result
    
    def predict_category(self, description: str) -> Optional[MLPrediction]:
        """
        Predict category for a transaction description

        Args:
            description: Transaction description

        Returns:
            ML prediction or None if prediction fails
        """
        if not SKLEARN_AVAILABLE or not self.is_trained or not self.category_model:
            self.logger.debug(f"Model not available: SKLEARN_AVAILABLE={SKLEARN_AVAILABLE}, is_trained={self.is_trained}, category_model={self.category_model is not None}")
            return None

        try:
            # Preprocess text
            processed_text = self.text_preprocessor.preprocess_text(description)

            if not processed_text:
                self.logger.debug(f"No processed text for: {description}")
                return None

            # Predict category
            category_proba = self.category_model.predict_proba([processed_text])[0]
            category_pred = self.category_model.predict([processed_text])[0]

            # Debug: Log all probabilities
            if hasattr(self.category_encoder, 'classes_'):
                class_probabilities = dict(zip(self.category_encoder.classes_, category_proba))
                self.logger.debug(f"Prediction probabilities for '{description[:50]}...': {class_probabilities}")

            # Decode category
            category = self.category_encoder.inverse_transform([category_pred])[0]
            category_confidence = float(np.max(category_proba))

            self.logger.debug(f"Predicted category: {category} (confidence: {category_confidence:.3f}) for '{description[:50]}...'")

            # Predict subcategory if model exists for this category
            sub_category = "Miscellaneous"
            subcategory_confidence = 0.5

            if category in self.subcategory_models:
                try:
                    sub_model = self.subcategory_models[category]
                    sub_encoder = self.subcategory_encoders[category]

                    sub_proba = sub_model.predict_proba([processed_text])[0]
                    sub_pred = sub_model.predict([processed_text])[0]

                    sub_category = sub_encoder.inverse_transform([sub_pred])[0]
                    subcategory_confidence = float(np.max(sub_proba))

                    self.logger.debug(f"Predicted subcategory: {sub_category} (confidence: {subcategory_confidence:.3f})")
                except Exception as sub_e:
                    self.logger.debug(f"Subcategory prediction failed: {str(sub_e)}")

            # Combined confidence (weighted average)
            combined_confidence = (category_confidence * 0.7) + (subcategory_confidence * 0.3)

            return MLPrediction(
                category=category,
                sub_category=sub_category,
                confidence=combined_confidence,
                model_version=self.model_version,
                predicted_at=datetime.now()
            )

        except Exception as e:
            self.logger.error(f"Error predicting category for '{description[:50]}...': {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            return None
    
    def _save_models(self) -> bool:
        """Save trained models to disk"""
        try:
            if self.category_model:
                joblib.dump(self.category_model, self.category_model_file)
            
            if self.subcategory_models:
                joblib.dump(self.subcategory_models, self.subcategory_model_file)
            
            # Save label encoders
            encoders = {
                'category_encoder': self.category_encoder,
                'subcategory_encoders': self.subcategory_encoders,
                'model_version': self.model_version,
                'is_trained': self.is_trained
            }
            joblib.dump(encoders, self.label_encoders_file)
            
            self.logger.info("ML models saved successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Error saving models: {str(e)}")
            return False
    
    def _load_models(self) -> bool:
        """Load trained models from disk"""
        try:
            if not SKLEARN_AVAILABLE:
                return False
            
            # Load category model
            if self.category_model_file.exists():
                self.category_model = joblib.load(self.category_model_file)
            
            # Load subcategory models
            if self.subcategory_model_file.exists():
                self.subcategory_models = joblib.load(self.subcategory_model_file)
            
            # Load label encoders
            if self.label_encoders_file.exists():
                encoders = joblib.load(self.label_encoders_file)
                self.category_encoder = encoders.get('category_encoder')
                self.subcategory_encoders = encoders.get('subcategory_encoders', {})
                self.model_version = encoders.get('model_version', '1.0')
                # Fix: If we have both category model and encoder, mark as trained
                stored_is_trained = encoders.get('is_trained', False)
                self.is_trained = stored_is_trained

            # Check if we have all required components for a trained model
            if self.category_model and self.category_encoder:
                # If we have both model and encoder but is_trained is False, fix it
                if not self.is_trained:
                    self.logger.warning("Model files exist but is_trained=False. Fixing this...")
                    self.is_trained = True
                    # Update the encoders file to fix this permanently
                    try:
                        encoders = joblib.load(self.label_encoders_file)
                        encoders['is_trained'] = True
                        joblib.dump(encoders, self.label_encoders_file)
                        self.logger.info("Updated is_trained flag in encoders file")
                    except Exception as e:
                        self.logger.warning(f"Could not update encoders file: {e}")

                self.logger.info(f"ML models loaded successfully (version: {self.model_version})")
                return True
            
        except Exception as e:
            self.logger.error(f"Error loading models: {str(e)}")
        
        return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the current models"""
        return {
            "is_trained": self.is_trained,
            "model_version": self.model_version,
            "sklearn_available": SKLEARN_AVAILABLE,
            "has_category_model": self.category_model is not None,
            "subcategory_models_count": len(self.subcategory_models),
            "subcategory_categories": list(self.subcategory_models.keys())
        }

    def evaluate_model(self) -> Dict[str, Any]:
        """
        Evaluate model performance on current training data

        Returns:
            Evaluation metrics
        """
        if not SKLEARN_AVAILABLE or not self.is_trained:
            return {"error": "Model not available or not trained"}

        try:
            training_data, has_sufficient_data = self.prepare_training_data()

            if not has_sufficient_data:
                return {"error": "Insufficient training data for evaluation"}

            # Preprocess text features
            training_data['processed_text'] = training_data['normalized_description'].apply(
                self.text_preprocessor.preprocess_text
            )

            X = training_data['processed_text'].values
            y_category = training_data['category'].values

            # Encode labels
            y_category_encoded = self.category_encoder.transform(y_category)

            # Handle small datasets with proper cross-validation
            import warnings
            with warnings.catch_warnings():
                warnings.filterwarnings("ignore", category=UserWarning)
                warnings.filterwarnings("ignore", message=".*ill-defined.*")
                warnings.filterwarnings("ignore", message=".*least populated class.*")

                # Adjust CV folds for small datasets
                n_samples = len(X)
                n_classes = len(np.unique(y_category_encoded))
                cv_folds = min(5, n_classes, n_samples // 2)
                cv_folds = max(2, cv_folds)  # Minimum 2 folds

                # Cross-validation evaluation
                if cv_folds >= 2 and n_samples >= cv_folds:
                    cv_scores = cross_val_score(self.category_model, X, y_category_encoded,
                                              cv=cv_folds, scoring='accuracy')
                    cv_mean = cv_scores.mean()
                    cv_std = cv_scores.std()
                else:
                    # Fallback for very small datasets
                    cv_mean = 1.0
                    cv_std = 0.0

                # Detailed evaluation on full dataset
                y_pred = self.category_model.predict(X)
                accuracy = accuracy_score(y_category_encoded, y_pred)

                # Get classification report with zero_division handling
                target_names = self.category_encoder.classes_
                report = classification_report(y_category_encoded, y_pred, target_names=target_names,
                                             output_dict=True, zero_division=0)

            return {
                "accuracy": accuracy,
                "cv_mean_accuracy": cv_mean,
                "cv_std_accuracy": cv_std,
                "classification_report": report,
                "training_samples": len(training_data),
                "categories_count": len(target_names),
                "cv_folds_used": cv_folds,
                "evaluation_date": datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Error evaluating model: {str(e)}")
            return {"error": str(e)}

    def predict_batch(self, descriptions: List[str]) -> List[Optional[MLPrediction]]:
        """
        Predict categories for a batch of descriptions

        Args:
            descriptions: List of transaction descriptions

        Returns:
            List of predictions (None for failed predictions)
        """
        predictions = []

        for description in descriptions:
            prediction = self.predict_category(description)
            predictions.append(prediction)

        return predictions

    def retrain_incremental(self) -> Dict[str, Any]:
        """
        Perform incremental retraining with new labeled data

        Returns:
            Retraining results
        """
        if not SKLEARN_AVAILABLE:
            return {"error": "scikit-learn not available"}

        # For now, we'll do full retraining since scikit-learn doesn't have
        # good incremental learning for text classification with TF-IDF
        # In a production system, you might want to use online learning algorithms

        self.logger.info("Performing full model retraining with updated data...")
        return self.train_models(force_retrain=True)
