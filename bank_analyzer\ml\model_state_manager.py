"""
Model State Manager
Handles ML model training status, locking, and version control
"""

import json
import shutil
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from dataclasses import dataclass, asdict
from enum import Enum

from ..core.logger import get_logger


class ModelStatus(Enum):
    """Model training status"""
    UNTRAINED = "untrained"
    TRAINING = "training"
    TRAINED = "trained"
    LOCKED = "locked"
    ERROR = "error"


@dataclass
class ModelVersion:
    """Represents a model version"""
    version_id: str
    created_at: datetime
    training_data_hash: str
    performance_metrics: Dict[str, float]
    training_stats: Dict[str, Any]
    model_files: List[str]
    is_active: bool = False
    notes: str = ""


@dataclass
class ModelState:
    """Current model state"""
    status: ModelStatus
    current_version: Optional[str]
    last_trained: Optional[datetime]
    is_locked: bool
    lock_reason: str
    training_data_hash: str
    performance_summary: Dict[str, float]
    versions: List[ModelVersion]


class ModelStateManager:
    """
    Manages ML model state, versions, and training locks
    """
    
    def __init__(self, ml_data_dir: str = "bank_analyzer_config/ml_data"):
        self.logger = get_logger(__name__)
        self.ml_data_dir = Path(ml_data_dir)
        self.ml_models_dir = Path(ml_data_dir).parent / "ml_models"
        self.ml_data_dir.mkdir(parents=True, exist_ok=True)
        self.ml_models_dir.mkdir(parents=True, exist_ok=True)
        
        # File paths
        self.model_state_file = self.ml_data_dir / "model_state.json"
        self.model_versions_file = self.ml_data_dir / "model_versions.json"
        self.model_lock_file = self.ml_data_dir / "model_lock.json"
        
        # Load current state
        self.model_state = self._load_model_state()
        self.model_versions = self._load_model_versions()
    
    def _load_model_state(self) -> ModelState:
        """Load current model state"""
        if not self.model_state_file.exists():
            return ModelState(
                status=ModelStatus.UNTRAINED,
                current_version=None,
                last_trained=None,
                is_locked=False,
                lock_reason="",
                training_data_hash="",
                performance_summary={},
                versions=[]
            )
        
        try:
            with open(self.model_state_file, 'r') as f:
                data = json.load(f)
            
            return ModelState(
                status=ModelStatus(data.get('status', 'untrained')),
                current_version=data.get('current_version'),
                last_trained=datetime.fromisoformat(data['last_trained']) if data.get('last_trained') else None,
                is_locked=data.get('is_locked', False),
                lock_reason=data.get('lock_reason', ''),
                training_data_hash=data.get('training_data_hash', ''),
                performance_summary=data.get('performance_summary', {}),
                versions=[]  # Loaded separately
            )
            
        except Exception as e:
            self.logger.error(f"Error loading model state: {str(e)}")
            return ModelState(
                status=ModelStatus.UNTRAINED,
                current_version=None,
                last_trained=None,
                is_locked=False,
                lock_reason="",
                training_data_hash="",
                performance_summary={},
                versions=[]
            )
    
    def _load_model_versions(self) -> List[ModelVersion]:
        """Load model versions"""
        if not self.model_versions_file.exists():
            return []
        
        try:
            with open(self.model_versions_file, 'r') as f:
                data = json.load(f)
            
            versions = []
            for version_data in data:
                versions.append(ModelVersion(
                    version_id=version_data['version_id'],
                    created_at=datetime.fromisoformat(version_data['created_at']),
                    training_data_hash=version_data['training_data_hash'],
                    performance_metrics=version_data['performance_metrics'],
                    training_stats=version_data['training_stats'],
                    model_files=version_data['model_files'],
                    is_active=version_data.get('is_active', False),
                    notes=version_data.get('notes', '')
                ))
            
            return versions
            
        except Exception as e:
            self.logger.error(f"Error loading model versions: {str(e)}")
            return []
    
    def _save_model_state(self):
        """Save current model state"""
        try:
            data = {
                'status': self.model_state.status.value,
                'current_version': self.model_state.current_version,
                'last_trained': self.model_state.last_trained.isoformat() if self.model_state.last_trained else None,
                'is_locked': self.model_state.is_locked,
                'lock_reason': self.model_state.lock_reason,
                'training_data_hash': self.model_state.training_data_hash,
                'performance_summary': self.model_state.performance_summary
            }
            
            with open(self.model_state_file, 'w') as f:
                json.dump(data, f, indent=2)
                
        except Exception as e:
            self.logger.error(f"Error saving model state: {str(e)}")
    
    def _save_model_versions(self):
        """Save model versions"""
        try:
            data = []
            for version in self.model_versions:
                version_data = asdict(version)
                version_data['created_at'] = version.created_at.isoformat()
                data.append(version_data)
            
            with open(self.model_versions_file, 'w') as f:
                json.dump(data, f, indent=2)
                
        except Exception as e:
            self.logger.error(f"Error saving model versions: {str(e)}")
    
    def get_model_status(self) -> ModelState:
        """Get current model status"""
        # Update versions in state
        self.model_state.versions = self.model_versions
        return self.model_state
    
    def is_model_locked(self) -> bool:
        """Check if model is locked"""
        return self.model_state.is_locked
    
    def can_modify_training_data(self) -> Tuple[bool, str]:
        """Check if training data can be modified"""
        if self.model_state.is_locked:
            return False, f"Model is locked: {self.model_state.lock_reason}"
        
        if self.model_state.status == ModelStatus.TRAINING:
            return False, "Model is currently training"
        
        return True, "Training data can be modified"
    
    def lock_model(self, reason: str = "Model training completed") -> bool:
        """Lock the model to prevent data changes"""
        try:
            if self.model_state.status not in [ModelStatus.TRAINED, ModelStatus.LOCKED]:
                return False
            
            self.model_state.is_locked = True
            self.model_state.lock_reason = reason
            self.model_state.status = ModelStatus.LOCKED
            
            # Create lock file with timestamp
            lock_info = {
                "locked_at": datetime.now().isoformat(),
                "reason": reason,
                "version": self.model_state.current_version
            }
            
            with open(self.model_lock_file, 'w') as f:
                json.dump(lock_info, f, indent=2)
            
            self._save_model_state()
            self.logger.info(f"Model locked: {reason}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error locking model: {str(e)}")
            return False
    
    def unlock_model(self, reason: str = "Unlocked for retraining") -> bool:
        """Unlock the model to allow data changes"""
        try:
            self.model_state.is_locked = False
            self.model_state.lock_reason = ""
            
            if self.model_state.status == ModelStatus.LOCKED:
                self.model_state.status = ModelStatus.TRAINED
            
            # Remove lock file
            if self.model_lock_file.exists():
                self.model_lock_file.unlink()
            
            self._save_model_state()
            self.logger.info(f"Model unlocked: {reason}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error unlocking model: {str(e)}")
            return False
    
    def start_training(self, training_data_hash: str) -> bool:
        """Mark model as training"""
        try:
            can_train, reason = self.can_modify_training_data()
            if not can_train:
                self.logger.warning(f"Cannot start training: {reason}")
                return False
            
            self.model_state.status = ModelStatus.TRAINING
            self.model_state.training_data_hash = training_data_hash
            self._save_model_state()
            
            self.logger.info("Model training started")
            return True
            
        except Exception as e:
            self.logger.error(f"Error starting training: {str(e)}")
            return False

    def complete_training(self, performance_metrics: Dict[str, float],
                         training_stats: Dict[str, Any],
                         model_files: List[str],
                         auto_lock: bool = True) -> str:
        """
        Complete model training and create new version

        Args:
            performance_metrics: Model performance metrics
            training_stats: Training statistics
            model_files: List of model files created
            auto_lock: Whether to automatically lock the model

        Returns:
            Version ID of the new model
        """
        try:
            # Create new version
            version_id = f"v{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            # Backup existing model files if they exist
            self._backup_model_files(version_id)

            # Create new version
            new_version = ModelVersion(
                version_id=version_id,
                created_at=datetime.now(),
                training_data_hash=self.model_state.training_data_hash,
                performance_metrics=performance_metrics,
                training_stats=training_stats,
                model_files=model_files,
                is_active=True,
                notes=f"Training completed at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            )

            # Deactivate previous versions
            for version in self.model_versions:
                version.is_active = False

            # Add new version
            self.model_versions.append(new_version)

            # Update model state
            self.model_state.status = ModelStatus.TRAINED
            self.model_state.current_version = version_id
            self.model_state.last_trained = datetime.now()
            self.model_state.performance_summary = performance_metrics

            # Auto-lock if requested
            if auto_lock:
                self.model_state.is_locked = True
                self.model_state.lock_reason = "Automatically locked after training"
                self.model_state.status = ModelStatus.LOCKED

            # Save state
            self._save_model_state()
            self._save_model_versions()

            self.logger.info(f"Training completed. New version: {version_id}")
            return version_id

        except Exception as e:
            self.logger.error(f"Error completing training: {str(e)}")
            self.model_state.status = ModelStatus.ERROR
            self._save_model_state()
            return ""

    def fail_training(self, error_message: str):
        """Mark training as failed"""
        try:
            self.model_state.status = ModelStatus.ERROR
            self.model_state.lock_reason = f"Training failed: {error_message}"
            self._save_model_state()

            self.logger.error(f"Training failed: {error_message}")

        except Exception as e:
            self.logger.error(f"Error marking training as failed: {str(e)}")

    def _backup_model_files(self, version_id: str):
        """Backup existing model files"""
        try:
            backup_dir = self.ml_models_dir / f"backup_{version_id}"
            backup_dir.mkdir(exist_ok=True)

            # Backup existing model files
            for model_file in self.ml_models_dir.glob("*.joblib"):
                if model_file.is_file():
                    backup_file = backup_dir / model_file.name
                    shutil.copy2(model_file, backup_file)
                    self.logger.debug(f"Backed up {model_file.name} to {backup_file}")

        except Exception as e:
            self.logger.error(f"Error backing up model files: {str(e)}")

    def rollback_to_version(self, version_id: str) -> bool:
        """
        Rollback to a previous model version

        Args:
            version_id: Version to rollback to

        Returns:
            Success status
        """
        try:
            # Find the version
            target_version = None
            for version in self.model_versions:
                if version.version_id == version_id:
                    target_version = version
                    break

            if not target_version:
                self.logger.error(f"Version {version_id} not found")
                return False

            # Check if model is locked
            can_modify, reason = self.can_modify_training_data()
            if not can_modify:
                self.logger.warning(f"Cannot rollback: {reason}")
                return False

            # Restore model files
            backup_dir = self.ml_models_dir / f"backup_{version_id}"
            if backup_dir.exists():
                # Remove current model files
                for model_file in self.ml_models_dir.glob("*.joblib"):
                    if model_file.is_file():
                        model_file.unlink()

                # Restore backup files
                for backup_file in backup_dir.glob("*.joblib"):
                    target_file = self.ml_models_dir / backup_file.name
                    shutil.copy2(backup_file, target_file)
                    self.logger.debug(f"Restored {backup_file.name}")

            # Update version status
            for version in self.model_versions:
                version.is_active = (version.version_id == version_id)

            # Update model state
            self.model_state.current_version = version_id
            self.model_state.last_trained = target_version.created_at
            self.model_state.performance_summary = target_version.performance_metrics
            self.model_state.training_data_hash = target_version.training_data_hash
            self.model_state.status = ModelStatus.TRAINED

            # Save state
            self._save_model_state()
            self._save_model_versions()

            self.logger.info(f"Rolled back to version {version_id}")
            return True

        except Exception as e:
            self.logger.error(f"Error rolling back to version {version_id}: {str(e)}")
            return False
