"""
SambaNova AI API integration module for enhanced ML categorization
Provides classification and cost-efficient API usage with extensive caching
"""

import json
import time
import hashlib
import pickle
from typing import List, Dict, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
from pathlib import Path
import logging
import requests

from ..core.logger import get_logger


@dataclass
class SambaNovaConfig:
    """Configuration for SambaNova AI API integration"""
    api_key: str
    base_url: str = "https://api.sambanova.ai/v1"
    model_name: str = "Meta-Llama-3.1-8B-Instruct"  # Most cost-effective model
    max_retries: int = 3
    retry_delay: float = 2.0
    timeout: int = 30
    enabled: bool = True
    fallback_on_error: bool = True
    max_tokens: int = 150  # Keep low for cost efficiency
    temperature: float = 0.1
    
    # Cost management
    max_daily_cost: float = 1.0  # $1 per day limit
    cost_tracking_enabled: bool = True
    cache_enabled: bool = True
    cache_ttl_hours: int = 24
    
    # Pricing (per million tokens)
    input_cost_per_million: float = 0.10
    output_cost_per_million: float = 0.20


@dataclass
class SambaNovaClassificationResult:
    """Result from SambaNova classification"""
    category: str
    sub_category: str
    confidence: float
    raw_response: Dict[str, Any]
    processing_time: float
    model_used: str
    tokens_used: int
    estimated_cost: float
    from_cache: bool = False


@dataclass
class CostTracker:
    """Track API usage costs"""
    daily_cost: float = 0.0
    total_cost: float = 0.0
    daily_requests: int = 0
    total_requests: int = 0
    daily_tokens: int = 0
    total_tokens: int = 0
    last_reset: datetime = None


class SambaNovaClient:
    """
    SambaNova AI API client for transaction categorization
    Focuses on cost efficiency with extensive caching and usage monitoring
    """
    
    def __init__(self, config: SambaNovaConfig, cache_dir: str = "bank_analyzer_config/sambanova_cache"):
        self.logger = get_logger(__name__)
        self.config = config
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # Cost tracking
        self.cost_tracker_file = self.cache_dir / "cost_tracker.json"
        self.cost_tracker = self._load_cost_tracker()
        
        # Cache files
        self.classification_cache_file = self.cache_dir / "classification_cache.pkl"
        self.classification_cache = self._load_classification_cache()
        
        # Session for connection pooling
        self.session = requests.Session()
        self.session.headers.update({
            "Authorization": f"Bearer {config.api_key}",
            "Content-Type": "application/json"
        })
        
        self.logger.info(f"SambaNova client initialized (cache: {len(self.classification_cache)} entries)")
        self._check_daily_reset()
    
    def is_available(self) -> bool:
        """Check if SambaNova client is available and within budget"""
        if not self.config.enabled:
            return False
        
        if not self.config.api_key:
            return False
        
        # Check daily cost limit
        if self.config.cost_tracking_enabled and self.cost_tracker.daily_cost >= self.config.max_daily_cost:
            self.logger.warning(f"Daily cost limit reached: ${self.cost_tracker.daily_cost:.4f}")
            return False
        
        return True
    
    def _check_daily_reset(self):
        """Reset daily counters if it's a new day"""
        now = datetime.now()
        if (self.cost_tracker.last_reset is None or 
            now.date() > self.cost_tracker.last_reset.date()):
            
            self.cost_tracker.daily_cost = 0.0
            self.cost_tracker.daily_requests = 0
            self.cost_tracker.daily_tokens = 0
            self.cost_tracker.last_reset = now
            self._save_cost_tracker()
            self.logger.info("Daily cost tracking reset")
    
    def _load_cost_tracker(self) -> CostTracker:
        """Load cost tracking data"""
        try:
            if self.cost_tracker_file.exists():
                with open(self.cost_tracker_file, 'r') as f:
                    data = json.load(f)
                
                tracker = CostTracker()
                tracker.daily_cost = data.get('daily_cost', 0.0)
                tracker.total_cost = data.get('total_cost', 0.0)
                tracker.daily_requests = data.get('daily_requests', 0)
                tracker.total_requests = data.get('total_requests', 0)
                tracker.daily_tokens = data.get('daily_tokens', 0)
                tracker.total_tokens = data.get('total_tokens', 0)
                
                if data.get('last_reset'):
                    tracker.last_reset = datetime.fromisoformat(data['last_reset'])
                
                return tracker
        except Exception as e:
            self.logger.error(f"Error loading cost tracker: {str(e)}")
        
        return CostTracker()
    
    def _save_cost_tracker(self):
        """Save cost tracking data"""
        try:
            data = {
                'daily_cost': self.cost_tracker.daily_cost,
                'total_cost': self.cost_tracker.total_cost,
                'daily_requests': self.cost_tracker.daily_requests,
                'total_requests': self.cost_tracker.total_requests,
                'daily_tokens': self.cost_tracker.daily_tokens,
                'total_tokens': self.cost_tracker.total_tokens,
                'last_reset': self.cost_tracker.last_reset.isoformat() if self.cost_tracker.last_reset else None
            }
            
            with open(self.cost_tracker_file, 'w') as f:
                json.dump(data, f, indent=2)
                
        except Exception as e:
            self.logger.error(f"Error saving cost tracker: {str(e)}")
    
    def _load_classification_cache(self) -> Dict[str, Dict]:
        """Load classification cache"""
        try:
            if self.classification_cache_file.exists():
                with open(self.classification_cache_file, 'rb') as f:
                    cache = pickle.load(f)
                
                # Clean expired entries
                now = datetime.now()
                ttl = timedelta(hours=self.config.cache_ttl_hours)
                
                cleaned_cache = {}
                for key, entry in cache.items():
                    if 'timestamp' in entry:
                        entry_time = datetime.fromisoformat(entry['timestamp'])
                        if now - entry_time < ttl:
                            cleaned_cache[key] = entry
                
                return cleaned_cache
        except Exception as e:
            self.logger.error(f"Error loading classification cache: {str(e)}")
        
        return {}
    
    def _save_classification_cache(self):
        """Save classification cache"""
        try:
            with open(self.classification_cache_file, 'wb') as f:
                pickle.dump(self.classification_cache, f)
        except Exception as e:
            self.logger.error(f"Error saving classification cache: {str(e)}")
    
    def _get_cache_key(self, description: str, examples: List[Dict] = None) -> str:
        """Generate cache key for a classification request"""
        # Include examples in cache key for few-shot learning
        cache_data = {
            'description': description.lower().strip(),
            'model': self.config.model_name,
            'max_tokens': self.config.max_tokens,
            'temperature': self.config.temperature
        }
        
        if examples:
            cache_data['examples'] = [
                f"{ex.get('description', '')}->{ex.get('category', '')}/{ex.get('sub_category', '')}"
                for ex in examples[:3]  # Only use first 3 examples for cache key
            ]
        
        cache_str = json.dumps(cache_data, sort_keys=True)
        return hashlib.md5(cache_str.encode()).hexdigest()
    
    def _estimate_cost(self, input_tokens: int, output_tokens: int) -> float:
        """Estimate cost for token usage"""
        input_cost = (input_tokens / 1_000_000) * self.config.input_cost_per_million
        output_cost = (output_tokens / 1_000_000) * self.config.output_cost_per_million
        return input_cost + output_cost
    
    def _count_tokens(self, text: str) -> int:
        """Rough token count estimation (4 chars per token average)"""
        return max(1, len(text) // 4)
    
    def _update_cost_tracking(self, tokens_used: int, estimated_cost: float):
        """Update cost tracking metrics"""
        self.cost_tracker.daily_cost += estimated_cost
        self.cost_tracker.total_cost += estimated_cost
        self.cost_tracker.daily_requests += 1
        self.cost_tracker.total_requests += 1
        self.cost_tracker.daily_tokens += tokens_used
        self.cost_tracker.total_tokens += tokens_used
        
        self._save_cost_tracker()
    
    def classify_transaction(self, description: str, examples: List[Dict[str, str]] = None) -> Optional[SambaNovaClassificationResult]:
        """
        Classify a transaction using SambaNova AI with caching and cost optimization
        
        Args:
            description: Transaction description to classify
            examples: Few-shot examples for classification
            
        Returns:
            Classification result or None if failed
        """
        if not self.is_available():
            return None
        
        # Check cache first
        cache_key = self._get_cache_key(description, examples)
        if self.config.cache_enabled and cache_key in self.classification_cache:
            cached_result = self.classification_cache[cache_key]
            self.logger.debug(f"Cache hit for classification: {description[:50]}...")
            
            return SambaNovaClassificationResult(
                category=cached_result['category'],
                sub_category=cached_result['sub_category'],
                confidence=cached_result['confidence'],
                raw_response=cached_result.get('raw_response', {}),
                processing_time=0.0,
                model_used=self.config.model_name,
                tokens_used=cached_result.get('tokens_used', 0),
                estimated_cost=0.0,  # No cost for cached results
                from_cache=True
            )
        
        start_time = time.time()
        
        try:
            # Build prompt for classification
            prompt = self._build_classification_prompt(description, examples)
            
            # Estimate input tokens
            input_tokens = self._count_tokens(prompt)
            
            # Make API request
            response = self._make_api_request(prompt)
            
            if response:
                processing_time = time.time() - start_time
                
                # Parse response
                result = self._parse_classification_response(response, processing_time, input_tokens)
                
                # Cache the result
                if self.config.cache_enabled and result:
                    self.classification_cache[cache_key] = {
                        'category': result.category,
                        'sub_category': result.sub_category,
                        'confidence': result.confidence,
                        'raw_response': result.raw_response,
                        'tokens_used': result.tokens_used,
                        'timestamp': datetime.now().isoformat()
                    }
                    self._save_classification_cache()
                
                return result
            
        except Exception as e:
            self.logger.error(f"SambaNova classification failed: {str(e)}")
        
        return None

    def _build_classification_prompt(self, description: str, examples: List[Dict[str, str]] = None) -> str:
        """Build a prompt for transaction classification"""
        prompt = "Classify this bank transaction into a category and subcategory.\n\n"

        # Add few-shot examples if provided
        if examples:
            prompt += "Examples:\n"
            for example in examples[:3]:  # Limit to 3 examples for cost efficiency
                prompt += f"Transaction: {example['description']}\n"
                prompt += f"Category: {example['category']}\n"
                prompt += f"Subcategory: {example['sub_category']}\n\n"

        # Add the transaction to classify
        prompt += f"Transaction: {description}\n"
        prompt += "Category:"

        return prompt

    def _make_api_request(self, prompt: str) -> Optional[Dict]:
        """Make API request to SambaNova"""
        payload = {
            "model": self.config.model_name,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "max_tokens": self.config.max_tokens,
            "temperature": self.config.temperature,
            "stream": False
        }

        for attempt in range(self.config.max_retries):
            try:
                response = self.session.post(
                    f"{self.config.base_url}/chat/completions",
                    json=payload,
                    timeout=self.config.timeout
                )

                if response.status_code == 200:
                    return response.json()
                elif response.status_code == 429:
                    # Rate limited - wait longer
                    wait_time = self.config.retry_delay * (2 ** attempt)
                    self.logger.warning(f"Rate limited, waiting {wait_time}s")
                    time.sleep(wait_time)
                else:
                    self.logger.error(f"API request failed: {response.status_code} - {response.text}")

            except Exception as e:
                self.logger.error(f"API request error (attempt {attempt + 1}): {str(e)}")
                if attempt < self.config.max_retries - 1:
                    time.sleep(self.config.retry_delay * (2 ** attempt))

        return None

    def _parse_classification_response(self, response: Dict, processing_time: float, input_tokens: int) -> Optional[SambaNovaClassificationResult]:
        """Parse SambaNova classification response"""
        try:
            if 'choices' not in response or not response['choices']:
                return None

            content = response['choices'][0]['message']['content'].strip()

            # Extract usage information
            usage = response.get('usage', {})
            total_tokens = usage.get('total_tokens', input_tokens + self._count_tokens(content))

            # Estimate cost
            output_tokens = total_tokens - input_tokens
            estimated_cost = self._estimate_cost(input_tokens, output_tokens)

            # Update cost tracking
            self._update_cost_tracking(total_tokens, estimated_cost)

            # Parse category and subcategory from response
            lines = content.split('\n')
            category = "Other"
            sub_category = "Miscellaneous"
            confidence = 0.6  # Default confidence for SambaNova

            for line in lines:
                line = line.strip()
                if line.startswith("Category:"):
                    category = line.replace("Category:", "").strip()
                elif line.startswith("Subcategory:"):
                    sub_category = line.replace("Subcategory:", "").strip()
                elif "category" in line.lower() and ":" in line:
                    # Handle variations in response format
                    parts = line.split(":", 1)
                    if len(parts) == 2:
                        category = parts[1].strip()

            # Improve confidence based on response quality
            if category != "Other" and sub_category != "Miscellaneous":
                confidence = 0.8
            elif category != "Other":
                confidence = 0.7

            return SambaNovaClassificationResult(
                category=category,
                sub_category=sub_category,
                confidence=confidence,
                raw_response=response,
                processing_time=processing_time,
                model_used=self.config.model_name,
                tokens_used=total_tokens,
                estimated_cost=estimated_cost,
                from_cache=False
            )

        except Exception as e:
            self.logger.error(f"Failed to parse SambaNova response: {str(e)}")
            return None

    def get_cost_stats(self) -> Dict[str, Any]:
        """Get cost and usage statistics"""
        self._check_daily_reset()

        return {
            "daily_cost": self.cost_tracker.daily_cost,
            "total_cost": self.cost_tracker.total_cost,
            "daily_requests": self.cost_tracker.daily_requests,
            "total_requests": self.cost_tracker.total_requests,
            "daily_tokens": self.cost_tracker.daily_tokens,
            "total_tokens": self.cost_tracker.total_tokens,
            "cache_size": len(self.classification_cache),
            "daily_limit": self.config.max_daily_cost,
            "remaining_budget": max(0, self.config.max_daily_cost - self.cost_tracker.daily_cost),
            "model_name": self.config.model_name,
            "cache_enabled": self.config.cache_enabled
        }

    def clear_cache(self):
        """Clear all caches"""
        self.classification_cache = {}
        self._save_classification_cache()
        self.logger.info("Classification cache cleared")

    def get_stats(self) -> Dict[str, Any]:
        """Get client statistics"""
        return {
            "enabled": self.config.enabled,
            "available": self.is_available(),
            "model_name": self.config.model_name,
            **self.get_cost_stats()
        }
