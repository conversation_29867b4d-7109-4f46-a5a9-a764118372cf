"""
SambaNova AI configuration management for Bank Statement Analyzer
Handles settings, cost tracking, and performance optimization
"""

import json
import os
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path
from datetime import datetime

from ..core.logger import get_logger


@dataclass
class SambaNovaSettings:
    """SambaNova AI settings and configuration"""
    
    # API Configuration
    api_key: str = ""
    base_url: str = "https://api.sambanova.ai/v1"
    model_name: str = "Meta-Llama-3.1-8B-Instruct"  # Most cost-effective model
    
    # Feature Flags
    enabled: bool = True
    use_few_shot: bool = True
    fallback_on_error: bool = True
    cache_enabled: bool = True
    
    # Performance Settings
    max_tokens: int = 100  # Keep low for cost efficiency
    temperature: float = 0.1
    max_retries: int = 3
    retry_delay: float = 2.0
    timeout: int = 30
    
    # Cost Management
    max_daily_cost: float = 1.0  # $1 per day limit
    max_total_cost: float = 5.0  # $5 total limit
    cost_tracking_enabled: bool = True
    budget_alerts_enabled: bool = True
    
    # Caching Settings
    cache_ttl_hours: int = 24
    max_cache_size: int = 1000
    
    # Pricing (per million tokens) - SambaNova rates
    input_cost_per_million: float = 0.10
    output_cost_per_million: float = 0.20
    
    # Few-shot Learning Settings
    max_examples: int = 3  # Reduced for cost efficiency
    similarity_threshold: float = 0.1
    use_examples_cache: bool = True
    examples_cache_ttl: int = 1800  # 30 minutes
    
    # Budget Thresholds
    few_shot_budget_threshold: float = 0.20  # Stop few-shot when < $0.20 remaining
    batch_processing_threshold: float = 0.10  # Stop batch when < $0.10 remaining
    
    # Rate Limiting
    max_requests_per_minute: int = 30
    max_requests_per_day: int = 200


class SambaNovaConfigManager:
    """
    Configuration manager for SambaNova AI integration
    Handles settings persistence, cost tracking, and budget management
    """
    
    def __init__(self, config_dir: str = "bank_analyzer_config"):
        self.logger = get_logger(__name__)
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        self.config_file = self.config_dir / "sambanova_config.json"
        self.cost_tracking_file = self.config_dir / "sambanova_costs.json"
        
        # Load or create configuration
        self.settings = self._load_settings()
        
        # Initialize cost tracking
        self.cost_data = self._load_cost_data()
        
        self.logger.info(f"SambaNova config manager initialized (enabled: {self.settings.enabled})")
    
    def _load_settings(self) -> SambaNovaSettings:
        """Load settings from configuration file"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r') as f:
                    data = json.load(f)
                
                # Create settings object with loaded data
                settings = SambaNovaSettings()
                for key, value in data.items():
                    if hasattr(settings, key):
                        setattr(settings, key, value)
                
                return settings
        except Exception as e:
            self.logger.error(f"Error loading SambaNova settings: {str(e)}")
        
        # Return default settings
        return SambaNovaSettings()
    
    def _save_settings(self):
        """Save settings to configuration file"""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(asdict(self.settings), f, indent=2)
        except Exception as e:
            self.logger.error(f"Error saving SambaNova settings: {str(e)}")
    
    def _load_cost_data(self) -> Dict[str, Any]:
        """Load cost tracking data"""
        try:
            if self.cost_tracking_file.exists():
                with open(self.cost_tracking_file, 'r') as f:
                    return json.load(f)
        except Exception as e:
            self.logger.error(f"Error loading cost data: {str(e)}")
        
        # Return default cost data structure
        return {
            "daily_cost": 0.0,
            "total_cost": 0.0,
            "daily_requests": 0,
            "total_requests": 0,
            "daily_tokens": 0,
            "total_tokens": 0,
            "last_reset": None,
            "cost_history": [],
            "budget_alerts": []
        }
    
    def _save_cost_data(self):
        """Save cost tracking data"""
        try:
            with open(self.cost_tracking_file, 'w') as f:
                json.dump(self.cost_data, f, indent=2)
        except Exception as e:
            self.logger.error(f"Error saving cost data: {str(e)}")
    
    def get_settings(self) -> SambaNovaSettings:
        """Get current settings"""
        return self.settings
    
    def update_settings(self, **kwargs):
        """Update settings"""
        for key, value in kwargs.items():
            if hasattr(self.settings, key):
                setattr(self.settings, key, value)
                self.logger.info(f"Updated SambaNova setting: {key} = {value}")
        
        self._save_settings()
    
    def set_api_key(self, api_key: str):
        """Set API key"""
        self.settings.api_key = api_key
        self._save_settings()
        self.logger.info("SambaNova API key updated")
    
    def enable_sambanova(self, enabled: bool = True):
        """Enable or disable SambaNova integration"""
        self.settings.enabled = enabled
        self._save_settings()
        self.logger.info(f"SambaNova integration {'enabled' if enabled else 'disabled'}")
    
    def set_daily_budget(self, budget: float):
        """Set daily cost budget"""
        self.settings.max_daily_cost = budget
        self._save_settings()
        self.logger.info(f"Daily budget set to ${budget:.2f}")
    
    def set_total_budget(self, budget: float):
        """Set total cost budget"""
        self.settings.max_total_cost = budget
        self._save_settings()
        self.logger.info(f"Total budget set to ${budget:.2f}")
    
    def get_cost_summary(self) -> Dict[str, Any]:
        """Get cost tracking summary"""
        self._check_daily_reset()
        
        return {
            "daily_cost": self.cost_data["daily_cost"],
            "total_cost": self.cost_data["total_cost"],
            "daily_requests": self.cost_data["daily_requests"],
            "total_requests": self.cost_data["total_requests"],
            "daily_tokens": self.cost_data["daily_tokens"],
            "total_tokens": self.cost_data["total_tokens"],
            "daily_budget": self.settings.max_daily_cost,
            "total_budget": self.settings.max_total_cost,
            "daily_remaining": max(0, self.settings.max_daily_cost - self.cost_data["daily_cost"]),
            "total_remaining": max(0, self.settings.max_total_cost - self.cost_data["total_cost"]),
            "daily_budget_used_pct": (self.cost_data["daily_cost"] / self.settings.max_daily_cost) * 100,
            "total_budget_used_pct": (self.cost_data["total_cost"] / self.settings.max_total_cost) * 100,
            "last_reset": self.cost_data["last_reset"]
        }
    
    def _check_daily_reset(self):
        """Reset daily counters if it's a new day"""
        now = datetime.now()
        last_reset = self.cost_data.get("last_reset")
        
        if not last_reset or datetime.fromisoformat(last_reset).date() < now.date():
            # Save yesterday's data to history
            if self.cost_data["daily_cost"] > 0:
                self.cost_data["cost_history"].append({
                    "date": last_reset or now.isoformat(),
                    "daily_cost": self.cost_data["daily_cost"],
                    "daily_requests": self.cost_data["daily_requests"],
                    "daily_tokens": self.cost_data["daily_tokens"]
                })
            
            # Reset daily counters
            self.cost_data["daily_cost"] = 0.0
            self.cost_data["daily_requests"] = 0
            self.cost_data["daily_tokens"] = 0
            self.cost_data["last_reset"] = now.isoformat()
            
            self._save_cost_data()
            self.logger.info("Daily cost tracking reset")
    
    def track_usage(self, tokens_used: int, estimated_cost: float):
        """Track API usage and costs"""
        self._check_daily_reset()
        
        # Update counters
        self.cost_data["daily_cost"] += estimated_cost
        self.cost_data["total_cost"] += estimated_cost
        self.cost_data["daily_requests"] += 1
        self.cost_data["total_requests"] += 1
        self.cost_data["daily_tokens"] += tokens_used
        self.cost_data["total_tokens"] += tokens_used
        
        # Check for budget alerts
        self._check_budget_alerts()
        
        self._save_cost_data()
    
    def _check_budget_alerts(self):
        """Check and log budget alerts"""
        if not self.settings.budget_alerts_enabled:
            return
        
        daily_pct = (self.cost_data["daily_cost"] / self.settings.max_daily_cost) * 100
        total_pct = (self.cost_data["total_cost"] / self.settings.max_total_cost) * 100
        
        # Daily budget alerts
        if daily_pct >= 90 and "daily_90" not in [alert["type"] for alert in self.cost_data["budget_alerts"]]:
            alert = {
                "type": "daily_90",
                "message": f"Daily budget 90% used (${self.cost_data['daily_cost']:.4f}/${self.settings.max_daily_cost:.2f})",
                "timestamp": datetime.now().isoformat()
            }
            self.cost_data["budget_alerts"].append(alert)
            self.logger.warning(alert["message"])
        
        # Total budget alerts
        if total_pct >= 80 and "total_80" not in [alert["type"] for alert in self.cost_data["budget_alerts"]]:
            alert = {
                "type": "total_80",
                "message": f"Total budget 80% used (${self.cost_data['total_cost']:.4f}/${self.settings.max_total_cost:.2f})",
                "timestamp": datetime.now().isoformat()
            }
            self.cost_data["budget_alerts"].append(alert)
            self.logger.warning(alert["message"])
    
    def is_within_budget(self) -> bool:
        """Check if usage is within budget limits"""
        self._check_daily_reset()
        
        daily_ok = self.cost_data["daily_cost"] < self.settings.max_daily_cost
        total_ok = self.cost_data["total_cost"] < self.settings.max_total_cost
        
        return daily_ok and total_ok
    
    def can_use_few_shot(self) -> bool:
        """Check if few-shot learning can be used based on budget"""
        remaining = self.settings.max_daily_cost - self.cost_data["daily_cost"]
        return remaining >= self.settings.few_shot_budget_threshold
    
    def can_process_batch(self) -> bool:
        """Check if batch processing can be used based on budget"""
        remaining = self.settings.max_daily_cost - self.cost_data["daily_cost"]
        return remaining >= self.settings.batch_processing_threshold
    
    def reset_cost_tracking(self):
        """Reset all cost tracking data"""
        self.cost_data = {
            "daily_cost": 0.0,
            "total_cost": 0.0,
            "daily_requests": 0,
            "total_requests": 0,
            "daily_tokens": 0,
            "total_tokens": 0,
            "last_reset": datetime.now().isoformat(),
            "cost_history": [],
            "budget_alerts": []
        }
        self._save_cost_data()
        self.logger.info("Cost tracking data reset")
    
    def export_config(self) -> Dict[str, Any]:
        """Export complete configuration"""
        return {
            "settings": asdict(self.settings),
            "cost_data": self.cost_data
        }
    
    def import_config(self, config_data: Dict[str, Any]):
        """Import configuration"""
        try:
            if "settings" in config_data:
                settings_data = config_data["settings"]
                for key, value in settings_data.items():
                    if hasattr(self.settings, key):
                        setattr(self.settings, key, value)
                self._save_settings()
            
            if "cost_data" in config_data:
                self.cost_data = config_data["cost_data"]
                self._save_cost_data()
            
            self.logger.info("Configuration imported successfully")
            
        except Exception as e:
            self.logger.error(f"Error importing configuration: {str(e)}")


# Global configuration manager instance
_config_manager = None

def get_sambanova_config() -> SambaNovaConfigManager:
    """Get global SambaNova configuration manager"""
    global _config_manager
    if _config_manager is None:
        _config_manager = SambaNovaConfigManager()
    return _config_manager
