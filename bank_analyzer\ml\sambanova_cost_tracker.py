"""
SambaNova AI cost tracking and budget management
Monitors API usage, costs, and enforces budget limits to stay within $5 total budget
"""

import json
import time
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from pathlib import Path
import threading

from ..core.logger import get_logger


@dataclass
class UsageRecord:
    """Individual API usage record"""
    timestamp: str
    description: str
    tokens_input: int
    tokens_output: int
    tokens_total: int
    cost_input: float
    cost_output: float
    cost_total: float
    model_used: str
    from_cache: bool = False
    processing_time: float = 0.0


@dataclass
class BudgetAlert:
    """Budget alert record"""
    timestamp: str
    alert_type: str  # 'daily_warning', 'daily_limit', 'total_warning', 'total_limit'
    message: str
    current_cost: float
    limit: float
    percentage: float


class SambaNovaCostTracker:
    """
    Advanced cost tracking for SambaNova AI API usage
    Provides real-time monitoring, budget enforcement, and detailed analytics
    """
    
    def __init__(self, config_dir: str = "bank_analyzer_config/sambanova"):
        self.logger = get_logger(__name__)
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # Data files
        self.usage_file = self.config_dir / "usage_records.json"
        self.daily_stats_file = self.config_dir / "daily_stats.json"
        self.alerts_file = self.config_dir / "budget_alerts.json"
        
        # Budget limits
        self.daily_budget = 1.0  # $1 per day
        self.total_budget = 5.0  # $5 total limit
        
        # Warning thresholds
        self.daily_warning_threshold = 0.80  # 80% of daily budget
        self.total_warning_threshold = 0.80  # 80% of total budget
        
        # Pricing (per million tokens)
        self.input_cost_per_million = 0.10
        self.output_cost_per_million = 0.20
        
        # Load existing data
        self.usage_records = self._load_usage_records()
        self.daily_stats = self._load_daily_stats()
        self.alerts = self._load_alerts()
        
        # Thread lock for concurrent access
        self._lock = threading.Lock()
        
        # Check for daily reset
        self._check_daily_reset()
        
        self.logger.info(f"Cost tracker initialized (total: ${self.get_total_cost():.4f})")
    
    def _load_usage_records(self) -> List[UsageRecord]:
        """Load usage records from file"""
        try:
            if self.usage_file.exists():
                with open(self.usage_file, 'r') as f:
                    data = json.load(f)
                
                records = []
                for record_data in data:
                    record = UsageRecord(**record_data)
                    records.append(record)
                
                return records
        except Exception as e:
            self.logger.error(f"Error loading usage records: {str(e)}")
        
        return []
    
    def _save_usage_records(self):
        """Save usage records to file"""
        try:
            with open(self.usage_file, 'w') as f:
                data = [asdict(record) for record in self.usage_records]
                json.dump(data, f, indent=2)
        except Exception as e:
            self.logger.error(f"Error saving usage records: {str(e)}")
    
    def _load_daily_stats(self) -> Dict[str, Any]:
        """Load daily statistics"""
        try:
            if self.daily_stats_file.exists():
                with open(self.daily_stats_file, 'r') as f:
                    return json.load(f)
        except Exception as e:
            self.logger.error(f"Error loading daily stats: {str(e)}")
        
        return self._create_empty_daily_stats()
    
    def _create_empty_daily_stats(self) -> Dict[str, Any]:
        """Create empty daily stats structure"""
        return {
            "date": datetime.now().date().isoformat(),
            "daily_cost": 0.0,
            "daily_requests": 0,
            "daily_tokens": 0,
            "daily_cache_hits": 0,
            "daily_processing_time": 0.0,
            "last_reset": datetime.now().isoformat()
        }
    
    def _save_daily_stats(self):
        """Save daily statistics"""
        try:
            with open(self.daily_stats_file, 'w') as f:
                json.dump(self.daily_stats, f, indent=2)
        except Exception as e:
            self.logger.error(f"Error saving daily stats: {str(e)}")
    
    def _load_alerts(self) -> List[BudgetAlert]:
        """Load budget alerts"""
        try:
            if self.alerts_file.exists():
                with open(self.alerts_file, 'r') as f:
                    data = json.load(f)
                
                alerts = []
                for alert_data in data:
                    alert = BudgetAlert(**alert_data)
                    alerts.append(alert)
                
                return alerts
        except Exception as e:
            self.logger.error(f"Error loading alerts: {str(e)}")
        
        return []
    
    def _save_alerts(self):
        """Save budget alerts"""
        try:
            with open(self.alerts_file, 'w') as f:
                data = [asdict(alert) for alert in self.alerts]
                json.dump(data, f, indent=2)
        except Exception as e:
            self.logger.error(f"Error saving alerts: {str(e)}")
    
    def _check_daily_reset(self):
        """Check if we need to reset daily counters"""
        today = datetime.now().date().isoformat()
        
        if self.daily_stats.get("date") != today:
            # Archive yesterday's stats if they exist
            if self.daily_stats.get("daily_cost", 0) > 0:
                self._archive_daily_stats()
            
            # Reset daily counters
            self.daily_stats = self._create_empty_daily_stats()
            self._save_daily_stats()
            
            self.logger.info("Daily cost tracking reset")
    
    def _archive_daily_stats(self):
        """Archive daily stats to historical data"""
        try:
            archive_file = self.config_dir / "daily_history.json"
            
            # Load existing history
            history = []
            if archive_file.exists():
                with open(archive_file, 'r') as f:
                    history = json.load(f)
            
            # Add current day's stats
            history.append(self.daily_stats.copy())
            
            # Keep only last 30 days
            if len(history) > 30:
                history = history[-30:]
            
            # Save updated history
            with open(archive_file, 'w') as f:
                json.dump(history, f, indent=2)
                
        except Exception as e:
            self.logger.error(f"Error archiving daily stats: {str(e)}")
    
    def calculate_cost(self, input_tokens: int, output_tokens: int) -> Dict[str, float]:
        """Calculate cost for token usage"""
        input_cost = (input_tokens / 1_000_000) * self.input_cost_per_million
        output_cost = (output_tokens / 1_000_000) * self.output_cost_per_million
        total_cost = input_cost + output_cost
        
        return {
            "input_cost": input_cost,
            "output_cost": output_cost,
            "total_cost": total_cost
        }
    
    def track_usage(self, description: str, input_tokens: int, output_tokens: int, 
                   model_used: str, from_cache: bool = False, processing_time: float = 0.0) -> bool:
        """
        Track API usage and check budget limits
        
        Returns:
            bool: True if usage was tracked successfully, False if budget exceeded
        """
        with self._lock:
            self._check_daily_reset()
            
            # Calculate costs
            costs = self.calculate_cost(input_tokens, output_tokens)
            total_tokens = input_tokens + output_tokens
            
            # Check budget limits before recording
            if not from_cache:  # Only check budget for actual API calls
                projected_daily = self.daily_stats["daily_cost"] + costs["total_cost"]
                projected_total = self.get_total_cost() + costs["total_cost"]
                
                if projected_daily > self.daily_budget:
                    self._create_alert("daily_limit", 
                                     f"Daily budget exceeded: ${projected_daily:.4f} > ${self.daily_budget:.2f}",
                                     projected_daily, self.daily_budget)
                    return False
                
                if projected_total > self.total_budget:
                    self._create_alert("total_limit",
                                     f"Total budget exceeded: ${projected_total:.4f} > ${self.total_budget:.2f}",
                                     projected_total, self.total_budget)
                    return False
            
            # Create usage record
            record = UsageRecord(
                timestamp=datetime.now().isoformat(),
                description=description,
                tokens_input=input_tokens,
                tokens_output=output_tokens,
                tokens_total=total_tokens,
                cost_input=costs["input_cost"],
                cost_output=costs["output_cost"],
                cost_total=costs["total_cost"],
                model_used=model_used,
                from_cache=from_cache,
                processing_time=processing_time
            )
            
            # Add to records
            self.usage_records.append(record)
            
            # Update daily stats
            if not from_cache:  # Only count actual API costs
                self.daily_stats["daily_cost"] += costs["total_cost"]
            
            self.daily_stats["daily_requests"] += 1
            self.daily_stats["daily_tokens"] += total_tokens
            self.daily_stats["daily_processing_time"] += processing_time
            
            if from_cache:
                self.daily_stats["daily_cache_hits"] += 1
            
            # Check for warnings
            self._check_budget_warnings()
            
            # Save data
            self._save_usage_records()
            self._save_daily_stats()
            
            return True
    
    def _create_alert(self, alert_type: str, message: str, current_cost: float, limit: float):
        """Create and save a budget alert"""
        percentage = (current_cost / limit) * 100
        
        alert = BudgetAlert(
            timestamp=datetime.now().isoformat(),
            alert_type=alert_type,
            message=message,
            current_cost=current_cost,
            limit=limit,
            percentage=percentage
        )
        
        self.alerts.append(alert)
        self._save_alerts()
        
        # Log the alert
        if "limit" in alert_type:
            self.logger.error(message)
        else:
            self.logger.warning(message)
    
    def _check_budget_warnings(self):
        """Check for budget warning thresholds"""
        daily_pct = (self.daily_stats["daily_cost"] / self.daily_budget) * 100
        total_pct = (self.get_total_cost() / self.total_budget) * 100
        
        # Check daily warning
        if (daily_pct >= self.daily_warning_threshold * 100 and 
            not self._has_recent_alert("daily_warning")):
            self._create_alert("daily_warning",
                             f"Daily budget warning: {daily_pct:.1f}% used (${self.daily_stats['daily_cost']:.4f}/${self.daily_budget:.2f})",
                             self.daily_stats["daily_cost"], self.daily_budget)
        
        # Check total warning
        if (total_pct >= self.total_warning_threshold * 100 and 
            not self._has_recent_alert("total_warning")):
            total_cost = self.get_total_cost()
            self._create_alert("total_warning",
                             f"Total budget warning: {total_pct:.1f}% used (${total_cost:.4f}/${self.total_budget:.2f})",
                             total_cost, self.total_budget)
    
    def _has_recent_alert(self, alert_type: str, hours: int = 1) -> bool:
        """Check if there's a recent alert of the given type"""
        cutoff = datetime.now() - timedelta(hours=hours)
        
        for alert in self.alerts:
            alert_time = datetime.fromisoformat(alert.timestamp)
            if alert.alert_type == alert_type and alert_time > cutoff:
                return True
        
        return False

    def get_total_cost(self) -> float:
        """Get total cost across all usage records"""
        return sum(record.cost_total for record in self.usage_records if not record.from_cache)

    def get_daily_cost(self) -> float:
        """Get today's cost"""
        self._check_daily_reset()
        return self.daily_stats["daily_cost"]

    def get_remaining_budget(self) -> Dict[str, float]:
        """Get remaining budget amounts"""
        daily_remaining = max(0, self.daily_budget - self.get_daily_cost())
        total_remaining = max(0, self.total_budget - self.get_total_cost())

        return {
            "daily_remaining": daily_remaining,
            "total_remaining": total_remaining,
            "daily_budget": self.daily_budget,
            "total_budget": self.total_budget
        }

    def can_afford_request(self, estimated_tokens: int) -> bool:
        """Check if we can afford a request with estimated token usage"""
        # Estimate cost (assume 50/50 input/output split)
        input_tokens = estimated_tokens // 2
        output_tokens = estimated_tokens - input_tokens

        costs = self.calculate_cost(input_tokens, output_tokens)

        daily_remaining = self.daily_budget - self.get_daily_cost()
        total_remaining = self.total_budget - self.get_total_cost()

        return (costs["total_cost"] <= daily_remaining and
                costs["total_cost"] <= total_remaining)

    def get_usage_stats(self) -> Dict[str, Any]:
        """Get comprehensive usage statistics"""
        self._check_daily_reset()

        total_cost = self.get_total_cost()
        daily_cost = self.get_daily_cost()

        # Calculate averages
        total_records = len([r for r in self.usage_records if not r.from_cache])
        avg_cost_per_request = total_cost / max(1, total_records)
        avg_tokens_per_request = sum(r.tokens_total for r in self.usage_records) / max(1, len(self.usage_records))

        # Cache statistics
        cache_hits = len([r for r in self.usage_records if r.from_cache])
        cache_rate = (cache_hits / max(1, len(self.usage_records))) * 100

        return {
            # Costs
            "total_cost": total_cost,
            "daily_cost": daily_cost,
            "total_budget": self.total_budget,
            "daily_budget": self.daily_budget,
            "total_remaining": max(0, self.total_budget - total_cost),
            "daily_remaining": max(0, self.daily_budget - daily_cost),
            "total_budget_used_pct": (total_cost / self.total_budget) * 100,
            "daily_budget_used_pct": (daily_cost / self.daily_budget) * 100,

            # Usage counts
            "total_requests": len(self.usage_records),
            "daily_requests": self.daily_stats["daily_requests"],
            "total_api_calls": total_records,
            "cache_hits": cache_hits,
            "cache_rate_pct": cache_rate,

            # Tokens
            "total_tokens": sum(r.tokens_total for r in self.usage_records),
            "daily_tokens": self.daily_stats["daily_tokens"],
            "avg_tokens_per_request": avg_tokens_per_request,

            # Performance
            "avg_cost_per_request": avg_cost_per_request,
            "total_processing_time": sum(r.processing_time for r in self.usage_records),
            "daily_processing_time": self.daily_stats["daily_processing_time"],

            # Alerts
            "total_alerts": len(self.alerts),
            "recent_alerts": len([a for a in self.alerts if self._is_recent_alert(a, hours=24)])
        }

    def set_budgets(self, daily_budget: float = None, total_budget: float = None):
        """Update budget limits"""
        if daily_budget is not None:
            self.daily_budget = daily_budget
            self.logger.info(f"Daily budget updated to ${daily_budget:.2f}")

        if total_budget is not None:
            self.total_budget = total_budget
            self.logger.info(f"Total budget updated to ${total_budget:.2f}")


# Global cost tracker instance
_cost_tracker = None

def get_cost_tracker() -> SambaNovaCostTracker:
    """Get global cost tracker instance"""
    global _cost_tracker
    if _cost_tracker is None:
        _cost_tracker = SambaNovaCostTracker()
    return _cost_tracker
