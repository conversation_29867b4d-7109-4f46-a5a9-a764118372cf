"""
Session-Based Training Data Manager
Manages training data on a per-session basis, allowing clean separation of labeled data
from different bank statement processing sessions.
"""

import pandas as pd
import json
from pathlib import Path
from typing import List, Dict, Optional, Any, Set
from datetime import datetime
from dataclasses import dataclass, asdict
import logging

from ..core.logger import get_logger
from .data_preparation import UniqueTransaction, TransactionDataPreparator


@dataclass
class SessionTrainingData:
    """Represents training data for a specific session"""
    session_id: str
    source_session_id: str  # The transaction session this came from
    created_at: datetime
    labeled_count: int = 0
    unlabeled_count: int = 0
    training_status: str = "active"  # active, archived, completed
    description: str = ""
    labeled_transactions: List[str] = None  # List of hash_ids
    
    def __post_init__(self):
        if self.labeled_transactions is None:
            self.labeled_transactions = []


class SessionTrainingManager:
    """
    Manages training data on a per-session basis
    Allows clean separation and combination of labeled data from multiple sessions
    """
    
    def __init__(self, data_dir: str = "bank_analyzer_config/ml_data"):
        self.logger = get_logger(__name__)
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # Session-specific directories
        self.sessions_dir = self.data_dir / "training_sessions"
        self.sessions_dir.mkdir(exist_ok=True)
        
        self.archived_dir = self.data_dir / "archived_sessions"
        self.archived_dir.mkdir(exist_ok=True)
        
        # Session registry
        self.session_registry_file = self.data_dir / "session_training_registry.json"
        
        # Main data preparator (for compatibility)
        self.data_preparator = TransactionDataPreparator(str(self.data_dir))
        
        # Current active session
        self.current_training_session: Optional[SessionTrainingData] = None
    
    def start_session_training(self, source_session_id: str, description: str = "") -> str:
        """
        Start training data management for a new session
        
        Args:
            source_session_id: The transaction session ID this training is based on
            description: Optional description of the session
            
        Returns:
            Training session ID
        """
        training_session_id = f"training_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        self.current_training_session = SessionTrainingData(
            session_id=training_session_id,
            source_session_id=source_session_id,
            created_at=datetime.now(),
            description=description
        )
        
        # Create session directory
        session_dir = self.sessions_dir / training_session_id
        session_dir.mkdir(exist_ok=True)
        
        # Save session info
        self._save_session_registry()
        
        self.logger.info(f"Started training session: {training_session_id} for source: {source_session_id}")
        return training_session_id
    
    def get_session_labeled_data(self, training_session_id: str) -> List[UniqueTransaction]:
        """Get labeled transactions for a specific training session"""
        try:
            session_file = self.sessions_dir / training_session_id / "labeled_transactions.csv"
            if not session_file.exists():
                return []
            
            df = pd.read_csv(session_file)
            labeled_transactions = []
            
            for _, row in df.iterrows():
                # Convert back to UniqueTransaction object
                txn = UniqueTransaction(
                    description=row['description'],
                    normalized_description=row['normalized_description'],
                    hash_id=row['hash_id'],
                    frequency=row.get('frequency', 1),
                    category=row.get('category', ''),
                    sub_category=row.get('sub_category', ''),
                    confidence=row.get('confidence', 1.0),
                    is_manually_labeled=row.get('is_manually_labeled', False),
                    labeled_by=row.get('labeled_by', ''),
                    labeled_at=row.get('labeled_at', '')
                )
                labeled_transactions.append(txn)
            
            return labeled_transactions
            
        except Exception as e:
            self.logger.error(f"Error loading session labeled data: {str(e)}")
            return []
    
    def save_session_labeled_transaction(self, training_session_id: str, transaction: UniqueTransaction):
        """Save a labeled transaction to a specific training session"""
        try:
            session_dir = self.sessions_dir / training_session_id
            session_dir.mkdir(exist_ok=True)
            
            session_file = session_dir / "labeled_transactions.csv"
            
            # Convert transaction to dict
            txn_data = {
                'hash_id': transaction.hash_id,
                'description': transaction.description,
                'normalized_description': transaction.normalized_description,
                'category': transaction.category,
                'sub_category': transaction.sub_category,
                'confidence': transaction.confidence,
                'frequency': transaction.frequency,
                'is_manually_labeled': transaction.is_manually_labeled,
                'labeled_by': transaction.labeled_by,
                'labeled_at': transaction.labeled_at,
                'session_id': training_session_id
            }
            
            # Append to session file
            df = pd.DataFrame([txn_data])
            if session_file.exists():
                df.to_csv(session_file, mode='a', header=False, index=False)
            else:
                df.to_csv(session_file, index=False)
            
            # Update session registry
            self._update_session_stats(training_session_id)
            
        except Exception as e:
            self.logger.error(f"Error saving session labeled transaction: {str(e)}")
    
    def get_combined_training_data(self, include_sessions: List[str] = None) -> pd.DataFrame:
        """
        Get combined labeled training data from multiple sessions
        
        Args:
            include_sessions: List of session IDs to include. If None, includes all active sessions
            
        Returns:
            Combined training data DataFrame
        """
        try:
            registry = self._load_session_registry()
            if include_sessions is None:
                # Include all sessions that have labeled data (active, completed, etc.)
                include_sessions = [
                    session_id for session_id, session_data in registry.items()
                    if session_data.get('training_status', 'active') in ['active', 'completed']
                ]
            
            combined_data = []
            
            for session_id in include_sessions:
                labeled_transactions = self.get_session_labeled_data(session_id)
                for txn in labeled_transactions:
                    if txn.category and txn.sub_category:  # Only include properly labeled transactions
                        combined_data.append({
                            'hash_id': txn.hash_id,
                            'description': txn.description,
                            'normalized_description': txn.normalized_description,
                            'category': txn.category,
                            'sub_category': txn.sub_category,
                            'confidence': txn.confidence,
                            'frequency': txn.frequency,
                            'is_manually_labeled': txn.is_manually_labeled,
                            'session_id': session_id
                        })
            
            return pd.DataFrame(combined_data)
            
        except Exception as e:
            self.logger.error(f"Error getting combined training data: {str(e)}")
            return pd.DataFrame()

    def get_all_trained_hash_ids(self, include_sessions: List[str] = None) -> Set[str]:
        """
        Get all hash IDs from trained transactions across sessions

        Args:
            include_sessions: List of session IDs to include. If None, includes all active sessions

        Returns:
            Set of hash IDs from all trained transactions
        """
        try:
            registry = self._load_session_registry()
            if include_sessions is None:
                # Include all sessions that have labeled data (active, completed, etc.)
                include_sessions = [
                    session_id for session_id, session_data in registry.items()
                    if session_data.get('training_status', 'active') in ['active', 'completed']
                ]

            all_hash_ids = set()

            for session_id in include_sessions:
                labeled_transactions = self.get_session_labeled_data(session_id)
                for txn in labeled_transactions:
                    if txn.hash_id and txn.category and txn.sub_category:  # Only include properly labeled transactions
                        all_hash_ids.add(txn.hash_id)

            self.logger.info(f"Collected {len(all_hash_ids)} trained transaction hash IDs from {len(include_sessions)} sessions")
            return all_hash_ids

        except Exception as e:
            self.logger.error(f"Error getting trained hash IDs: {str(e)}")
            return set()

    def get_trained_transaction_signatures(self, include_sessions: List[str] = None) -> Dict[str, Dict[str, any]]:
        """
        Get detailed signatures of all trained transactions for duplicate detection

        Args:
            include_sessions: List of session IDs to include. If None, includes all active sessions

        Returns:
            Dictionary mapping hash_id to transaction signature details
        """
        try:
            registry = self._load_session_registry()
            if include_sessions is None:
                # Include all sessions that have labeled data (active, completed, etc.)
                include_sessions = [
                    session_id for session_id, session_data in registry.items()
                    if session_data.get('training_status', 'active') in ['active', 'completed']
                ]

            signatures = {}

            for session_id in include_sessions:
                labeled_transactions = self.get_session_labeled_data(session_id)
                for txn in labeled_transactions:
                    if txn.hash_id and txn.category and txn.sub_category:  # Only include properly labeled transactions
                        signatures[txn.hash_id] = {
                            'hash_id': txn.hash_id,
                            'description': txn.description,
                            'normalized_description': txn.normalized_description,
                            'category': txn.category,
                            'sub_category': txn.sub_category,
                            'training_session_id': session_id,
                            'is_manually_labeled': txn.is_manually_labeled,
                            'labeled_by': txn.labeled_by,
                            'labeled_at': txn.labeled_at
                        }

            self.logger.info(f"Collected {len(signatures)} trained transaction signatures from {len(include_sessions)} sessions")
            return signatures

        except Exception as e:
            self.logger.error(f"Error getting trained transaction signatures: {str(e)}")
            return {}
    
    def archive_session_unlabeled_data(self, training_session_id: str):
        """Archive unlabeled data from a session to keep only labeled data active"""
        try:
            session_dir = self.sessions_dir / training_session_id
            if not session_dir.exists():
                return
            
            # Move session to archived directory
            archived_session_dir = self.archived_dir / training_session_id
            archived_session_dir.mkdir(exist_ok=True)
            
            # Copy labeled data to archive
            labeled_file = session_dir / "labeled_transactions.csv"
            if labeled_file.exists():
                import shutil
                shutil.copy2(labeled_file, archived_session_dir / "labeled_transactions.csv")
            
            # Update registry
            registry = self._load_session_registry()
            if training_session_id in registry:
                registry[training_session_id]['training_status'] = 'archived'
                registry[training_session_id]['archived_at'] = datetime.now().isoformat()
            
            self._save_session_registry(registry)
            
            self.logger.info(f"Archived session: {training_session_id}")
            
        except Exception as e:
            self.logger.error(f"Error archiving session: {str(e)}")
    
    def get_session_stats(self, training_session_id: str) -> Dict[str, Any]:
        """Get statistics for a specific training session"""
        try:
            labeled_transactions = self.get_session_labeled_data(training_session_id)
            
            return {
                'session_id': training_session_id,
                'labeled_count': len(labeled_transactions),
                'categories_used': len(set(txn.category for txn in labeled_transactions if txn.category)),
                'manual_labels': len([txn for txn in labeled_transactions if txn.is_manually_labeled]),
                'ai_labels': len([txn for txn in labeled_transactions if not txn.is_manually_labeled])
            }
            
        except Exception as e:
            self.logger.error(f"Error getting session stats: {str(e)}")
            return {}
    
    def _load_session_registry(self) -> Dict[str, Any]:
        """Load session registry from file"""
        if not self.session_registry_file.exists():
            return {}
        
        try:
            with open(self.session_registry_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            self.logger.error(f"Error loading session registry: {str(e)}")
            return {}
    
    def _save_session_registry(self, registry: Dict[str, Any] = None):
        """Save session registry to file"""
        try:
            if registry is None:
                registry = self._load_session_registry()
            
            # Add current session if it exists
            if self.current_training_session:
                session_dict = asdict(self.current_training_session)
                session_dict['created_at'] = self.current_training_session.created_at.isoformat()
                registry[self.current_training_session.session_id] = session_dict
            
            with open(self.session_registry_file, 'w') as f:
                json.dump(registry, f, indent=2)
                
        except Exception as e:
            self.logger.error(f"Error saving session registry: {str(e)}")
    
    def _update_session_stats(self, training_session_id: str):
        """Update statistics for a training session"""
        try:
            labeled_transactions = self.get_session_labeled_data(training_session_id)

            registry = self._load_session_registry()
            if training_session_id in registry:
                registry[training_session_id]['labeled_count'] = len(labeled_transactions)
                registry[training_session_id]['updated_at'] = datetime.now().isoformat()
                self._save_session_registry(registry)

        except Exception as e:
            self.logger.error(f"Error updating session stats: {str(e)}")

    def list_all_sessions(self) -> Dict[str, Dict[str, Any]]:
        """List all training sessions with their status"""
        registry = self._load_session_registry()
        sessions_info = {}

        for session_id, session_data in registry.items():
            stats = self.get_session_stats(session_id)
            sessions_info[session_id] = {
                **session_data,
                **stats
            }

        return sessions_info

    def clear_session_data(self, training_session_id: str, archive_first: bool = True):
        """Clear data for a specific training session"""
        try:
            if archive_first:
                self.archive_session_unlabeled_data(training_session_id)

            # Remove from active sessions
            session_dir = self.sessions_dir / training_session_id
            if session_dir.exists():
                import shutil
                shutil.rmtree(session_dir)

            # Update registry
            registry = self._load_session_registry()
            if training_session_id in registry:
                registry[training_session_id]['training_status'] = 'cleared'
                registry[training_session_id]['cleared_at'] = datetime.now().isoformat()
                self._save_session_registry(registry)

            self.logger.info(f"Cleared session data: {training_session_id}")

        except Exception as e:
            self.logger.error(f"Error clearing session data: {str(e)}")

    def migrate_existing_training_data(self, source_session_id: str = "legacy"):
        """
        Migrate existing training data to session-based format
        This helps transition from the old system to the new session-based system
        """
        try:
            # Get existing manually labeled transactions directly from unique_transactions
            unique_transactions = self.data_preparator.load_unique_transactions()

            # Filter for manually labeled transactions only
            labeled_transactions = [
                txn for txn in unique_transactions.values()
                if txn.is_manually_labeled and txn.category and txn.sub_category
            ]

            if not labeled_transactions:
                self.logger.info("No existing manually labeled training data to migrate")
                return None

            # Create a legacy session
            legacy_session_id = f"legacy_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            # Create session data object
            self.current_training_session = SessionTrainingData(
                session_id=legacy_session_id,
                source_session_id=source_session_id,
                created_at=datetime.now(),
                description="Migrated from legacy training data - contains all previously labeled transactions",
                labeled_count=len(labeled_transactions),
                unlabeled_count=0,
                training_status="completed"
            )

            # Create session directory
            session_dir = self.sessions_dir / legacy_session_id
            session_dir.mkdir(exist_ok=True)

            # Convert transactions to DataFrame format for session storage
            session_data = []
            for txn in labeled_transactions:
                session_data.append({
                    'hash_id': txn.hash_id,
                    'description': txn.description,
                    'normalized_description': txn.normalized_description,
                    'category': txn.category,
                    'sub_category': txn.sub_category,
                    'confidence': txn.confidence,
                    'frequency': txn.frequency,
                    'is_manually_labeled': txn.is_manually_labeled,
                    'labeled_by': txn.labeled_by,
                    'labeled_at': txn.labeled_at,
                    'session_id': legacy_session_id
                })

            # Save to session file
            session_file = session_dir / "labeled_transactions.csv"
            df = pd.DataFrame(session_data)
            df.to_csv(session_file, index=False)

            # Update session registry
            self._save_session_registry()

            self.logger.info(f"Successfully migrated {len(labeled_transactions)} manually labeled transactions to legacy session: {legacy_session_id}")
            return legacy_session_id

        except Exception as e:
            self.logger.error(f"Error migrating existing training data: {str(e)}", exc_info=True)
            return None
