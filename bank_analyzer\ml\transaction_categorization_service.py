"""
Transaction Categorization Service
Provides three-way categorization of transactions: Already Labeled, Unlabeled, and New
"""

import pandas as pd
from typing import List, Dict, Set, Tuple, Optional
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path

from ..core.logger import get_logger
from ..models.transaction import RawTransaction
from .data_preparation import TransactionDataPreparator, UniqueTransaction


@dataclass
class TransactionCategorizationResult:
    """Result of three-way transaction categorization"""
    already_labeled: List[UniqueTransaction]
    unlabeled: List[UniqueTransaction]
    new: List[UniqueTransaction]
    
    # Detailed information
    already_labeled_details: List[Dict[str, any]]
    unlabeled_details: List[Dict[str, any]]
    new_details: List[Dict[str, any]]
    
    # Summary counts
    total_already_labeled: int
    total_unlabeled: int
    total_new: int
    
    # Summary text
    categorization_summary: str


class TransactionCategorizationService:
    """
    Service for categorizing transactions into three categories:
    1. Already Labeled - transactions that have been manually labeled and categorized
    2. Unlabeled - transactions that exist in the system but haven't been labeled yet
    3. New - completely new transactions not seen before
    """
    
    def __init__(self, ml_data_dir: str = "bank_analyzer_config/ml_data"):
        self.logger = get_logger(__name__)
        self.ml_data_dir = Path(ml_data_dir)
        self.data_preparator = TransactionDataPreparator()
        
        # File paths
        self.unique_transactions_file = self.ml_data_dir / "unique_transactions.csv"
        self.labeling_history_file = self.ml_data_dir / "labeling_history.csv"
    
    def categorize_transactions(self, raw_transactions: List[RawTransaction]) -> TransactionCategorizationResult:
        """
        Categorize transactions into three categories
        
        Args:
            raw_transactions: List of raw transactions to categorize
            
        Returns:
            TransactionCategorizationResult with separated transactions
        """
        try:
            # Load existing training data
            existing_transactions = self._load_existing_transactions()
            labeled_hash_ids = self._load_labeled_hash_ids()
            
            # Convert raw transactions to unique transactions
            new_unique_transactions = self._convert_raw_to_unique(raw_transactions)
            
            # Categorize transactions
            already_labeled = []
            unlabeled = []
            new = []
            
            already_labeled_details = []
            unlabeled_details = []
            new_details = []
            
            for unique_txn in new_unique_transactions:
                hash_id = unique_txn.hash_id
                
                if hash_id in labeled_hash_ids:
                    # Already labeled transaction
                    already_labeled.append(unique_txn)
                    
                    # Get labeling details
                    existing_txn = existing_transactions.get(hash_id)
                    label_info = labeled_hash_ids[hash_id]
                    
                    already_labeled_details.append({
                        'transaction': unique_txn,
                        'hash_id': hash_id,
                        'existing_category': label_info.get('category', ''),
                        'existing_sub_category': label_info.get('sub_category', ''),
                        'labeled_by': label_info.get('labeled_by', ''),
                        'labeled_at': label_info.get('labeled_at', ''),
                        'confidence': label_info.get('confidence', 1.0),
                        'reason': 'Previously labeled transaction'
                    })
                    
                elif hash_id in existing_transactions:
                    # Unlabeled transaction (exists but not labeled)
                    unlabeled.append(unique_txn)
                    
                    existing_txn = existing_transactions[hash_id]
                    unlabeled_details.append({
                        'transaction': unique_txn,
                        'hash_id': hash_id,
                        'frequency': existing_txn.get('frequency', 1),
                        'first_seen': existing_txn.get('first_seen', ''),
                        'last_seen': existing_txn.get('last_seen', ''),
                        'reason': 'Exists in system but not labeled'
                    })
                    
                else:
                    # New transaction
                    new.append(unique_txn)
                    
                    new_details.append({
                        'transaction': unique_txn,
                        'hash_id': hash_id,
                        'reason': 'Completely new transaction'
                    })
            
            # Create summary
            total_already_labeled = len(already_labeled)
            total_unlabeled = len(unlabeled)
            total_new = len(new)
            total_transactions = len(new_unique_transactions)
            
            summary = self._create_categorization_summary(
                total_already_labeled, total_unlabeled, total_new, total_transactions
            )
            
            return TransactionCategorizationResult(
                already_labeled=already_labeled,
                unlabeled=unlabeled,
                new=new,
                already_labeled_details=already_labeled_details,
                unlabeled_details=unlabeled_details,
                new_details=new_details,
                total_already_labeled=total_already_labeled,
                total_unlabeled=total_unlabeled,
                total_new=total_new,
                categorization_summary=summary
            )
            
        except Exception as e:
            self.logger.error(f"Error categorizing transactions: {str(e)}", exc_info=True)
            
            # Return all as new transactions if categorization fails
            new_unique_transactions = self._convert_raw_to_unique(raw_transactions)
            return TransactionCategorizationResult(
                already_labeled=[],
                unlabeled=[],
                new=new_unique_transactions,
                already_labeled_details=[],
                unlabeled_details=[],
                new_details=[{'transaction': txn, 'hash_id': txn.hash_id, 'reason': 'Categorization failed - treated as new'} 
                           for txn in new_unique_transactions],
                total_already_labeled=0,
                total_unlabeled=0,
                total_new=len(new_unique_transactions),
                categorization_summary=f"Categorization failed - all {len(new_unique_transactions)} transactions treated as new"
            )
    
    def _load_existing_transactions(self) -> Dict[str, Dict]:
        """Load existing transactions from unique_transactions.csv and supplement from labeling history"""
        try:
            existing = {}

            # First, load from unique_transactions.csv if it exists
            if self.unique_transactions_file.exists():
                df = pd.read_csv(self.unique_transactions_file)

                for _, row in df.iterrows():
                    hash_id = row['hash_id']
                    existing[hash_id] = {
                        'hash_id': hash_id,
                        'description': row.get('description', ''),
                        'normalized_description': row.get('normalized_description', ''),
                        'frequency': row.get('frequency', 1),
                        'first_seen': row.get('first_seen', ''),
                        'last_seen': row.get('last_seen', ''),
                        'category': row.get('category', ''),
                        'sub_category': row.get('sub_category', ''),
                        'is_manually_labeled': row.get('is_manually_labeled', False),
                        'source': 'unique_transactions'
                    }

                self.logger.info(f"Loaded {len(existing)} transactions from unique_transactions.csv")

            # Then, supplement with hash IDs from labeling history for missing transactions
            if self.labeling_history_file.exists():
                labeling_df = pd.read_csv(self.labeling_history_file)

                # Get unique hash IDs from labeling history
                labeled_hash_ids = set(labeling_df['hash_id'].unique())
                existing_hash_ids = set(existing.keys())
                missing_hash_ids = labeled_hash_ids - existing_hash_ids

                # Add placeholders for missing labeled transactions
                for hash_id in missing_hash_ids:
                    # Get labeling info for this hash
                    hash_labels = labeling_df[labeling_df['hash_id'] == hash_id]
                    latest_label = hash_labels.iloc[-1]  # Get most recent label

                    existing[hash_id] = {
                        'hash_id': hash_id,
                        'description': f'[MISSING] Hash: {hash_id}',  # Placeholder description
                        'normalized_description': '',
                        'frequency': len(hash_labels),
                        'first_seen': hash_labels['timestamp'].min(),
                        'last_seen': hash_labels['timestamp'].max(),
                        'category': latest_label['category'],
                        'sub_category': latest_label['sub_category'],
                        'is_manually_labeled': True,
                        'source': 'labeling_history_only'
                    }

                if missing_hash_ids:
                    self.logger.info(f"Added {len(missing_hash_ids)} missing transactions from labeling history")

            self.logger.info(f"Total existing transactions: {len(existing)}")
            return existing

        except Exception as e:
            self.logger.error(f"Error loading existing transactions: {str(e)}")
            return {}
    
    def _load_labeled_hash_ids(self) -> Dict[str, Dict]:
        """Load labeled hash IDs from labeling_history.csv"""
        try:
            if not self.labeling_history_file.exists():
                return {}
            
            df = pd.read_csv(self.labeling_history_file)
            labeled = {}
            
            # Get the most recent labeling for each hash_id
            for _, row in df.iterrows():
                hash_id = row['hash_id']
                labeled[hash_id] = {
                    'hash_id': hash_id,
                    'category': row.get('category', ''),
                    'sub_category': row.get('sub_category', ''),
                    'confidence': row.get('confidence', 1.0),
                    'labeled_by': row.get('user_name', ''),
                    'labeled_at': row.get('timestamp', ''),
                    'session_id': row.get('session_id', '')
                }
            
            self.logger.info(f"Loaded {len(labeled)} labeled transactions from history")
            return labeled
            
        except Exception as e:
            self.logger.error(f"Error loading labeled hash IDs: {str(e)}")
            return {}
    
    def _convert_raw_to_unique(self, raw_transactions: List[RawTransaction]) -> List[UniqueTransaction]:
        """Convert raw transactions to unique transactions with hash IDs"""
        try:
            unique_transactions = []
            existing_transactions = self._load_existing_transactions()

            for raw_txn in raw_transactions:
                if not raw_txn or not hasattr(raw_txn, 'description'):
                    continue

                # Normalize description
                normalized = self.data_preparator.normalize_description(raw_txn.description)
                if not normalized:
                    continue

                # First, try to find existing hash ID by matching description
                existing_hash_id = None
                matched_existing_data = None

                for existing_hash, existing_data in existing_transactions.items():
                    if existing_data['description'] == raw_txn.description:
                        existing_hash_id = existing_hash
                        matched_existing_data = existing_data
                        break

                # If not found, try matching normalized description
                if not existing_hash_id:
                    for existing_hash, existing_data in existing_transactions.items():
                        if existing_data.get('normalized_description', '') == normalized:
                            existing_hash_id = existing_hash
                            matched_existing_data = existing_data
                            break

                # If still not found, try multiple hash generation approaches to find the correct one
                if not existing_hash_id:
                    txn_type = "debit" if raw_txn.amount < 0 else "credit"

                    # Try multiple hash generation approaches to match legacy labeled data
                    hash_candidates = self._generate_hash_candidates(raw_txn.description, normalized, txn_type)

                    # Check if any of these hash candidates exist in our existing transactions
                    for candidate_hash in hash_candidates:
                        if candidate_hash in existing_transactions:
                            existing_hash_id = candidate_hash
                            matched_existing_data = existing_transactions[candidate_hash]

                            # Update the placeholder description with the real description
                            if matched_existing_data.get('source') == 'labeling_history_only':
                                matched_existing_data['description'] = raw_txn.description
                                matched_existing_data['normalized_description'] = normalized
                            break

                    # If no match found, use the first candidate (current algorithm)
                    if not existing_hash_id:
                        existing_hash_id = hash_candidates[0]

                # Create unique transaction
                unique_txn = UniqueTransaction(
                    description=raw_txn.description,
                    normalized_description=normalized,
                    hash_id=existing_hash_id,
                    frequency=1,
                    first_seen=raw_txn.date,
                    last_seen=raw_txn.date,
                    amount_range=(float(raw_txn.amount), float(raw_txn.amount)),
                    sample_amounts=[float(raw_txn.amount)],
                    transaction_types={"debit" if raw_txn.amount < 0 else "credit"},
                    debit_frequency=1 if raw_txn.amount < 0 else 0,
                    credit_frequency=1 if raw_txn.amount >= 0 else 0,
                    source_files={raw_txn.source_file} if raw_txn.source_file else set(),
                    bank_names={raw_txn.bank_name} if raw_txn.bank_name else set()
                )

                unique_transactions.append(unique_txn)

            return unique_transactions

        except Exception as e:
            self.logger.error(f"Error converting raw to unique transactions: {str(e)}")
            return []

    def _generate_hash_candidates(self, original_description: str, normalized_description: str, txn_type: str) -> List[str]:
        """
        Generate multiple hash candidates using different algorithms to match legacy labeled data

        Args:
            original_description: Original transaction description
            normalized_description: Normalized transaction description
            txn_type: Transaction type ('debit' or 'credit')

        Returns:
            List of hash candidates to try, ordered by likelihood
        """
        import hashlib

        candidates = []

        # Method 1: Current algorithm (context-aware with type)
        candidates.append(self.data_preparator.generate_context_aware_hash_id(normalized_description, txn_type))

        # Method 2: Simple MD5 of normalized description only (legacy approach)
        candidates.append(hashlib.md5(normalized_description.encode()).hexdigest()[:12])

        # Method 3: Simple MD5 of original description
        candidates.append(hashlib.md5(original_description.encode()).hexdigest()[:12])

        # Method 4: Context-aware with opposite transaction type (in case type detection was wrong)
        opposite_type = "credit" if txn_type == "debit" else "debit"
        candidates.append(self.data_preparator.generate_context_aware_hash_id(normalized_description, opposite_type))

        # Method 5: Try with original description and current type
        candidates.append(self.data_preparator.generate_context_aware_hash_id(original_description, txn_type))

        # Method 6: Try with original description and opposite type
        candidates.append(self.data_preparator.generate_context_aware_hash_id(original_description, opposite_type))

        # Method 7: Legacy approach - just description without type context
        legacy_context = f"{normalized_description}"
        candidates.append(hashlib.md5(legacy_context.encode()).hexdigest()[:12])

        # Method 8: Try with uppercase normalized
        candidates.append(hashlib.md5(normalized_description.upper().encode()).hexdigest()[:12])

        # Method 9: Try with lowercase normalized
        candidates.append(hashlib.md5(normalized_description.lower().encode()).hexdigest()[:12])

        # Remove duplicates while preserving order
        seen = set()
        unique_candidates = []
        for candidate in candidates:
            if candidate not in seen:
                seen.add(candidate)
                unique_candidates.append(candidate)

        return unique_candidates
    
    def _create_categorization_summary(self, already_labeled: int, unlabeled: int, 
                                     new: int, total: int) -> str:
        """Create a human-readable summary of categorization results"""
        if total == 0:
            return "No transactions to categorize"
        
        already_labeled_pct = (already_labeled / total) * 100
        unlabeled_pct = (unlabeled / total) * 100
        new_pct = (new / total) * 100
        
        summary = f"Categorized {total} transactions:\n"
        summary += f"• Already Labeled: {already_labeled} ({already_labeled_pct:.1f}%)\n"
        summary += f"• Unlabeled: {unlabeled} ({unlabeled_pct:.1f}%)\n"
        summary += f"• New: {new} ({new_pct:.1f}%)"
        
        return summary
