"""
Smart transaction filtering for SambaNova AI categorization
Intelligently determines which transactions warrant AI categorization vs manual review
"""

import re
import logging
from typing import List, Dict, Set, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path

from ..models.transaction import RawTransaction
from ..core.logger import get_logger


@dataclass
class FilterResult:
    """Result of transaction filtering"""
    should_use_ai: bool
    reason: str
    confidence: float
    priority_score: int  # 1-10, higher = more priority for AI
    filter_category: str  # "generic", "specific", "manual", "excluded"


@dataclass
class FilterStats:
    """Statistics for transaction filtering"""
    total_transactions: int = 0
    ai_suitable: int = 0
    manual_required: int = 0
    excluded: int = 0
    generic_patterns: int = 0
    specific_patterns: int = 0
    
    def get_ai_percentage(self) -> float:
        """Get percentage of transactions suitable for AI"""
        return (self.ai_suitable / max(1, self.total_transactions)) * 100


class SmartTransactionFilter:
    """
    Intelligent transaction filter that determines which transactions
    should be sent to SambaNova AI vs handled manually
    """
    
    def __init__(self, config_dir: str = "bank_analyzer_config"):
        self.logger = get_logger(__name__)
        self.config_dir = Path(config_dir)
        
        # Statistics
        self.stats = FilterStats()
        
        # Generic transaction patterns (good for AI)
        self.generic_patterns = {
            # Banking operations
            'atm_withdrawal': [
                r'atm\s+withdrawal',
                r'cash\s+withdrawal',
                r'atm\s+cash',
                r'withdrawal\s+atm'
            ],
            'bank_transfer': [
                r'bank\s+transfer',
                r'wire\s+transfer',
                r'electronic\s+transfer',
                r'online\s+transfer'
            ],
            'deposit': [
                r'deposit',
                r'credit\s+deposit',
                r'direct\s+deposit'
            ],
            
            # Shopping categories
            'grocery_store': [
                r'grocery\s+store',
                r'supermarket',
                r'food\s+mart',
                r'market\s+place',
                r'grocery\s+outlet'
            ],
            'gas_station': [
                r'gas\s+station',
                r'fuel\s+station',
                r'petrol\s+pump',
                r'service\s+station'
            ],
            'pharmacy': [
                r'pharmacy',
                r'drug\s+store',
                r'medical\s+store',
                r'chemist'
            ],
            'restaurant': [
                r'restaurant',
                r'cafe',
                r'coffee\s+shop',
                r'fast\s+food',
                r'dining'
            ],
            
            # Utilities
            'electric_bill': [
                r'electric\s+bill',
                r'electricity\s+payment',
                r'power\s+bill',
                r'utility\s+bill'
            ],
            'phone_bill': [
                r'phone\s+bill',
                r'mobile\s+bill',
                r'telecom\s+payment',
                r'cellular\s+bill'
            ],
            'internet_bill': [
                r'internet\s+bill',
                r'broadband\s+payment',
                r'wifi\s+bill'
            ],
            
            # Transportation
            'public_transport': [
                r'bus\s+fare',
                r'train\s+ticket',
                r'metro\s+card',
                r'public\s+transport'
            ],
            'taxi_ride': [
                r'taxi\s+fare',
                r'cab\s+ride',
                r'ride\s+share'
            ],
            
            # Online services
            'subscription': [
                r'subscription',
                r'monthly\s+fee',
                r'annual\s+fee',
                r'membership\s+fee'
            ],
            'online_purchase': [
                r'online\s+purchase',
                r'e-commerce',
                r'web\s+order'
            ]
        }
        
        # Specific patterns that indicate manual categorization needed
        self.manual_patterns = [
            # Person names (common patterns)
            r'\b[A-Z][a-z]+\s+[A-Z][a-z]+\b',  # First Last name
            r'\b[A-Z]\.\s*[A-Z][a-z]+\b',      # J. Smith
            r'\bMr\.\s+[A-Z][a-z]+\b',         # Mr. Smith
            r'\bMs\.\s+[A-Z][a-z]+\b',         # Ms. Smith
            r'\bDr\.\s+[A-Z][a-z]+\b',         # Dr. Smith
            
            # Unique identifiers
            r'\b[A-Z0-9]{10,}\b',              # Long alphanumeric codes
            r'\bREF\s*#?\s*[A-Z0-9]+\b',       # Reference numbers
            r'\bTXN\s*#?\s*[A-Z0-9]+\b',       # Transaction numbers
            r'\bINV\s*#?\s*[A-Z0-9]+\b',       # Invoice numbers
            
            # Specific business names with unique identifiers
            r'\b[A-Z][a-z]+\s+LLC\b',          # Company LLC
            r'\b[A-Z][a-z]+\s+Inc\b',          # Company Inc
            r'\b[A-Z][a-z]+\s+Corp\b',         # Company Corp
            r'\b[A-Z][a-z]+\s+Ltd\b',          # Company Ltd
        ]
        
        # Excluded patterns (never send to AI)
        self.excluded_patterns = [
            r'test\s+transaction',
            r'pending\s+transaction',
            r'error\s+transaction',
            r'failed\s+transaction',
            r'cancelled\s+transaction'
        ]
        
        # Common merchant chains (good for AI)
        self.common_merchants = {
            'walmart', 'target', 'costco', 'amazon', 'mcdonalds', 'starbucks',
            'shell', 'exxon', 'chevron', 'bp', 'cvs', 'walgreens', 'rite aid',
            'home depot', 'lowes', 'best buy', 'apple store', 'microsoft store'
        }
        
        # Load custom patterns and thresholds from config if available
        self._load_custom_patterns()
        self.thresholds = self._load_thresholds()

        self.logger.info(f"Smart transaction filter initialized with thresholds: "
                        f"manual={self.thresholds['manual_score_threshold']}, "
                        f"generic={self.thresholds['generic_score_threshold']}")
    
    def _load_custom_patterns(self):
        """Load custom filtering patterns from configuration"""
        try:
            custom_file = self.config_dir / "transaction_filter_patterns.json"
            if custom_file.exists():
                import json
                with open(custom_file, 'r') as f:
                    custom_patterns = json.load(f)
                
                # Merge custom patterns
                if 'generic_patterns' in custom_patterns:
                    for category, patterns in custom_patterns['generic_patterns'].items():
                        if category in self.generic_patterns:
                            self.generic_patterns[category].extend(patterns)
                        else:
                            self.generic_patterns[category] = patterns
                
                if 'manual_patterns' in custom_patterns:
                    self.manual_patterns.extend(custom_patterns['manual_patterns'])
                
                if 'excluded_patterns' in custom_patterns:
                    self.excluded_patterns.extend(custom_patterns['excluded_patterns'])
                
                self.logger.info("Loaded custom filtering patterns")
                
        except Exception as e:
            self.logger.error(f"Error loading custom patterns: {str(e)}")

    def _load_thresholds(self):
        """Load threshold configuration from file or use defaults"""
        try:
            custom_file = self.config_dir / "transaction_filter_patterns.json"
            if custom_file.exists():
                import json
                with open(custom_file, 'r') as f:
                    custom_patterns = json.load(f)

                if 'thresholds' in custom_patterns:
                    thresholds = custom_patterns['thresholds']
                    self.logger.info("Loaded custom thresholds from config")
                    return thresholds

        except Exception as e:
            self.logger.error(f"Error loading thresholds: {str(e)}")

        # Default thresholds
        return {
            "manual_score_threshold": 0.7,
            "generic_score_threshold": 0.6,
            "merchant_score_threshold": 0.5,
            "complexity_threshold": 0.3,
            "amount_threshold": 0.3
        }

    def filter_transaction(self, transaction: RawTransaction) -> FilterResult:
        """
        Determine if a transaction should be sent to AI categorization
        
        Args:
            transaction: Raw transaction to filter
            
        Returns:
            FilterResult with decision and reasoning
        """
        self.stats.total_transactions += 1
        description = transaction.description.lower().strip()
        
        # Check excluded patterns first
        for pattern in self.excluded_patterns:
            if re.search(pattern, description, re.IGNORECASE):
                self.stats.excluded += 1
                return FilterResult(
                    should_use_ai=False,
                    reason=f"Excluded pattern matched: {pattern}",
                    confidence=1.0,
                    priority_score=0,
                    filter_category="excluded"
                )
        
        # Check for manual categorization indicators
        manual_score = self._calculate_manual_score(description)
        if manual_score > self.thresholds['manual_score_threshold']:
            self.stats.manual_required += 1
            return FilterResult(
                should_use_ai=False,
                reason="Contains specific identifiers requiring manual review",
                confidence=manual_score,
                priority_score=1,
                filter_category="manual"
            )

        # Check for generic patterns (good for AI)
        generic_score, matched_category = self._calculate_generic_score(description)
        if generic_score > self.thresholds['generic_score_threshold']:
            self.stats.ai_suitable += 1
            self.stats.generic_patterns += 1
            priority = self._calculate_priority_score(transaction, generic_score)
            return FilterResult(
                should_use_ai=True,
                reason=f"Generic pattern matched: {matched_category}",
                confidence=generic_score,
                priority_score=priority,
                filter_category="generic"
            )
        
        # Check for common merchants
        merchant_score = self._calculate_merchant_score(description)
        if merchant_score > self.thresholds['merchant_score_threshold']:
            self.stats.ai_suitable += 1
            priority = self._calculate_priority_score(transaction, merchant_score)
            return FilterResult(
                should_use_ai=True,
                reason="Common merchant pattern",
                confidence=merchant_score,
                priority_score=priority,
                filter_category="generic"
            )
        
        # Check transaction amount for prioritization
        amount_score = self._calculate_amount_score(transaction.amount)
        
        # Default decision based on description complexity
        complexity_score = self._calculate_complexity_score(description)
        
        if complexity_score < self.thresholds['complexity_threshold'] and amount_score > self.thresholds['amount_threshold']:
            # Simple description with significant amount - good for AI
            self.stats.ai_suitable += 1
            priority = self._calculate_priority_score(transaction, complexity_score + amount_score)
            return FilterResult(
                should_use_ai=True,
                reason="Simple description with significant amount",
                confidence=0.6,
                priority_score=priority,
                filter_category="specific"
            )
        else:
            # Complex or low-value transaction - manual review
            self.stats.manual_required += 1
            return FilterResult(
                should_use_ai=False,
                reason="Complex description or low value requiring manual review",
                confidence=0.7,
                priority_score=2,
                filter_category="manual"
            )

    def _calculate_manual_score(self, description: str) -> float:
        """Calculate score indicating need for manual categorization"""
        score = 0.0
        matches = 0

        for pattern in self.manual_patterns:
            if re.search(pattern, description, re.IGNORECASE):
                matches += 1
                score += 0.3

        # Boost score for multiple matches
        if matches > 1:
            score += 0.2

        return min(1.0, score)

    def _calculate_generic_score(self, description: str) -> Tuple[float, str]:
        """Calculate score for generic patterns and return matched category"""
        best_score = 0.0
        best_category = ""

        for category, patterns in self.generic_patterns.items():
            category_score = 0.0
            matches = 0

            for pattern in patterns:
                if re.search(pattern, description, re.IGNORECASE):
                    matches += 1
                    category_score += 0.4

            # Boost for multiple matches in same category
            if matches > 1:
                category_score += 0.3

            if category_score > best_score:
                best_score = category_score
                best_category = category

        return min(1.0, best_score), best_category

    def _calculate_merchant_score(self, description: str) -> float:
        """Calculate score for common merchant patterns"""
        score = 0.0

        for merchant in self.common_merchants:
            if merchant.lower() in description:
                score += 0.6
                break

        return min(1.0, score)

    def _calculate_complexity_score(self, description: str) -> float:
        """Calculate complexity score (higher = more complex)"""
        # Count various complexity indicators
        word_count = len(description.split())
        number_count = len(re.findall(r'\d+', description))
        special_char_count = len(re.findall(r'[^a-zA-Z0-9\s]', description))
        uppercase_ratio = sum(1 for c in description if c.isupper()) / max(1, len(description))

        # Normalize scores
        word_complexity = min(1.0, word_count / 10)  # 10+ words = complex
        number_complexity = min(1.0, number_count / 5)  # 5+ numbers = complex
        special_complexity = min(1.0, special_char_count / 10)  # 10+ special chars = complex
        case_complexity = uppercase_ratio  # High uppercase ratio = complex

        # Weighted average
        complexity = (
            word_complexity * 0.3 +
            number_complexity * 0.2 +
            special_complexity * 0.3 +
            case_complexity * 0.2
        )

        return complexity

    def _calculate_amount_score(self, amount: float) -> float:
        """Calculate score based on transaction amount (higher = more significant)"""
        abs_amount = float(abs(amount))

        if abs_amount < 1:
            return 0.1
        elif abs_amount < 10:
            return 0.3
        elif abs_amount < 50:
            return 0.5
        elif abs_amount < 200:
            return 0.7
        elif abs_amount < 1000:
            return 0.9
        else:
            return 1.0

    def _calculate_priority_score(self, transaction: RawTransaction, confidence: float) -> int:
        """Calculate priority score (1-10) for AI categorization"""
        # Base priority from confidence
        priority = int(confidence * 5) + 1

        # Boost for higher amounts
        amount_boost = min(3, int(float(abs(transaction.amount)) / 100))
        priority += amount_boost

        # Boost for recent transactions
        try:
            if hasattr(transaction, 'date') and transaction.date:
                # Assume recent transactions are more important
                priority += 1
        except:
            pass

        return min(10, max(1, priority))

    def filter_batch(self, transactions: List[RawTransaction]) -> Dict[str, List[RawTransaction]]:
        """
        Filter a batch of transactions into categories

        Args:
            transactions: List of raw transactions

        Returns:
            Dictionary with categorized transactions
        """
        result = {
            'ai_suitable': [],
            'manual_required': [],
            'excluded': [],
            'priority_order': []  # AI suitable transactions in priority order
        }

        ai_with_priority = []

        for transaction in transactions:
            filter_result = self.filter_transaction(transaction)

            if filter_result.filter_category == "excluded":
                result['excluded'].append(transaction)
            elif filter_result.should_use_ai:
                result['ai_suitable'].append(transaction)
                ai_with_priority.append((transaction, filter_result.priority_score))
            else:
                result['manual_required'].append(transaction)

        # Sort AI suitable transactions by priority (highest first)
        ai_with_priority.sort(key=lambda x: x[1], reverse=True)
        result['priority_order'] = [txn for txn, _ in ai_with_priority]

        return result

    def get_stats(self) -> Dict[str, any]:
        """Get filtering statistics"""
        return {
            "total_transactions": self.stats.total_transactions,
            "ai_suitable": self.stats.ai_suitable,
            "manual_required": self.stats.manual_required,
            "excluded": self.stats.excluded,
            "generic_patterns": self.stats.generic_patterns,
            "specific_patterns": self.stats.specific_patterns,
            "ai_percentage": self.stats.get_ai_percentage(),
            "manual_percentage": (self.stats.manual_required / max(1, self.stats.total_transactions)) * 100,
            "excluded_percentage": (self.stats.excluded / max(1, self.stats.total_transactions)) * 100
        }

    def reset_stats(self):
        """Reset filtering statistics"""
        self.stats = FilterStats()
        self.logger.info("Filter statistics reset")

    def get_pattern_summary(self) -> Dict[str, any]:
        """Get summary of available patterns"""
        return {
            "generic_categories": list(self.generic_patterns.keys()),
            "generic_pattern_count": sum(len(patterns) for patterns in self.generic_patterns.values()),
            "manual_pattern_count": len(self.manual_patterns),
            "excluded_pattern_count": len(self.excluded_patterns),
            "common_merchant_count": len(self.common_merchants)
        }

    def _calculate_manual_score(self, description: str) -> float:
        """Calculate score indicating need for manual categorization"""
        score = 0.0
        matches = 0

        for pattern in self.manual_patterns:
            if re.search(pattern, description, re.IGNORECASE):
                matches += 1
                score += 0.3

        # Boost score for multiple matches
        if matches > 1:
            score += 0.2

        return min(1.0, score)

    def _calculate_generic_score(self, description: str) -> Tuple[float, str]:
        """Calculate score for generic patterns and return matched category"""
        best_score = 0.0
        best_category = ""

        for category, patterns in self.generic_patterns.items():
            category_score = 0.0
            matches = 0

            for pattern in patterns:
                if re.search(pattern, description, re.IGNORECASE):
                    matches += 1
                    category_score += 0.4

            # Boost for multiple matches in same category
            if matches > 1:
                category_score += 0.3

            if category_score > best_score:
                best_score = category_score
                best_category = category

        return min(1.0, best_score), best_category

    def _calculate_merchant_score(self, description: str) -> float:
        """Calculate score for common merchant patterns"""
        score = 0.0

        for merchant in self.common_merchants:
            if merchant.lower() in description:
                score += 0.6
                break

        return min(1.0, score)

    def _calculate_complexity_score(self, description: str) -> float:
        """Calculate complexity score (higher = more complex)"""
        # Count various complexity indicators
        word_count = len(description.split())
        number_count = len(re.findall(r'\d+', description))
        special_char_count = len(re.findall(r'[^a-zA-Z0-9\s]', description))
        uppercase_ratio = sum(1 for c in description if c.isupper()) / max(1, len(description))

        # Normalize scores
        word_complexity = min(1.0, word_count / 10)  # 10+ words = complex
        number_complexity = min(1.0, number_count / 5)  # 5+ numbers = complex
        special_complexity = min(1.0, special_char_count / 10)  # 10+ special chars = complex
        case_complexity = uppercase_ratio  # High uppercase ratio = complex

        # Weighted average
        complexity = (
            word_complexity * 0.3 +
            number_complexity * 0.2 +
            special_complexity * 0.3 +
            case_complexity * 0.2
        )

        return complexity
