"""
Transaction prioritization system for SambaNova AI categorization
Intelligently prioritizes transactions that benefit most from AI categorization
"""

import math
from typing import List, Dict, <PERSON><PERSON>, Optional
from dataclasses import dataclass
from datetime import datetime, timedelta

from ..models.transaction import RawTransaction
from ..core.logger import get_logger
from .transaction_filter import Smart<PERSON>ransactionFilter, FilterResult


@dataclass
class PriorityScore:
    """Detailed priority scoring for a transaction"""
    total_score: int  # 1-10 final priority score
    amount_score: float  # 0.0-1.0
    frequency_score: float  # 0.0-1.0
    complexity_score: float  # 0.0-1.0
    recency_score: float  # 0.0-1.0
    ai_benefit_score: float  # 0.0-1.0
    business_impact_score: float  # 0.0-1.0
    reasoning: str


@dataclass
class PrioritizationConfig:
    """Configuration for transaction prioritization"""
    # Weight factors for different scoring components
    amount_weight: float = 0.25
    frequency_weight: float = 0.20
    complexity_weight: float = 0.15
    recency_weight: float = 0.15
    ai_benefit_weight: float = 0.15
    business_impact_weight: float = 0.10
    
    # Thresholds
    high_priority_threshold: int = 8
    medium_priority_threshold: int = 5
    low_priority_threshold: int = 3
    
    # Amount thresholds for scoring
    high_amount_threshold: float = 500.0
    medium_amount_threshold: float = 100.0
    low_amount_threshold: float = 20.0
    
    # Frequency analysis window (days)
    frequency_analysis_days: int = 30


class TransactionPrioritizer:
    """
    Intelligent transaction prioritizer that determines which transactions
    should be processed first by AI categorization
    """
    
    def __init__(self, config: PrioritizationConfig = None):
        self.logger = get_logger(__name__)
        self.config = config or PrioritizationConfig()
        
        # Initialize transaction filter for AI benefit analysis
        self.transaction_filter = SmartTransactionFilter()
        
        # Cache for transaction analysis
        self._transaction_cache = {}
        self._cache_timestamp = None
        self._cache_ttl = 3600  # 1 hour cache
        
        # Statistics
        self.stats = {
            "total_prioritized": 0,
            "high_priority": 0,
            "medium_priority": 0,
            "low_priority": 0,
            "avg_priority_score": 0.0
        }
        
        self.logger.info("Transaction prioritizer initialized")
    
    def prioritize_transaction(self, transaction: RawTransaction, 
                             transaction_history: List[RawTransaction] = None) -> PriorityScore:
        """
        Calculate priority score for a single transaction
        
        Args:
            transaction: Transaction to prioritize
            transaction_history: Historical transactions for frequency analysis
            
        Returns:
            PriorityScore with detailed scoring breakdown
        """
        # Calculate individual score components
        amount_score = self._calculate_amount_score(transaction)
        frequency_score = self._calculate_frequency_score(transaction, transaction_history or [])
        complexity_score = self._calculate_complexity_score(transaction)
        recency_score = self._calculate_recency_score(transaction)
        ai_benefit_score = self._calculate_ai_benefit_score(transaction)
        business_impact_score = self._calculate_business_impact_score(transaction)
        
        # Calculate weighted total score
        weighted_score = (
            amount_score * self.config.amount_weight +
            frequency_score * self.config.frequency_weight +
            complexity_score * self.config.complexity_weight +
            recency_score * self.config.recency_weight +
            ai_benefit_score * self.config.ai_benefit_weight +
            business_impact_score * self.config.business_impact_weight
        )
        
        # Convert to 1-10 scale
        total_score = max(1, min(10, int(weighted_score * 10)))
        
        # Generate reasoning
        reasoning = self._generate_reasoning(
            total_score, amount_score, frequency_score, complexity_score,
            recency_score, ai_benefit_score, business_impact_score
        )
        
        # Update statistics
        self._update_stats(total_score)
        
        return PriorityScore(
            total_score=total_score,
            amount_score=amount_score,
            frequency_score=frequency_score,
            complexity_score=complexity_score,
            recency_score=recency_score,
            ai_benefit_score=ai_benefit_score,
            business_impact_score=business_impact_score,
            reasoning=reasoning
        )
    
    def _calculate_amount_score(self, transaction: RawTransaction) -> float:
        """Calculate priority score based on transaction amount"""
        abs_amount = float(abs(transaction.amount))
        
        if abs_amount >= self.config.high_amount_threshold:
            return 1.0
        elif abs_amount >= self.config.medium_amount_threshold:
            return 0.7
        elif abs_amount >= self.config.low_amount_threshold:
            return 0.4
        else:
            return 0.1
    
    def _calculate_frequency_score(self, transaction: RawTransaction, 
                                 transaction_history: List[RawTransaction]) -> float:
        """Calculate priority score based on transaction frequency"""
        if not transaction_history:
            return 0.5  # Default score when no history available
        
        # Look for similar transactions in recent history
        similar_count = 0
        cutoff_date = datetime.now() - timedelta(days=self.config.frequency_analysis_days)
        
        for hist_txn in transaction_history:
            try:
                # Simple similarity check based on description and amount
                if (self._transactions_similar(transaction, hist_txn) and
                    self._parse_date(hist_txn.date) >= cutoff_date):
                    similar_count += 1
            except:
                continue
        
        # Score based on frequency (more frequent = higher priority for automation)
        if similar_count >= 10:
            return 1.0  # Very frequent
        elif similar_count >= 5:
            return 0.8  # Frequent
        elif similar_count >= 2:
            return 0.6  # Somewhat frequent
        elif similar_count == 1:
            return 0.4  # Rare
        else:
            return 0.2  # Unique
    
    def _calculate_complexity_score(self, transaction: RawTransaction) -> float:
        """Calculate priority score based on transaction complexity"""
        description = transaction.description.lower()
        
        # Count complexity indicators
        word_count = len(description.split())
        number_count = len([c for c in description if c.isdigit()])
        special_char_count = len([c for c in description if not c.isalnum() and c != ' '])
        
        # Simple transactions get higher priority (easier for AI)
        complexity = 0.0
        
        if word_count <= 3:
            complexity += 0.4
        elif word_count <= 6:
            complexity += 0.3
        else:
            complexity += 0.1
        
        if number_count <= 2:
            complexity += 0.3
        elif number_count <= 5:
            complexity += 0.2
        else:
            complexity += 0.1
        
        if special_char_count <= 3:
            complexity += 0.3
        else:
            complexity += 0.1
        
        return min(1.0, complexity)
    
    def _calculate_recency_score(self, transaction: RawTransaction) -> float:
        """Calculate priority score based on transaction recency"""
        try:
            txn_date = self._parse_date(transaction.date)
            days_ago = (datetime.now() - txn_date).days
            
            if days_ago <= 7:
                return 1.0  # Very recent
            elif days_ago <= 30:
                return 0.8  # Recent
            elif days_ago <= 90:
                return 0.6  # Somewhat recent
            elif days_ago <= 180:
                return 0.4  # Old
            else:
                return 0.2  # Very old
                
        except:
            return 0.5  # Default if date parsing fails
    
    def _calculate_ai_benefit_score(self, transaction: RawTransaction) -> float:
        """Calculate how much this transaction would benefit from AI categorization"""
        # Use transaction filter to determine AI suitability
        filter_result = self.transaction_filter.filter_transaction(transaction)
        
        if filter_result.should_use_ai:
            # Higher confidence in filter = higher AI benefit
            return filter_result.confidence
        else:
            # Transaction not suitable for AI
            return 0.1
    
    def _calculate_business_impact_score(self, transaction: RawTransaction) -> float:
        """Calculate business impact score for prioritization"""
        abs_amount = float(abs(transaction.amount))
        description = transaction.description.lower()
        
        # High impact indicators
        high_impact_keywords = [
            'salary', 'payroll', 'bonus', 'commission', 'dividend',
            'rent', 'mortgage', 'loan', 'emi', 'insurance',
            'tax', 'fee', 'penalty', 'interest', 'investment'
        ]
        
        medium_impact_keywords = [
            'utility', 'bill', 'subscription', 'membership',
            'fuel', 'gas', 'grocery', 'medical', 'healthcare'
        ]
        
        # Check for high impact keywords
        for keyword in high_impact_keywords:
            if keyword in description:
                return min(1.0, 0.8 + (abs_amount / 1000) * 0.2)
        
        # Check for medium impact keywords
        for keyword in medium_impact_keywords:
            if keyword in description:
                return min(1.0, 0.6 + (abs_amount / 500) * 0.2)
        
        # Default scoring based on amount
        if abs_amount >= 1000:
            return 0.8
        elif abs_amount >= 200:
            return 0.6
        elif abs_amount >= 50:
            return 0.4
        else:
            return 0.2
    
    def _transactions_similar(self, txn1: RawTransaction, txn2: RawTransaction) -> bool:
        """Check if two transactions are similar"""
        # Simple similarity check
        desc1_words = set(txn1.description.lower().split())
        desc2_words = set(txn2.description.lower().split())
        
        # Check word overlap
        if desc1_words and desc2_words:
            overlap = len(desc1_words.intersection(desc2_words))
            union = len(desc1_words.union(desc2_words))
            similarity = overlap / union if union > 0 else 0
            
            # Also check amount similarity
            amount_diff = abs(abs(txn1.amount) - abs(txn2.amount))
            amount_similarity = 1.0 - min(1.0, amount_diff / max(abs(txn1.amount), abs(txn2.amount), 1))
            
            return similarity > 0.5 and amount_similarity > 0.8
        
        return False
    
    def _parse_date(self, date_str: str) -> datetime:
        """Parse date string to datetime object"""
        try:
            # Try common date formats
            for fmt in ['%Y-%m-%d', '%m/%d/%Y', '%d/%m/%Y', '%Y-%m-%d %H:%M:%S']:
                try:
                    return datetime.strptime(date_str, fmt)
                except ValueError:
                    continue
            
            # If all formats fail, return current date
            return datetime.now()
        except:
            return datetime.now()
    
    def _generate_reasoning(self, total_score: int, amount_score: float, frequency_score: float,
                          complexity_score: float, recency_score: float, ai_benefit_score: float,
                          business_impact_score: float) -> str:
        """Generate human-readable reasoning for the priority score"""
        reasons = []
        
        if total_score >= self.config.high_priority_threshold:
            priority_level = "High"
        elif total_score >= self.config.medium_priority_threshold:
            priority_level = "Medium"
        else:
            priority_level = "Low"
        
        # Add specific reasons based on scores
        if amount_score >= 0.8:
            reasons.append("high transaction amount")
        if frequency_score >= 0.8:
            reasons.append("frequent transaction pattern")
        if complexity_score >= 0.8:
            reasons.append("simple description suitable for AI")
        if recency_score >= 0.8:
            reasons.append("recent transaction")
        if ai_benefit_score >= 0.8:
            reasons.append("high AI categorization benefit")
        if business_impact_score >= 0.8:
            reasons.append("high business impact")
        
        if reasons:
            return f"{priority_level} priority due to: {', '.join(reasons)}"
        else:
            return f"{priority_level} priority transaction"

    def _update_stats(self, priority_score: int):
        """Update prioritization statistics"""
        self.stats["total_prioritized"] += 1

        if priority_score >= self.config.high_priority_threshold:
            self.stats["high_priority"] += 1
        elif priority_score >= self.config.medium_priority_threshold:
            self.stats["medium_priority"] += 1
        else:
            self.stats["low_priority"] += 1

        # Update average
        total = self.stats["total_prioritized"]
        current_avg = self.stats["avg_priority_score"]
        self.stats["avg_priority_score"] = (current_avg * (total - 1) + priority_score) / total

    def prioritize_batch(self, transactions: List[RawTransaction]) -> List[Tuple[RawTransaction, PriorityScore]]:
        """
        Prioritize a batch of transactions

        Args:
            transactions: List of transactions to prioritize

        Returns:
            List of (transaction, priority_score) tuples sorted by priority (highest first)
        """
        prioritized = []

        for transaction in transactions:
            priority_score = self.prioritize_transaction(transaction, transactions)
            prioritized.append((transaction, priority_score))

        # Sort by priority score (highest first)
        prioritized.sort(key=lambda x: x[1].total_score, reverse=True)

        return prioritized

    def get_high_priority_transactions(self, transactions: List[RawTransaction]) -> List[RawTransaction]:
        """Get only high priority transactions from a list"""
        prioritized = self.prioritize_batch(transactions)

        return [
            txn for txn, score in prioritized
            if score.total_score >= self.config.high_priority_threshold
        ]

    def get_prioritization_summary(self, transactions: List[RawTransaction]) -> Dict[str, any]:
        """Get summary of prioritization for a batch of transactions"""
        prioritized = self.prioritize_batch(transactions)

        high_priority = sum(1 for _, score in prioritized if score.total_score >= self.config.high_priority_threshold)
        medium_priority = sum(1 for _, score in prioritized if self.config.medium_priority_threshold <= score.total_score < self.config.high_priority_threshold)
        low_priority = sum(1 for _, score in prioritized if score.total_score < self.config.medium_priority_threshold)

        avg_score = sum(score.total_score for _, score in prioritized) / len(prioritized) if prioritized else 0

        return {
            "total_transactions": len(transactions),
            "high_priority": high_priority,
            "medium_priority": medium_priority,
            "low_priority": low_priority,
            "average_priority_score": avg_score,
            "high_priority_percentage": (high_priority / len(transactions)) * 100 if transactions else 0,
            "recommended_ai_processing_order": [
                {
                    "description": txn.description[:50],
                    "amount": txn.amount,
                    "priority_score": score.total_score,
                    "reasoning": score.reasoning
                }
                for txn, score in prioritized[:10]  # Top 10
            ]
        }

    def get_stats(self) -> Dict[str, any]:
        """Get prioritization statistics"""
        return {
            **self.stats,
            "high_priority_percentage": (self.stats["high_priority"] / max(1, self.stats["total_prioritized"])) * 100,
            "medium_priority_percentage": (self.stats["medium_priority"] / max(1, self.stats["total_prioritized"])) * 100,
            "low_priority_percentage": (self.stats["low_priority"] / max(1, self.stats["total_prioritized"])) * 100,
            "config": {
                "high_priority_threshold": self.config.high_priority_threshold,
                "medium_priority_threshold": self.config.medium_priority_threshold,
                "amount_weight": self.config.amount_weight,
                "frequency_weight": self.config.frequency_weight,
                "ai_benefit_weight": self.config.ai_benefit_weight
            }
        }

    def reset_stats(self):
        """Reset prioritization statistics"""
        self.stats = {
            "total_prioritized": 0,
            "high_priority": 0,
            "medium_priority": 0,
            "low_priority": 0,
            "avg_priority_score": 0.0
        }
        self.logger.info("Prioritization statistics reset")
