"""
Base parser class for bank statement parsing
"""

from abc import ABC, abstractmethod
from pathlib import Path
from typing import List, Dict, Any, Optional
import logging
from datetime import datetime

from ..models.transaction import RawTransaction
from ..core.logger import get_logger


class BaseStatementParser(ABC):
    """
    Abstract base class for all bank statement parsers
    """
    
    def __init__(self, bank_name: str = "Unknown"):
        self.bank_name = bank_name
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
        self.supported_formats = []
        self.parsing_errors = []
        
    @abstractmethod
    def can_parse(self, file_path: Path) -> bool:
        """
        Check if this parser can handle the given file
        
        Args:
            file_path: Path to the statement file
            
        Returns:
            True if parser can handle this file, False otherwise
        """
        pass
    
    @abstractmethod
    def parse(self, file_path: Path) -> List[RawTransaction]:
        """
        Parse the bank statement file and extract transactions
        
        Args:
            file_path: Path to the statement file
            
        Returns:
            List of RawTransaction objects
        """
        pass
    
    def validate_file(self, file_path: Path) -> bool:
        """
        Validate that the file exists and is readable
        
        Args:
            file_path: Path to the statement file
            
        Returns:
            True if file is valid, False otherwise
        """
        if not file_path.exists():
            self.logger.error(f"File does not exist: {file_path}")
            return False
        
        if not file_path.is_file():
            self.logger.error(f"Path is not a file: {file_path}")
            return False
        
        try:
            # Try to read the file
            with open(file_path, 'rb') as f:
                f.read(1024)  # Read first 1KB to check readability
            return True
        except Exception as e:
            self.logger.error(f"Cannot read file {file_path}: {str(e)}")
            return False
    
    def get_file_info(self, file_path: Path) -> Dict[str, Any]:
        """
        Get basic information about the file
        
        Args:
            file_path: Path to the statement file
            
        Returns:
            Dictionary with file information
        """
        try:
            stat = file_path.stat()
            return {
                'name': file_path.name,
                'size': stat.st_size,
                'modified': datetime.fromtimestamp(stat.st_mtime),
                'extension': file_path.suffix.lower(),
                'exists': True
            }
        except Exception as e:
            self.logger.error(f"Error getting file info for {file_path}: {str(e)}")
            return {
                'name': file_path.name,
                'exists': False,
                'error': str(e)
            }
    
    def clean_amount_string(self, amount_str: str) -> str:
        """
        Clean amount string by removing currency symbols and formatting
        
        Args:
            amount_str: Raw amount string from statement
            
        Returns:
            Cleaned amount string
        """
        if not amount_str:
            return "0"
        
        # Remove common currency symbols and formatting
        cleaned = str(amount_str).strip()

        # Handle common non-numeric values
        if cleaned.lower() in ['', 'nil', 'null', 'none', 'n/a', '-', '--', 'na']:
            return "0"

        # Remove currency symbols
        currency_symbols = ['₹', '$', '€', '£', 'Rs.', 'INR', 'USD', 'EUR', 'GBP']
        for symbol in currency_symbols:
            cleaned = cleaned.replace(symbol, '')

        # Remove commas and spaces
        cleaned = cleaned.replace(',', '').replace(' ', '')

        # Handle parentheses (negative amounts)
        if cleaned.startswith('(') and cleaned.endswith(')'):
            cleaned = '-' + cleaned[1:-1]

        # Remove any remaining non-numeric characters except decimal point and minus
        import re
        cleaned = re.sub(r'[^\d.-]', '', cleaned)

        # Handle multiple decimal points (keep only the last one)
        if cleaned.count('.') > 1:
            parts = cleaned.split('.')
            cleaned = '.'.join(parts[:-1]).replace('.', '') + '.' + parts[-1]

        # Handle multiple minus signs (keep only the first one)
        if cleaned.count('-') > 1:
            is_negative = cleaned.startswith('-')
            cleaned = cleaned.replace('-', '')
            if is_negative:
                cleaned = '-' + cleaned

        # Ensure we have a valid number
        if not cleaned or cleaned in ['-', '.', '-.', '--']:
            return "0"

        # Handle edge cases
        if cleaned.startswith('.'):
            cleaned = '0' + cleaned
        if cleaned.endswith('.'):
            cleaned = cleaned[:-1]
        if cleaned == '-':
            cleaned = "0"

        # Validate the result
        try:
            float(cleaned)
            return cleaned
        except (ValueError, TypeError):
            self.logger.warning(f"Could not parse amount: '{amount_str}' -> '{cleaned}', returning 0")
            return "0"
    
    def parse_date_string(self, date_str) -> Optional[datetime]:
        """
        Parse date string or datetime object using common date formats

        Args:
            date_str: Date string from statement or datetime object

        Returns:
            Parsed datetime object or None if parsing fails
        """
        if not date_str:
            return None

        # Handle datetime objects (from Excel/CSV)
        if hasattr(date_str, 'date'):
            try:
                # It's already a datetime object
                return date_str
            except:
                pass

        # Handle date objects
        if hasattr(date_str, 'year') and hasattr(date_str, 'month') and hasattr(date_str, 'day'):
            try:
                # Convert date to datetime
                from datetime import time
                return datetime.combine(date_str, time())
            except:
                pass

        # Convert to string for parsing
        date_str = str(date_str).strip()

        # Skip obvious non-date values
        if date_str.lower() in ['', 'nil', 'null', 'none', 'n/a', '-', '--', 'na', 'false', 'true']:
            return None

        # Skip numeric-only values that aren't dates
        if date_str.replace('.', '').replace('-', '').isdigit() and len(date_str) < 6:
            return None

        # Handle Excel datetime strings like "2024-03-31 00:00:00"
        if ' 00:00:00' in date_str:
            date_str = date_str.replace(' 00:00:00', '')

        # Common date formats found in bank statements
        date_formats = [
            '%Y-%m-%d',      # 2024-03-31 (Excel format)
            '%d/%m/%Y',      # 31/03/2024
            '%d-%m-%Y',      # 31-03-2024
            '%d.%m.%Y',      # 31.03.2024
            '%d/%m/%y',      # 31/03/24
            '%d-%m-%y',      # 31-03-24
            '%d.%m.%y',      # 31.03.24
            '%d %b %Y',      # 31 Mar 2024
            '%d %B %Y',      # 31 March 2024
            '%b %d, %Y',     # Mar 31, 2024
            '%B %d, %Y',     # March 31, 2024
            '%d-%b-%Y',      # 31-Mar-2024
            '%d-%B-%Y'       # 31-March-2024
        ]

        for fmt in date_formats:
            try:
                return datetime.strptime(date_str, fmt)
            except ValueError:
                continue

        # Only log warning for values that look like they should be dates
        if any(char in date_str for char in ['/', '-', '.']) and len(date_str) >= 6:
            self.logger.debug(f"Could not parse date: {date_str}")

        return None
    
    def add_parsing_error(self, error: str, line_number: Optional[int] = None):
        """
        Add a parsing error to the error list
        
        Args:
            error: Error message
            line_number: Line number where error occurred (if applicable)
        """
        error_msg = f"Line {line_number}: {error}" if line_number else error
        self.parsing_errors.append(error_msg)
        self.logger.warning(f"Parsing error: {error_msg}")
    
    def get_parsing_errors(self) -> List[str]:
        """Get list of parsing errors"""
        return self.parsing_errors.copy()
    
    def clear_parsing_errors(self):
        """Clear the parsing errors list"""
        self.parsing_errors.clear()
