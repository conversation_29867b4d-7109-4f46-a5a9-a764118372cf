"""
CSV parser for bank statements
Handles CSV format bank statements with automatic column detection
"""

import csv
import pandas as pd
import decimal
from pathlib import Path
from typing import List, Dict, Any, Optional
from decimal import Decimal
from datetime import datetime, date

from .base_parser import BaseStatementParser
from ..models.transaction import RawTransaction


class CSVStatementParser(BaseStatementParser):
    """
    Parser for CSV bank statements
    Automatically detects column mappings and handles various CSV formats
    """
    
    def __init__(self, bank_name: str = "Unknown"):
        super().__init__(bank_name)
        self.supported_formats = ['.csv']
        
        # Common column name variations for automatic detection
        self.column_mappings = {
            'date': [
                'date', 'transaction date', 'txn date', 'posting date',
                'value date', 'trans date', 'transaction_date', 'txn_date'
            ],
            'description': [
                'description', 'particulars', 'details', 'narration',
                'transaction details', 'remarks', 'memo', 'reference'
            ],
            'amount': [
                'amount', 'transaction amount', 'txn amount', 'value',
                'debit amount', 'credit amount', 'withdrawal', 'deposit'
            ],
            'debit': [
                'debit', 'debit amount', 'withdrawal', 'dr', 'debits',
                'outgoing', 'paid out', 'expense'
            ],
            'credit': [
                'credit', 'credit amount', 'deposit', 'cr', 'credits',
                'incoming', 'received', 'income'
            ],
            'balance': [
                'balance', 'running balance', 'available balance',
                'closing balance', 'current balance', 'account balance'
            ],
            'reference': [
                'reference', 'ref no', 'reference number', 'transaction id',
                'txn id', 'cheque number', 'check number'
            ]
        }
    
    def can_parse(self, file_path: Path) -> bool:
        """Check if this parser can handle the given CSV file"""
        if file_path.suffix.lower() != '.csv':
            return False
        
        return self.validate_file(file_path)
    
    def parse(self, file_path: Path) -> List[RawTransaction]:
        """Parse CSV bank statement and extract transactions"""
        if not self.can_parse(file_path):
            return []
        
        self.clear_parsing_errors()
        transactions = []
        
        try:
            # Try to detect encoding
            encoding = self._detect_encoding(file_path)
            
            # Read CSV with pandas for better handling
            df = pd.read_csv(file_path, encoding=encoding)
            
            if df.empty:
                self.add_parsing_error("CSV file is empty")
                return []
            
            # Detect column mappings
            column_map = self._detect_columns(df.columns.tolist())
            
            if not column_map.get('date') or not (column_map.get('amount') or 
                                                 (column_map.get('debit') and column_map.get('credit'))):
                self.add_parsing_error("Could not detect required columns (date and amount)")
                return []
            
            # Process each row
            for index, row in df.iterrows():
                try:
                    # Skip empty rows
                    if self._is_empty_row(row):
                        continue

                    transaction = self._extract_transaction_from_row(
                        row, column_map, file_path, index + 2  # +2 for header and 1-based indexing
                    )

                    if transaction:
                        transactions.append(transaction)

                except Exception as e:
                    self.add_parsing_error(f"Error parsing row {index + 2}: {str(e)}", index + 2)
            
        except Exception as e:
            self.logger.error(f"Error parsing CSV {file_path}: {str(e)}")
            self.add_parsing_error(f"Failed to parse CSV: {str(e)}")
        
        self.logger.info(f"Extracted {len(transactions)} transactions from {file_path}")
        return transactions

    def _is_empty_row(self, row) -> bool:
        """Check if a row is empty or contains only whitespace"""
        try:
            # Convert row to list of string values
            row_values = []
            for value in row:
                if pd.isna(value):
                    row_values.append("")
                else:
                    row_values.append(str(value).strip())

            # Check if any cell has non-empty content
            return not any(cell.strip() for cell in row_values if cell)

        except Exception as e:
            self.logger.debug(f"Error checking empty row: {str(e)}")
            return False  # If we can't determine, assume it's not empty
    
    def _detect_encoding(self, file_path: Path) -> str:
        """Detect file encoding"""
        encodings = ['utf-8', 'utf-8-sig', 'latin-1', 'cp1252', 'iso-8859-1']
        
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    f.read(1024)  # Try to read first 1KB
                return encoding
            except UnicodeDecodeError:
                continue
        
        # Default to utf-8 if nothing works
        return 'utf-8'
    
    def _detect_columns(self, columns: List[str]) -> Dict[str, str]:
        """Detect column mappings based on column names"""
        column_map = {}
        
        # Convert columns to lowercase for matching
        columns_lower = [col.lower().strip() for col in columns]
        
        for field, possible_names in self.column_mappings.items():
            for i, col_name in enumerate(columns_lower):
                for possible_name in possible_names:
                    if possible_name.lower() in col_name:
                        column_map[field] = columns[i]  # Use original column name
                        break
                if field in column_map:
                    break
        
        self.logger.info(f"Detected column mappings: {column_map}")
        return column_map

    def _extract_transaction_from_row(self, row: pd.Series, column_map: Dict[str, str],
                                    file_path: Path, row_num: int) -> Optional[RawTransaction]:
        """Extract transaction from CSV row"""
        try:
            # Extract date
            date_col = column_map.get('date')
            if not date_col or pd.isna(row[date_col]):
                return None

            transaction_date = self.parse_date_string(str(row[date_col]))
            if not transaction_date:
                return None

            # Extract description
            desc_col = column_map.get('description')
            description = str(row[desc_col]) if desc_col and not pd.isna(row[desc_col]) else ""
            description = description.strip()

            # Extract amount - handle both single amount column and separate debit/credit columns
            amount = None
            transaction_type = ""

            if column_map.get('amount'):
                # Single amount column
                amount_col = column_map['amount']
                if not pd.isna(row[amount_col]):
                    amount_str = self.clean_amount_string(str(row[amount_col]))
                    if amount_str and amount_str != "0":
                        try:
                            amount = Decimal(amount_str)
                            # Determine if it's debit or credit based on sign
                            if amount < 0:
                                transaction_type = "DEBIT"
                                amount = abs(amount)
                            else:
                                transaction_type = "CREDIT"
                        except (ValueError, decimal.InvalidOperation):
                            return None

            elif column_map.get('debit') and column_map.get('credit'):
                # Separate debit and credit columns
                debit_col = column_map['debit']
                credit_col = column_map['credit']

                debit_amount = None
                credit_amount = None

                if not pd.isna(row[debit_col]) and str(row[debit_col]).strip():
                    debit_str = self.clean_amount_string(str(row[debit_col]))
                    if debit_str and debit_str != "0":
                        try:
                            debit_amount = Decimal(debit_str)
                        except (ValueError, decimal.InvalidOperation):
                            pass

                if not pd.isna(row[credit_col]) and str(row[credit_col]).strip():
                    credit_str = self.clean_amount_string(str(row[credit_col]))
                    if credit_str and credit_str != "0":
                        try:
                            credit_amount = Decimal(credit_str)
                        except (ValueError, decimal.InvalidOperation):
                            pass

                # Use whichever amount is present
                if debit_amount and debit_amount > 0:
                    amount = debit_amount
                    transaction_type = "DEBIT"
                elif credit_amount and credit_amount > 0:
                    amount = credit_amount
                    transaction_type = "CREDIT"

            if not amount or amount <= 0:
                return None

            # Extract balance
            balance = None
            balance_col = column_map.get('balance')
            if balance_col and not pd.isna(row[balance_col]):
                balance_str = self.clean_amount_string(str(row[balance_col]))
                if balance_str and balance_str != "0":
                    balance = Decimal(balance_str)

            # Extract reference number
            reference = None
            ref_col = column_map.get('reference')
            if ref_col and not pd.isna(row[ref_col]):
                reference = str(row[ref_col]).strip()

            return RawTransaction(
                date=transaction_date.date(),
                description=description,
                amount=amount if transaction_type == "CREDIT" else -amount,
                balance=balance,
                transaction_type=transaction_type,
                reference_number=reference,
                source_file=str(file_path),
                source_line=row_num,
                bank_name=self.bank_name
            )

        except Exception as e:
            self.logger.error(f"Error extracting transaction from CSV row: {str(e)}")
            return None
