"""
Indian Bank specific PDF parser
Optimized for Indian Bank statement format
"""

from .pdf_parser import PDFStatementParser
import re
from typing import List, Optional
from datetime import datetime


class IndianBankPDFParser(PDFStatementParser):
    """
    Specialized parser for Indian Bank PDF statements
    """
    
    def __init__(self, bank_name: str = "Indian Bank"):
        super().__init__(bank_name)
        
        # Indian Bank specific patterns
        self.transaction_patterns = [
            # Date, Description, Debit, Credit, Balance
            r'(\d{2}/\d{2}/\d{4})\s+(.+?)\s+(\d+\.\d{2})\s+(\d+\.\d{2})\s+(\d+\.\d{2})',
            # Date, Description, Amount, Balance
            r'(\d{2}/\d{2}/\d{4})\s+(.+?)\s+(\d+\.\d{2})\s+(\d+\.\d{2})',
            # Alternative format
            r'(\d{2}-\d{2}-\d{4})\s+(.+?)\s+(\d+\.\d{2})\s+(\d+\.\d{2})',
        ]
    
    def _should_skip_line(self, line: str) -> bool:
        """Enhanced skip logic for Indian Bank statements"""
        line_lower = line.lower().strip()
        
        # Call parent method first
        if super()._should_skip_line(line):
            return True
        
        # Indian Bank specific skip patterns
        indian_bank_skip = [
            'indian bank',
            'account number:',
            'customer name:',
            'statement period:',
            'opening balance:',
            'closing balance:',
            'total credits:',
            'total debits:',
            'page no:',
            'generated on:',
        ]
        
        for pattern in indian_bank_skip:
            if pattern in line_lower:
                return True
        
        return False
    
    def parse_date_string(self, date_str: str) -> Optional[datetime]:
        """Enhanced date parsing for Indian Bank format"""
        if not date_str:
            return None
        
        # Indian Bank typically uses DD/MM/YYYY or DD-MM-YYYY
        date_formats = [
            "%d/%m/%Y",
            "%d-%m-%Y", 
            "%d.%m.%Y",
            "%d/%m/%y",
            "%d-%m-%y"
        ]
        
        for fmt in date_formats:
            try:
                return datetime.strptime(date_str.strip(), fmt)
            except ValueError:
                continue
        
        # Fall back to parent method
        return super().parse_date_string(date_str)
