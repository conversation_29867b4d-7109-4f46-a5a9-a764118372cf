"""
Duplicate Detection Results Dialog
Shows the results of duplicate detection when loading session data
"""

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                              QTableWidget, QTableWidgetItem, QPushButton,
                              QTabWidget, QWidget, QTextEdit, QGroupBox,
                              QHeaderView, QMessageBox, QSplitter)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont
from typing import List, Dict, Any
from decimal import Decimal

from ..core.logger import get_logger
from ..core.transaction_data_manager import SessionLoadResult
from ..models.transaction import RawTransaction


class DuplicateDetectionDialog(QDialog):
    """Dialog to display duplicate detection results"""
    
    proceed_with_new = Signal()  # User wants to proceed with new transactions only
    proceed_with_all = Signal()  # User wants to proceed with all transactions
    
    def __init__(self, load_result: SessionLoadResult, parent=None):
        super().__init__(parent)
        self.load_result = load_result
        self.logger = get_logger(__name__)
        
        self.setWindowTitle(f"Duplicate Detection Results - {load_result.session_id}")
        self.setModal(True)
        self.resize(1000, 700)
        
        self.setup_ui()
        self.populate_data()
    
    def setup_ui(self):
        """Setup the user interface"""
        layout = QVBoxLayout(self)
        
        # Summary section
        summary_group = QGroupBox("Detection Summary")
        summary_layout = QVBoxLayout(summary_group)
        
        # Summary label
        self.summary_label = QLabel()
        self.summary_label.setWordWrap(True)
        font = QFont()
        font.setPointSize(10)
        self.summary_label.setFont(font)
        summary_layout.addWidget(self.summary_label)
        
        layout.addWidget(summary_group)
        
        # Main content with tabs
        self.tab_widget = QTabWidget()
        
        # New transactions tab
        self.new_transactions_tab = self.create_transactions_tab("New Transactions")
        self.tab_widget.addTab(self.new_transactions_tab, "New Transactions")
        
        # Duplicate transactions tab
        self.duplicate_transactions_tab = self.create_transactions_tab("Already Trained Transactions")
        self.tab_widget.addTab(self.duplicate_transactions_tab, "Already Trained")
        
        # Details tab
        self.details_tab = self.create_details_tab()
        self.tab_widget.addTab(self.details_tab, "Detection Details")
        
        layout.addWidget(self.tab_widget)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.proceed_new_btn = QPushButton("Proceed with New Transactions Only")
        self.proceed_new_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        self.proceed_new_btn.clicked.connect(self.on_proceed_with_new)
        
        self.proceed_all_btn = QPushButton("Proceed with All Transactions")
        self.proceed_all_btn.clicked.connect(self.on_proceed_with_all)
        
        self.cancel_btn = QPushButton("Cancel")
        self.cancel_btn.clicked.connect(self.reject)
        
        button_layout.addWidget(self.proceed_new_btn)
        button_layout.addWidget(self.proceed_all_btn)
        button_layout.addStretch()
        button_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(button_layout)
    
    def create_transactions_tab(self, title: str) -> QWidget:
        """Create a tab for displaying transactions"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Info label
        info_label = QLabel()
        info_label.setWordWrap(True)
        layout.addWidget(info_label)
        
        # Table
        table = QTableWidget()
        table.setColumnCount(5)
        table.setHorizontalHeaderLabels(["Date", "Description", "Amount", "Type", "Source"])
        
        # Configure table
        header = table.horizontalHeader()
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # Description column
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # Date
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # Amount
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Type
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Source
        
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectRows)
        
        layout.addWidget(table)
        
        # Store references
        if "New" in title:
            self.new_info_label = info_label
            self.new_table = table
        else:
            self.duplicate_info_label = info_label
            self.duplicate_table = table
        
        return widget
    
    def create_details_tab(self) -> QWidget:
        """Create the details tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Details text
        self.details_text = QTextEdit()
        self.details_text.setReadOnly(True)
        self.details_text.setFont(QFont("Courier", 9))
        
        layout.addWidget(QLabel("Detailed Detection Information:"))
        layout.addWidget(self.details_text)
        
        return widget
    
    def populate_data(self):
        """Populate the dialog with data"""
        # Summary
        summary_text = f"<b>Session:</b> {self.load_result.session_id}<br>"
        summary_text += f"<b>Total Transactions:</b> {len(self.load_result.raw_transactions)}<br>"
        summary_text += f"<b>New Transactions:</b> {self.load_result.total_new}<br>"
        summary_text += f"<b>Already Trained:</b> {self.load_result.total_duplicates}<br><br>"
        summary_text += f"<b>Summary:</b> {self.load_result.detection_summary}"
        
        self.summary_label.setText(summary_text)
        
        # New transactions
        self.populate_transaction_table(
            self.new_table, 
            self.load_result.new_transactions,
            self.new_info_label,
            "These transactions are new and will be available for labeling and training."
        )
        
        # Duplicate transactions
        self.populate_transaction_table(
            self.duplicate_table,
            self.load_result.duplicate_transactions,
            self.duplicate_info_label,
            "These transactions have already been trained and categorized in previous sessions."
        )
        
        # Details
        self.populate_details()
        
        # Update button states
        if self.load_result.total_new == 0:
            self.proceed_new_btn.setEnabled(False)
            self.proceed_new_btn.setText("No New Transactions")
    
    def populate_transaction_table(self, table: QTableWidget, transactions: List[RawTransaction],
                                 info_label: QLabel, info_text: str):
        """Populate a transaction table"""
        info_label.setText(f"{info_text} ({len(transactions)} transactions)")
        
        table.setRowCount(len(transactions))
        
        for row, txn in enumerate(transactions):
            # Date
            date_item = QTableWidgetItem(str(txn.date))
            table.setItem(row, 0, date_item)
            
            # Description
            desc_item = QTableWidgetItem(txn.description)
            table.setItem(row, 1, desc_item)
            
            # Amount
            amount_item = QTableWidgetItem(f"₹{txn.amount:,.2f}")
            amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            table.setItem(row, 2, amount_item)
            
            # Type
            type_item = QTableWidgetItem(txn.transaction_type or "Unknown")
            table.setItem(row, 3, type_item)
            
            # Source
            source_item = QTableWidgetItem(txn.source_file or "Unknown")
            table.setItem(row, 4, source_item)
    
    def populate_details(self):
        """Populate the details tab"""
        details = []
        details.append(f"Session ID: {self.load_result.session_id}")
        details.append(f"Detection Summary: {self.load_result.detection_summary}")
        details.append(f"Has Duplicates: {self.load_result.has_duplicates}")
        details.append("")
        
        if self.load_result.duplicate_details:
            details.append("Duplicate Detection Details:")
            details.append("-" * 50)
            
            for i, detail in enumerate(self.load_result.duplicate_details, 1):
                txn = detail['transaction']
                details.append(f"{i}. {txn.description}")
                details.append(f"   Hash ID: {detail['hash_id']}")
                details.append(f"   Normalized: {detail['normalized_description']}")
                details.append(f"   Type: {detail['transaction_type']}")
                details.append(f"   Reason: {detail['reason']}")
                details.append("")
        
        self.details_text.setPlainText("\n".join(details))
    
    def on_proceed_with_new(self):
        """Handle proceed with new transactions only"""
        if self.load_result.total_new == 0:
            QMessageBox.information(self, "No New Transactions", 
                                  "There are no new transactions to proceed with.")
            return
        
        self.proceed_with_new.emit()
        self.accept()
    
    def on_proceed_with_all(self):
        """Handle proceed with all transactions"""
        if self.load_result.has_duplicates:
            reply = QMessageBox.question(
                self, "Include Already Trained Transactions",
                f"This will include {self.load_result.total_duplicates} transactions that have "
                "already been trained. This may result in duplicate training data.\n\n"
                "Are you sure you want to proceed with all transactions?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply != QMessageBox.Yes:
                return
        
        self.proceed_with_all.emit()
        self.accept()
