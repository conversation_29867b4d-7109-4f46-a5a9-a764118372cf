"""
Hybrid Categorization Dialog - PySide6/Qt Version
Three-phase categorization: ML Model → AI → Manual
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, QWidget,
    QPushButton, QLabel, QProgressBar, QTableWidget, QTableWidgetItem,
    QGroupBox, QFormLayout, QSpinBox, QDoubleSpinBox, QComboBox,
    QTextEdit, QMessageBox, QHeaderView, QAbstractItemView,
    QSplitter, QFrame, QCheckBox, QLineEdit, QColorDialog,
    QInputDialog, QProgressDialog
)
from PySide6.QtCore import Qt, Signal, QThread, QTimer
from PySide6.QtGui import QFont, QColor

from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime
import threading
import re
import json
import hashlib

# Lazy imports for heavy components - imported only when needed
from ..core.logger import get_logger


class CreateCategoryDialog(QDialog):
    """Dialog for creating new categories"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Create New Category")
        self.setModal(True)
        self.setFixedSize(400, 300)

        self.category_data = None
        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout(self)

        # Header
        header = QLabel("Create New Main Category")
        header.setFont(QFont("Arial", 14, QFont.Bold))
        layout.addWidget(header)

        # Form
        form_layout = QFormLayout()

        # Category name
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("Enter category name (e.g., 'Personal Expenses')")
        form_layout.addRow("Category Name:", self.name_edit)

        # Category type
        self.type_combo = QComboBox()
        self.type_combo.addItems(["expense", "income", "both"])
        self.type_combo.setCurrentText("expense")
        form_layout.addRow("Category Type:", self.type_combo)

        # Description
        self.description_edit = QTextEdit()
        self.description_edit.setPlaceholderText("Optional description for this category")
        self.description_edit.setMaximumHeight(60)
        form_layout.addRow("Description:", self.description_edit)

        # Color selection
        color_layout = QHBoxLayout()
        self.color_button = QPushButton()
        self.color_button.setFixedSize(40, 30)
        self.selected_color = "#007ACC"
        self.color_button.setStyleSheet(f"background-color: {self.selected_color}; border: 1px solid #ccc;")
        self.color_button.clicked.connect(self.select_color)

        color_label = QLabel("Category Color:")
        color_layout.addWidget(color_label)
        color_layout.addWidget(self.color_button)
        color_layout.addStretch()

        layout.addLayout(form_layout)
        layout.addLayout(color_layout)

        # Buttons
        button_layout = QHBoxLayout()

        self.create_btn = QPushButton("Create Category")
        self.create_btn.clicked.connect(self.create_category)
        self.create_btn.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold; padding: 8px;")

        cancel_btn = QPushButton("Cancel")
        cancel_btn.clicked.connect(self.reject)

        button_layout.addWidget(cancel_btn)
        button_layout.addWidget(self.create_btn)

        layout.addLayout(button_layout)

    def select_color(self):
        """Open color picker dialog"""
        color = QColorDialog.getColor(QColor(self.selected_color), self, "Select Category Color")
        if color.isValid():
            self.selected_color = color.name()
            self.color_button.setStyleSheet(f"background-color: {self.selected_color}; border: 1px solid #ccc;")

    def create_category(self):
        """Validate and create the category"""
        name = self.name_edit.text().strip()
        if not name:
            QMessageBox.warning(self, "Invalid Input", "Please enter a category name")
            return

        # Validate name doesn't contain special characters
        if any(char in name for char in ['/', '\\', ':', '*', '?', '"', '<', '>', '|']):
            QMessageBox.warning(self, "Invalid Input", "Category name cannot contain special characters: / \\ : * ? \" < > |")
            return

        self.category_data = {
            'name': name,
            'type': self.type_combo.currentText(),
            'description': self.description_edit.toPlainText().strip(),
            'color': self.selected_color
        }

        self.accept()


class CreateSubcategoryDialog(QDialog):
    """Dialog for creating new subcategories"""

    def __init__(self, parent_category_name, parent=None):
        super().__init__(parent)
        self.parent_category_name = parent_category_name
        self.setWindowTitle("Create New Subcategory")
        self.setModal(True)
        self.setFixedSize(350, 200)

        self.subcategory_name = None
        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout(self)

        # Header
        header = QLabel(f"Create New Subcategory under '{self.parent_category_name}'")
        header.setFont(QFont("Arial", 12, QFont.Bold))
        header.setWordWrap(True)
        layout.addWidget(header)

        # Form
        form_layout = QFormLayout()

        # Subcategory name
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("Enter subcategory name (e.g., 'Online Shopping')")
        form_layout.addRow("Subcategory Name:", self.name_edit)

        layout.addLayout(form_layout)

        # Buttons
        button_layout = QHBoxLayout()

        self.create_btn = QPushButton("Create Subcategory")
        self.create_btn.clicked.connect(self.create_subcategory)
        self.create_btn.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold; padding: 8px;")

        cancel_btn = QPushButton("Cancel")
        cancel_btn.clicked.connect(self.reject)

        button_layout.addWidget(cancel_btn)
        button_layout.addWidget(self.create_btn)

        layout.addLayout(button_layout)

    def create_subcategory(self):
        """Validate and create the subcategory"""
        name = self.name_edit.text().strip()
        if not name:
            QMessageBox.warning(self, "Invalid Input", "Please enter a subcategory name")
            return

        # Validate name doesn't contain special characters
        if any(char in name for char in ['/', '\\', ':', '*', '?', '"', '<', '>', '|']):
            QMessageBox.warning(self, "Invalid Input", "Subcategory name cannot contain special characters: / \\ : * ? \" < > |")
            return

        self.subcategory_name = name
        self.accept()


class MLProcessingThread(QThread):
    """Thread for ML processing to avoid UI freezing"""
    
    progress_updated = Signal(int, str)  # progress, status
    transaction_processed = Signal(dict, str)  # transaction_data, status
    processing_completed = Signal(int, int)  # categorized_count, uncategorized_count
    error_occurred = Signal(str)  # error message
    
    def __init__(self, transactions, ml_categorizer, confidence_threshold):
        super().__init__()
        self.transactions = transactions
        self.ml_categorizer = ml_categorizer
        self.confidence_threshold = confidence_threshold
        self.logger = get_logger(__name__)
    
    def run(self):
        """Run ML processing"""
        try:
            categorized_count = 0
            uncategorized_count = 0
            total = len(self.transactions)
            
            for idx, txn in enumerate(self.transactions):
                # Get ML prediction
                prediction = self.ml_categorizer.predict_category(txn.description)
                
                transaction_data = {
                    'index': idx,
                    'description': txn.description,
                    'amount': float(txn.amount),  # Convert Decimal to float
                    'date': str(txn.date),
                    'transaction_type': getattr(txn, 'transaction_type', 'Unknown'),
                    'transaction': txn  # Keep reference to original transaction
                }
                
                if prediction and prediction.confidence >= self.confidence_threshold:
                    # Categorized by ML
                    transaction_data.update({
                        'category': prediction.category,
                        'sub_category': prediction.sub_category,
                        'confidence': prediction.confidence,
                        'method': 'ML'
                    })
                    categorized_count += 1
                    self.transaction_processed.emit(transaction_data, "Categorized")
                else:
                    # Not categorized by ML
                    transaction_data.update({
                        'category': None,
                        'sub_category': None,
                        'confidence': prediction.confidence if prediction else 0.0,
                        'method': None
                    })
                    uncategorized_count += 1
                    self.transaction_processed.emit(transaction_data, "Needs AI/Manual")
                
                # Update progress
                progress = int((idx + 1) * 100 / total)
                self.progress_updated.emit(progress, f"Processing {idx + 1}/{total}")
            
            self.processing_completed.emit(categorized_count, uncategorized_count)
            
        except Exception as e:
            self.logger.error(f"ML processing error: {str(e)}")
            self.error_occurred.emit(str(e))


class HybridCategorizationDialog(QDialog):
    """Main dialog for hybrid categorization"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger(__name__)

        # Initialize components with lazy loading to prevent UI blocking
        self._ml_categorizer = None  # Lazy initialization
        self._category_manager = None  # Lazy initialization
        self._enhanced_training_manager = None  # Lazy initialization
        self._transaction_data_manager = None  # Lazy initialization
        self._sambanova_client = None  # Lazy initialization

        # Store config parameters for lazy initialization
        self._sambanova_config_params = {
            "api_key": "fc10c11a-7490-41c7-96c6-f0d6ab4761cf",  # Actual API key
            "model_name": "Meta-Llama-3.1-8B-Instruct",
            "enabled": True,
            "fallback_on_error": True,
            "max_tokens": 150,
            "temperature": 0.1,
            "max_daily_cost": 1.0,
            "cache_enabled": True,
            "cache_ttl_hours": 24
        }
        self._sambanova_config = None  # Will be created lazily

        # Component initialization status
        self._components_initialized = False
        self._initialization_in_progress = False
        
        # Data
        self.raw_transactions = []
        self.processed_transactions = []
        self.categorized_transactions = []
        self.uncategorized_transactions = []
        self.ai_categorized_transactions = []
        self.manual_categorized_transactions = []

        # Session persistence
        self.session_file = None
        self.session_id = None
        self.sessions_dir = Path("data/categorization_sessions")
        self.sessions_dir.mkdir(parents=True, exist_ok=True)
        
        # Configuration
        self.ml_confidence_threshold = 0.4
        self.ai_batch_size = 20
        
        # Processing thread
        self.ml_processing_thread = None
        
        self.setup_ui()

        # Start async initialization of components
        QTimer.singleShot(100, self._initialize_components_async)

    @property
    def ml_categorizer(self):
        """Lazy initialization of ML categorizer"""
        if self._ml_categorizer is None:
            self.logger.debug("Initializing ML categorizer...")
            from ..ml.ml_categorizer import MLTransactionCategorizer
            self._ml_categorizer = MLTransactionCategorizer()
        return self._ml_categorizer

    @property
    def category_manager(self):
        """Lazy initialization of category manager"""
        if self._category_manager is None:
            self.logger.debug("Initializing category manager...")
            from ..ml.category_manager import CategoryManager
            self._category_manager = CategoryManager()
        return self._category_manager

    @property
    def enhanced_training_manager(self):
        """Lazy initialization of enhanced training manager"""
        if self._enhanced_training_manager is None:
            self.logger.debug("Initializing enhanced training manager...")
            from ..ml.enhanced_training_data_manager import EnhancedTrainingDataManager
            self._enhanced_training_manager = EnhancedTrainingDataManager()
        return self._enhanced_training_manager

    @property
    def transaction_data_manager(self):
        """Lazy initialization of transaction data manager"""
        if self._transaction_data_manager is None:
            self.logger.debug("Initializing transaction data manager...")
            from ..core.transaction_data_manager import TransactionDataManager
            self._transaction_data_manager = TransactionDataManager()
        return self._transaction_data_manager

    @property
    def sambanova_client(self):
        """Lazy initialization of SambaNova client"""
        if self._sambanova_client is None:
            self.logger.debug("Initializing SambaNova client...")
            from ..ml.sambanova_client import SambaNovaClient, SambaNovaConfig
            if self._sambanova_config is None:
                self._sambanova_config = SambaNovaConfig(**self._sambanova_config_params)
            self._sambanova_client = SambaNovaClient(self._sambanova_config)
        return self._sambanova_client

    def _initialize_components_async(self):
        """Initialize components asynchronously to prevent UI blocking"""
        if self._initialization_in_progress or self._components_initialized:
            return

        self._initialization_in_progress = True
        self.logger.info("Starting asynchronous component initialization...")

        # Create a worker thread for initialization
        from PySide6.QtCore import QThread, QObject, Signal

        class ComponentInitWorker(QObject):
            finished = Signal()
            progress = Signal(str, int)  # message, percentage

            def __init__(self, dialog):
                super().__init__()
                self.dialog = dialog

            def run(self):
                try:
                    # Initialize components one by one with progress updates
                    components = [
                        ("Initializing ML categorizer...", 20, lambda: self.dialog.ml_categorizer),
                        ("Initializing category manager...", 40, lambda: self.dialog.category_manager),
                        ("Initializing training manager...", 60, lambda: self.dialog.enhanced_training_manager),
                        ("Initializing data manager...", 80, lambda: self.dialog.transaction_data_manager),
                        ("Initializing AI client...", 90, lambda: self.dialog.sambanova_client),
                    ]

                    for message, percentage, initializer in components:
                        self.progress.emit(message, percentage)
                        try:
                            _ = initializer()  # Trigger lazy initialization
                        except Exception as e:
                            self.dialog.logger.warning(f"Failed to initialize component: {message} - {str(e)}")
                            # Continue with other components even if one fails

                    self.progress.emit("Initialization complete!", 100)
                    self.finished.emit()

                except Exception as e:
                    self.dialog.logger.error(f"Component initialization failed: {str(e)}")
                    self.finished.emit()

        self.init_worker = ComponentInitWorker(self)
        self.init_thread = QThread()
        self.init_worker.moveToThread(self.init_thread)

        # Connect signals
        self.init_thread.started.connect(self.init_worker.run)
        self.init_worker.finished.connect(self._on_initialization_complete)
        self.init_worker.finished.connect(self.init_thread.quit)
        self.init_worker.finished.connect(self.init_worker.deleteLater)
        self.init_thread.finished.connect(self.init_thread.deleteLater)
        self.init_worker.progress.connect(self._on_initialization_progress)

        # Start the thread
        self.init_thread.start()

    def _on_initialization_progress(self, message: str, percentage: int):
        """Handle initialization progress updates"""
        self.logger.debug(f"Initialization progress: {message} ({percentage}%)")
        if hasattr(self, 'status_bar'):
            self.status_bar.setText(f"{message} ({percentage}%)")

    def _on_initialization_complete(self):
        """Handle completion of component initialization"""
        self._components_initialized = True
        self._initialization_in_progress = False
        self.logger.info("✅ All components initialized successfully")

        # Update status bar
        if hasattr(self, 'status_bar'):
            self.status_bar.setText("✅ All components ready")
            self.status_bar.setStyleSheet("QLabel { color: #2e7d32; font-style: italic; padding: 5px; }")

        # Enable any UI elements that require initialized components
        self._update_ui_after_initialization()

        # Process any pending session data
        self._process_pending_session_data()

    def _update_ui_after_initialization(self):
        """Update UI elements after components are initialized"""
        # Enable buttons that require initialized components
        if hasattr(self, 'ml_process_btn') and hasattr(self, 'raw_transactions') and self.raw_transactions:
            self.ml_process_btn.setEnabled(True)
            self.logger.debug("ML process button enabled after component initialization")

        # Update status or show ready indicator
        self.logger.debug("UI updated after component initialization")

    def is_component_ready(self, component_name: str) -> bool:
        """Check if a specific component is ready"""
        component_map = {
            'ml_categorizer': self._ml_categorizer,
            'category_manager': self._category_manager,
            'enhanced_training_manager': self._enhanced_training_manager,
            'transaction_data_manager': self._transaction_data_manager,
            'sambanova_client': self._sambanova_client
        }
        return component_map.get(component_name) is not None

    def _ensure_components_ready(self) -> bool:
        """Ensure all components are initialized before processing"""
        if not self._components_initialized:
            if self._initialization_in_progress:
                QMessageBox.information(
                    self,
                    "Components Loading",
                    "Components are still being initialized. Please wait a moment and try again."
                )
            else:
                QMessageBox.warning(
                    self,
                    "Components Not Ready",
                    "Components failed to initialize. Please restart the dialog."
                )
            return False
        return True

    def setup_ui(self):
        """Setup the dialog UI"""
        self.setWindowTitle("Hybrid Transaction Categorization")
        self.setModal(True)
        self.resize(1400, 900)  # Wider to accommodate better column display
        
        layout = QVBoxLayout(self)
        
        # Create tab widget
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # Phase 1: ML Categorization
        self.setup_ml_tab()
        
        # Phase 2: AI Categorization  
        self.setup_ai_tab()
        
        # Phase 3: Manual Categorization
        self.setup_manual_tab()
        
        # Phase 4: Export
        self.setup_export_tab()

        # Initialize tab states
        self.tab_widget.setTabEnabled(1, False)  # AI tab disabled initially
        self.tab_widget.setTabEnabled(2, False)  # Manual tab disabled initially
        self.tab_widget.setTabEnabled(3, False)  # Export tab disabled initially

        # Add tab change handler to update status
        self.tab_widget.currentChanged.connect(self.on_tab_changed)

        # Status bar for initialization progress
        self.status_bar = QLabel("Initializing components in background...")
        self.status_bar.setStyleSheet("QLabel { color: #666; font-style: italic; padding: 5px; }")
        layout.addWidget(self.status_bar)

        # Dialog buttons
        button_layout = QHBoxLayout()

        self.close_btn = QPushButton("Close")
        self.close_btn.clicked.connect(self.accept)
        button_layout.addStretch()
        button_layout.addWidget(self.close_btn)

        layout.addLayout(button_layout)

        # Initialize session status
        self.update_session_status()

    def setup_ml_tab(self):
        """Setup ML categorization tab"""
        ml_widget = QWidget()
        layout = QVBoxLayout(ml_widget)
        
        # Header
        header = QLabel("Phase 1: ML Model Categorization")
        header.setFont(QFont("Arial", 14, QFont.Bold))
        layout.addWidget(header)
        
        # Status
        self.ml_status_label = QLabel("Load transactions to begin")
        layout.addWidget(self.ml_status_label)
        
        # Configuration
        config_group = QGroupBox("ML Configuration")
        config_layout = QFormLayout(config_group)
        
        self.confidence_spinbox = QDoubleSpinBox()
        self.confidence_spinbox.setRange(0.1, 0.9)
        self.confidence_spinbox.setValue(self.ml_confidence_threshold)
        self.confidence_spinbox.setSingleStep(0.1)
        self.confidence_spinbox.setDecimals(2)
        config_layout.addRow("Confidence Threshold:", self.confidence_spinbox)
        
        layout.addWidget(config_group)
        
        # Process button and progress
        process_layout = QHBoxLayout()
        
        self.ml_process_btn = QPushButton("Process with ML Model")
        self.ml_process_btn.clicked.connect(self.process_ml_categorization)
        self.ml_process_btn.setEnabled(False)
        process_layout.addWidget(self.ml_process_btn)
        
        self.ml_progress = QProgressBar()
        process_layout.addWidget(self.ml_progress)
        
        layout.addLayout(process_layout)

        # Categorized Transactions Section
        categorized_label = QLabel("Categorized Transactions (Confidence >= Threshold)")
        categorized_label.setFont(QFont("Arial", 12, QFont.Bold))
        layout.addWidget(categorized_label)

        self.categorized_count_label = QLabel("Count: 0")
        layout.addWidget(self.categorized_count_label)

        self.categorized_table = QTableWidget()
        self.categorized_table.setColumnCount(7)
        self.categorized_table.setHorizontalHeaderLabels([
            "Description", "Amount", "Date", "Mode", "Category", "Confidence", "Status"
        ])

        # Set column widths for categorized table
        header = self.categorized_table.horizontalHeader()
        header.setStretchLastSection(False)
        header.resizeSection(0, 300)  # Description
        header.resizeSection(1, 100)  # Amount
        header.resizeSection(2, 100)  # Date
        header.resizeSection(3, 80)   # Mode
        header.resizeSection(4, 150)  # Category
        header.resizeSection(5, 80)   # Confidence
        header.resizeSection(6, 120)  # Status

        self.categorized_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.categorized_table.setAlternatingRowColors(True)
        self.categorized_table.setWordWrap(True)
        self.categorized_table.verticalHeader().setDefaultSectionSize(60)
        layout.addWidget(self.categorized_table)

        # Uncategorized Transactions Section
        uncategorized_label = QLabel("Uncategorized Transactions (Confidence < Threshold)")
        uncategorized_label.setFont(QFont("Arial", 12, QFont.Bold))
        layout.addWidget(uncategorized_label)

        self.uncategorized_count_label = QLabel("Count: 0")
        layout.addWidget(self.uncategorized_count_label)

        self.uncategorized_table = QTableWidget()
        self.uncategorized_table.setColumnCount(7)
        self.uncategorized_table.setHorizontalHeaderLabels([
            "Description", "Amount", "Date", "Mode", "Category", "Confidence", "Status"
        ])

        # Set column widths for uncategorized table
        header = self.uncategorized_table.horizontalHeader()
        header.setStretchLastSection(False)
        header.resizeSection(0, 300)  # Description
        header.resizeSection(1, 100)  # Amount
        header.resizeSection(2, 100)  # Date
        header.resizeSection(3, 80)   # Mode
        header.resizeSection(4, 150)  # Category
        header.resizeSection(5, 80)   # Confidence
        header.resizeSection(6, 120)  # Status

        self.uncategorized_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.uncategorized_table.setAlternatingRowColors(True)
        self.uncategorized_table.setWordWrap(True)
        self.uncategorized_table.verticalHeader().setDefaultSectionSize(60)
        layout.addWidget(self.uncategorized_table)
        
        # Summary
        self.ml_summary_label = QLabel("Ready to process transactions")
        layout.addWidget(self.ml_summary_label)
        
        self.tab_widget.addTab(ml_widget, "Phase 1: ML Categorization")
        
    def setup_ai_tab(self):
        """Setup AI categorization tab"""
        ai_widget = QWidget()
        layout = QVBoxLayout(ai_widget)
        
        # Header
        header = QLabel("Phase 2: AI-Assisted Categorization")
        header.setFont(QFont("Arial", 14, QFont.Bold))
        layout.addWidget(header)

        # Status
        self.ai_status_label = QLabel("Complete Phase 1 (ML) to enable AI processing")
        layout.addWidget(self.ai_status_label)
        
        # Configuration
        config_group = QGroupBox("AI Configuration")
        config_layout = QFormLayout(config_group)
        
        self.batch_size_spinbox = QSpinBox()
        self.batch_size_spinbox.setRange(10, 50)
        self.batch_size_spinbox.setValue(self.ai_batch_size)
        config_layout.addRow("Batch Size:", self.batch_size_spinbox)
        
        self.cost_label = QLabel("Estimated Cost: $0.00")
        config_layout.addRow("Cost:", self.cost_label)

        # Skip AI Processing option
        self.skip_ai_checkbox = QCheckBox("Skip AI Processing")
        self.skip_ai_checkbox.setToolTip("Skip costly AI processing and proceed directly to manual categorization")
        self.skip_ai_checkbox.stateChanged.connect(self.on_skip_ai_changed)
        config_layout.addRow("Options:", self.skip_ai_checkbox)

        layout.addWidget(config_group)
        
        # AI Categorized Transactions Section
        ai_categorized_label = QLabel("AI Categorized Transactions")
        ai_categorized_label.setFont(QFont("Arial", 12, QFont.Bold))
        layout.addWidget(ai_categorized_label)

        self.ai_categorized_count_label = QLabel("Count: 0")
        layout.addWidget(self.ai_categorized_count_label)

        self.ai_categorized_table = QTableWidget()
        self.ai_categorized_table.setColumnCount(7)
        self.ai_categorized_table.setHorizontalHeaderLabels([
            "Description", "Amount", "Date", "Mode", "AI Category", "Confidence", "Status"
        ])

        # Set column widths for AI categorized table
        header = self.ai_categorized_table.horizontalHeader()
        header.setStretchLastSection(False)
        header.resizeSection(0, 300)  # Description
        header.resizeSection(1, 100)  # Amount
        header.resizeSection(2, 100)  # Date
        header.resizeSection(3, 80)   # Mode
        header.resizeSection(4, 150)  # AI Category
        header.resizeSection(5, 80)   # Confidence
        header.resizeSection(6, 120)  # Status

        self.ai_categorized_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.ai_categorized_table.setAlternatingRowColors(True)
        self.ai_categorized_table.setWordWrap(True)
        self.ai_categorized_table.verticalHeader().setDefaultSectionSize(60)
        layout.addWidget(self.ai_categorized_table)

        # AI Uncategorized Transactions Section
        ai_uncategorized_label = QLabel("Still Uncategorized Transactions (Need Manual Processing)")
        ai_uncategorized_label.setFont(QFont("Arial", 12, QFont.Bold))
        layout.addWidget(ai_uncategorized_label)

        self.ai_uncategorized_count_label = QLabel("Count: 0")
        layout.addWidget(self.ai_uncategorized_count_label)

        self.ai_uncategorized_table = QTableWidget()
        self.ai_uncategorized_table.setColumnCount(7)
        self.ai_uncategorized_table.setHorizontalHeaderLabels([
            "Description", "Amount", "Date", "Mode", "Category", "Confidence", "Status"
        ])

        # Set column widths for AI uncategorized table
        header = self.ai_uncategorized_table.horizontalHeader()
        header.setStretchLastSection(False)
        header.resizeSection(0, 300)  # Description
        header.resizeSection(1, 100)  # Amount
        header.resizeSection(2, 100)  # Date
        header.resizeSection(3, 80)   # Mode
        header.resizeSection(4, 150)  # Category
        header.resizeSection(5, 80)   # Confidence
        header.resizeSection(6, 120)  # Status

        self.ai_uncategorized_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.ai_uncategorized_table.setAlternatingRowColors(True)
        self.ai_uncategorized_table.setWordWrap(True)
        self.ai_uncategorized_table.verticalHeader().setDefaultSectionSize(60)
        layout.addWidget(self.ai_uncategorized_table)

        # Process buttons near uncategorized list
        process_layout = QHBoxLayout()

        self.ai_process_btn = QPushButton("Process Uncategorized with AI")
        self.ai_process_btn.clicked.connect(self.process_ai_categorization)
        self.ai_process_btn.setEnabled(False)
        process_layout.addWidget(self.ai_process_btn)

        self.skip_phase2_btn = QPushButton("Skip Phase 2 → Go to Manual")
        self.skip_phase2_btn.clicked.connect(self.skip_phase2)
        self.skip_phase2_btn.setEnabled(False)
        self.skip_phase2_btn.setStyleSheet("background-color: #FFA500; color: white; font-weight: bold;")
        process_layout.addWidget(self.skip_phase2_btn)

        self.ai_progress = QProgressBar()
        process_layout.addWidget(self.ai_progress)

        layout.addLayout(process_layout)

        # Skip AI message
        self.skip_ai_message = QLabel("AI processing skipped. Proceed to Phase 3 (Manual) or Phase 4 (Export).")
        self.skip_ai_message.setStyleSheet("color: orange; font-weight: bold;")
        self.skip_ai_message.setVisible(False)
        layout.addWidget(self.skip_ai_message)
        
        # Summary
        self.ai_summary_label = QLabel("Complete ML categorization first")
        layout.addWidget(self.ai_summary_label)
        
        self.tab_widget.addTab(ai_widget, "Phase 2: AI Categorization")
        
    def setup_manual_tab(self):
        """Setup manual categorization tab"""
        manual_widget = QWidget()
        layout = QVBoxLayout(manual_widget)

        # Header
        header = QLabel("Phase 3: Manual Categorization")
        header.setFont(QFont("Arial", 14, QFont.Bold))
        layout.addWidget(header)

        # Status
        self.manual_status_label = QLabel("Complete Phase 1 and 2 to begin manual categorization")
        layout.addWidget(self.manual_status_label)

        # Enhanced Filter section
        filter_group = QGroupBox("Advanced Transaction Filters")
        filter_layout = QFormLayout(filter_group)

        # Multi-word description filter
        self.desc_filter_edit = QLineEdit()
        self.desc_filter_edit.setPlaceholderText("Multi-word filter: 'UPI payment', 'ATM withdrawal', 'bank transfer'...")
        self.desc_filter_edit.textChanged.connect(self.apply_manual_filters)
        filter_layout.addRow("Description (Multi-word):", self.desc_filter_edit)

        # Amount range filter
        amount_layout = QHBoxLayout()
        self.min_amount_edit = QLineEdit()
        self.min_amount_edit.setPlaceholderText("Min amount")
        self.min_amount_edit.textChanged.connect(self.apply_manual_filters)
        amount_layout.addWidget(self.min_amount_edit)

        amount_layout.addWidget(QLabel("to"))

        self.max_amount_edit = QLineEdit()
        self.max_amount_edit.setPlaceholderText("Max amount")
        self.max_amount_edit.textChanged.connect(self.apply_manual_filters)
        amount_layout.addWidget(self.max_amount_edit)

        filter_layout.addRow("Amount Range:", amount_layout)

        # Transaction type filter (Credit/Debit based)
        self.type_filter_combo = QComboBox()
        self.type_filter_combo.addItems(["All Types", "DEBIT (Expenses)", "CREDIT (Income)", "TRANSFER"])
        self.type_filter_combo.currentTextChanged.connect(self.apply_manual_filters)
        filter_layout.addRow("Transaction Type:", self.type_filter_combo)

        # Missing categories filter
        self.missing_categories_checkbox = QCheckBox("Show only transactions with missing/unknown categories")
        self.missing_categories_checkbox.stateChanged.connect(self.apply_manual_filters)
        filter_layout.addRow("Missing Categories:", self.missing_categories_checkbox)

        # Clear filters button
        clear_filters_btn = QPushButton("Clear All Filters")
        clear_filters_btn.clicked.connect(self.clear_manual_filters)
        filter_layout.addRow("", clear_filters_btn)

        layout.addWidget(filter_group)

        # Transactions to categorize
        self.manual_transactions_label = QLabel("Transactions to Categorize (0)")
        self.manual_transactions_label.setFont(QFont("Arial", 12, QFont.Bold))
        layout.addWidget(self.manual_transactions_label)

        # Enhanced manual categorization table with bulk selection
        self.manual_table = QTableWidget()
        self.manual_table.setColumnCount(9)
        self.manual_table.setHorizontalHeaderLabels([
            "Select", "Description", "Amount", "Date", "Mode", "Current Category", "New Category", "New Subcategory", "Action"
        ])

        # Set column widths
        header = self.manual_table.horizontalHeader()
        header.setStretchLastSection(False)
        header.resizeSection(0, 60)   # Select checkbox
        header.resizeSection(1, 220)  # Description
        header.resizeSection(2, 100)  # Amount
        header.resizeSection(3, 100)  # Date
        header.resizeSection(4, 80)   # Mode
        header.resizeSection(5, 120)  # Current Category
        header.resizeSection(6, 150)  # New Category
        header.resizeSection(7, 150)  # New Subcategory
        header.resizeSection(8, 100)  # Action

        self.manual_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.manual_table.setAlternatingRowColors(True)
        self.manual_table.setWordWrap(True)
        self.manual_table.verticalHeader().setDefaultSectionSize(60)
        layout.addWidget(self.manual_table)

        # Bulk categorization section
        bulk_group = QGroupBox("Bulk Categorization for Selected Transactions")
        bulk_layout = QFormLayout(bulk_group)

        # Bulk category selection
        bulk_category_layout = QHBoxLayout()
        self.bulk_category_combo = QComboBox()
        self.bulk_category_combo.addItem("Select Category for Bulk Assignment...")
        self.bulk_category_combo.currentTextChanged.connect(self.on_bulk_category_changed)
        bulk_category_layout.addWidget(self.bulk_category_combo)

        self.bulk_subcategory_combo = QComboBox()
        self.bulk_subcategory_combo.addItem("Select Subcategory...")
        self.bulk_subcategory_combo.currentTextChanged.connect(self.on_bulk_subcategory_changed)
        bulk_category_layout.addWidget(self.bulk_subcategory_combo)

        bulk_layout.addRow("Bulk Category:", bulk_category_layout)

        # Selection status
        self.selection_status_label = QLabel("Selected: 0 transactions")
        bulk_layout.addRow("Selection Status:", self.selection_status_label)

        # Bulk action buttons
        bulk_buttons_layout = QHBoxLayout()

        self.select_all_btn = QPushButton("Select All")
        self.select_all_btn.clicked.connect(self.select_all_transactions)
        bulk_buttons_layout.addWidget(self.select_all_btn)

        self.select_none_btn = QPushButton("Select None")
        self.select_none_btn.clicked.connect(self.select_none_transactions)
        bulk_buttons_layout.addWidget(self.select_none_btn)

        self.apply_bulk_btn = QPushButton("Apply to Selected")
        self.apply_bulk_btn.clicked.connect(self.apply_bulk_categorization)
        self.apply_bulk_btn.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold;")
        bulk_buttons_layout.addWidget(self.apply_bulk_btn)

        bulk_layout.addRow("Actions:", bulk_buttons_layout)
        layout.addWidget(bulk_group)

        # Manual categorization controls
        controls_layout = QHBoxLayout()

        self.load_manual_btn = QPushButton("Load Uncategorized Transactions")
        self.load_manual_btn.clicked.connect(self.load_manual_transactions)
        self.load_manual_btn.setEnabled(False)
        controls_layout.addWidget(self.load_manual_btn)

        self.save_manual_btn = QPushButton("Save Manual Categories")
        self.save_manual_btn.clicked.connect(self.save_manual_categories)
        self.save_manual_btn.setEnabled(False)
        controls_layout.addWidget(self.save_manual_btn)

        controls_layout.addStretch()
        layout.addLayout(controls_layout)

        self.tab_widget.addTab(manual_widget, "Phase 3: Manual Categorization")
        
    def setup_export_tab(self):
        """Setup export tab"""
        export_widget = QWidget()
        layout = QVBoxLayout(export_widget)
        
        # Header
        header = QLabel("Phase 4: Export to Main Application")
        header.setFont(QFont("Arial", 14, QFont.Bold))
        layout.addWidget(header)
        
        # Summary text
        self.export_summary = QTextEdit()
        self.export_summary.setReadOnly(True)
        layout.addWidget(self.export_summary)
        
        # Export buttons
        button_layout = QHBoxLayout()

        self.generate_summary_btn = QPushButton("Generate Summary")
        self.generate_summary_btn.clicked.connect(self.generate_summary)
        button_layout.addWidget(self.generate_summary_btn)

        # Add validation report button
        self.validation_report_btn = QPushButton("🔍 Validation Report")
        self.validation_report_btn.clicked.connect(self.show_validation_report)
        self.validation_report_btn.setToolTip("Show detailed validation report of the transaction processing pipeline")
        button_layout.addWidget(self.validation_report_btn)

        self.export_csv_btn = QPushButton("Export to CSV")
        self.export_csv_btn.clicked.connect(self.export_to_csv)
        button_layout.addWidget(self.export_csv_btn)

        button_layout.addStretch()
        layout.addLayout(button_layout)

        # Enhanced Training Data Management Section
        training_group = QGroupBox("Enhanced Training Data Management")
        training_layout = QVBoxLayout(training_group)

        # Training data buttons
        training_button_layout = QHBoxLayout()

        self.backup_training_btn = QPushButton("💾 Backup Training Data")
        self.backup_training_btn.clicked.connect(self.backup_training_data)
        self.backup_training_btn.setToolTip("Create a backup of current ML model training data")
        training_button_layout.addWidget(self.backup_training_btn)

        self.clear_interface_btn = QPushButton("🗑️ Clear Interface")
        self.clear_interface_btn.clicked.connect(self.clear_manual_interface)
        self.clear_interface_btn.setToolTip("Clear manual labeling interface while preserving trained model")
        training_button_layout.addWidget(self.clear_interface_btn)

        self.auto_label_btn = QPushButton("🤖 Auto-Label New Data")
        self.auto_label_btn.clicked.connect(self.auto_label_transactions)
        self.auto_label_btn.setToolTip("Apply auto-labeling to new transactions based on trained model")
        training_button_layout.addWidget(self.auto_label_btn)

        self.review_auto_labels_btn = QPushButton("✓ Review Auto-Labels")
        self.review_auto_labels_btn.clicked.connect(self.open_auto_label_review)
        self.review_auto_labels_btn.setToolTip("Review and manage auto-labeled transactions")
        training_button_layout.addWidget(self.review_auto_labels_btn)

        training_layout.addLayout(training_button_layout)

        # Auto-labeling statistics
        self.auto_label_stats = QLabel("Auto-labeling statistics will appear here")
        self.auto_label_stats.setStyleSheet("padding: 10px; background-color: #f0f0f0; border-radius: 5px;")
        training_layout.addWidget(self.auto_label_stats)

        layout.addWidget(training_group)

        # Transaction Session Management Section
        session_group = QGroupBox("Transaction Session Management")
        session_layout = QVBoxLayout(session_group)

        # Session management buttons
        session_button_layout = QHBoxLayout()

        self.clear_current_data_btn = QPushButton("🗑️ Clear Current Data")
        self.clear_current_data_btn.clicked.connect(self.clear_current_transaction_data)
        self.clear_current_data_btn.setToolTip("Clear current transaction data with automatic backup")
        session_button_layout.addWidget(self.clear_current_data_btn)

        self.manage_sessions_btn = QPushButton("📁 Manage Sessions")
        self.manage_sessions_btn.clicked.connect(self.open_session_management)
        self.manage_sessions_btn.setToolTip("View, load, and manage stored transaction sessions")
        session_button_layout.addWidget(self.manage_sessions_btn)

        self.create_session_btn = QPushButton("💾 Save as Session")
        self.create_session_btn.clicked.connect(self.create_transaction_session)
        self.create_session_btn.setToolTip("Save current transaction data as a new session")
        session_button_layout.addWidget(self.create_session_btn)

        session_layout.addLayout(session_button_layout)

        # Session status
        self.session_status = QLabel("No active session")
        self.session_status.setStyleSheet("padding: 10px; background-color: #f0f0f0; border-radius: 5px;")
        session_layout.addWidget(self.session_status)

        layout.addWidget(session_group)

        self.tab_widget.addTab(export_widget, "Phase 4: Export")

    def set_transactions(self, raw_transactions, processed_transactions=None):
        """Set transactions from main app (no CSV needed)"""

        # Store the raw data immediately for quick UI response
        self._pending_raw_transactions = raw_transactions
        self._pending_processed_transactions = processed_transactions or []

        # Update status to show we're processing
        if hasattr(self, 'status_bar'):
            self.status_bar.setText("Processing transactions...")
            self.status_bar.setStyleSheet("QLabel { color: #1976d2; font-style: italic; padding: 5px; }")

        # Process transactions asynchronously to avoid UI blocking
        QTimer.singleShot(50, self._process_transactions_async)

    def _process_transactions_async(self):
        """Process transactions asynchronously to avoid blocking UI"""
        # For immediate UI response, skip heavy validation and use transactions as-is
        # We can do validation later when components are ready
        validated_transactions = self._pending_raw_transactions
        parsing_issues = []

        # Check if this is new data or an update to existing data
        is_new_data = not hasattr(self, 'raw_transactions') or not self.raw_transactions

        # Skip heavy session detection for now - do it later when components are ready
        # This ensures immediate UI response

        # Set the transactions immediately for basic UI functionality
        self.raw_transactions = validated_transactions
        self.processed_transactions = self._pending_processed_transactions

        # For session management, defer to when components are ready
        if self._components_initialized:
            self._handle_session_management(validated_transactions, is_new_data)
        else:
            # Store for later processing when components are ready
            self._pending_session_data = {
                'validated_transactions': validated_transactions,
                'is_new_data': is_new_data
            }

        # Complete the basic setup
        self._complete_transaction_setup(validated_transactions)

    def _handle_session_management(self, validated_transactions, is_new_data):
        """Handle session management when components are ready"""
        # Check for existing session
        existing_session_id = self.find_existing_session(validated_transactions)

        if existing_session_id and is_new_data:
            # Ask user if they want to restore the previous session
            reply = QMessageBox.question(self, "Previous Session Found",
                                       f"Found a previous categorization session for these transactions.\n\n"
                                       f"Would you like to restore your previous work?\n\n"
                                       f"• Yes: Continue from where you left off\n"
                                       f"• No: Start fresh categorization",
                                       QMessageBox.Yes | QMessageBox.No,
                                       QMessageBox.Yes)

            if reply == QMessageBox.Yes:
                if self.load_session(existing_session_id):
                    self.session_id = existing_session_id
                    self.session_file = self.sessions_dir / f"{existing_session_id}.json"
                    return  # Session loaded, no need to continue with fresh setup

        # Create new session or update existing one
        if is_new_data:
            self.session_id = self.generate_session_id(validated_transactions)
            self.session_file = self.sessions_dir / f"{self.session_id}.json"

    def _complete_transaction_setup(self, validated_transactions):
        """Complete the basic transaction setup without heavy operations"""
        # Update status
        count = len(validated_transactions)

        status_msg = f"Loaded {count} transactions from main application"
        self.ml_status_label.setText(status_msg)

        # Update status bar
        if hasattr(self, 'status_bar'):
            self.status_bar.setText(f"✅ {count} transactions loaded")
            self.status_bar.setStyleSheet("QLabel { color: #2e7d32; font-style: italic; padding: 5px; }")

        # Only enable ML button if components are ready
        if self._components_initialized:
            self.ml_process_btn.setEnabled(True)

    def _process_pending_session_data(self):
        """Process pending session data when components are ready"""
        if hasattr(self, '_pending_session_data'):
            session_data = self._pending_session_data
            self._handle_session_management(
                session_data['validated_transactions'],
                session_data['is_new_data']
            )
            delattr(self, '_pending_session_data')

        # Optionally run background validation when components are ready
        if hasattr(self, 'raw_transactions') and self.raw_transactions:
            QTimer.singleShot(1000, self._run_background_validation)  # Delay validation

    def _run_background_validation(self):
        """Run transaction validation in background when components are ready"""
        try:
            if not hasattr(self, 'raw_transactions') or not self.raw_transactions:
                return

            # Quick validation check - don't block UI
            issues_found = 0
            for i, txn in enumerate(self.raw_transactions[:10]):  # Check only first 10
                try:
                    amount_val = float(txn.get('amount', 0))
                    if abs(amount_val) > 1000000:  # Suspiciously large
                        issues_found += 1
                except:
                    issues_found += 1

            if issues_found > 0:
                self.logger.info(f"Background validation found {issues_found} potential issues in first 10 transactions")

        except Exception as e:
            self.logger.debug(f"Background validation error: {e}")  # Don't show to user



    def reset_categorization_data(self):
        """Reset all categorization data to start fresh"""
        # Clear all transaction data lists
        self.categorized_transactions = []
        self.uncategorized_transactions = []
        self.ai_categorized_transactions = []
        self.manual_categorized_transactions = []
        self.manual_transactions_data = []
        self.filtered_manual_transactions = []

        # Clear any cached or temporary data
        if hasattr(self, 'ai_uncategorized_transactions'):
            self.ai_uncategorized_transactions = []
        if hasattr(self, 'export_transactions'):
            self.export_transactions = []

        # Clear tables
        if hasattr(self, 'categorized_table'):
            self.categorized_table.setRowCount(0)
            self.categorized_table.clearContents()
        if hasattr(self, 'uncategorized_table'):
            self.uncategorized_table.setRowCount(0)
            self.uncategorized_table.clearContents()
        if hasattr(self, 'ai_categorized_table'):
            self.ai_categorized_table.setRowCount(0)
            self.ai_categorized_table.clearContents()
        if hasattr(self, 'manual_table'):
            self.manual_table.setRowCount(0)
            self.manual_table.clearContents()

        # Clear count labels
        if hasattr(self, 'categorized_count_label'):
            self.categorized_count_label.setText("Count: 0")
        if hasattr(self, 'uncategorized_count_label'):
            self.uncategorized_count_label.setText("Count: 0")
        if hasattr(self, 'ai_categorized_count_label'):
            self.ai_categorized_count_label.setText("Count: 0")
        if hasattr(self, 'manual_count_label'):
            self.manual_count_label.setText("Count: 0")

        # Clear summary labels
        if hasattr(self, 'ml_summary_label'):
            self.ml_summary_label.setText("No transaction data loaded")
        if hasattr(self, 'ai_summary_label'):
            self.ai_summary_label.setText("No transaction data loaded")
        if hasattr(self, 'manual_summary_label'):
            self.manual_summary_label.setText("No transaction data loaded")

        # Clear progress bars
        if hasattr(self, 'ml_progress'):
            self.ml_progress.setValue(0)
        if hasattr(self, 'ai_progress'):
            self.ai_progress.setValue(0)

        # Reset UI states
        if hasattr(self, 'ml_process_btn'):
            self.ml_process_btn.setEnabled(False)  # Disabled until data is loaded
        if hasattr(self, 'ai_process_btn'):
            self.ai_process_btn.setEnabled(False)
        if hasattr(self, 'save_manual_btn'):
            self.save_manual_btn.setEnabled(False)

        self.logger.info("Reset all categorization data for fresh start")

    def refresh_all_tabs(self):
        """Refresh all tab displays to ensure UI is properly cleared"""
        try:
            # Force repaint of all tables
            if hasattr(self, 'categorized_table'):
                self.categorized_table.viewport().update()
            if hasattr(self, 'uncategorized_table'):
                self.uncategorized_table.viewport().update()
            if hasattr(self, 'ai_categorized_table'):
                self.ai_categorized_table.viewport().update()
            if hasattr(self, 'manual_table'):
                self.manual_table.viewport().update()

            # Update tab widget
            if hasattr(self, 'tab_widget'):
                self.tab_widget.repaint()

            self.logger.debug("Refreshed all tab displays")

        except Exception as e:
            self.logger.error(f"Error refreshing tabs: {str(e)}")

    def validate_transaction_pipeline(self, phase="unknown"):
        """Validate the transaction processing pipeline and report issues"""
        validation_report = {
            "phase": phase,
            "timestamp": datetime.now().isoformat(),
            "issues": [],
            "warnings": [],
            "statistics": {}
        }

        try:
            # Check raw transactions
            raw_count = len(self.raw_transactions) if hasattr(self, 'raw_transactions') else 0
            validation_report["statistics"]["raw_transactions"] = raw_count

            if raw_count == 0:
                validation_report["issues"].append("No raw transactions loaded")
                return validation_report

            # Check categorization data
            categorized_count = len(self.categorized_transactions) if hasattr(self, 'categorized_transactions') else 0
            uncategorized_count = len(self.uncategorized_transactions) if hasattr(self, 'uncategorized_transactions') else 0
            ai_count = len(self.ai_categorized_transactions) if hasattr(self, 'ai_categorized_transactions') else 0
            manual_count = len(self.manual_categorized_transactions) if hasattr(self, 'manual_categorized_transactions') else 0

            validation_report["statistics"].update({
                "categorized": categorized_count,
                "uncategorized": uncategorized_count,
                "ai_categorized": ai_count,
                "manual_categorized": manual_count,
                "total_processed": categorized_count + uncategorized_count + ai_count + manual_count
            })

            # Check for data consistency issues
            total_processed = validation_report["statistics"]["total_processed"]

            if phase == "after_ml" and total_processed == 0:
                validation_report["issues"].append("ML processing completed but no transactions were processed")

            if phase == "after_ai" and ai_count == 0 and uncategorized_count > 0:
                validation_report["warnings"].append(f"AI processing completed but {uncategorized_count} transactions remain uncategorized")

            if phase == "manual_loading":
                manual_data_count = len(self.manual_transactions_data) if hasattr(self, 'manual_transactions_data') else 0
                if manual_data_count == 0 and uncategorized_count > 0:
                    validation_report["issues"].append(f"Manual loading found no transactions but {uncategorized_count} uncategorized transactions exist")
                elif manual_data_count != uncategorized_count:
                    validation_report["warnings"].append(f"Manual data count ({manual_data_count}) differs from uncategorized count ({uncategorized_count})")

            # Check for duplicate transactions
            all_transactions = []
            if hasattr(self, 'categorized_transactions'):
                all_transactions.extend(self.categorized_transactions)
            if hasattr(self, 'uncategorized_transactions'):
                all_transactions.extend(self.uncategorized_transactions)
            if hasattr(self, 'ai_categorized_transactions'):
                all_transactions.extend(self.ai_categorized_transactions)
            if hasattr(self, 'manual_categorized_transactions'):
                all_transactions.extend(self.manual_categorized_transactions)

            # Check for duplicates based on description and amount
            seen = set()
            duplicates = 0
            for txn in all_transactions:
                key = (txn.get('description', ''), txn.get('amount', 0))
                if key in seen:
                    duplicates += 1
                else:
                    seen.add(key)

            if duplicates > 0:
                validation_report["warnings"].append(f"Found {duplicates} potential duplicate transactions")

            # Log validation results
            if validation_report["issues"]:
                self.logger.error(f"Pipeline validation issues in {phase}: {validation_report['issues']}")
            if validation_report["warnings"]:
                self.logger.warning(f"Pipeline validation warnings in {phase}: {validation_report['warnings']}")

            self.logger.info(f"Pipeline validation for {phase}: {validation_report['statistics']}")

        except Exception as e:
            validation_report["issues"].append(f"Validation error: {str(e)}")
            self.logger.error(f"Error during pipeline validation: {str(e)}", exc_info=True)

        return validation_report

    def show_validation_report(self):
        """Show a comprehensive validation report to the user"""
        try:
            validation_report = self.validate_transaction_pipeline("user_requested")

            # Create detailed report
            report_text = f"""
<h3>Transaction Processing Pipeline Validation Report</h3>
<p><b>Generated:</b> {validation_report['timestamp']}</p>
<p><b>Phase:</b> {validation_report['phase']}</p>

<h4>📊 Statistics:</h4>
<ul>
"""

            stats = validation_report.get('statistics', {})
            for key, value in stats.items():
                report_text += f"<li><b>{key.replace('_', ' ').title()}:</b> {value}</li>"

            report_text += "</ul>"

            # Add issues if any
            if validation_report.get('issues'):
                report_text += "<h4>❌ Issues:</h4><ul>"
                for issue in validation_report['issues']:
                    report_text += f"<li>{issue}</li>"
                report_text += "</ul>"

            # Add warnings if any
            if validation_report.get('warnings'):
                report_text += "<h4>⚠️ Warnings:</h4><ul>"
                for warning in validation_report['warnings']:
                    report_text += f"<li>{warning}</li>"
                report_text += "</ul>"

            # Overall status
            if not validation_report.get('issues') and not validation_report.get('warnings'):
                report_text += "<h4>✅ Status: All checks passed</h4>"
            elif validation_report.get('issues'):
                report_text += "<h4>❌ Status: Issues detected that need attention</h4>"
            else:
                report_text += "<h4>⚠️ Status: Minor warnings detected</h4>"

            # Show report
            msg = QMessageBox(self)
            msg.setWindowTitle("Pipeline Validation Report")
            msg.setTextFormat(Qt.RichText)
            msg.setText(report_text)
            msg.setStandardButtons(QMessageBox.Ok)
            msg.exec()

        except Exception as e:
            QMessageBox.critical(self, "Validation Error",
                               f"Failed to generate validation report:\n{str(e)}")
            self.logger.error(f"Error generating validation report: {str(e)}", exc_info=True)

    def backup_training_data(self):
        """Create a backup of current training data"""
        try:
            # Get description from user
            description, ok = QInputDialog.getText(
                self, "Backup Training Data",
                "Enter a description for this backup:",
                text=f"Manual backup created on {datetime.now().strftime('%Y-%m-%d %H:%M')}"
            )

            if not ok:
                return

            # Create backup
            backup_id = self.enhanced_training_manager.create_training_data_backup(description)

            QMessageBox.information(
                self, "Backup Created",
                f"Training data backup created successfully!\n\nBackup ID: {backup_id}"
            )

            self.logger.info(f"Training data backup created: {backup_id}")

        except Exception as e:
            self.logger.error(f"Error creating training data backup: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "Backup Error", f"Failed to create backup:\n{str(e)}")

    def clear_manual_interface(self):
        """Clear manual labeling interface while preserving trained model"""
        try:
            reply = QMessageBox.question(
                self, "Clear Manual Interface",
                "This will clear all transactions from the manual labeling interface.\n"
                "A backup will be created automatically.\n"
                "The trained model and its patterns will be preserved.\n\n"
                "Are you sure you want to continue?",
                QMessageBox.Yes | QMessageBox.No, QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # Clear interface
            success = self.enhanced_training_manager.clear_manual_labeling_interface(create_backup=True)

            if success:
                # Clear UI tables
                self.manual_table.setRowCount(0)
                self.manual_transactions_data = []
                self.filtered_manual_transactions = []

                # Update status
                self.manual_status_label.setText("Manual labeling interface cleared")

                QMessageBox.information(
                    self, "Interface Cleared",
                    "Manual labeling interface has been cleared successfully.\n"
                    "A backup was created automatically."
                )

                self.logger.info("Manual labeling interface cleared")
            else:
                QMessageBox.warning(self, "Clear Failed", "Failed to clear manual labeling interface.")

        except Exception as e:
            self.logger.error(f"Error clearing manual interface: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "Clear Error", f"Failed to clear interface:\n{str(e)}")

    def auto_label_transactions(self):
        """Apply auto-labeling to current transactions"""
        try:
            if not self.raw_transactions:
                QMessageBox.warning(self, "No Data", "No transactions loaded to auto-label.")
                return

            reply = QMessageBox.question(
                self, "Auto-Label Transactions",
                f"This will apply auto-labeling to {len(self.raw_transactions)} transactions "
                f"based on the trained model's knowledge.\n\n"
                f"Transactions with high similarity and confidence will be automatically categorized.\n"
                f"You can review and modify the results afterwards.\n\n"
                f"Continue?",
                QMessageBox.Yes | QMessageBox.No, QMessageBox.Yes
            )

            if reply != QMessageBox.Yes:
                return

            # Show progress
            progress = QProgressDialog("Processing transactions for auto-labeling...", "Cancel", 0, 100, self)
            progress.setWindowModality(Qt.WindowModal)
            progress.show()

            # Process transactions
            results = self.enhanced_training_manager.process_new_transactions_with_auto_labeling(
                self.raw_transactions
            )

            progress.close()

            # Update statistics display
            self.update_auto_label_statistics()

            # Show results
            QMessageBox.information(
                self, "Auto-Labeling Complete",
                f"Auto-labeling completed!\n\n"
                f"Total processed: {results['total_processed']}\n"
                f"Auto-labeled: {results['auto_labeled']}\n"
                f"Manual review required: {results['manual_required']}\n"
                f"Processing time: {results['processing_time']:.2f} seconds\n\n"
                f"Use 'Review Auto-Labels' to review and confirm the results."
            )

            self.logger.info(f"Auto-labeling complete: {results}")

        except Exception as e:
            self.logger.error(f"Error in auto-labeling: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "Auto-Label Error", f"Auto-labeling failed:\n{str(e)}")

    def open_auto_label_review(self):
        """Open the auto-label review dialog"""
        try:
            # Check if there are auto-labeled transactions to review
            pending_count = len(self.enhanced_training_manager.get_pending_auto_labeled_transactions())

            if pending_count == 0:
                QMessageBox.information(
                    self, "No Auto-Labels",
                    "No auto-labeled transactions found for review.\n\n"
                    "Use 'Auto-Label New Data' first to generate auto-labeled transactions."
                )
                return

            # Import the dialog here to avoid circular imports
            from .auto_label_review_dialog import AutoLabelReviewDialog

            # Open review dialog
            dialog = AutoLabelReviewDialog(self.enhanced_training_manager, self)
            dialog.exec()

            # Update statistics after review
            self.update_auto_label_statistics()

        except Exception as e:
            self.logger.error(f"Error opening auto-label review: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "Review Error", f"Failed to open auto-label review:\n{str(e)}")

    def update_auto_label_statistics(self):
        """Update auto-labeling statistics display"""
        try:
            stats = self.enhanced_training_manager.get_auto_labeling_statistics()

            stats_text = f"""
Auto-Labeling Statistics:
• Total Auto-Labeled: {stats.get('total_auto_labeled', 0)}
• Pending Review: {stats.get('pending_review', 0)}
• Confirmed: {stats.get('status_breakdown', {}).get('confirmed', 0)}
• Modified: {stats.get('status_breakdown', {}).get('modified', 0)}
• Rejected: {stats.get('status_breakdown', {}).get('rejected', 0)}
• Accuracy: {stats.get('accuracy', 0):.1%}
• Avg Confidence: {stats.get('average_confidence', 0):.1%}
• Avg Similarity: {stats.get('average_similarity', 0):.1%}
            """.strip()

            self.auto_label_stats.setText(stats_text)

        except Exception as e:
            self.logger.error(f"Error updating auto-label statistics: {str(e)}", exc_info=True)
            self.auto_label_stats.setText("Error loading statistics")

    def _handle_similar_sessions_detected(self, new_transactions, similar_sessions):
        """Handle detection of similar sessions"""
        try:
            # Create dialog for handling similar sessions
            from .session_management_dialog import SessionManagementDialog

            # Show information about similar sessions
            session_info = []
            for session_data in similar_sessions[:3]:  # Show top 3 matches
                session = session_data["session"]
                similarity = session_data["similarity_score"]
                session_info.append(f"• {session.session_id[-12:]}... ({similarity:.1%} similar, {session.transaction_count} transactions)")

            message = f"Found {len(similar_sessions)} similar transaction sessions:\n\n"
            message += "\n".join(session_info)
            message += f"\n\nWhat would you like to do with the new {len(new_transactions)} transactions?"

            # Create custom dialog with options
            reply = QMessageBox.question(
                self, "Similar Sessions Detected", message,
                QMessageBox.Yes | QMessageBox.No | QMessageBox.Cancel,
                QMessageBox.Yes
            )

            if reply == QMessageBox.Yes:
                # Open session management dialog
                self.open_session_management()
            elif reply == QMessageBox.No:
                # Continue with new data, create new session
                self._create_new_session_with_transactions(new_transactions)
            # Cancel: do nothing, keep current data

        except Exception as e:
            self.logger.error(f"Error handling similar sessions: {str(e)}", exc_info=True)
            # Fallback to normal processing
            self.raw_transactions = new_transactions

    def clear_current_transaction_data(self):
        """Clear current transaction data with automatic backup"""
        try:
            # Check current state
            dialog_raw_count = len(self.raw_transactions) if hasattr(self, 'raw_transactions') and self.raw_transactions else 0
            dialog_processed_count = len(self.processed_transactions) if hasattr(self, 'processed_transactions') and self.processed_transactions else 0

            # Check parent window state
            parent_window = self.parent()
            parent_raw_count = 0
            parent_processed_count = 0

            if parent_window:
                if hasattr(parent_window, 'raw_transactions'):
                    parent_raw_count = len(parent_window.raw_transactions) if parent_window.raw_transactions else 0

                if hasattr(parent_window, 'processed_transactions'):
                    parent_processed_count = len(parent_window.processed_transactions) if parent_window.processed_transactions else 0

            total_count = max(dialog_raw_count, dialog_processed_count, parent_raw_count, parent_processed_count)

            if total_count == 0:
                QMessageBox.information(self, "No Data", "No transaction data to clear.")
                return

            reply = QMessageBox.question(
                self, "Clear Transaction Data",
                f"This will clear all current transaction data ({total_count} transactions).\n\n"
                f"A backup will be created automatically before clearing.\n"
                f"All categorization work will be preserved in the backup.\n\n"
                f"Are you sure you want to continue?",
                QMessageBox.Yes | QMessageBox.No, QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # Create session from current data before clearing
            if self.transaction_data_manager.current_session:
                backup_id = self.transaction_data_manager.backup_current_session("Pre-clear backup")
                self.logger.info(f"Created backup before clearing: {backup_id}")

            # Clear current session
            success = self.transaction_data_manager.clear_current_session(
                create_backup=True, archive_session=True
            )

            if success:
                # Clear UI data
                old_raw_count = len(self.raw_transactions) if self.raw_transactions else 0
                old_processed_count = len(self.processed_transactions) if self.processed_transactions else 0

                self.raw_transactions = []
                self.processed_transactions = []

                self.reset_categorization_data()

                # Also clear parent window's transaction data to prevent reloading
                try:
                    parent_window = self.parent()
                    if parent_window:
                        # Clear raw_transactions
                        if hasattr(parent_window, 'raw_transactions'):
                            parent_window.raw_transactions = []

                        # Clear processed_transactions
                        if hasattr(parent_window, 'processed_transactions'):
                            parent_window.processed_transactions = []

                except Exception as e:
                    self.logger.error(f"Error clearing parent window data: {str(e)}", exc_info=True)

                # Update UI status labels
                self.ml_status_label.setText("No transaction data loaded")

                # Force UI refresh by updating all tabs
                self.refresh_all_tabs()

                # Update session status
                self.update_session_status()

                # Reset tab to first tab
                if hasattr(self, 'tab_widget'):
                    self.tab_widget.setCurrentIndex(0)

                QMessageBox.information(
                    self, "Data Cleared",
                    "Transaction data has been cleared successfully.\n"
                    "A backup was created and the session was archived."
                )

            else:
                QMessageBox.warning(self, "Clear Failed", "Failed to clear transaction data.")

        except Exception as e:
            self.logger.error(f"Error clearing transaction data: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "Clear Error", f"Failed to clear data:\n{str(e)}")

    def open_session_management(self):
        """Open the session management dialog"""
        try:
            from .session_management_dialog import SessionManagementDialog

            dialog = SessionManagementDialog(self.transaction_data_manager, self)
            dialog.session_selected.connect(self.load_session_data)
            dialog.exec()

        except Exception as e:
            self.logger.error(f"Error opening session management: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "Session Management Error", f"Failed to open session management:\n{str(e)}")

    def create_transaction_session(self):
        """Create a new transaction session from current data"""
        try:
            if not hasattr(self, 'raw_transactions') or not self.raw_transactions:
                QMessageBox.warning(self, "No Data", "No transaction data to save as session.")
                return

            # Get session description from user
            description, ok = QInputDialog.getText(
                self, "Create Session",
                "Enter a description for this session:",
                text=f"Session created on {datetime.now().strftime('%Y-%m-%d %H:%M')}"
            )

            if not ok:
                return

            # Create session
            session_id = self.transaction_data_manager.create_session(
                self.raw_transactions,
                getattr(self, 'processed_transactions', []),
                description
            )

            # Update session status
            self.update_session_status()

            QMessageBox.information(
                self, "Session Created",
                f"Transaction session created successfully!\n\nSession ID: {session_id}"
            )

            self.logger.info(f"Created transaction session: {session_id}")

        except Exception as e:
            self.logger.error(f"Error creating transaction session: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "Session Creation Error", f"Failed to create session:\n{str(e)}")

    def load_session_data(self, session_id: str, raw_transactions, processed_transactions):
        """Load session data into the interface"""
        try:
            # Set transactions
            self.raw_transactions = raw_transactions
            self.processed_transactions = processed_transactions

            # Reset categorization data
            self.reset_categorization_data()

            # Update UI
            self.ml_status_label.setText(f"Loaded {len(raw_transactions)} transactions from session {session_id[-12:]}...")
            self.ml_process_btn.setEnabled(True)

            # Update session status
            self.update_session_status()

            self.logger.info(f"Loaded session data: {session_id} ({len(raw_transactions)} transactions)")

        except Exception as e:
            self.logger.error(f"Error loading session data: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "Session Load Error", f"Failed to load session data:\n{str(e)}")

    def _create_new_session_with_transactions(self, transactions):
        """Create a new session with the provided transactions"""
        try:
            # Create session
            session_id = self.transaction_data_manager.create_session(
                transactions,
                description=f"Auto-created session on {datetime.now().strftime('%Y-%m-%d %H:%M')}"
            )

            # Set transactions
            self.raw_transactions = transactions
            self.processed_transactions = []

            # Update UI
            self.ml_status_label.setText(f"Loaded {len(transactions)} transactions (new session)")
            self.ml_process_btn.setEnabled(True)

            # Update session status
            self.update_session_status()

            self.logger.info(f"Created new session with transactions: {session_id}")

        except Exception as e:
            self.logger.error(f"Error creating new session: {str(e)}", exc_info=True)

    def update_session_status(self):
        """Update session status display"""
        try:
            current_session = self.transaction_data_manager.current_session

            if current_session:
                status_text = f"""
Current Session: {current_session.session_id[-12:]}...
Status: {current_session.status.value.title()}
Transactions: {current_session.transaction_count}
Created: {current_session.created_at.strftime('%Y-%m-%d %H:%M')}
                """.strip()
            else:
                status_text = "No active session"

            self.session_status.setText(status_text)

        except Exception as e:
            self.logger.error(f"Error updating session status: {str(e)}", exc_info=True)
            self.session_status.setText("Error loading session status")

    def on_skip_ai_changed(self, state):
        """Handle skip AI checkbox state change"""
        skip_ai = state == 2  # Qt.Checked

        # Enable/disable AI processing button and skip button
        if skip_ai:
            self.ai_process_btn.setEnabled(False)
            self.skip_phase2_btn.setEnabled(False)
            self.skip_ai_message.setVisible(True)

            # If there are uncategorized transactions, enable manual categorization
            if self.uncategorized_transactions:
                self.load_manual_btn.setEnabled(True)
                self.manual_status_label.setText(f"AI processing skipped - {len(self.uncategorized_transactions)} transactions ready for manual categorization")
        else:
            # Re-enable if there are uncategorized transactions
            if self.uncategorized_transactions:
                self.ai_process_btn.setEnabled(True)
                self.skip_phase2_btn.setEnabled(True)
            self.skip_ai_message.setVisible(False)

    def process_ml_categorization(self):
        """Process transactions using ML model"""
        if not self.raw_transactions:
            QMessageBox.warning(self, "No Data", "No transactions to process")
            return

        # Ensure components are ready
        if not self._ensure_components_ready():
            return

        if not self.ml_categorizer.is_trained:
            QMessageBox.critical(self, "Model Error", "ML model is not trained")
            return

        # Update configuration
        self.ml_confidence_threshold = self.confidence_spinbox.value()

        # Disable button and clear tables
        self.ml_process_btn.setEnabled(False)
        self.categorized_table.setRowCount(0)
        self.uncategorized_table.setRowCount(0)

        # Reset data
        self.categorized_transactions = []
        self.uncategorized_transactions = []

        # Start processing thread
        self.ml_processing_thread = MLProcessingThread(
            self.raw_transactions,
            self.ml_categorizer,
            self.ml_confidence_threshold
        )

        # Connect signals
        self.ml_processing_thread.progress_updated.connect(self.on_ml_progress_updated)
        self.ml_processing_thread.transaction_processed.connect(self.on_ml_transaction_processed)
        self.ml_processing_thread.processing_completed.connect(self.on_ml_processing_completed)
        self.ml_processing_thread.error_occurred.connect(self.on_ml_error)

        # Start thread
        self.ml_processing_thread.start()

        self.logger.info(f"Started ML processing of {len(self.raw_transactions)} transactions")

    def on_ml_progress_updated(self, progress, status):
        """Handle ML progress updates"""
        self.ml_progress.setValue(progress)
        self.ml_status_label.setText(status)

    def on_ml_transaction_processed(self, transaction_data, status):
        """Handle processed transaction"""
        # Add to appropriate list
        if status == "Categorized":
            self.categorized_transactions.append(transaction_data)
            target_table = self.categorized_table
        else:
            self.uncategorized_transactions.append(transaction_data)
            target_table = self.uncategorized_table

        # Add to appropriate table
        row = target_table.rowCount()
        target_table.insertRow(row)

        # Format description - don't truncate, let column width handle it
        desc = transaction_data['description'].strip()

        # Format amount properly
        amount = transaction_data['amount']
        if isinstance(amount, (int, float)):
            amount_str = f"₹{amount:,.2f}"
        else:
            # Try to parse string amount
            try:
                amount_val = float(str(amount).replace(',', '').replace('₹', '').strip())
                amount_str = f"₹{amount_val:,.2f}"
            except:
                amount_str = str(amount)

        # Format date
        date_str = str(transaction_data['date'])
        if len(date_str) > 10:  # If it's a full datetime, just show date part
            date_str = date_str[:10]

        # Get transaction type/mode
        transaction_type = transaction_data.get('transaction_type', 'Unknown')

        # Create table items with proper formatting
        desc_item = QTableWidgetItem(desc)
        desc_item.setToolTip(desc)  # Full description on hover

        amount_item = QTableWidgetItem(amount_str)
        amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)

        date_item = QTableWidgetItem(date_str)
        date_item.setTextAlignment(Qt.AlignCenter)

        mode_item = QTableWidgetItem(transaction_type)
        mode_item.setTextAlignment(Qt.AlignCenter)

        category_item = QTableWidgetItem(transaction_data['category'] or "N/A")

        confidence_item = QTableWidgetItem(f"{transaction_data['confidence']:.3f}")
        confidence_item.setTextAlignment(Qt.AlignCenter)

        status_item = QTableWidgetItem(status)
        status_item.setTextAlignment(Qt.AlignCenter)

        # Set items in table
        target_table.setItem(row, 0, desc_item)
        target_table.setItem(row, 1, amount_item)
        target_table.setItem(row, 2, date_item)
        target_table.setItem(row, 3, mode_item)
        target_table.setItem(row, 4, category_item)
        target_table.setItem(row, 5, confidence_item)
        target_table.setItem(row, 6, status_item)

        # Update count labels
        self.categorized_count_label.setText(f"Count: {len(self.categorized_transactions)}")
        self.uncategorized_count_label.setText(f"Count: {len(self.uncategorized_transactions)}")

    def on_ml_processing_completed(self, categorized_count, uncategorized_count):
        """Handle ML processing completion"""
        # Update summary
        summary = f"ML Results: {categorized_count} categorized, {uncategorized_count} need further processing"
        self.ml_summary_label.setText(summary)

        # Enable Phase 2 (AI) tab
        self.tab_widget.setTabEnabled(1, True)  # Enable AI tab

        if uncategorized_count > 0:
            # Populate AI uncategorized table with transactions from Phase 1
            self.populate_ai_uncategorized_table()

            # Enable AI processing if there are uncategorized transactions and AI is not skipped
            if not self.skip_ai_checkbox.isChecked():
                self.ai_process_btn.setEnabled(True)
                estimated_cost = uncategorized_count * 0.001  # Rough estimate
                self.cost_label.setText(f"Estimated Cost: ${estimated_cost:.2f}")

            # Enable skip Phase 2 button
            self.skip_phase2_btn.setEnabled(True)

            # Update AI status
            self.ai_status_label.setText(f"Ready for AI processing - {uncategorized_count} uncategorized transactions")

            # Enable manual categorization
            self.load_manual_btn.setEnabled(True)
            self.manual_status_label.setText(f"Ready for manual categorization - {uncategorized_count} transactions need categorization")
        else:
            # All transactions categorized by ML
            self.ai_status_label.setText("All transactions categorized by ML - AI processing not needed")
            self.manual_status_label.setText("All transactions categorized - manual categorization not needed")

            # Enable export directly
            self.export_btn.setEnabled(True)

        # Enable Phase 3 (Manual) tab
        self.tab_widget.setTabEnabled(2, True)  # Enable Manual tab

        # Re-enable ML button
        self.ml_process_btn.setEnabled(True)

        self.logger.info(f"ML processing complete: {categorized_count} categorized, {uncategorized_count} uncategorized")

        # Validate pipeline after ML processing
        validation_report = self.validate_transaction_pipeline("after_ml")
        if validation_report["issues"]:
            QMessageBox.warning(self, "Processing Issues",
                              f"ML processing completed with issues:\n\n" +
                              "\n".join(validation_report["issues"]))

        # Save session after ML processing
        self.save_session()

    def on_ml_error(self, error_message):
        """Handle ML processing error"""
        QMessageBox.critical(self, "Processing Error", f"ML processing failed: {error_message}")
        self.ml_process_btn.setEnabled(True)
        self.logger.error(f"ML processing error: {error_message}")

    def process_ai_categorization(self):
        """Process uncategorized transactions using AI"""
        if not self.uncategorized_transactions:
            QMessageBox.information(self, "No Data", "No uncategorized transactions to process")
            return

        # Confirm cost
        estimated_cost = len(self.uncategorized_transactions) * 0.001
        reply = QMessageBox.question(self, "Confirm AI Processing",
                                   f"Process {len(self.uncategorized_transactions)} transactions with AI?\n"
                                   f"Estimated cost: ${estimated_cost:.2f}",
                                   QMessageBox.Yes | QMessageBox.No)

        if reply != QMessageBox.Yes:
            return

        # Simulate AI processing (replace with actual AI implementation)
        self.ai_process_btn.setEnabled(False)
        self.ai_progress.setVisible(True)
        self.ai_progress.setRange(0, len(self.uncategorized_transactions))

        ai_categorized_count = 0
        still_uncategorized = []

        for i, txn_data in enumerate(self.uncategorized_transactions):
            self.ai_progress.setValue(i + 1)

            # Simulate AI categorization (replace with actual AI call)
            # For demo: categorize transactions with "UPI" or "ATM" in description
            desc_lower = txn_data['description'].lower()
            ai_category = None

            if 'upi' in desc_lower:
                ai_category = 'Digital Payments'
            elif 'atm' in desc_lower:
                ai_category = 'ATM'
            elif 'transfer' in desc_lower:
                ai_category = 'Bank Transfer'

            if ai_category:
                # Add to AI categorized
                txn_data['category'] = ai_category
                txn_data['method'] = 'AI'
                txn_data['confidence'] = 0.90
                self.ai_categorized_transactions.append(txn_data)
                self.add_to_ai_categorized_table(txn_data, ai_category)
                ai_categorized_count += 1
            else:
                # Still uncategorized
                still_uncategorized.append(txn_data)

        # Update uncategorized list
        self.uncategorized_transactions = still_uncategorized

        # Clear and repopulate AI uncategorized table
        self.ai_uncategorized_table.setRowCount(0)
        if still_uncategorized:
            for txn_data in still_uncategorized:
                row = self.ai_uncategorized_table.rowCount()
                self.ai_uncategorized_table.insertRow(row)

                # Populate row (similar to populate_ai_uncategorized_table)
                desc_item = QTableWidgetItem(txn_data['description'])
                self.ai_uncategorized_table.setItem(row, 0, desc_item)

                amount_str = f"₹{txn_data['amount']:,.2f}"
                amount_item = QTableWidgetItem(amount_str)
                amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                self.ai_uncategorized_table.setItem(row, 1, amount_item)

                date_item = QTableWidgetItem(str(txn_data['date'])[:10])
                date_item.setTextAlignment(Qt.AlignCenter)
                self.ai_uncategorized_table.setItem(row, 2, date_item)

                mode_item = QTableWidgetItem(txn_data.get('transaction_type', 'Unknown'))
                mode_item.setTextAlignment(Qt.AlignCenter)
                self.ai_uncategorized_table.setItem(row, 3, mode_item)

                category_item = QTableWidgetItem("N/A")
                self.ai_uncategorized_table.setItem(row, 4, category_item)

                confidence_item = QTableWidgetItem(f"{txn_data['confidence']:.3f}")
                confidence_item.setTextAlignment(Qt.AlignCenter)
                self.ai_uncategorized_table.setItem(row, 5, confidence_item)

                status_item = QTableWidgetItem("Still Uncategorized")
                status_item.setTextAlignment(Qt.AlignCenter)
                self.ai_uncategorized_table.setItem(row, 6, status_item)

        # Update counts
        self.ai_uncategorized_count_label.setText(f"Count: {len(still_uncategorized)}")

        # Hide progress and re-enable button
        self.ai_progress.setVisible(False)
        self.ai_process_btn.setEnabled(True)

        # Update status
        self.ai_status_label.setText(f"AI processing complete - {ai_categorized_count} categorized, {len(still_uncategorized)} still need manual processing")

        # Enable manual categorization if there are still uncategorized
        if still_uncategorized:
            self.load_manual_btn.setEnabled(True)
            self.manual_status_label.setText(f"Ready for manual categorization - {len(still_uncategorized)} transactions from AI processing")
        else:
            self.manual_status_label.setText("All transactions categorized - manual processing not needed")

        # Show completion message
        QMessageBox.information(self, "AI Processing Complete",
                              f"AI processing completed!\n\n"
                              f"Categorized: {ai_categorized_count}\n"
                              f"Still need manual processing: {len(still_uncategorized)}")

        self.logger.info(f"AI processing complete: {ai_categorized_count} categorized, {len(still_uncategorized)} still uncategorized")

        # Validate pipeline after AI processing
        validation_report = self.validate_transaction_pipeline("after_ai")
        if validation_report["issues"]:
            QMessageBox.warning(self, "Processing Issues",
                              f"AI processing completed with issues:\n\n" +
                              "\n".join(validation_report["issues"]))

        # Save session after AI processing
        self.save_session()

    def generate_summary(self):
        """Generate categorization summary"""
        total = len(self.raw_transactions)
        ml_categorized = len(self.categorized_transactions)
        ai_categorized = len(self.ai_categorized_transactions)
        manual_categorized = len(self.manual_categorized_transactions)
        still_uncategorized = len([t for t in self.uncategorized_transactions if not t.get('category')])

        summary = f"""HYBRID CATEGORIZATION SUMMARY
{'='*50}

Total Transactions: {total}

PHASE 1 - ML MODEL CATEGORIZATION:
- Successfully categorized: {ml_categorized}
- Confidence threshold: {self.ml_confidence_threshold:.2f}

PHASE 2 - AI CATEGORIZATION:
- Successfully categorized: {ai_categorized}
- Batch size used: {self.ai_batch_size}

PHASE 3 - MANUAL CATEGORIZATION:
- Manually categorized: {manual_categorized}

FINAL RESULTS:
- Total categorized: {ml_categorized + ai_categorized + manual_categorized}
- Still uncategorized: {still_uncategorized}
- Success rate: {((ml_categorized + ai_categorized + manual_categorized) / total * 100):.1f}%

CATEGORIZATION BREAKDOWN:
"""

        # Add category breakdown
        all_categorized = self.categorized_transactions + self.ai_categorized_transactions + self.manual_categorized_transactions
        category_counts = {}
        for transaction in all_categorized:
            category = transaction.get('category', 'Unknown')
            category_counts[category] = category_counts.get(category, 0) + 1

        for category, count in sorted(category_counts.items()):
            summary += f"- {category}: {count} transactions\n"

        self.export_summary.setPlainText(summary)

    def export_to_csv(self):
        """Export categorized transactions to CSV"""
        # Collect all categorized transactions
        all_categorized = []
        all_categorized.extend(self.categorized_transactions)
        all_categorized.extend(self.ai_categorized_transactions)
        all_categorized.extend(self.manual_categorized_transactions)

        if not all_categorized:
            QMessageBox.warning(self, "No Data", "No categorized transactions to export")
            return

        try:
            # Create processed transactions for export
            from ..models.transaction import ProcessedTransaction
            processed_transactions = []

            for txn_data in all_categorized:
                # Get original transaction
                original_txn = txn_data.get('transaction')
                if not original_txn:
                    continue

                # Create processed transaction
                processed = ProcessedTransaction()
                processed.update_from_raw(original_txn)

                # Set categorization data
                processed.category = txn_data.get('category', '')
                processed.sub_category = txn_data.get('sub_category', '')
                processed.confidence_score = txn_data.get('confidence', 0.0)
                processed.is_manually_reviewed = txn_data.get('method') == 'Manual'

                # Set transaction mode based on type
                if original_txn.transaction_type == "DEBIT":
                    processed.transaction_mode = "Bank Transfer"
                elif original_txn.transaction_type == "CREDIT":
                    processed.transaction_mode = "Bank Deposit"
                else:
                    processed.transaction_mode = "Transfer"

                processed_transactions.append(processed)

            # Signal to parent that transactions are ready
            if hasattr(self.parent(), 'on_hybrid_categorization_completed'):
                self.parent().on_hybrid_categorization_completed(processed_transactions)

            QMessageBox.information(self, "Export Complete",
                                  f"Successfully exported {len(processed_transactions)} categorized transactions to main application")

            # Close dialog
            self.accept()

        except Exception as e:
            self.logger.error(f"Export error: {str(e)}")
            QMessageBox.critical(self, "Export Error", f"Failed to export transactions: {str(e)}")

    def load_manual_transactions(self):
        """Load uncategorized transactions for manual categorization"""
        # Combine uncategorized transactions from ML and AI phases
        transactions_to_categorize = []

        # Add ALL uncategorized from ML phase (regardless of whether they have a tentative category)
        for txn_data in self.uncategorized_transactions:
            transactions_to_categorize.append(txn_data)

        # Add any remaining from AI phase that are still uncategorized
        for txn_data in self.ai_categorized_transactions:
            # Check if this transaction needs manual review
            if (not txn_data.get('category') or
                txn_data.get('category') == 'Other' or
                txn_data.get('sub_category') == 'Needs Review' or
                (txn_data.get('confidence', 0) < 0.7)):  # Low confidence needs review
                transactions_to_categorize.append(txn_data)

        # Also check for any transactions that were marked as needing manual review
        # even if they have a category assigned
        for txn_data in self.categorized_transactions:
            if (txn_data.get('category') == 'Other' and
                txn_data.get('sub_category') == 'Needs Review'):
                transactions_to_categorize.append(txn_data)

        # Remove duplicates based on transaction description and amount
        seen = set()
        unique_transactions = []
        for txn in transactions_to_categorize:
            key = (txn.get('description', ''), txn.get('amount', 0))
            if key not in seen:
                seen.add(key)
                unique_transactions.append(txn)

        # Store for filtering
        self.manual_transactions_data = unique_transactions
        self.filtered_manual_transactions = unique_transactions.copy()

        # Validate pipeline during manual loading
        validation_report = self.validate_transaction_pipeline("manual_loading")

        # Update UI
        self.populate_manual_table()

        # Show status with validation info
        status_msg = f"Loaded {len(unique_transactions)} transactions for manual categorization"
        if validation_report["warnings"]:
            status_msg += f" (⚠️ {len(validation_report['warnings'])} warnings)"
        if validation_report["issues"]:
            status_msg += f" (❌ {len(validation_report['issues'])} issues)"

        self.manual_status_label.setText(status_msg)
        self.save_manual_btn.setEnabled(True)

        # Show detailed issues if any
        if validation_report["issues"]:
            QMessageBox.warning(self, "Manual Loading Issues",
                              f"Issues detected during manual transaction loading:\n\n" +
                              "\n".join(validation_report["issues"]))

        self.logger.info(f"Loaded {len(unique_transactions)} transactions for manual categorization (including {len(transactions_to_categorize) - len(unique_transactions)} duplicates removed)")

    def apply_manual_filters(self):
        """Apply enhanced filters to manual categorization transactions"""
        if not hasattr(self, 'manual_transactions_data'):
            return

        desc_filter = self.desc_filter_edit.text().strip()
        type_filter = self.type_filter_combo.currentText()
        missing_categories_only = self.missing_categories_checkbox.isChecked()

        # Amount filters
        min_amount = None
        max_amount = None
        try:
            if self.min_amount_edit.text().strip():
                min_amount = float(self.min_amount_edit.text().strip())
        except ValueError:
            pass

        try:
            if self.max_amount_edit.text().strip():
                max_amount = float(self.max_amount_edit.text().strip())
        except ValueError:
            pass

        # Apply filters
        self.filtered_manual_transactions = []
        for txn_data in self.manual_transactions_data:
            # Multi-word description filter
            if desc_filter:
                desc_lower = txn_data['description'].lower()
                # Split filter into words and check if all words are present
                filter_words = desc_filter.lower().split()
                if not all(word in desc_lower for word in filter_words):
                    continue

            # Type filter with credit/debit mapping
            if type_filter != "All Types":
                txn_type = txn_data.get('transaction_type', '')
                if type_filter == "DEBIT (Expenses)" and txn_type != "DEBIT":
                    continue
                elif type_filter == "CREDIT (Income)" and txn_type != "CREDIT":
                    continue
                elif type_filter == "TRANSFER" and txn_type != "TRANSFER":
                    continue

            # Missing categories filter
            if missing_categories_only:
                category = txn_data.get('category', '')
                if category and category not in ['', 'N/A', 'Unknown', 'Uncategorized']:
                    continue

            # Amount filters
            amount = txn_data['amount']
            if min_amount is not None and abs(amount) < min_amount:
                continue
            if max_amount is not None and abs(amount) > max_amount:
                continue

            self.filtered_manual_transactions.append(txn_data)

        # Update table
        self.populate_manual_table()

    def clear_manual_filters(self):
        """Clear all manual categorization filters"""
        self.desc_filter_edit.clear()
        self.min_amount_edit.clear()
        self.max_amount_edit.clear()
        self.type_filter_combo.setCurrentIndex(0)
        self.missing_categories_checkbox.setChecked(False)
        self.apply_manual_filters()

    def populate_manual_table(self):
        """Populate the enhanced manual categorization table"""
        if not hasattr(self, 'filtered_manual_transactions'):
            return

        self.manual_table.setRowCount(len(self.filtered_manual_transactions))
        self.manual_transactions_label.setText(f"Transactions to Categorize ({len(self.filtered_manual_transactions)})")

        # Get available categories organized with subcategories
        categories_with_subs = self.get_categories_with_subcategories()

        for row, txn_data in enumerate(self.filtered_manual_transactions):
            # Selection checkbox with better styling
            checkbox = QCheckBox()
            checkbox.setChecked(False)
            checkbox.setStyleSheet("""
                QCheckBox {
                    margin: 5px;
                }
                QCheckBox::indicator {
                    width: 18px;
                    height: 18px;
                }
                QCheckBox::indicator:unchecked {
                    border: 2px solid #cccccc;
                    background-color: white;
                    border-radius: 3px;
                }
                QCheckBox::indicator:checked {
                    border: 2px solid #4CAF50;
                    background-color: #4CAF50;
                    border-radius: 3px;
                }
                QCheckBox::indicator:checked:hover {
                    background-color: #45a049;
                }
            """)
            # Connect checkbox to update selection status
            checkbox.stateChanged.connect(self.update_selection_status)
            self.manual_table.setCellWidget(row, 0, checkbox)

            # Description
            desc_item = QTableWidgetItem(txn_data['description'])
            desc_item.setToolTip(txn_data['description'])
            self.manual_table.setItem(row, 1, desc_item)

            # Amount
            amount = txn_data['amount']
            amount_str = f"₹{amount:,.2f}"
            amount_item = QTableWidgetItem(amount_str)
            amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.manual_table.setItem(row, 2, amount_item)

            # Date
            date_str = str(txn_data['date'])[:10]
            date_item = QTableWidgetItem(date_str)
            date_item.setTextAlignment(Qt.AlignCenter)
            self.manual_table.setItem(row, 3, date_item)

            # Mode
            mode_item = QTableWidgetItem(txn_data.get('transaction_type', 'Unknown'))
            mode_item.setTextAlignment(Qt.AlignCenter)
            self.manual_table.setItem(row, 4, mode_item)

            # Current Category
            current_cat = txn_data.get('category', 'Uncategorized')
            current_item = QTableWidgetItem(current_cat)
            self.manual_table.setItem(row, 5, current_item)

            # Filter categories based on transaction type (Credit/Debit)
            txn_type = txn_data.get('transaction_type', '')
            filtered_categories = self.filter_categories_by_type_dict(categories_with_subs, txn_type)
            category_names = list(filtered_categories.keys())

            # New Category dropdown with create option
            category_combo = QComboBox()
            category_combo.addItem('Select Category...')
            category_combo.addItem('+ Create New Category...')
            category_combo.addItems(category_names)
            category_combo.currentTextChanged.connect(
                lambda text, r=row: self.on_individual_category_changed(r, text)
            )
            self.manual_table.setCellWidget(row, 6, category_combo)

            # New Subcategory dropdown (initially empty)
            subcategory_combo = QComboBox()
            subcategory_combo.addItem('Select Subcategory...')
            subcategory_combo.currentTextChanged.connect(
                lambda text, r=row: self.on_individual_subcategory_changed(r, text)
            )
            self.manual_table.setCellWidget(row, 7, subcategory_combo)

            # Action button
            action_btn = QPushButton("Apply")
            action_btn.clicked.connect(lambda checked, r=row: self.apply_manual_category(r))
            self.manual_table.setCellWidget(row, 8, action_btn)

        # Update bulk category dropdown
        self.update_bulk_category_dropdown()

        # Initialize selection status
        self.update_selection_status()

    def update_subcategories(self, row: int, category_name: str):
        """Update subcategories when category is selected"""
        subcategory_combo = self.manual_table.cellWidget(row, 7)  # Updated column index
        if not subcategory_combo or category_name in ['Select Category...', '+ Create New Category...']:
            return

        # Clear existing subcategories
        subcategory_combo.clear()
        subcategory_combo.addItem('Select Subcategory...')

        # Add create new subcategory option if valid category is selected
        if category_name and category_name not in ['Select Category...', '+ Create New Category...']:
            subcategory_combo.addItem('+ Create New Subcategory...')

        # Get subcategories for selected category
        categories_with_subs = self.get_categories_with_subcategories()
        if category_name in categories_with_subs:
            subcategories = categories_with_subs[category_name]['subcategories']
            if subcategories:
                # Sort subcategories alphabetically
                sorted_subcategories = sorted(subcategories)
                subcategory_combo.addItems(sorted_subcategories)

    def apply_manual_category(self, row: int):
        """Apply manual categorization for a specific row"""
        category_combo = self.manual_table.cellWidget(row, 6)  # Updated column index
        subcategory_combo = self.manual_table.cellWidget(row, 7)  # Updated column index

        if not category_combo or not subcategory_combo:
            return

        category = category_combo.currentText()
        subcategory = subcategory_combo.currentText()

        if category == 'Select Category...' or subcategory == 'Select Subcategory...':
            QMessageBox.warning(self, "Invalid Selection", "Please select both category and subcategory")
            return

        # Update transaction data
        if row < len(self.filtered_manual_transactions):
            txn_data = self.filtered_manual_transactions[row]
            txn_data['category'] = category
            txn_data['sub_category'] = subcategory
            txn_data['method'] = 'Manual'
            txn_data['confidence'] = 1.0  # Manual categorization has full confidence

            # Update current category display
            current_item = QTableWidgetItem(f"{category} > {subcategory}")
            self.manual_table.setItem(row, 5, current_item)  # Updated column index

            # Add to manual categorized list
            if txn_data not in self.manual_categorized_transactions:
                self.manual_categorized_transactions.append(txn_data)

            QMessageBox.information(self, "Success", f"Transaction categorized as {category} > {subcategory}")

    def save_manual_categories(self):
        """Save all manual categorizations"""
        if not self.manual_categorized_transactions:
            QMessageBox.information(self, "No Changes", "No manual categorizations to save")
            return

        # Update the main categorized transactions list
        for manual_txn in self.manual_categorized_transactions:
            # Remove from uncategorized if it was there
            if manual_txn in self.uncategorized_transactions:
                self.uncategorized_transactions.remove(manual_txn)

            # Add to categorized if not already there
            if manual_txn not in self.categorized_transactions:
                self.categorized_transactions.append(manual_txn)

        # Update counts
        self.categorized_count_label.setText(f"Count: {len(self.categorized_transactions)}")
        self.uncategorized_count_label.setText(f"Count: {len(self.uncategorized_transactions)}")

        QMessageBox.information(self, "Success",
                              f"Saved {len(self.manual_categorized_transactions)} manual categorizations")

        self.logger.info(f"Saved {len(self.manual_categorized_transactions)} manual categorizations")

        # Save session after manual categorization
        self.save_session()

    def generate_session_id(self, transactions):
        """Generate a unique session ID based on transaction data"""
        # Create a hash based on transaction descriptions and amounts
        transaction_data = []
        for txn in transactions:
            if hasattr(txn, 'description') and hasattr(txn, 'amount'):
                transaction_data.append(f"{txn.description}_{txn.amount}")
            elif isinstance(txn, dict):
                transaction_data.append(f"{txn.get('description', '')}_{txn.get('amount', 0)}")

        combined_data = "|".join(sorted(transaction_data))
        session_hash = hashlib.md5(combined_data.encode()).hexdigest()[:12]
        timestamp = datetime.now().strftime("%Y%m%d_%H%M")
        return f"session_{timestamp}_{session_hash}"

    def save_session(self):
        """Save current categorization session to disk"""
        if not self.session_id:
            self.logger.warning("Cannot save session: no session_id set")
            return

        try:
            # Ensure sessions directory exists
            self.sessions_dir.mkdir(parents=True, exist_ok=True)

            # Validate data before saving
            categorized_count = len(self.categorized_transactions) if hasattr(self, 'categorized_transactions') else 0
            uncategorized_count = len(self.uncategorized_transactions) if hasattr(self, 'uncategorized_transactions') else 0
            ai_count = len(self.ai_categorized_transactions) if hasattr(self, 'ai_categorized_transactions') else 0
            manual_count = len(self.manual_categorized_transactions) if hasattr(self, 'manual_categorized_transactions') else 0

            session_data = {
                "session_id": self.session_id,
                "created_at": datetime.now().isoformat(),
                "raw_transactions_count": len(self.raw_transactions) if hasattr(self, 'raw_transactions') else 0,
                "categorized_transactions": getattr(self, 'categorized_transactions', []),
                "uncategorized_transactions": getattr(self, 'uncategorized_transactions', []),
                "ai_categorized_transactions": getattr(self, 'ai_categorized_transactions', []),
                "manual_categorized_transactions": getattr(self, 'manual_categorized_transactions', []),
                "phase_status": {
                    "ml_completed": categorized_count > 0 or uncategorized_count > 0,
                    "ai_completed": ai_count > 0,
                    "manual_completed": manual_count > 0
                },
                "statistics": {
                    "categorized_count": categorized_count,
                    "uncategorized_count": uncategorized_count,
                    "ai_count": ai_count,
                    "manual_count": manual_count,
                    "total_processed": categorized_count + uncategorized_count + ai_count + manual_count
                }
            }

            # Write to temporary file first, then rename for atomic operation
            temp_file = self.session_file.with_suffix('.tmp')
            with open(temp_file, 'w') as f:
                json.dump(session_data, f, indent=2, default=str)

            # Atomic rename
            temp_file.rename(self.session_file)

            self.logger.info(f"Session saved: {self.session_file} (total: {session_data['statistics']['total_processed']} transactions)")

        except Exception as e:
            self.logger.error(f"Error saving session: {str(e)}", exc_info=True)

    def load_session(self, session_id):
        """Load a previous categorization session"""
        session_file = self.sessions_dir / f"{session_id}.json"

        if not session_file.exists():
            self.logger.warning(f"Session file not found: {session_file}")
            return False

        try:
            with open(session_file, 'r') as f:
                session_data = json.load(f)

            # Validate session data
            if not isinstance(session_data, dict):
                self.logger.error("Invalid session data format")
                return False

            # Restore transaction lists with validation
            self.categorized_transactions = session_data.get("categorized_transactions", [])
            self.uncategorized_transactions = session_data.get("uncategorized_transactions", [])
            self.ai_categorized_transactions = session_data.get("ai_categorized_transactions", [])
            self.manual_categorized_transactions = session_data.get("manual_categorized_transactions", [])

            # Validate that we have some data
            total_transactions = (len(self.categorized_transactions) +
                                len(self.uncategorized_transactions) +
                                len(self.ai_categorized_transactions) +
                                len(self.manual_categorized_transactions))

            if total_transactions == 0:
                self.logger.warning("Loaded session has no transaction data")

            # Update UI
            self.update_ui_after_session_load(session_data)

            # Log statistics
            self.logger.info(f"Session loaded: {session_file} (total: {total_transactions} transactions, "
                           f"categorized: {len(self.categorized_transactions)}, "
                           f"uncategorized: {len(self.uncategorized_transactions)}, "
                           f"AI: {len(self.ai_categorized_transactions)}, "
                           f"manual: {len(self.manual_categorized_transactions)})")

            return True

        except json.JSONDecodeError as e:
            self.logger.error(f"Invalid JSON in session file: {str(e)}")
            return False
        except Exception as e:
            self.logger.error(f"Error loading session: {str(e)}", exc_info=True)
            return False

    def update_ui_after_session_load(self, session_data):
        """Update UI after loading a session"""
        phase_status = session_data.get("phase_status", {})

        # Update Phase 1 (ML) UI
        if phase_status.get("ml_completed", False):
            categorized_count = len(self.categorized_transactions)
            uncategorized_count = len(self.uncategorized_transactions)

            # Update ML tables
            self.populate_ml_categorized_table()
            self.populate_ml_uncategorized_table()

            # Enable subsequent phases
            self.tab_widget.setTabEnabled(1, True)  # AI tab
            self.tab_widget.setTabEnabled(2, True)  # Manual tab

            # Update status
            self.ml_summary_label.setText(f"ML Results: {categorized_count} categorized, {uncategorized_count} need further processing")

            if uncategorized_count > 0:
                self.ai_process_btn.setEnabled(True)
                self.skip_phase2_btn.setEnabled(True)
                self.load_manual_btn.setEnabled(True)

        # Update Phase 2 (AI) UI
        if phase_status.get("ai_completed", False):
            self.populate_ai_categorized_table()
            self.populate_ai_uncategorized_table()

        # Update Phase 3 (Manual) UI
        if phase_status.get("manual_completed", False):
            self.save_manual_btn.setEnabled(True)

        # Show restoration message
        total_categorized = len(self.categorized_transactions) + len(self.ai_categorized_transactions) + len(self.manual_categorized_transactions)
        total_uncategorized = len(self.uncategorized_transactions)

        QMessageBox.information(self, "Session Restored",
                              f"Previous session restored successfully!\n\n"
                              f"Categorized: {total_categorized} transactions\n"
                              f"Uncategorized: {total_uncategorized} transactions\n\n"
                              f"You can continue from where you left off.")

    def find_existing_session(self, transactions):
        """Find existing session for the same set of transactions"""
        session_id = self.generate_session_id(transactions)
        session_file = self.sessions_dir / f"{session_id}.json"

        if session_file.exists():
            return session_id
        return None

    def populate_ml_categorized_table(self):
        """Populate ML categorized table from session data"""
        if not hasattr(self, 'categorized_table'):
            return

        self.categorized_table.setRowCount(len(self.categorized_transactions))
        self.categorized_count_label.setText(f"Count: {len(self.categorized_transactions)}")

        for row, txn_data in enumerate(self.categorized_transactions):
            # Description
            desc_item = QTableWidgetItem(txn_data['description'])
            desc_item.setToolTip(txn_data['description'])
            self.categorized_table.setItem(row, 0, desc_item)

            # Amount
            amount = txn_data['amount']
            amount_str = f"₹{amount:,.2f}"
            amount_item = QTableWidgetItem(amount_str)
            amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.categorized_table.setItem(row, 1, amount_item)

            # Date
            date_str = str(txn_data['date'])[:10]
            date_item = QTableWidgetItem(date_str)
            date_item.setTextAlignment(Qt.AlignCenter)
            self.categorized_table.setItem(row, 2, date_item)

            # Mode
            mode_item = QTableWidgetItem(txn_data.get('transaction_type', 'Unknown'))
            mode_item.setTextAlignment(Qt.AlignCenter)
            self.categorized_table.setItem(row, 3, mode_item)

            # Category
            category = txn_data.get('category', 'Unknown')
            category_item = QTableWidgetItem(category)
            self.categorized_table.setItem(row, 4, category_item)

            # Confidence
            confidence = txn_data.get('confidence', 0.0)
            confidence_item = QTableWidgetItem(f"{confidence:.3f}")
            confidence_item.setTextAlignment(Qt.AlignCenter)
            self.categorized_table.setItem(row, 5, confidence_item)

            # Status
            status_item = QTableWidgetItem("ML Categorized")
            status_item.setTextAlignment(Qt.AlignCenter)
            self.categorized_table.setItem(row, 6, status_item)

    def populate_ml_uncategorized_table(self):
        """Populate ML uncategorized table from session data"""
        if not hasattr(self, 'uncategorized_table'):
            return

        self.uncategorized_table.setRowCount(len(self.uncategorized_transactions))
        self.uncategorized_count_label.setText(f"Count: {len(self.uncategorized_transactions)}")

        for row, txn_data in enumerate(self.uncategorized_transactions):
            # Description
            desc_item = QTableWidgetItem(txn_data['description'])
            desc_item.setToolTip(txn_data['description'])
            self.uncategorized_table.setItem(row, 0, desc_item)

            # Amount
            amount = txn_data['amount']
            amount_str = f"₹{amount:,.2f}"
            amount_item = QTableWidgetItem(amount_str)
            amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.uncategorized_table.setItem(row, 1, amount_item)

            # Date
            date_str = str(txn_data['date'])[:10]
            date_item = QTableWidgetItem(date_str)
            date_item.setTextAlignment(Qt.AlignCenter)
            self.uncategorized_table.setItem(row, 2, date_item)

            # Mode
            mode_item = QTableWidgetItem(txn_data.get('transaction_type', 'Unknown'))
            mode_item.setTextAlignment(Qt.AlignCenter)
            self.uncategorized_table.setItem(row, 3, mode_item)

            # Category
            category_item = QTableWidgetItem("N/A")
            self.uncategorized_table.setItem(row, 4, category_item)

            # Confidence
            confidence = txn_data.get('confidence', 0.0)
            confidence_item = QTableWidgetItem(f"{confidence:.3f}")
            confidence_item.setTextAlignment(Qt.AlignCenter)
            self.uncategorized_table.setItem(row, 5, confidence_item)

            # Status
            status_item = QTableWidgetItem("Needs Categorization")
            status_item.setTextAlignment(Qt.AlignCenter)
            self.uncategorized_table.setItem(row, 6, status_item)

    def get_categories_with_subcategories(self):
        """Get categories organized with their subcategories"""
        all_categories = self.category_manager.get_all_categories()

        # Organize categories by parent-child relationship
        main_categories = {}

        # First, get all main categories (no parent)
        for cat in all_categories:
            if cat.parent_id is None:
                main_categories[cat.name] = {
                    'id': cat.id,
                    'name': cat.name,
                    'category_type': cat.category_type,
                    'subcategories': []
                }

        # Then, add subcategories to their parents
        for cat in all_categories:
            if cat.parent_id is not None:
                # Find the parent category
                parent_cat = None
                for parent in all_categories:
                    if parent.id == cat.parent_id:
                        parent_cat = parent
                        break

                if parent_cat and parent_cat.name in main_categories:
                    main_categories[parent_cat.name]['subcategories'].append(cat.name)

        return main_categories

    def filter_categories_by_type_dict(self, categories_dict, transaction_type):
        """Filter categories dictionary based on transaction type (Credit/Debit)"""
        if transaction_type == "CREDIT":
            # For income transactions, show income-related categories
            income_keywords = ['salary', 'income', 'interest', 'dividend', 'bonus', 'refund', 'cashback']
            filtered = {}
            for cat_name, cat_data in categories_dict.items():
                if (cat_data['category_type'] == 'income' or
                    cat_data['category_type'] == 'both' or
                    any(keyword in cat_name.lower() for keyword in income_keywords)):
                    filtered[cat_name] = cat_data
            # If no specific income categories, return all
            return filtered if filtered else categories_dict

        elif transaction_type == "DEBIT":
            # For expense transactions, exclude income-only categories
            filtered = {}
            for cat_name, cat_data in categories_dict.items():
                if (cat_data['category_type'] == 'expense' or
                    cat_data['category_type'] == 'both'):
                    filtered[cat_name] = cat_data
            return filtered if filtered else categories_dict

        else:
            # For transfers and others, return all categories
            return categories_dict

    def filter_categories_by_type(self, categories, transaction_type):
        """Filter categories based on transaction type (Credit/Debit)"""
        if transaction_type == "CREDIT":
            # For income transactions, show income-related categories
            income_keywords = ['salary', 'income', 'interest', 'dividend', 'bonus', 'refund', 'cashback']
            filtered = []
            for cat in categories:
                if any(keyword in cat.name.lower() for keyword in income_keywords):
                    filtered.append(cat)
            # If no specific income categories, return all
            return filtered if filtered else categories

        elif transaction_type == "DEBIT":
            # For expense transactions, exclude income-related categories
            income_keywords = ['salary', 'income', 'interest', 'dividend', 'bonus']
            filtered = []
            for cat in categories:
                if not any(keyword in cat.name.lower() for keyword in income_keywords):
                    filtered.append(cat)
            return filtered if filtered else categories

        else:
            # For transfers and others, return all categories
            return categories

    def update_bulk_category_dropdown(self):
        """Update bulk category dropdown based on current transactions"""
        self.bulk_category_combo.clear()
        self.bulk_category_combo.addItem("Select Category for Bulk Assignment...")

        # Add create new category option at the top
        self.bulk_category_combo.addItem("+ Create New Category...")

        if hasattr(self, 'filtered_manual_transactions') and self.filtered_manual_transactions:
            # Get categories based on the most common transaction type in current filter
            transaction_types = [txn.get('transaction_type', '') for txn in self.filtered_manual_transactions]
            most_common_type = max(set(transaction_types), key=transaction_types.count) if transaction_types else ''

            categories_with_subs = self.get_categories_with_subcategories()
            filtered_categories = self.filter_categories_by_type_dict(categories_with_subs, most_common_type)
            category_names = sorted(list(filtered_categories.keys()))  # Sort alphabetically

            self.bulk_category_combo.addItems(category_names)

    def update_bulk_subcategories(self, category_name):
        """Update bulk subcategory dropdown when category is selected"""
        self.bulk_subcategory_combo.clear()
        self.bulk_subcategory_combo.addItem('Select Subcategory...')

        if category_name and category_name not in ['Select Category for Bulk Assignment...', '+ Create New Category...']:
            # Add create new subcategory option
            self.bulk_subcategory_combo.addItem('+ Create New Subcategory...')

            categories_with_subs = self.get_categories_with_subcategories()
            if category_name in categories_with_subs:
                subcategories = categories_with_subs[category_name]['subcategories']
                if subcategories:
                    # Sort subcategories alphabetically
                    sorted_subcategories = sorted(subcategories)
                    self.bulk_subcategory_combo.addItems(sorted_subcategories)
                # Note: We always show the create option, even if no subcategories exist

    def get_checkbox_from_row(self, row):
        """Helper method to get checkbox from table row"""
        checkbox_widget = self.manual_table.cellWidget(row, 0)
        if isinstance(checkbox_widget, QCheckBox):
            return checkbox_widget
        return None

    def update_selection_status(self):
        """Update the selection status label"""
        selected_count = 0
        for row in range(self.manual_table.rowCount()):
            checkbox = self.get_checkbox_from_row(row)
            if checkbox and checkbox.isChecked():
                selected_count += 1

        total_count = self.manual_table.rowCount()
        self.selection_status_label.setText(f"Selected: {selected_count} of {total_count} transactions")

        # Enable/disable bulk apply button based on selection
        self.apply_bulk_btn.setEnabled(selected_count > 0)

    def select_all_transactions(self):
        """Select all transactions in the current view"""
        for row in range(self.manual_table.rowCount()):
            checkbox = self.get_checkbox_from_row(row)
            if checkbox:
                checkbox.setChecked(True)
        self.update_selection_status()

    def select_none_transactions(self):
        """Deselect all transactions"""
        for row in range(self.manual_table.rowCount()):
            checkbox = self.get_checkbox_from_row(row)
            if checkbox:
                checkbox.setChecked(False)
        self.update_selection_status()

    def apply_bulk_categorization(self):
        """Apply bulk categorization to selected transactions"""
        category = self.bulk_category_combo.currentText()
        subcategory = self.bulk_subcategory_combo.currentText()

        if category == 'Select Category for Bulk Assignment...' or subcategory == 'Select Subcategory...':
            QMessageBox.warning(self, "Invalid Selection", "Please select both category and subcategory for bulk assignment")
            return

        # Get selected transactions
        selected_rows = []
        for row in range(self.manual_table.rowCount()):
            checkbox = self.get_checkbox_from_row(row)
            if checkbox and checkbox.isChecked():
                selected_rows.append(row)

        if not selected_rows:
            QMessageBox.information(self, "No Selection", "Please select transactions to apply bulk categorization")
            return

        # Apply categorization to selected transactions
        categorized_count = 0
        for row in selected_rows:
            if row < len(self.filtered_manual_transactions):
                txn_data = self.filtered_manual_transactions[row]
                txn_data['category'] = category
                txn_data['sub_category'] = subcategory
                txn_data['method'] = 'Manual (Bulk)'
                txn_data['confidence'] = 1.0

                # Update current category display
                current_item = QTableWidgetItem(f"{category} > {subcategory}")
                self.manual_table.setItem(row, 5, current_item)

                # Add to manual categorized list if not already there
                if txn_data not in self.manual_categorized_transactions:
                    self.manual_categorized_transactions.append(txn_data)

                # Uncheck the checkbox
                checkbox = self.get_checkbox_from_row(row)
                if checkbox:
                    checkbox.setChecked(False)

                categorized_count += 1

        QMessageBox.information(self, "Bulk Categorization Complete",
                              f"Successfully applied {category} > {subcategory} to {categorized_count} transactions")

        self.logger.info(f"Bulk categorization applied: {category} > {subcategory} to {categorized_count} transactions")

    def on_bulk_category_changed(self, category_name):
        """Handle bulk category selection change"""
        if category_name == "+ Create New Category...":
            self.create_new_category()
        else:
            self.update_bulk_subcategories(category_name)

    def on_bulk_subcategory_changed(self, subcategory_name):
        """Handle bulk subcategory selection change"""
        if subcategory_name == "+ Create New Subcategory...":
            current_category = self.bulk_category_combo.currentText()
            if current_category and current_category not in ["Select Category for Bulk Assignment...", "+ Create New Category..."]:
                self.create_new_subcategory(current_category)

    def create_new_category(self):
        """Open dialog to create a new category"""
        dialog = CreateCategoryDialog(self)
        if dialog.exec() == QDialog.Accepted and dialog.category_data:
            category_data = dialog.category_data

            # Check if category already exists
            categories_with_subs = self.get_categories_with_subcategories()
            if category_data['name'] in categories_with_subs:
                QMessageBox.warning(self, "Category Exists",
                                  f"Category '{category_data['name']}' already exists.")
                return

            # Create the category using category manager
            category_id = self.category_manager.create_category(
                name=category_data['name'],
                parent_name=None,  # Main category
                description=category_data['description'],
                color=category_data['color'],
                category_type=category_data['type']
            )

            if category_id:
                # Refresh category cache
                self.category_manager._load_categories()

                # Update all dropdowns
                self.refresh_all_category_dropdowns()

                # Select the newly created category
                self.bulk_category_combo.setCurrentText(category_data['name'])

                QMessageBox.information(self, "Category Created",
                                      f"Category '{category_data['name']}' created successfully!")

                self.logger.info(f"Created new category: {category_data['name']} (ID: {category_id})")
            else:
                QMessageBox.critical(self, "Creation Failed",
                                   "Failed to create category. Please try again.")

    def create_new_subcategory(self, parent_category_name):
        """Open dialog to create a new subcategory"""
        dialog = CreateSubcategoryDialog(parent_category_name, self)
        if dialog.exec() == QDialog.Accepted and dialog.subcategory_name:
            subcategory_name = dialog.subcategory_name

            # Check if subcategory already exists under this parent
            categories_with_subs = self.get_categories_with_subcategories()
            if parent_category_name in categories_with_subs:
                existing_subcategories = categories_with_subs[parent_category_name]['subcategories']
                if subcategory_name in existing_subcategories:
                    QMessageBox.warning(self, "Subcategory Exists",
                                      f"Subcategory '{subcategory_name}' already exists under '{parent_category_name}'.")
                    return

            # Create the subcategory using category manager
            category_id = self.category_manager.create_category(
                name=subcategory_name,
                parent_name=parent_category_name,
                description=f"Subcategory under {parent_category_name}",
                category_type=categories_with_subs[parent_category_name]['category_type']
            )

            if category_id:
                # Refresh category cache
                self.category_manager._load_categories()

                # Update all dropdowns
                self.refresh_all_category_dropdowns()

                # Update subcategory dropdown for current category
                self.update_bulk_subcategories(parent_category_name)

                # Select the newly created subcategory
                self.bulk_subcategory_combo.setCurrentText(subcategory_name)

                QMessageBox.information(self, "Subcategory Created",
                                      f"Subcategory '{subcategory_name}' created successfully under '{parent_category_name}'!")

                self.logger.info(f"Created new subcategory: {subcategory_name} under {parent_category_name} (ID: {category_id})")
            else:
                QMessageBox.critical(self, "Creation Failed",
                                   "Failed to create subcategory. Please try again.")

    def refresh_all_category_dropdowns(self):
        """Refresh all category dropdowns in the interface"""
        # Refresh bulk category dropdown
        current_bulk_category = self.bulk_category_combo.currentText()
        self.update_bulk_category_dropdown()

        # Try to restore selection if it still exists
        if current_bulk_category and current_bulk_category not in ["Select Category for Bulk Assignment...", "+ Create New Category..."]:
            index = self.bulk_category_combo.findText(current_bulk_category)
            if index >= 0:
                self.bulk_category_combo.setCurrentIndex(index)

        # Refresh individual transaction category dropdowns
        if hasattr(self, 'manual_table'):
            for row in range(self.manual_table.rowCount()):
                category_combo = self.manual_table.cellWidget(row, 6)
                if category_combo and isinstance(category_combo, QComboBox):
                    current_selection = category_combo.currentText()

                    # Get transaction type for filtering
                    if hasattr(self, 'filtered_manual_transactions') and row < len(self.filtered_manual_transactions):
                        txn_data = self.filtered_manual_transactions[row]
                        txn_type = txn_data.get('transaction_type', '')

                        # Update category dropdown
                        categories_with_subs = self.get_categories_with_subcategories()
                        filtered_categories = self.filter_categories_by_type_dict(categories_with_subs, txn_type)
                        category_names = sorted(list(filtered_categories.keys()))

                        category_combo.clear()
                        category_combo.addItems(['Select Category...'] + category_names)

                        # Try to restore selection
                        if current_selection and current_selection != 'Select Category...':
                            index = category_combo.findText(current_selection)
                            if index >= 0:
                                category_combo.setCurrentIndex(index)

    def on_individual_category_changed(self, row, category_name):
        """Handle individual transaction category change"""
        if category_name == "+ Create New Category...":
            self.create_new_category()
        else:
            self.update_subcategories(row, category_name)

    def on_individual_subcategory_changed(self, row, subcategory_name):
        """Handle individual transaction subcategory change"""
        if subcategory_name == "+ Create New Subcategory...":
            category_combo = self.manual_table.cellWidget(row, 6)
            if category_combo:
                current_category = category_combo.currentText()
                if current_category and current_category not in ["Select Category...", "+ Create New Category..."]:
                    self.create_new_subcategory(current_category)

    def populate_ai_uncategorized_table(self):
        """Populate AI uncategorized table with transactions from Phase 1"""
        self.ai_uncategorized_table.setRowCount(len(self.uncategorized_transactions))
        self.ai_uncategorized_count_label.setText(f"Count: {len(self.uncategorized_transactions)}")

        for row, txn_data in enumerate(self.uncategorized_transactions):
            # Description
            desc_item = QTableWidgetItem(txn_data['description'])
            desc_item.setToolTip(txn_data['description'])
            self.ai_uncategorized_table.setItem(row, 0, desc_item)

            # Amount
            amount = txn_data['amount']
            amount_str = f"₹{amount:,.2f}"
            amount_item = QTableWidgetItem(amount_str)
            amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.ai_uncategorized_table.setItem(row, 1, amount_item)

            # Date
            date_str = str(txn_data['date'])[:10]
            date_item = QTableWidgetItem(date_str)
            date_item.setTextAlignment(Qt.AlignCenter)
            self.ai_uncategorized_table.setItem(row, 2, date_item)

            # Mode
            mode_item = QTableWidgetItem(txn_data.get('transaction_type', 'Unknown'))
            mode_item.setTextAlignment(Qt.AlignCenter)
            self.ai_uncategorized_table.setItem(row, 3, mode_item)

            # Category
            category_item = QTableWidgetItem("N/A")
            self.ai_uncategorized_table.setItem(row, 4, category_item)

            # Confidence
            confidence_item = QTableWidgetItem(f"{txn_data['confidence']:.3f}")
            confidence_item.setTextAlignment(Qt.AlignCenter)
            self.ai_uncategorized_table.setItem(row, 5, confidence_item)

            # Status
            status_item = QTableWidgetItem("Needs AI Processing")
            status_item.setTextAlignment(Qt.AlignCenter)
            self.ai_uncategorized_table.setItem(row, 6, status_item)

    def add_to_ai_categorized_table(self, txn_data, ai_category):
        """Add a transaction to the AI categorized table"""
        row = self.ai_categorized_table.rowCount()
        self.ai_categorized_table.insertRow(row)

        # Description
        desc_item = QTableWidgetItem(txn_data['description'])
        desc_item.setToolTip(txn_data['description'])
        self.ai_categorized_table.setItem(row, 0, desc_item)

        # Amount
        amount = txn_data['amount']
        amount_str = f"₹{amount:,.2f}"
        amount_item = QTableWidgetItem(amount_str)
        amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.ai_categorized_table.setItem(row, 1, amount_item)

        # Date
        date_str = str(txn_data['date'])[:10]
        date_item = QTableWidgetItem(date_str)
        date_item.setTextAlignment(Qt.AlignCenter)
        self.ai_categorized_table.setItem(row, 2, date_item)

        # Mode
        mode_item = QTableWidgetItem(txn_data.get('transaction_type', 'Unknown'))
        mode_item.setTextAlignment(Qt.AlignCenter)
        self.ai_categorized_table.setItem(row, 3, mode_item)

        # AI Category
        category_item = QTableWidgetItem(ai_category)
        self.ai_categorized_table.setItem(row, 4, category_item)

        # Confidence (AI confidence would be different from ML)
        confidence_item = QTableWidgetItem("0.900")  # AI typically has high confidence
        confidence_item.setTextAlignment(Qt.AlignCenter)
        self.ai_categorized_table.setItem(row, 5, confidence_item)

        # Status
        status_item = QTableWidgetItem("AI Categorized")
        status_item.setTextAlignment(Qt.AlignCenter)
        self.ai_categorized_table.setItem(row, 6, status_item)

        # Update count
        self.ai_categorized_count_label.setText(f"Count: {self.ai_categorized_table.rowCount()}")

    def remove_from_ai_uncategorized_table(self, txn_data):
        """Remove a transaction from the AI uncategorized table"""
        for row in range(self.ai_uncategorized_table.rowCount()):
            if self.ai_uncategorized_table.item(row, 0).text() == txn_data['description']:
                self.ai_uncategorized_table.removeRow(row)
                break

        # Update count
        self.ai_uncategorized_count_label.setText(f"Count: {self.ai_uncategorized_table.rowCount()}")

    def skip_phase2(self):
        """Skip Phase 2 (AI processing) and go directly to Phase 3 (Manual)"""
        if not self.uncategorized_transactions:
            QMessageBox.information(self, "No Uncategorized Transactions",
                                  "All transactions are already categorized. No need to skip Phase 2.")
            return

        # Mark Phase 2 as skipped
        self.skip_ai_checkbox.setChecked(True)
        self.skip_ai_message.setVisible(True)

        # Disable AI processing
        self.ai_process_btn.setEnabled(False)

        # Update AI status
        self.ai_status_label.setText(f"Phase 2 skipped - {len(self.uncategorized_transactions)} transactions moved to manual categorization")

        # Enable manual categorization
        self.load_manual_btn.setEnabled(True)
        self.manual_status_label.setText(f"Ready for manual categorization - {len(self.uncategorized_transactions)} transactions from Phase 1")

        # Switch to Phase 3 tab
        self.tab_widget.setCurrentIndex(2)  # Switch to Manual tab

        QMessageBox.information(self, "Phase 2 Skipped",
                              f"Phase 2 (AI processing) has been skipped.\n\n"
                              f"{len(self.uncategorized_transactions)} uncategorized transactions are now ready for manual categorization in Phase 3.")

        self.logger.info(f"Phase 2 skipped - {len(self.uncategorized_transactions)} transactions moved to manual categorization")

    def on_tab_changed(self, index):
        """Handle tab change to update status and enable appropriate controls"""
        tab_names = ["Phase 1: ML", "Phase 2: AI", "Phase 3: Manual", "Phase 4: Export"]

        if index < len(tab_names):
            current_tab = tab_names[index]

            # Update status based on current tab
            if index == 1:  # AI tab
                if self.uncategorized_transactions:
                    if not self.skip_ai_checkbox.isChecked():
                        self.skip_phase2_btn.setEnabled(True)
                    else:
                        self.skip_phase2_btn.setEnabled(False)
                else:
                    self.skip_phase2_btn.setEnabled(False)

            elif index == 2:  # Manual tab
                if self.uncategorized_transactions:
                    self.load_manual_btn.setEnabled(True)

            elif index == 3:  # Export tab
                # Enable export if there are any categorized transactions
                total_categorized = len(self.categorized_transactions) + len(self.ai_categorized_transactions) + len(self.manual_categorized_transactions)
                if total_categorized > 0:
                    self.export_btn.setEnabled(True)

    def validate_transaction(self, txn, index):
        """Validate transaction data and identify potential parsing issues"""
        issues = []

        try:
            amount_val = float(txn.amount)
            amount_str = str(txn.amount)

            # Issue 1: Amount contains year (common parsing error)
            if any(year in amount_str for year in ['2020', '2021', '2022', '2023', '2024', '2025', '2026']):
                issues.append(f"Transaction {index+1}: Amount contains year: {amount_str}")

            # Issue 2: Amount is suspiciously large (might be date/timestamp)
            if abs(amount_val) > 1000000:  # More than 10 lakh
                issues.append(f"Transaction {index+1}: Suspiciously large amount: {amount_val}")

            # Issue 3: Amount looks like a date (YYYYMMDD format)
            if len(amount_str.replace('-', '').replace('.', '')) == 8 and amount_str.replace('-', '').replace('.', '').isdigit():
                issues.append(f"Transaction {index+1}: Amount looks like date (YYYYMMDD): {amount_str}")

            # Issue 4: Amount is exactly a year
            if abs(amount_val) in [2020, 2021, 2022, 2023, 2024, 2025, 2026]:
                issues.append(f"Transaction {index+1}: Amount is exactly a year: {amount_val}")

            # Issue 5: Check for common date patterns in amount
            if re.search(r'^-?20\d{2}$', amount_str):  # Matches -2024, 2023, etc.
                issues.append(f"Transaction {index+1}: Amount matches year pattern: {amount_str}")

        except Exception as e:
            issues.append(f"Transaction {index+1}: Cannot validate amount '{txn.amount}': {e}")

        return issues

    def attempt_transaction_fix(self, txn):
        """Attempt to fix common parsing issues in transactions"""
        try:
            amount_val = float(txn.amount)
            amount_str = str(txn.amount)

            # Fix 1: If amount is a year, it's likely a parsing error - set to 0 and flag
            if abs(amount_val) in [2020, 2021, 2022, 2023, 2024, 2025, 2026]:
                self.logger.warning(f"Fixed transaction with year as amount: {amount_str} -> 0.00")
                from decimal import Decimal
                from copy import deepcopy
                fixed_txn = deepcopy(txn)
                fixed_txn.amount = Decimal('0.00')
                fixed_txn.description = f"[PARSING ERROR - AMOUNT WAS {amount_str}] {txn.description}"
                return fixed_txn

            # Fix 2: If amount is suspiciously large (>1M), likely parsing error
            if abs(amount_val) > 1000000:
                self.logger.warning(f"Fixed transaction with large amount: {amount_str} -> 0.00")
                from decimal import Decimal
                from copy import deepcopy
                fixed_txn = deepcopy(txn)
                fixed_txn.amount = Decimal('0.00')
                fixed_txn.description = f"[PARSING ERROR - AMOUNT WAS {amount_str}] {txn.description}"
                return fixed_txn

            # Fix 3: If amount looks like YYYYMMDD date
            if len(amount_str.replace('-', '').replace('.', '')) == 8 and amount_str.replace('-', '').replace('.', '').isdigit():
                self.logger.warning(f"Fixed transaction with date as amount: {amount_str} -> 0.00")
                from decimal import Decimal
                from copy import deepcopy
                fixed_txn = deepcopy(txn)
                fixed_txn.amount = Decimal('0.00')
                fixed_txn.description = f"[PARSING ERROR - AMOUNT WAS {amount_str}] {txn.description}"
                return fixed_txn

            # If no fixes needed, return original
            return txn

        except Exception as e:
            self.logger.error(f"Error attempting to fix transaction: {e}")
            return None
