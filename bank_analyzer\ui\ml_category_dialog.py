"""
Category Management Dialog for ML System
Allows users to create and manage categories and subcategories
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QLineEdit, QPushButton,
    QLabel, QComboBox, QTextEdit, QGroupBox, QMessageBox, QTabWidget,
    QWidget, QTableWidget, QTableWidgetItem, QHeaderView, QColorDialog,
    QFrame, QSplitter
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont, QColor, QPalette

from typing import List, Dict, Optional, Any
import logging

from ..ml.category_manager import CategoryManager, Category
from ..core.logger import get_logger


class CategoryCreationWidget(QWidget):
    """Widget for creating new categories"""

    category_created = Signal(str)  # Emits category ID when created
    categories_updated = Signal()  # Emits when categories need refresh
    
    def __init__(self, category_manager: CategoryManager):
        super().__init__()
        self.category_manager = category_manager
        self.logger = get_logger(__name__)
        self.setup_ui()
        self.load_parent_categories()
    
    def setup_ui(self):
        """Setup the UI for category creation"""
        layout = QVBoxLayout(self)
        
        # Main category creation
        main_group = QGroupBox("Create New Category")
        main_layout = QFormLayout(main_group)
        
        self.category_name_edit = QLineEdit()
        self.category_name_edit.setPlaceholderText("e.g., Food & Dining, Transportation")
        
        self.category_description_edit = QTextEdit()
        self.category_description_edit.setMaximumHeight(60)
        self.category_description_edit.setPlaceholderText("Optional description for this category")
        
        self.category_color_button = QPushButton("Choose Color")
        self.category_color_button.clicked.connect(self.choose_category_color)
        self.selected_color = "#007ACC"
        self.update_color_button()
        
        # Category type selection
        self.category_type_combo = QComboBox()
        self.category_type_combo.addItems(["Expense", "Income", "Both"])
        self.category_type_combo.setCurrentText("Expense")  # Default to expense
        self.category_type_combo.setToolTip("Select whether this category is for expenses, income, or both")

        main_layout.addRow("Category Name*:", self.category_name_edit)
        main_layout.addRow("Category Type*:", self.category_type_combo)
        main_layout.addRow("Description:", self.category_description_edit)
        main_layout.addRow("Color:", self.category_color_button)
        
        # Create category button
        self.create_category_button = QPushButton("Create Category")
        self.create_category_button.clicked.connect(self.create_category)
        self.create_category_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; padding: 8px; }")
        
        main_layout.addRow("", self.create_category_button)
        
        layout.addWidget(main_group)
        
        # Subcategory creation
        sub_group = QGroupBox("Create New Subcategory")
        sub_layout = QFormLayout(sub_group)
        
        self.parent_category_combo = QComboBox()
        
        self.subcategory_name_edit = QLineEdit()
        self.subcategory_name_edit.setPlaceholderText("e.g., Delivery, Groceries, Fast Food")
        
        self.subcategory_description_edit = QTextEdit()
        self.subcategory_description_edit.setMaximumHeight(60)
        self.subcategory_description_edit.setPlaceholderText("Optional description for this subcategory")
        
        sub_layout.addRow("Parent Category*:", self.parent_category_combo)
        sub_layout.addRow("Subcategory Name*:", self.subcategory_name_edit)
        sub_layout.addRow("Description:", self.subcategory_description_edit)
        
        # Create subcategory button
        self.create_subcategory_button = QPushButton("Create Subcategory")
        self.create_subcategory_button.clicked.connect(self.create_subcategory)
        self.create_subcategory_button.setStyleSheet("QPushButton { background-color: #2196F3; color: white; font-weight: bold; padding: 8px; }")
        
        sub_layout.addRow("", self.create_subcategory_button)
        
        layout.addWidget(sub_group)
        
        # Quick presets
        presets_group = QGroupBox("Quick Category Presets")
        presets_layout = QVBoxLayout(presets_group)
        
        preset_info = QLabel("Click to quickly create common categories:")
        preset_info.setStyleSheet("color: gray; font-style: italic;")
        presets_layout.addWidget(preset_info)
        
        # Preset buttons
        preset_buttons_layout = QHBoxLayout()
        
        presets = [
            ("Entertainment", "#E91E63"),
            ("Healthcare", "#4CAF50"),
            ("Education", "#9C27B0"),
            ("Travel", "#FF9800"),
            ("Investments", "#607D8B")
        ]
        
        for preset_name, preset_color in presets:
            preset_button = QPushButton(preset_name)
            preset_button.setStyleSheet(f"QPushButton {{ background-color: {preset_color}; color: white; padding: 6px; }}")
            preset_button.clicked.connect(lambda checked, name=preset_name, color=preset_color: self.create_preset_category(name, color))
            preset_buttons_layout.addWidget(preset_button)
        
        presets_layout.addLayout(preset_buttons_layout)
        layout.addWidget(presets_group)
    
    def load_parent_categories(self):
        """Load parent categories into combo box"""
        self.parent_category_combo.clear()
        self.parent_category_combo.addItem("Select Parent Category...")
        
        main_categories = self.category_manager.get_main_categories()
        for category in main_categories:
            self.parent_category_combo.addItem(category.name)
    
    def choose_category_color(self):
        """Open color picker for category color"""
        color = QColorDialog.getColor(QColor(self.selected_color), self, "Choose Category Color")
        if color.isValid():
            self.selected_color = color.name()
            self.update_color_button()
    
    def update_color_button(self):
        """Update color button appearance"""
        self.category_color_button.setStyleSheet(
            f"QPushButton {{ background-color: {self.selected_color}; color: white; font-weight: bold; padding: 8px; }}"
        )
        self.category_color_button.setText(f"Color: {self.selected_color}")
    
    def create_category(self):
        """Create a new main category"""
        name = self.category_name_edit.text().strip()
        description = self.category_description_edit.toPlainText().strip()
        
        if not name:
            QMessageBox.warning(self, "Validation Error", "Category name is required")
            return
        
        try:
            # Get category type
            category_type = self.category_type_combo.currentText().lower()

            category_id = self.category_manager.create_category(
                name=name,
                description=description,
                color=self.selected_color,
                category_type=category_type
            )
            
            if category_id:
                # Force refresh of category manager first
                self.category_manager.refresh_categories()

                # Emit signals immediately for UI responsiveness
                self.category_created.emit(category_id)
                self.categories_updated.emit()

                # Reload parent categories
                self.load_parent_categories()

                # Clear form
                self.category_name_edit.clear()
                self.category_description_edit.clear()
                self.category_type_combo.setCurrentText("Expense")  # Reset to default
                self.selected_color = "#007ACC"
                self.update_color_button()

                # Show success message after UI updates
                QMessageBox.information(self, "Success", f"Category '{name}' created successfully!")
                
            else:
                QMessageBox.warning(self, "Error", "Failed to create category. It may already exist.")
                
        except Exception as e:
            self.logger.error(f"Error creating category: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to create category: {str(e)}")
    
    def create_subcategory(self):
        """Create a new subcategory"""
        parent_name = self.parent_category_combo.currentText()
        name = self.subcategory_name_edit.text().strip()
        description = self.subcategory_description_edit.toPlainText().strip()
        
        if parent_name == "Select Parent Category...":
            QMessageBox.warning(self, "Validation Error", "Please select a parent category")
            return
        
        if not name:
            QMessageBox.warning(self, "Validation Error", "Subcategory name is required")
            return
        
        try:
            category_id = self.category_manager.create_category(
                name=name,
                parent_name=parent_name,
                description=description
            )
            
            if category_id:
                # Force refresh of category manager first
                self.category_manager.refresh_categories()

                # Emit signals immediately for UI responsiveness
                self.category_created.emit(category_id)
                self.categories_updated.emit()

                # Clear form
                self.subcategory_name_edit.clear()
                self.subcategory_description_edit.clear()
                self.parent_category_combo.setCurrentIndex(0)

                # Show success message after UI updates
                QMessageBox.information(self, "Success", f"Subcategory '{name}' created under '{parent_name}' successfully!")
                
            else:
                QMessageBox.warning(self, "Error", "Failed to create subcategory. It may already exist.")
                
        except Exception as e:
            self.logger.error(f"Error creating subcategory: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to create subcategory: {str(e)}")
    
    def create_preset_category(self, name: str, color: str):
        """Create a preset category"""
        try:
            category_id = self.category_manager.create_category(
                name=name,
                description=f"Auto-created {name.lower()} category",
                color=color
            )
            
            if category_id:
                # Emit signals immediately for UI responsiveness
                self.category_created.emit(category_id)
                self.categories_updated.emit()
                self.load_parent_categories()
                QMessageBox.information(self, "Success", f"Preset category '{name}' created successfully!")
            else:
                QMessageBox.information(self, "Info", f"Category '{name}' already exists.")
                
        except Exception as e:
            self.logger.error(f"Error creating preset category: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to create preset category: {str(e)}")


class CategoryListWidget(QWidget):
    """Widget for displaying and managing existing categories"""
    
    def __init__(self, category_manager: CategoryManager):
        super().__init__()
        self.category_manager = category_manager
        self.setup_ui()
        self.load_categories()
    
    def setup_ui(self):
        """Setup the UI for category list"""
        layout = QVBoxLayout(self)
        
        # Header
        header_label = QLabel("Existing Categories")
        header_font = QFont()
        header_font.setBold(True)
        header_font.setPointSize(12)
        header_label.setFont(header_font)
        layout.addWidget(header_label)
        
        # Categories table
        self.categories_table = QTableWidget()
        self.categories_table.setColumnCount(4)
        self.categories_table.setHorizontalHeaderLabels(["Category", "Subcategory", "Type", "Description"])
        
        # Set column widths
        header = self.categories_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.Stretch)
        
        layout.addWidget(self.categories_table)
        
        # Refresh button
        refresh_button = QPushButton("Refresh Categories")
        refresh_button.clicked.connect(self.load_categories)
        layout.addWidget(refresh_button)
    
    def load_categories(self):
        """Load categories into the table"""
        try:
            flat_categories = self.category_manager.get_flat_category_list()
            
            self.categories_table.setRowCount(len(flat_categories))
            
            for i, cat_info in enumerate(flat_categories):
                # Category
                self.categories_table.setItem(i, 0, QTableWidgetItem(cat_info["category"]))
                
                # Subcategory
                self.categories_table.setItem(i, 1, QTableWidgetItem(cat_info["sub_category"]))
                
                # Type (determine if it's system or custom)
                category_type = "System" if "main_" in cat_info.get("category_id", "") else "Custom"
                self.categories_table.setItem(i, 2, QTableWidgetItem(category_type))
                
                # Description (placeholder for now)
                self.categories_table.setItem(i, 3, QTableWidgetItem(""))
            
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to load categories: {str(e)}")


class MLCategoryDialog(QDialog):
    """
    Main dialog for managing ML categories
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.category_manager = CategoryManager()
        self.setup_ui()
        self.setModal(True)
    
    def setup_ui(self):
        """Setup the main dialog UI"""
        self.setWindowTitle("ML Category Management")
        self.setGeometry(200, 200, 800, 600)
        
        layout = QVBoxLayout(self)
        
        # Header
        header_label = QLabel("Machine Learning Category Management")
        header_font = QFont()
        header_font.setBold(True)
        header_font.setPointSize(14)
        header_label.setFont(header_font)
        header_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(header_label)
        
        # Description
        desc_label = QLabel("Create and manage categories for intelligent transaction categorization")
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setStyleSheet("color: gray; margin-bottom: 10px;")
        layout.addWidget(desc_label)
        
        # Tab widget
        tab_widget = QTabWidget()
        
        # Create categories tab
        self.creation_widget = CategoryCreationWidget(self.category_manager)
        self.creation_widget.category_created.connect(self.on_category_created)
        self.creation_widget.categories_updated.connect(self.on_categories_updated)
        tab_widget.addTab(self.creation_widget, "Create Categories")
        
        # View categories tab
        self.list_widget = CategoryListWidget(self.category_manager)
        tab_widget.addTab(self.list_widget, "View Categories")
        
        layout.addWidget(tab_widget)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        export_button = QPushButton("Export Categories")
        export_button.clicked.connect(self.export_categories)
        
        close_button = QPushButton("Close")
        close_button.clicked.connect(self.accept)
        close_button.setDefault(True)
        
        button_layout.addWidget(export_button)
        button_layout.addStretch()
        button_layout.addWidget(close_button)
        
        layout.addLayout(button_layout)
    
    def on_category_created(self, category_id: str):
        """Handle category creation"""
        # Refresh the category list
        self.list_widget.load_categories()
        # Also refresh the creation widget's parent category combo
        self.creation_widget.load_parent_categories()

    def on_categories_updated(self):
        """Handle categories update signal"""
        # Force refresh of all category displays
        self.list_widget.load_categories()
        self.creation_widget.load_parent_categories()
    
    def export_categories(self):
        """Export categories to file"""
        try:
            export_path = self.category_manager.export_categories("csv")
            if export_path:
                QMessageBox.information(self, "Export Complete", f"Categories exported to:\n{export_path}")
            else:
                QMessageBox.warning(self, "Export Failed", "Failed to export categories")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Export failed: {str(e)}")
