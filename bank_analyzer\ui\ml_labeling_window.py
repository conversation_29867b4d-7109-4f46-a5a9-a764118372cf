"""
Manual labeling interface for ML transaction categorization
Provides UI for manually categorizing unique transaction descriptions
"""

from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QPushButton, QLabel, QComboBox, QLineEdit, QProgressBar, QTextEdit,
    QSplitter, QGroupBox, QFormLayout, QMessageBox, QHeaderView,
    QCheckBox, QSpinBox, QTabWidget, QFrame, QScrollArea, QDialog,
    QInputDialog, QToolButton, QMenu, QApplication
)
from PySide6.QtCore import Qt, QThread, QTimer, Signal, QSize
from PySide6.QtGui import QFont, QColor, QPalette

import pandas as pd
from typing import List, Dict, Optional, Any
from datetime import datetime
from decimal import Decimal
from pathlib import Path
import logging
import time

from ..ml.training_data_manager import TrainingDataManager, LabelingStats, TransactionFilter
from ..ml.session_training_manager import SessionTrainingManager
from ..ml.category_manager import CategoryManager
from ..ml.data_preparation import UniqueTransaction
from ..ml.hybrid_ml_categorizer import HybridMLCategorizer
from ..models.transaction import RawTransaction
from ..core.transaction_data_manager import TransactionDataManager, DataMergeStrategy, DataMergeOptions
from ..core.logger import get_logger
from .ml_category_dialog import MLCategoryDialog
from .filter_panel import FilterPanel
from .enhanced_transaction_table import SelectableTableWidget, BatchLabelingDialog
from .enhanced_category_selector import EnhancedCategorySelector
from .background_workers import (CategoryRefreshWorker, DataLoadWorker, PerformanceMonitor,
                                FilterWorker, SimilarityWorker, BatchLabelWorker)
from .performance_utils import (global_profiler, profile_operation, ThrottledRefreshManager)
# Removed old training data manager window - now using session-based training


class LabelingWorker(QThread):
    """Worker thread for batch labeling operations"""
    progress_updated = Signal(int)
    labeling_completed = Signal(dict)
    error_occurred = Signal(str)
    
    def __init__(self, training_manager: TrainingDataManager, labels: List[Dict[str, Any]]):
        super().__init__()
        self.training_manager = training_manager
        self.labels = labels
    
    def run(self):
        try:
            total = len(self.labels)
            results = {"success_count": 0, "error_count": 0, "errors": []}
            
            for i, label_data in enumerate(self.labels):
                success = self.training_manager.label_transaction(
                    label_data["hash_id"],
                    label_data["category"],
                    label_data["sub_category"],
                    label_data.get("confidence", 1.0)
                )
                
                if success:
                    results["success_count"] += 1
                else:
                    results["error_count"] += 1
                    results["errors"].append(f"Failed to label {label_data['hash_id']}")
                
                # Update progress
                progress = int((i + 1) / total * 100)
                self.progress_updated.emit(progress)
            
            self.labeling_completed.emit(results)
            
        except Exception as e:
            self.error_occurred.emit(str(e))


class TransactionLabelingWidget(QWidget):
    """Widget for labeling individual transactions with AI assistance"""

    def __init__(self, category_manager: CategoryManager):
        super().__init__()
        self.logger = get_logger(__name__)
        self.category_manager = category_manager
        self.current_transaction: Optional[UniqueTransaction] = None
        self.hybrid_categorizer = HybridMLCategorizer()
        self.ai_suggestions = {}
        self.setup_ui()
    
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # Transaction info group
        info_group = QGroupBox("Transaction Information")
        info_layout = QFormLayout(info_group)
        
        self.description_label = QLabel()
        self.description_label.setWordWrap(True)
        self.description_label.setStyleSheet("font-weight: bold; padding: 8px; background-color: #f0f0f0; border: 1px solid #ddd; border-radius: 4px;")
        self.description_label.setMinimumHeight(60)  # Ensure enough space for full description
        self.description_label.setAlignment(Qt.AlignTop)  # Align text to top
        
        self.frequency_label = QLabel()
        self.amount_range_label = QLabel()
        self.source_info_label = QLabel()

        # Transaction type display
        self.transaction_type_label = QLabel()
        self.transaction_type_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                padding: 6px 12px;
                border-radius: 4px;
                color: white;
            }
        """)

        info_layout.addRow("Description:", self.description_label)
        info_layout.addRow("Transaction Type:", self.transaction_type_label)
        info_layout.addRow("Frequency:", self.frequency_label)
        info_layout.addRow("Amount Range:", self.amount_range_label)
        info_layout.addRow("Sources:", self.source_info_label)
        
        layout.addWidget(info_group)
        
        # Categorization group
        cat_group = QGroupBox("Categorization")
        cat_layout = QFormLayout(cat_group)

        # Enhanced category selector
        self.category_selector = EnhancedCategorySelector(self.category_manager)
        self.category_selector.category_changed.connect(self.on_category_changed)
        self.category_selector.subcategory_changed.connect(self.on_subcategory_changed)
        self.category_selector.categories_updated.connect(self.on_categories_updated)

        cat_layout.addRow("", self.category_selector)

        # Keep references to combo boxes for compatibility
        self.category_combo = self.category_selector.category_combo
        self.subcategory_combo = self.category_selector.subcategory_combo

        self.confidence_spin = QSpinBox()
        self.confidence_spin.setRange(1, 100)
        self.confidence_spin.setValue(100)
        self.confidence_spin.setSuffix("%")

        cat_layout.addRow("Confidence:", self.confidence_spin)

        # Category management buttons row
        management_layout = QHBoxLayout()

        # Quick actions menu button
        self.quick_actions_btn = QToolButton()
        self.quick_actions_btn.setText("Quick Actions")
        self.quick_actions_btn.setPopupMode(QToolButton.InstantPopup)
        self.setup_quick_actions_menu()
        self.quick_actions_btn.setStyleSheet("""
            QToolButton {
                background-color: #FF9800;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
                padding: 6px 12px;
            }
            QToolButton:hover {
                background-color: #F57C00;
            }
        """)

        # Full category manager button
        manage_categories_button = QPushButton("Manage Categories")
        manage_categories_button.clicked.connect(self.open_category_manager)
        manage_categories_button.setStyleSheet("QPushButton { background-color: #9C27B0; color: white; font-weight: bold; padding: 6px; }")

        management_layout.addWidget(self.quick_actions_btn)
        management_layout.addWidget(manage_categories_button)
        management_layout.addStretch()

        cat_layout.addRow("", management_layout)
        
        layout.addWidget(cat_group)
        
        # AI Assistance and Suggestions group
        suggestions_group = QGroupBox("🤖 AI Assistant & Suggestions")
        suggestions_layout = QVBoxLayout(suggestions_group)

        # AI assistance controls
        ai_controls_layout = QHBoxLayout()

        self.get_ai_suggestion_btn = QPushButton("🤖 Get AI Suggestion")
        self.get_ai_suggestion_btn.clicked.connect(self.get_ai_suggestion)
        self.get_ai_suggestion_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
                padding: 6px 12px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)

        self.apply_ai_suggestion_btn = QPushButton("✅ Apply AI Suggestion")
        self.apply_ai_suggestion_btn.clicked.connect(self.apply_ai_suggestion)
        self.apply_ai_suggestion_btn.setEnabled(False)
        self.apply_ai_suggestion_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
                padding: 6px 12px;
            }
            QPushButton:hover {
                background-color: #388E3C;
            }
            QPushButton:disabled {
                background-color: #BDBDBD;
                color: #757575;
            }
        """)

        ai_controls_layout.addWidget(self.get_ai_suggestion_btn)
        ai_controls_layout.addWidget(self.apply_ai_suggestion_btn)
        ai_controls_layout.addStretch()

        suggestions_layout.addLayout(ai_controls_layout)

        # Suggestions display
        self.suggestions_text = QTextEdit()
        self.suggestions_text.setMaximumHeight(120)
        self.suggestions_text.setReadOnly(True)
        self.suggestions_text.setPlaceholderText("AI suggestions and similar transaction patterns will appear here...")

        suggestions_layout.addWidget(self.suggestions_text)
        layout.addWidget(suggestions_group)
        
        # Action buttons
        button_layout = QHBoxLayout()
        
        self.label_button = QPushButton("Label Transaction")
        self.label_button.clicked.connect(self.label_transaction)
        self.label_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        
        self.skip_button = QPushButton("Skip")
        self.skip_button.clicked.connect(self.skip_transaction)
        
        button_layout.addWidget(self.label_button)
        button_layout.addWidget(self.skip_button)
        button_layout.addStretch()
        
        layout.addLayout(button_layout)
        
        # Load categories
        self.load_categories()
    
    def load_categories(self):
        """Load categories into combo boxes"""
        if hasattr(self, 'category_selector'):
            self.category_selector.load_categories()
        else:
            # Fallback for compatibility
            self.category_combo.clear()
            self.category_combo.addItem("Select Category...")

            main_categories = self.category_manager.get_main_categories()
            for category in main_categories:
                self.category_combo.addItem(category.name)

    def on_category_changed(self, category_name: str):
        """Handle category selection change"""
        # This is now handled by the enhanced category selector
        pass

    def on_subcategory_changed(self, subcategory_name: str):
        """Handle subcategory selection change"""
        # This can be used for additional logic if needed
        pass

    def on_categories_updated(self):
        """Handle categories update from the enhanced selector"""
        # Refresh any other components that depend on categories
        self.reload_categories()

    def reload_categories(self):
        """Reload categories in the selector"""
        if hasattr(self, 'category_selector'):
            self.category_selector.refresh_categories()

    def clear_transaction_display(self):
        """Clear all transaction display elements"""
        try:
            # Clear transaction information labels
            if hasattr(self, 'description_label'):
                self.description_label.setText("No transaction selected")
            if hasattr(self, 'frequency_label'):
                self.frequency_label.setText("0 times")
            if hasattr(self, 'amount_range_label'):
                self.amount_range_label.setText("₹0.00")
            if hasattr(self, 'transaction_type_label'):
                self.transaction_type_label.setText("No Type")
                self.transaction_type_label.setStyleSheet("")

            # Clear category selector
            if hasattr(self, 'category_selector'):
                # Reset category selector to default state
                if hasattr(self.category_selector, 'category_combo'):
                    self.category_selector.category_combo.setCurrentIndex(0)  # "Select Category..."
                if hasattr(self.category_selector, 'subcategory_combo'):
                    self.category_selector.subcategory_combo.setCurrentIndex(0)  # "Select Sub-category..."

            # Clear any suggestion displays
            if hasattr(self, 'suggestions_widget'):
                self.suggestions_widget.clear()

            # Reset current transaction
            self.current_transaction = None

            self.logger.info("Transaction display cleared successfully")

        except Exception as e:
            self.logger.error(f"Error clearing transaction display: {e}")

    def set_transaction(self, transaction: UniqueTransaction, suggestions: List[Dict[str, Any]] = None):
        """Set the current transaction to label"""
        self.current_transaction = transaction

        # Handle None transaction (clear the widget)
        if not transaction:
            self.clear_transaction_display()
            return

        # Update UI with safe attribute access
        description = getattr(transaction, 'description', '[No Description]') or '[No Description]'
        frequency = getattr(transaction, 'frequency', 0)
        amount_range = getattr(transaction, 'amount_range', [0, 0])

        self.description_label.setText(description)
        self.frequency_label.setText(f"{frequency} times")

        # Handle amount range safely
        if isinstance(amount_range, (list, tuple)) and len(amount_range) >= 2:
            self.amount_range_label.setText(f"₹{amount_range[0]:.2f} - ₹{amount_range[1]:.2f}")
        else:
            self.amount_range_label.setText(f"₹{amount_range}")

        # Update transaction type display and category filtering
        predominant_type = transaction.get_predominant_transaction_type()

        if predominant_type == "debit":
            self.transaction_type_label.setText("DEBIT (Money Out)")
            self.transaction_type_label.setStyleSheet("""
                QLabel {
                    font-weight: bold;
                    padding: 6px 12px;
                    border-radius: 4px;
                    color: white;
                    background-color: #f44336;
                }
            """)
            transaction_type = "debit"
        elif predominant_type == "credit":
            self.transaction_type_label.setText("CREDIT (Money In)")
            self.transaction_type_label.setStyleSheet("""
                QLabel {
                    font-weight: bold;
                    padding: 6px 12px;
                    border-radius: 4px;
                    color: white;
                    background-color: #4CAF50;
                }
            """)
            transaction_type = "credit"
        else:
            self.transaction_type_label.setText("MIXED (Both Debit & Credit)")
            self.transaction_type_label.setStyleSheet("""
                QLabel {
                    font-weight: bold;
                    padding: 6px 12px;
                    border-radius: 4px;
                    color: white;
                    background-color: #FF9800;
                }
            """)
            transaction_type = None  # Show all categories for mixed transactions

        # Set transaction type on category selector to filter categories
        if hasattr(self, 'category_selector'):
            self.category_selector.set_transaction_type(transaction_type)
        
        source_info = []
        if transaction.source_files:
            source_info.append(f"Files: {len(transaction.source_files)}")
        if transaction.bank_names:
            source_info.append(f"Banks: {', '.join(transaction.bank_names)}")
        self.source_info_label.setText("; ".join(source_info))
        
        # Show suggestions
        suggestions_text = ""
        if suggestions:
            suggestions_text += "📊 Similar transactions:\n"
            for i, suggestion in enumerate(suggestions[:3], 1):
                suggestions_text += f"{i}. {suggestion['category']} > {suggestion['sub_category']} "
                suggestions_text += f"(similarity: {suggestion['similarity']:.2f})\n"

        # Clear AI suggestions for new transaction
        self.ai_suggestions = {}
        self.apply_ai_suggestion_btn.setEnabled(False)

        if suggestions_text:
            self.suggestions_text.setText(suggestions_text + "\n💡 Click '🤖 Get AI Suggestion' for AI-powered categorization help!")
        else:
            self.suggestions_text.setText("💡 Click '🤖 Get AI Suggestion' for AI-powered categorization help!")

        # Reset form
        self.category_combo.setCurrentIndex(0)
        self.subcategory_combo.setCurrentIndex(0)
        self.confidence_spin.setValue(100)
    
    def label_transaction(self):
        """Label the current transaction with validation"""
        if not self.current_transaction:
            QMessageBox.warning(self, "Warning", "No transaction selected for labeling")
            return

        # Get category and subcategory from enhanced selector
        if hasattr(self, 'category_selector'):
            category = self.category_selector.get_selected_category()
            subcategory = self.category_selector.get_selected_subcategory()
        else:
            # Fallback for compatibility
            category = self.category_combo.currentText()
            subcategory = self.subcategory_combo.currentText()

            if category == "Select Category...":
                category = ""
            if subcategory == "Select Sub-category...":
                subcategory = ""

        # Validate selections
        validation_result = self.validate_labeling_data(category, subcategory)
        if not validation_result["valid"]:
            if validation_result["show_dialog"]:
                self.show_labeling_conflict_dialog(validation_result)
            else:
                QMessageBox.warning(self, "Validation Error", validation_result["message"])
            return

        confidence = self.confidence_spin.value() / 100.0

        # Final validation before emitting
        if confidence < 0.1:
            reply = QMessageBox.question(
                self,
                "Low Confidence",
                f"You've set a very low confidence ({confidence*100:.0f}%).\n"
                f"Are you sure you want to proceed?",
                QMessageBox.Yes | QMessageBox.No
            )
            if reply != QMessageBox.Yes:
                return

        # Emit signal with labeling data
        self.transaction_labeled.emit({
            "hash_id": self.current_transaction.hash_id,
            "category": category,
            "sub_category": subcategory,
            "confidence": confidence
        })

    def validate_labeling_data(self, category: str, subcategory: str) -> Dict[str, Any]:
        """Validate labeling data before submission"""
        if not category or not subcategory:
            return {
                "valid": False,
                "message": "Please select both category and sub-category",
                "show_dialog": False
            }

        # Check if category/subcategory combination exists
        try:
            subcategories = self.category_manager.get_subcategories(category)
            if not any(sub.name == subcategory for sub in subcategories):
                return {
                    "valid": False,
                    "message": f"Subcategory '{subcategory}' does not exist under '{category}'",
                    "show_dialog": True,
                    "suggested_action": "create_subcategory",
                    "category": category,
                    "subcategory": subcategory
                }
        except Exception as e:
            return {
                "valid": False,
                "message": f"Error validating categories: {str(e)}",
                "show_dialog": False
            }

        return {"valid": True, "message": ""}

    def show_labeling_conflict_dialog(self, validation_result: Dict[str, Any]):
        """Show dialog for resolving labeling conflicts"""
        if validation_result.get("suggested_action") == "create_subcategory":
            category = validation_result.get("category", "")
            subcategory = validation_result.get("subcategory", "")

            reply = QMessageBox.question(
                self,
                "Subcategory Not Found",
                f"The subcategory '{subcategory}' does not exist under '{category}'.\n\n"
                f"Would you like to:\n"
                f"• Create the subcategory '{subcategory}' (Yes)\n"
                f"• Choose a different subcategory (No)",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # Create the subcategory
                try:
                    subcategory_id = self.category_manager.create_category(
                        name=subcategory,
                        parent_name=category,
                        description=f"Created during transaction labeling"
                    )

                    if subcategory_id:
                        # Refresh categories and retry labeling
                        self.reload_categories()
                        QMessageBox.information(
                            self,
                            "Success",
                            f"Subcategory '{subcategory}' created successfully!\n"
                            f"You can now proceed with labeling."
                        )
                        # Set the newly created subcategory
                        if hasattr(self, 'category_selector'):
                            self.category_selector.set_subcategory(subcategory)
                    else:
                        QMessageBox.warning(
                            self,
                            "Error",
                            f"Failed to create subcategory '{subcategory}'"
                        )
                except Exception as e:
                    QMessageBox.critical(
                        self,
                        "Error",
                        f"Failed to create subcategory: {str(e)}"
                    )
    
    def skip_transaction(self):
        """Skip the current transaction"""
        self.transaction_skipped.emit()

    def get_ai_suggestion(self):
        """Get AI suggestion for the current transaction"""
        if not self.current_transaction:
            QMessageBox.warning(self, "Warning", "No transaction selected")
            return

        # Additional validation for transaction attributes
        if not hasattr(self.current_transaction, 'description') or not hasattr(self.current_transaction, 'amount_range'):
            QMessageBox.warning(self, "Warning", "Transaction data is incomplete")
            return

        try:
            # Show loading state
            self.get_ai_suggestion_btn.setEnabled(False)
            self.get_ai_suggestion_btn.setText("🔄 Getting AI Suggestion...")

            # Get transaction data safely
            description = self.current_transaction.description or "[No Description]"
            amount_range = getattr(self.current_transaction, 'amount_range', [0, 0])
            amount = float(amount_range[0]) if isinstance(amount_range, (list, tuple)) and len(amount_range) > 0 else 0.0

            # Create a RawTransaction for AI processing
            raw_txn = RawTransaction(
                description=description,
                amount=amount,
                date=datetime.now().strftime("%Y-%m-%d")
            )

            # Get AI suggestions
            ai_suggestions = self.hybrid_categorizer.get_ai_suggestions_for_manual_labeling([raw_txn])

            if raw_txn.description in ai_suggestions:
                suggestion = ai_suggestions[raw_txn.description]
                self.ai_suggestions = suggestion

                # Update suggestions display
                current_text = self.suggestions_text.toPlainText()
                ai_text = f"\n🤖 AI Suggestion:\n"
                ai_text += f"Category: {suggestion['category']}\n"
                ai_text += f"Subcategory: {suggestion['subcategory']}\n"
                ai_text += f"Confidence: {suggestion['confidence']:.1%}\n"
                ai_text += f"Notes: {suggestion['notes']}\n"
                ai_text += f"💡 {suggestion['recommendation']}"

                self.suggestions_text.setText(current_text + ai_text)
                self.apply_ai_suggestion_btn.setEnabled(True)

            else:
                QMessageBox.information(self, "AI Suggestion",
                                      "No AI suggestion available for this transaction. "
                                      "This might be due to budget constraints or the transaction "
                                      "being filtered out by smart filtering.")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to get AI suggestion: {str(e)}")

        finally:
            # Reset button state
            self.get_ai_suggestion_btn.setEnabled(True)
            self.get_ai_suggestion_btn.setText("🤖 Get AI Suggestion")

    def apply_ai_suggestion(self):
        """Apply the AI suggestion to the form"""
        if not self.ai_suggestions:
            QMessageBox.warning(self, "Warning", "No AI suggestion available")
            return

        try:
            # Set category and subcategory using the enhanced selector
            if hasattr(self, 'category_selector'):
                self.category_selector.set_category(self.ai_suggestions['category'])
                self.category_selector.set_subcategory(self.ai_suggestions['subcategory'])

            # Set confidence based on AI confidence
            ai_confidence = int(self.ai_suggestions['confidence'] * 100)
            self.confidence_spin.setValue(ai_confidence)

            # Update suggestions text to show applied
            current_text = self.suggestions_text.toPlainText()
            updated_text = current_text.replace("🤖 AI Suggestion:", "✅ AI Suggestion Applied:")
            self.suggestions_text.setText(updated_text)

            # Disable apply button
            self.apply_ai_suggestion_btn.setEnabled(False)

            QMessageBox.information(self, "AI Suggestion Applied",
                                  f"Applied AI suggestion:\n"
                                  f"Category: {self.ai_suggestions['category']}\n"
                                  f"Subcategory: {self.ai_suggestions['subcategory']}\n"
                                  f"Confidence: {ai_confidence}%\n\n"
                                  f"Please review and click 'Label Transaction' to confirm.")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to apply AI suggestion: {str(e)}")

    def reload_categories(self):
        """Reload categories from the category manager"""
        try:
            if hasattr(self, 'category_selector'):
                # Store current transaction context
                current_transaction = getattr(self, 'current_transaction', None)

                # Use enhanced category selector's refresh method
                self.category_selector.refresh_categories()

                # If there was a current transaction, reapply its transaction type filter
                # This ensures the filter is correct but new categories are still loaded
                if current_transaction:
                    predominant_type = current_transaction.get_predominant_transaction_type()
                    if predominant_type == "debit":
                        transaction_type = "debit"
                    elif predominant_type == "credit":
                        transaction_type = "credit"
                    else:
                        transaction_type = None

                    # Reapply the transaction type filter
                    self.category_selector.set_transaction_type(transaction_type)
            else:
                # Fallback for compatibility
                # Store current selections
                current_category = self.category_combo.currentText()
                current_subcategory = self.subcategory_combo.currentText()

                # Reload categories
                self.load_categories()

                # Try to restore selections if they still exist
                if current_category and current_category != "Select Category...":
                    index = self.category_combo.findText(current_category)
                    if index >= 0:
                        self.category_combo.setCurrentIndex(index)
                        # This will trigger on_category_changed and reload subcategories

                        if current_subcategory and current_subcategory != "Select Sub-category...":
                            sub_index = self.subcategory_combo.findText(current_subcategory)
                            if sub_index >= 0:
                                self.subcategory_combo.setCurrentIndex(sub_index)

        except Exception as e:
            QMessageBox.warning(self, "Warning", f"Error reloading categories: {str(e)}")

    def setup_quick_actions_menu(self):
        """Setup the quick actions menu"""
        menu = QMenu(self)

        # Add preset categories
        preset_action = menu.addAction("Add Preset Categories")
        preset_action.triggered.connect(self.add_preset_categories)

        # Edit existing category
        edit_action = menu.addAction("Edit Selected Category")
        edit_action.triggered.connect(self.edit_current_category)

        # Delete category
        delete_action = menu.addAction("Delete Selected Category")
        delete_action.triggered.connect(self.delete_current_category)

        menu.addSeparator()

        # Import/Export
        import_action = menu.addAction("Import Categories")
        import_action.triggered.connect(self.import_categories)

        export_action = menu.addAction("Export Categories")
        export_action.triggered.connect(self.export_categories)

        menu.addSeparator()

        # Training Data Management
        training_data_action = menu.addAction("🗂️ Manage Training Data")
        # Find the MLLabelingWindow parent
        parent_window = self.parent()
        while parent_window and not hasattr(parent_window, 'open_training_data_manager'):
            parent_window = parent_window.parent()

        if parent_window:
            training_data_action.triggered.connect(parent_window.open_training_data_manager)
        else:
            training_data_action.setEnabled(False)

        self.quick_actions_btn.setMenu(menu)

    # Note: Quick add category/subcategory functionality is now handled
    # by the EnhancedCategorySelector widget

    def add_preset_categories(self):
        """Add common preset categories"""
        presets = [
            ("Food & Dining", "#E91E63"),
            ("Transportation", "#FF9800"),
            ("Entertainment", "#9C27B0"),
            ("Healthcare", "#4CAF50"),
            ("Shopping", "#2196F3"),
            ("Utilities", "#607D8B"),
            ("Education", "#795548"),
            ("Travel", "#FF5722")
        ]

        added_count = 0
        for name, color in presets:
            try:
                category_id = self.category_manager.create_category(
                    name=name,
                    description=f"Preset {name.lower()} category",
                    color=color
                )
                if category_id:
                    added_count += 1
            except Exception:
                continue  # Skip if already exists or error

        if added_count > 0:
            self.load_categories()
            QMessageBox.information(
                self,
                "Success",
                f"Added {added_count} preset categories!"
            )
        else:
            QMessageBox.information(
                self,
                "Info",
                "All preset categories already exist."
            )

    def edit_current_category(self):
        """Edit the currently selected category"""
        current_category = self.category_combo.currentText()

        if not current_category or current_category == "Select Category...":
            QMessageBox.warning(
                self,
                "Warning",
                "Please select a category first"
            )
            return

        # Open the full category editor focused on this category
        self.open_category_manager()

    def delete_current_category(self):
        """Delete the currently selected category"""
        current_category = self.category_combo.currentText()

        if not current_category or current_category == "Select Category...":
            QMessageBox.warning(
                self,
                "Warning",
                "Please select a category first"
            )
            return

        # Find category ID
        category_id = None
        for cat in self.category_manager.get_main_categories():
            if cat.name == current_category:
                category_id = cat.id
                break

        if not category_id:
            QMessageBox.warning(self, "Warning", "Category not found")
            return

        # Confirm deletion
        reply = QMessageBox.question(
            self,
            "Confirm Deletion",
            f"Are you sure you want to delete the category '{current_category}'?\n\n"
            f"This will also delete all its subcategories and may affect existing transaction labels.",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                success = self.category_manager.delete_category(category_id, force=True)
                if success:
                    self.load_categories()
                    QMessageBox.information(
                        self,
                        "Success",
                        f"Category '{current_category}' deleted successfully"
                    )
                else:
                    QMessageBox.critical(
                        self,
                        "Error",
                        "Failed to delete category"
                    )
            except Exception as e:
                QMessageBox.critical(
                    self,
                    "Error",
                    f"Failed to delete category: {str(e)}"
                )

    def import_categories(self):
        """Import categories from file"""
        QMessageBox.information(
            self,
            "Feature Coming Soon",
            "Category import functionality will be available in a future update."
        )

    def export_categories(self):
        """Export categories to file"""
        try:
            export_path = self.category_manager.export_categories("csv")
            if export_path:
                QMessageBox.information(
                    self,
                    "Export Complete",
                    f"Categories exported to:\n{export_path}"
                )
            else:
                QMessageBox.warning(
                    self,
                    "Export Failed",
                    "Failed to export categories"
                )
        except Exception as e:
            QMessageBox.critical(
                self,
                "Error",
                f"Export failed: {str(e)}"
            )

    def open_category_manager(self):
        """Open category management dialog"""
        try:
            dialog = MLCategoryDialog(self)

            # Connect to category update signals for real-time updates
            if hasattr(dialog, 'creation_widget'):
                dialog.creation_widget.category_created.connect(self.on_category_created_externally)
                dialog.creation_widget.categories_updated.connect(self.on_categories_updated_externally)

            try:
                dialog.exec()
            except KeyboardInterrupt:
                # User interrupted the category manager dialog
                self.logger.info("Category manager dialog interrupted by user")
                pass
            # Always reload categories after dialog closes (regardless of result)
            self.reload_categories()
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to open category manager: {str(e)}")

    def on_category_created_externally(self, category_id: str):
        """Handle category creation from external dialogs"""
        try:
            # Force refresh of category manager
            self.category_manager.refresh_categories()

            # Refresh categories in this widget
            self.reload_categories()

            self.logger.info(f"Category created externally: {category_id}")

        except Exception as e:
            self.logger.error(f"Error handling external category creation: {str(e)}")

    def on_categories_updated_externally(self):
        """Handle categories update from external dialogs"""
        try:
            # This is called during category creation/update from MLCategoryDialog
            # Force refresh of categories
            self.reload_categories()

            self.logger.info("Categories updated externally")

        except Exception as e:
            self.logger.error(f"Error handling external categories update: {str(e)}")

    # Signals
    from PySide6.QtCore import Signal
    transaction_labeled = Signal(dict)
    transaction_skipped = Signal()


class MLLabelingWindow(QMainWindow):
    """
    Main window for ML transaction labeling
    Provides interface for manually categorizing unique transaction descriptions
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger(__name__)

        # Performance optimization components
        global_profiler.start("ml_window_init")

        # Initialize refresh manager first (lightweight)
        self.refresh_manager = ThrottledRefreshManager(self)
        self.refresh_manager.refresh_requested.connect(self._handle_throttled_refresh)

        # Initialize managers with lazy loading
        global_profiler.start("managers_init")
        self._training_manager = None  # Lazy initialization
        self._category_manager = None  # Lazy initialization
        self.transaction_data_manager = TransactionDataManager("bank_analyzer_config/sessions")
        self.session_training_manager = SessionTrainingManager("bank_analyzer_config/ml_data")
        global_profiler.end("managers_init")

        # Current data (initialize empty for fast startup)
        self.unlabeled_transactions: List[UniqueTransaction] = []
        self.filtered_transactions: List[UniqueTransaction] = []
        self.current_index = 0
        self.current_session_id: Optional[str] = None
        self.current_training_session_id: Optional[str] = None  # For session-based training
        self.current_filter: Optional[TransactionFilter] = None

        # Load auto-reload prevention state
        self._prevent_auto_reload = self._load_auto_reload_state()

        # Data loading state (for clean start behavior)
        self._data_explicitly_loaded = False

        # Worker thread
        self.labeling_worker: Optional[LabelingWorker] = None

        # UI components
        self.filter_panel: Optional[FilterPanel] = None
        self.enhanced_table: Optional[SelectableTableWidget] = None

        # Setup UI first (fast)
        global_profiler.start("ui_setup")
        self.setup_ui()
        global_profiler.end("ui_setup")

        # Don't automatically load data on startup - wait for user action
        # QTimer.singleShot(50, self._load_initial_data_async)  # Disabled for clean start

        # Instead, show clean start message after UI is ready
        QTimer.singleShot(100, self._show_clean_start_message)

        global_profiler.end("ml_window_init")
        self.logger.info("ML Labeling Window initialized with performance optimizations")

    @property
    def training_manager(self):
        """Lazy initialization of training manager"""
        if self._training_manager is None:
            self._training_manager = TrainingDataManager()
        return self._training_manager

    @property
    def category_manager(self):
        """Lazy initialization of category manager"""
        if self._category_manager is None:
            self._category_manager = CategoryManager()
        return self._category_manager

    def _load_initial_data_async(self):
        """Load initial data asynchronously to prevent blocking UI initialization"""
        try:
            global_profiler.start("async_data_load")

            # Show loading indicator
            self.statusBar().showMessage("Loading transaction data...")

            # Load data in background
            self.load_initial_data()

            # Update status
            self.statusBar().showMessage("Ready", 2000)

            global_profiler.end("async_data_load")
            self.logger.info("Asynchronous data loading completed")

        except Exception as e:
            self.logger.error(f"Error in async data loading: {str(e)}")
            self.statusBar().showMessage("Error loading data", 5000)

    def _show_clean_start_message(self):
        """Show clean start message after UI is ready"""
        try:
            # Start with clean session
            self.transaction_data_manager.start_clean_session()

            # Show clean start message
            self.statusBar().showMessage("Ready to start - Use Data → Load Existing Data or Data → Import from Sessions", 10000)

            # Show empty stats WITHOUT calling update_stats() which would load data
            self._show_empty_stats_without_loading()

            self.logger.info("Clean start message displayed")

        except Exception as e:
            self.logger.error(f"Error showing clean start message: {str(e)}")
            self.statusBar().showMessage("Ready", 2000)
    
    def setup_ui(self):
        """Setup the main window UI with optimized loading"""
        self.setWindowTitle("🤖 Manual Labeling with AI Assistant")
        self.setGeometry(100, 100, 1400, 800)  # Larger window for new features

        # Create menu bar
        self.create_menu_bar()

        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Main layout - three panels
        main_layout = QHBoxLayout(central_widget)

        # Create placeholder widgets first for fast UI display
        self._create_placeholder_ui(main_layout)

        # Defer heavy UI components creation
        QTimer.singleShot(10, self._create_heavy_ui_components)

    def create_menu_bar(self):
        """Create menu bar with session management options"""
        menubar = self.menuBar()

        # Session menu
        session_menu = menubar.addMenu("📁 Session")

        # Save session
        save_action = session_menu.addAction("💾 Save Current Session")
        save_action.triggered.connect(self.save_current_session)
        save_action.setToolTip("Save current labeling work as a named session")

        # Load session
        load_action = session_menu.addAction("📂 Load Session")
        load_action.triggered.connect(self.open_session_management)
        load_action.setToolTip("Load a previous labeling session")

        session_menu.addSeparator()

        # Backup data
        backup_action = session_menu.addAction("💾 Create Backup")
        backup_action.triggered.connect(self.backup_current_data)
        backup_action.setToolTip("Create a backup of current labeling work")

        # Clear data with backup
        clear_action = session_menu.addAction("🗑️ Clear All Data")
        clear_action.triggered.connect(self.clear_current_data)
        clear_action.setToolTip("Clear all data with automatic backup")

        session_menu.addSeparator()

        # Session management
        manage_action = session_menu.addAction("🗂️ Manage All Sessions")
        manage_action.triggered.connect(self.open_session_management)
        manage_action.setToolTip("View, load, and manage all sessions")

        # Data menu
        data_menu = menubar.addMenu("📊 Data")

        # Load existing data
        load_data_action = data_menu.addAction("📥 Load Existing Data")
        load_data_action.triggered.connect(self.load_data_explicitly)
        load_data_action.setToolTip("Load existing transaction data from storage")

        # Import from sessions
        import_session_action = data_menu.addAction("📂 Import from Sessions")
        import_session_action.triggered.connect(self.import_from_sessions)
        import_session_action.setToolTip("Import transaction data from processed sessions")

        # Load directly from latest session (bypass all caches)
        load_latest_action = data_menu.addAction("🚀 Load Latest Session Data")
        load_latest_action.triggered.connect(self.load_latest_session_directly)
        load_latest_action.setToolTip("Bypass all caches and load directly from the most recent session")

        # Load specific session with picker
        load_session_action = data_menu.addAction("📋 Choose Session to Load")
        load_session_action.triggered.connect(self.choose_and_load_session)
        load_session_action.setToolTip("Choose a specific session to load from a list of all available sessions")

        # Session-based training management
        data_menu.addSeparator()
        session_training_action = data_menu.addAction("🎯 Manage Session Training")
        session_training_action.triggered.connect(self.manage_session_training)
        session_training_action.setToolTip("Manage session-based training data and combine labeled data from multiple sessions")

        # Debug sessions
        debug_sessions_action = data_menu.addAction("🔍 Debug Sessions")
        debug_sessions_action.triggered.connect(self.debug_sessions)
        debug_sessions_action.setToolTip("Show detailed information about all sessions")

        # Replace training data with latest session
        replace_training_action = data_menu.addAction("🔄 Replace Training Data with Latest Session")
        replace_training_action.triggered.connect(self.replace_training_data_with_latest_session)
        replace_training_action.setToolTip("Replace all training data with the latest session data")

        # NUCLEAR OPTION: Delete old data and load only from session
        nuclear_action = data_menu.addAction("💥 NUCLEAR: Delete Old Data & Load Session")
        nuclear_action.triggered.connect(self.nuclear_load_session_only)
        nuclear_action.setToolTip("DELETE all old training data and load ONLY from latest session")

        data_menu.addSeparator()

        # Refresh data
        refresh_action = data_menu.addAction("🔄 Refresh Data")
        refresh_action.triggered.connect(self.load_transaction_batch)
        refresh_action.setToolTip("Refresh transaction data from storage")

        # Force refresh (clear caches)
        force_refresh_action = data_menu.addAction("🔄 Force Refresh (Clear Cache)")
        force_refresh_action.triggered.connect(self.force_refresh_data)
        force_refresh_action.setToolTip("Clear all caches and force reload of fresh data")

        # Statistics
        stats_action = data_menu.addAction("📈 View Statistics")
        stats_action.triggered.connect(self.show_detailed_stats)
        stats_action.setToolTip("Show detailed labeling statistics")

        # Debug info
        debug_action = data_menu.addAction("🔍 Debug Data Info")
        debug_action.triggered.connect(self.show_debug_data_info)
        debug_action.setToolTip("Show debug information about loaded data")

        # Show file sources
        file_sources_action = data_menu.addAction("📁 Show Data File Sources")
        file_sources_action.triggered.connect(self.show_data_file_sources)
        file_sources_action.setToolTip("Show exactly which files contain the 402 transactions")

        data_menu.addSeparator()

        # Export options
        export_action = data_menu.addAction("📤 Export Labeled Data")
        export_action.triggered.connect(self.export_labeled_data)
        export_action.setToolTip("Export labeled transactions to CSV")

        # Help menu
        help_menu = menubar.addMenu("❓ Help")

        # Session management help
        session_help_action = help_menu.addAction("📖 Session Management Guide")
        session_help_action.triggered.connect(self.show_session_help)
        session_help_action.setToolTip("Learn how to use session management features")

        # Labeling help
        labeling_help_action = help_menu.addAction("🏷️ Labeling Guide")
        labeling_help_action.triggered.connect(self.show_labeling_help)
        labeling_help_action.setToolTip("Learn how to use the labeling interface")

    def _create_placeholder_ui(self, main_layout):
        """Create lightweight placeholder UI for fast initial display"""
        # Simple loading label
        loading_label = QLabel("Loading ML Labeling Interface...")
        loading_label.setAlignment(Qt.AlignCenter)
        loading_label.setStyleSheet("font-size: 16px; color: #666; padding: 50px;")
        main_layout.addWidget(loading_label)
        self._loading_label = loading_label

    def _create_heavy_ui_components(self):
        """Create the actual heavy UI components after initial display"""
        try:
            # Remove loading placeholder
            if hasattr(self, '_loading_label'):
                self._loading_label.setParent(None)
                del self._loading_label

            # Get the central widget layout
            central_widget = self.centralWidget()
            main_layout = central_widget.layout()

            # Left panel - Filters
            filter_panel = self.create_filter_panel()

            # Middle panel - Enhanced transaction table
            middle_panel = self.create_middle_panel()

            # Right panel - Labeling interface
            right_panel = self.create_right_panel()

            # Splitter with three sections
            splitter = QSplitter(Qt.Horizontal)
            splitter.addWidget(filter_panel)
            splitter.addWidget(middle_panel)
            splitter.addWidget(right_panel)
            splitter.setSizes([300, 600, 400])  # Filter, Table, Labeling

            main_layout.addWidget(splitter)

        except Exception as e:
            self.logger.error(f"Error creating heavy UI components: {str(e)}")

        # Status bar
        self.statusBar().showMessage("Ready - Manual Labeling with AI Assistant")

    def create_filter_panel(self) -> QWidget:
        """Create filter panel with advanced filtering options"""
        # Create scroll area for filter panel
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # Create filter panel
        self.filter_panel = FilterPanel()
        self.filter_panel.filter_changed.connect(self.on_filter_changed)
        self.filter_panel.filter_reset.connect(self.on_filter_reset)

        # Load categories into filter panel
        self.load_filter_categories()

        scroll_area.setWidget(self.filter_panel)
        return scroll_area

    def create_middle_panel(self) -> QWidget:
        """Create middle panel with enhanced transaction table and batch controls"""
        panel = QWidget()
        layout = QVBoxLayout(panel)

        # Stats and controls group
        controls_group = QGroupBox("Batch Operations & Statistics")
        controls_layout = QVBoxLayout(controls_group)

        # Statistics row
        stats_layout = QHBoxLayout()
        self.progress_bar = QProgressBar()
        self.stats_label = QLabel("Loading...")
        stats_layout.addWidget(QLabel("Progress:"))
        stats_layout.addWidget(self.progress_bar)
        stats_layout.addWidget(self.stats_label)
        controls_layout.addLayout(stats_layout)

        # Session and batch controls
        session_layout = QHBoxLayout()

        self.start_session_button = QPushButton("Start Session")
        self.start_session_button.clicked.connect(self.start_labeling_session)

        self.end_session_button = QPushButton("End Session")
        self.end_session_button.clicked.connect(self.end_labeling_session)
        self.end_session_button.setEnabled(False)

        self.batch_size_spin = QSpinBox()
        self.batch_size_spin.setRange(10, 500)
        self.batch_size_spin.setValue(50)

        self.load_batch_button = QPushButton("Load Batch")
        self.load_batch_button.clicked.connect(self.load_transaction_batch)

        session_layout.addWidget(self.start_session_button)
        session_layout.addWidget(self.end_session_button)
        session_layout.addWidget(QLabel("Batch Size:"))
        session_layout.addWidget(self.batch_size_spin)
        session_layout.addWidget(self.load_batch_button)
        session_layout.addStretch()

        controls_layout.addLayout(session_layout)

        # Batch operation controls
        batch_ops_layout = QHBoxLayout()

        self.batch_label_button = QPushButton("Batch Label Selected")
        self.batch_label_button.setObjectName("batchLabelButton")  # Set object name for specific styling
        self.batch_label_button.clicked.connect(self.on_batch_label_button_clicked)
        self.batch_label_button.setEnabled(False)


        self.batch_label_button.setToolTip(
            "Apply the same category label to all selected transactions.\n"
            "Select one or more transactions using checkboxes to enable this button."
        )
        # Apply styling with a more robust approach
        self._apply_batch_button_styling()

        self.select_similar_button = QPushButton("Select Similar")
        self.select_similar_button.clicked.connect(self.on_select_similar_button_clicked)
        self.select_similar_button.setEnabled(False)
        self.select_similar_button.setToolTip(
            "Automatically select transactions similar to the currently selected one.\n"
            "Select exactly ONE transaction using its checkbox to enable this button."
        )
        self.select_similar_button.setStyleSheet("""
            QPushButton {
                background-color: #9C27B0;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #7B1FA2;
            }
            QPushButton:disabled {
                background-color: #CCCCCC;
                color: #666666;
            }
        """)

        self.clear_selection_button = QPushButton("Clear Selection")
        self.clear_selection_button.clicked.connect(self.clear_all_selections)

        self.edit_categories_button = QPushButton("Edit Categories")
        self.edit_categories_button.clicked.connect(self.open_category_editor)
        self.edit_categories_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)

        # Help button for user guidance
        self.help_button = QPushButton("❓ Help")
        self.help_button.clicked.connect(self.show_help_dialog)
        self.help_button.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)





        self.selection_count_label = QLabel("Selected: 0")

        batch_ops_layout.addWidget(self.batch_label_button)
        batch_ops_layout.addWidget(self.select_similar_button)
        batch_ops_layout.addWidget(self.clear_selection_button)
        batch_ops_layout.addWidget(self.edit_categories_button)
        batch_ops_layout.addWidget(self.help_button)



        batch_ops_layout.addWidget(self.selection_count_label)
        batch_ops_layout.addStretch()

        controls_layout.addLayout(batch_ops_layout)
        layout.addWidget(controls_group)

        # Enhanced transaction table
        self.enhanced_table = SelectableTableWidget()
        self.enhanced_table.selection_changed.connect(self.on_selection_changed)
        self.enhanced_table.similarity_requested.connect(self.on_similarity_requested)
        self.enhanced_table.batch_label_requested.connect(self.on_batch_label_requested)
        self.enhanced_table.transaction_clicked.connect(self.on_transaction_clicked)

        layout.addWidget(self.enhanced_table)

        # Create legacy transaction table for backward compatibility
        self.transaction_table = QTableWidget()
        self.transaction_table.setColumnCount(4)
        self.transaction_table.setHorizontalHeaderLabels(["Description", "Frequency", "Amount Range", "Status"])
        self.transaction_table.setVisible(False)  # Hide it since we're using enhanced table
        self.transaction_table.itemSelectionChanged.connect(self.on_transaction_selected)

        # Also create navigation buttons for compatibility
        self.prev_button = QPushButton("Previous")
        self.prev_button.clicked.connect(self.previous_transaction)
        self.prev_button.setVisible(False)  # Hide since we have enhanced navigation

        self.next_button = QPushButton("Next")
        self.next_button.clicked.connect(self.next_transaction)
        self.next_button.setVisible(False)  # Hide since we have enhanced navigation

        return panel

    def create_left_panel(self) -> QWidget:
        """Create left panel with transaction list and controls"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Stats group
        stats_group = QGroupBox("Labeling Progress")
        stats_layout = QFormLayout(stats_group)
        
        self.progress_bar = QProgressBar()
        self.stats_label = QLabel("Loading...")
        
        stats_layout.addRow("Progress:", self.progress_bar)
        stats_layout.addRow("Statistics:", self.stats_label)
        
        layout.addWidget(stats_group)
        
        # Controls group
        controls_group = QGroupBox("Controls")
        controls_layout = QVBoxLayout(controls_group)
        
        # Session controls
        session_layout = QHBoxLayout()
        self.start_session_button = QPushButton("Start Session")
        self.start_session_button.clicked.connect(self.start_labeling_session)
        
        self.end_session_button = QPushButton("End Session")
        self.end_session_button.clicked.connect(self.end_labeling_session)
        self.end_session_button.setEnabled(False)
        
        session_layout.addWidget(self.start_session_button)
        session_layout.addWidget(self.end_session_button)
        
        controls_layout.addLayout(session_layout)
        
        # Batch size control
        batch_layout = QHBoxLayout()
        batch_layout.addWidget(QLabel("Batch Size:"))
        
        self.batch_size_spin = QSpinBox()
        self.batch_size_spin.setRange(10, 100)
        self.batch_size_spin.setValue(20)
        
        self.load_batch_button = QPushButton("Load Batch")
        self.load_batch_button.clicked.connect(self.load_transaction_batch)
        
        batch_layout.addWidget(self.batch_size_spin)
        batch_layout.addWidget(self.load_batch_button)
        
        controls_layout.addLayout(batch_layout)
        
        # Sort options
        sort_layout = QHBoxLayout()
        sort_layout.addWidget(QLabel("Sort by:"))
        
        self.sort_combo = QComboBox()
        self.sort_combo.addItems(["Frequency", "Recent", "Amount"])
        
        sort_layout.addWidget(self.sort_combo)
        
        controls_layout.addLayout(sort_layout)
        
        layout.addWidget(controls_group)
        
        # Transaction list
        list_group = QGroupBox("Transactions to Label")
        list_layout = QVBoxLayout(list_group)
        
        self.transaction_table = QTableWidget()
        self.transaction_table.setColumnCount(4)
        self.transaction_table.setHorizontalHeaderLabels(["Description", "Frequency", "Amount Range", "Status"])
        
        # Set column widths and row height
        header = self.transaction_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)

        # Set minimum row height to accommodate full descriptions
        self.transaction_table.verticalHeader().setDefaultSectionSize(60)

        # Enable word wrap for table items
        self.transaction_table.setWordWrap(True)
        
        self.transaction_table.itemSelectionChanged.connect(self.on_transaction_selected)
        
        list_layout.addWidget(self.transaction_table)
        
        # Navigation buttons
        nav_layout = QHBoxLayout()
        
        self.prev_button = QPushButton("Previous")
        self.prev_button.clicked.connect(self.previous_transaction)
        
        self.next_button = QPushButton("Next")
        self.next_button.clicked.connect(self.next_transaction)
        
        nav_layout.addWidget(self.prev_button)
        nav_layout.addWidget(self.next_button)
        
        list_layout.addLayout(nav_layout)
        
        layout.addWidget(list_group)
        
        return panel
    
    def create_right_panel(self) -> QWidget:
        """Create right panel with labeling interface"""
        panel = QWidget()
        layout = QVBoxLayout(panel)

        # Session Management Section
        session_group = QGroupBox("🗂️ Session Management")
        session_layout = QVBoxLayout(session_group)

        # Session status display
        self.session_status_label = QLabel("No active session")
        self.session_status_label.setStyleSheet("""
            QLabel {
                padding: 8px;
                background-color: #f0f0f0;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-weight: bold;
            }
        """)
        session_layout.addWidget(self.session_status_label)

        # Session management buttons
        session_buttons_layout = QHBoxLayout()

        self.save_session_btn = QPushButton("💾 Save Session")
        self.save_session_btn.clicked.connect(self.save_current_session)
        self.save_session_btn.setToolTip("Save current labeling work as a session")
        session_buttons_layout.addWidget(self.save_session_btn)

        self.load_session_btn = QPushButton("📁 Load Session")
        self.load_session_btn.clicked.connect(self.open_session_management)
        self.load_session_btn.setToolTip("Load a previous labeling session")
        session_buttons_layout.addWidget(self.load_session_btn)

        session_layout.addLayout(session_buttons_layout)

        # Data management buttons
        data_buttons_layout = QHBoxLayout()

        self.clear_data_btn = QPushButton("🗑️ Clear Data")
        self.clear_data_btn.clicked.connect(self.handle_clear_data_request)
        self.clear_data_btn.setToolTip("Clear current data with automatic backup")
        self.clear_data_btn.setStyleSheet("QPushButton { background-color: #f44336; color: white; }")
        data_buttons_layout.addWidget(self.clear_data_btn)

        self.backup_data_btn = QPushButton("💾 Backup Data")
        self.backup_data_btn.clicked.connect(self.backup_current_data)
        self.backup_data_btn.setToolTip("Create backup of current labeling work")
        data_buttons_layout.addWidget(self.backup_data_btn)

        # Add force clear button for testing
        self.force_clear_btn = QPushButton("🔧 Force Clear")
        self.force_clear_btn.clicked.connect(self.test_force_clear)
        self.force_clear_btn.setToolTip("Force clear all data (UI + persistent storage)")
        self.force_clear_btn.setStyleSheet("QPushButton { background-color: #ff9800; color: white; }")
        data_buttons_layout.addWidget(self.force_clear_btn)

        # Add button to re-enable loading
        self.enable_loading_btn = QPushButton("🔄 Enable Loading")
        self.enable_loading_btn.clicked.connect(self.enable_loading_and_reload)
        self.enable_loading_btn.setToolTip("Re-enable data loading and reload transactions")
        self.enable_loading_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; }")
        data_buttons_layout.addWidget(self.enable_loading_btn)

        session_layout.addLayout(data_buttons_layout)

        layout.addWidget(session_group)

        # Labeling widget
        self.labeling_widget = TransactionLabelingWidget(self.category_manager)
        self.labeling_widget.transaction_labeled.connect(self.on_transaction_labeled)
        self.labeling_widget.transaction_skipped.connect(self.on_transaction_skipped)

        # Override the labeling widget's category manager method to use main window's method
        self.labeling_widget.open_category_manager = self.open_category_manager

        layout.addWidget(self.labeling_widget)

        # Initialize session status
        self.update_session_status()

        return panel
    
    def load_initial_data(self):
        """Load initial data and update UI with performance optimization"""
        try:
            # This method is now only called when data should actually be loaded
            # (e.g., when user explicitly loads data or sessions)

            global_profiler.start("load_stats")
            self.update_stats()
            global_profiler.end("load_stats")

            global_profiler.start("load_batch")
            self.load_transaction_batch()
            global_profiler.end("load_batch")

        except Exception as e:
            self.logger.error(f"Error loading initial data: {str(e)}")
            # Show error but don't crash the window
            self.statusBar().showMessage(f"Error loading data: {str(e)}", 10000)

    def _check_for_existing_sessions(self):
        """Check for existing sessions and offer to load previous work"""
        try:
            # Get available sessions
            available_sessions = self.transaction_data_manager.get_available_sessions(include_archived=False)

            if available_sessions:
                # Show dialog asking if user wants to load previous session
                reply = QMessageBox.question(
                    self, "Previous Sessions Found",
                    f"Found {len(available_sessions)} previous labeling sessions.\n\n"
                    f"Would you like to:\n"
                    f"• Load a previous session to continue labeling work?\n"
                    f"• Start fresh with new data?\n\n"
                    f"Click Yes to load a session, No to start fresh.",
                    QMessageBox.Yes | QMessageBox.No, QMessageBox.Yes
                )

                if reply == QMessageBox.Yes:
                    # Open session management dialog
                    self.open_session_management()
                    return True

            return False

        except Exception as e:
            self.logger.error(f"Error checking for existing sessions: {str(e)}", exc_info=True)
            return False
    
    def _should_load_stats(self) -> bool:
        """Check if stats should be loaded (only when there's active data)"""
        try:
            # Check if there's an active session
            if (hasattr(self, 'transaction_data_manager') and
                self.transaction_data_manager.current_session is not None):
                return True

            # Check if unique transactions file exists and has data
            unique_transactions_file = Path("bank_analyzer_config/ml_data/unique_transactions.csv")
            if unique_transactions_file.exists():
                # Only load if file is not empty and user has explicitly loaded data
                file_size = unique_transactions_file.stat().st_size
                if file_size > 100:  # More than just headers
                    # Check if we have a flag indicating data was explicitly loaded
                    return getattr(self, '_data_explicitly_loaded', False)

            return False

        except Exception as e:
            self.logger.error(f"Error checking if stats should be loaded: {str(e)}")
            return False

    def _show_empty_stats(self):
        """Show empty/default stats when no data is loaded"""
        try:
            # Update progress bar to 0
            self.progress_bar.setValue(0)

            # Update stats label to show no data
            self.stats_label.setText("No data loaded - Import bank statements or load a session")

            # Clear any existing transaction display
            if hasattr(self, 'current_transactions'):
                self.current_transactions = []

            # Clear transaction table if it exists
            if hasattr(self, 'transaction_table'):
                self.transaction_table.setRowCount(0)

        except Exception as e:
            self.logger.error(f"Error showing empty stats: {str(e)}")

    def _show_empty_stats_without_loading(self):
        """Show empty stats without triggering any data loading"""
        try:
            # Update progress bar to 0
            self.progress_bar.setValue(0)

            # Update stats label to show no data
            self.stats_label.setText("No data loaded - Import bank statements or load a session")

            # Clear any existing transaction display
            if hasattr(self, 'current_transactions'):
                self.current_transactions = []

            # Show empty transaction display
            self._show_empty_transaction_display()

        except Exception as e:
            self.logger.error(f"Error showing empty stats without loading: {str(e)}")

    def _show_empty_transaction_display(self):
        """Show empty transaction display when no data is loaded"""
        try:
            # Clear current transactions
            if hasattr(self, 'current_transactions'):
                self.current_transactions = []

            # Clear enhanced transaction table (the main one being used)
            if hasattr(self, 'enhanced_table') and self.enhanced_table:
                self.enhanced_table.setRowCount(0)
                self.enhanced_table.clearContents()
                if hasattr(self.enhanced_table, 'clear_all_selections'):
                    self.enhanced_table.clear_all_selections()
                # Clear internal transaction data
                self.enhanced_table.populate_table([])

            # Clear legacy transaction table if it exists
            if hasattr(self, 'transaction_table'):
                self.transaction_table.setRowCount(0)

            # Show helpful message
            self.statusBar().showMessage("No data loaded - Use Data → Load Existing Data or Session → Manage All Sessions", 10000)

        except Exception as e:
            self.logger.error(f"Error showing empty transaction display: {str(e)}")

    def _get_stats(self):
        """Get labeling statistics (no caching)"""
        # If we're in session-based mode, show session-specific stats
        if self.current_training_session_id:
            return self._get_session_specific_stats()
        else:
            return self.training_manager.get_labeling_stats()

    def _get_session_specific_stats(self):
        """Get statistics for the current session only"""
        try:
            if not self.current_training_session_id:
                return self.training_manager.get_labeling_stats()

            # Count transactions in current session
            current_transactions = self.unlabeled_transactions + self.filtered_transactions
            total_in_session = len(set(txn.hash_id for txn in current_transactions))

            # Count labeled transactions in current session
            labeled_in_session = len([txn for txn in current_transactions if txn.is_manually_labeled])
            unlabeled_in_session = total_in_session - labeled_in_session

            # Calculate progress
            progress = (labeled_in_session / total_in_session * 100) if total_in_session > 0 else 0

            # Create session-specific stats object
            from ..ml.training_data_manager import LabelingStats
            return LabelingStats(
                total_unique_transactions=total_in_session,
                labeled_transactions=labeled_in_session,
                unlabeled_transactions=unlabeled_in_session,
                labeling_progress=progress,
                categories_used=[],
                most_frequent_unlabeled=[]
            )

        except Exception as e:
            self.logger.error(f"Error getting session-specific stats: {str(e)}")
            return self.training_manager.get_labeling_stats()

    def update_stats(self):
        """Update labeling statistics with caching"""
        try:
            # Only load stats if there's an active session or data has been explicitly loaded
            if not self._should_load_stats():
                self._show_empty_stats()
                return

            stats = self._get_stats()

            # Update progress bar
            self.progress_bar.setValue(int(stats.labeling_progress))

            # Update stats label with session info if in session mode
            if self.current_training_session_id:
                stats_text = f"📋 Session Mode | Total: {stats.total_unique_transactions}, "
                stats_text += f"Labeled: {stats.labeled_transactions}, "
                stats_text += f"Remaining: {stats.unlabeled_transactions}"
            else:
                stats_text = f"Total: {stats.total_unique_transactions}, "
                stats_text += f"Labeled: {stats.labeled_transactions}, "
                stats_text += f"Remaining: {stats.unlabeled_transactions}"

            self.stats_label.setText(stats_text)

        except Exception as e:
            self.logger.error(f"Error updating stats: {str(e)}")
    
    def start_labeling_session(self):
        """Start a new labeling session"""
        self.current_session_id = self.training_manager.start_labeling_session()

    def start_session_based_training(self, source_session_id: str, description: str = ""):
        """Start session-based training for a specific transaction session"""
        try:
            self.current_training_session_id = self.session_training_manager.start_session_training(
                source_session_id, description
            )
            self.logger.info(f"Started session-based training: {self.current_training_session_id}")

            # Update UI to show session-based mode
            self.statusBar().showMessage(f"Session-based training started: {self.current_training_session_id}")
            return self.current_training_session_id
        except Exception as e:
            self.logger.error(f"Error starting session-based training: {str(e)}")
            return None
    
    def end_labeling_session(self):
        """End the current labeling session"""
        if self.training_manager.end_labeling_session():
            self.start_session_button.setEnabled(True)
            self.end_session_button.setEnabled(False)
            
            self.statusBar().showMessage("Session ended")
            self.current_session_id = None
    
    def load_transaction_batch(self):
        """Load a batch of unlabeled transactions"""
        try:
            # Check if auto-reload is prevented (after force clear)
            if getattr(self, '_prevent_auto_reload', False):
                self.logger.info("Skipping transaction batch loading - auto-reload prevented after clear")
                return

            # Check if data should be loaded (clean start behavior)
            if not self._should_load_stats():
                self.logger.info("Skipping transaction batch loading - no active session or data")
                self._show_empty_transaction_display()
                return
            # Get batch size safely
            batch_size = getattr(self, 'batch_size_spin', None)
            if batch_size is not None:
                batch_size = batch_size.value()
            else:
                batch_size = 50  # Default batch size

            # Use current filter if available, otherwise get unlabeled batch
            if self.current_filter:
                self.filtered_transactions = self.training_manager.get_filtered_transactions(
                    self.current_filter, batch_size=batch_size
                )
                transactions_to_display = self.filtered_transactions
            else:
                # Default sort by frequency for unlabeled batch
                # Check if there's a frequency filter from the filter panel
                min_frequency = None
                if hasattr(self, 'filter_panel') and self.filter_panel:
                    current_filter = self.filter_panel.get_current_filter()
                    if current_filter and current_filter.min_frequency:
                        min_frequency = current_filter.min_frequency

                self.unlabeled_transactions = self.training_manager.get_unlabeled_batch(
                    batch_size=batch_size,
                    sort_by="frequency",
                    min_frequency=min_frequency
                )
                transactions_to_display = self.unlabeled_transactions
                self.filtered_transactions = self.unlabeled_transactions

            # Validate transactions before displaying
            valid_transactions = []
            invalid_count = 0

            for txn in transactions_to_display:
                if txn and hasattr(txn, 'description') and txn.description and hasattr(txn, 'hash_id') and txn.hash_id:
                    valid_transactions.append(txn)
                else:
                    invalid_count += 1
                    self.logger.warning(f"Invalid transaction found: {txn}")

            if invalid_count > 0:
                self.logger.warning(f"Found {invalid_count} invalid transactions out of {len(transactions_to_display)}")

            # Update enhanced table with valid transactions
            if hasattr(self, 'enhanced_table') and self.enhanced_table:
                self.enhanced_table.populate_table(valid_transactions)

            # Store valid transactions for other methods to use
            if self.current_filter:
                self.filtered_transactions = valid_transactions
            else:
                self.unlabeled_transactions = valid_transactions

            # Also update old table for compatibility
            self.current_index = 0
            self.populate_transaction_table()

            if valid_transactions:
                self.show_current_transaction()

            status_msg = f"Loaded {len(valid_transactions)} transactions"
            if invalid_count > 0:
                status_msg += f" ({invalid_count} invalid transactions skipped)"
            self.statusBar().showMessage(status_msg)

        except Exception as e:
            self.logger.error(f"Error loading transaction batch: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to load transactions: {str(e)}")
    
    def populate_transaction_table(self):
        """Populate the transaction table"""
        # Use filtered transactions if available, otherwise unlabeled transactions
        transactions_to_show = getattr(self, 'filtered_transactions', None) or getattr(self, 'unlabeled_transactions', [])

        if not hasattr(self, 'transaction_table') or self.transaction_table is None:
            return  # Table not initialized yet

        self.transaction_table.setRowCount(len(transactions_to_show))

        for i, transaction in enumerate(transactions_to_show):
            # Skip None transactions
            if not transaction:
                self.logger.warning(f"Skipping None transaction at index {i}")
                continue

            # Validate required attributes
            if not hasattr(transaction, 'description') or not hasattr(transaction, 'frequency'):
                self.logger.warning(f"Skipping transaction with missing attributes at index {i}")
                continue

            # Description - show full description with word wrap
            description = transaction.description or "[No Description]"
            desc_item = QTableWidgetItem(description)
            desc_item.setToolTip(description)
            self.transaction_table.setItem(i, 0, desc_item)

            # Frequency
            frequency = getattr(transaction, 'frequency', 0)
            freq_item = QTableWidgetItem(str(frequency))
            self.transaction_table.setItem(i, 1, freq_item)

            # Amount range
            amount_range = getattr(transaction, 'amount_range', [0, 0])
            if isinstance(amount_range, (list, tuple)) and len(amount_range) >= 2:
                amount_item = QTableWidgetItem(f"₹{amount_range[0]:.0f}-{amount_range[1]:.0f}")
            else:
                amount_item = QTableWidgetItem(f"₹{amount_range}")
            self.transaction_table.setItem(i, 2, amount_item)

            # Status
            status_item = QTableWidgetItem("Pending")
            self.transaction_table.setItem(i, 3, status_item)
    
    def show_current_transaction(self):
        """Show the current transaction in the labeling widget"""
        # Use filtered transactions if available, otherwise unlabeled transactions
        transactions_to_show = getattr(self, 'filtered_transactions', None) or getattr(self, 'unlabeled_transactions', [])

        if 0 <= self.current_index < len(transactions_to_show):
            transaction = transactions_to_show[self.current_index]

            # Check if transaction is valid
            if not transaction or not hasattr(transaction, 'description'):
                self.logger.warning(f"Invalid transaction at index {self.current_index}")
                return

            # Get suggestions
            description = transaction.description or "[No Description]"
            suggestions = self.training_manager.suggest_similar_labels(description)

            # Show in labeling widget
            self.labeling_widget.set_transaction(transaction, suggestions)
            
            # Select in table
            self.transaction_table.selectRow(self.current_index)
            
            # Update navigation buttons
            transactions_to_show = getattr(self, 'filtered_transactions', None) or getattr(self, 'unlabeled_transactions', [])
            self.prev_button.setEnabled(self.current_index > 0)
            self.next_button.setEnabled(self.current_index < len(transactions_to_show) - 1)
    
    def on_transaction_selected(self):
        """Handle transaction selection in table"""
        current_row = self.transaction_table.currentRow()
        transactions_to_show = getattr(self, 'filtered_transactions', None) or getattr(self, 'unlabeled_transactions', [])
        if 0 <= current_row < len(transactions_to_show):
            self.current_index = current_row
            self.show_current_transaction()
    
    def on_transaction_labeled(self, label_data: Dict[str, Any]):
        """Handle transaction labeling with auto-refresh"""
        try:
            success = self.training_manager.label_transaction(
                label_data["hash_id"],
                label_data["category"],
                label_data["sub_category"],
                label_data["confidence"]
            )

            if success:
                # Update enhanced table with category and subcategory
                if hasattr(self, 'enhanced_table') and self.enhanced_table:
                    self.enhanced_table.update_transaction_status(
                        label_data["hash_id"],
                        label_data["category"],
                        label_data["sub_category"]
                    )

                # Update legacy table status for backward compatibility
                if 0 <= self.current_index < self.transaction_table.rowCount():
                    status_item = QTableWidgetItem("Labeled")
                    status_item.setBackground(QColor(144, 238, 144))  # Light green
                    self.transaction_table.setItem(self.current_index, 3, status_item)

                # AUTO-REFRESH: Reload transaction data to reflect changes
                self.reload_transaction_data()

                # Update statistics to show new counts
                self.update_stats()

                # Log the successful labeling
                self.logger.info(f"Transaction labeled and data refreshed: {label_data['hash_id']}")

                # Show brief status message
                self.statusBar().showMessage(f"Transaction labeled: {label_data['category']} > {label_data['sub_category']}", 3000)

                # Move to next transaction
                self.next_transaction()

                # Update stats
                self.update_stats()

                self.statusBar().showMessage(f"Transaction labeled as {label_data['category']}/{label_data['sub_category']}")
            else:
                QMessageBox.warning(self, "Warning", "Failed to label transaction")

        except Exception as e:
            self.logger.error(f"Error labeling transaction: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to label transaction: {str(e)}")
    
    def on_transaction_skipped(self):
        """Handle transaction skipping"""
        # Update table status
        if 0 <= self.current_index < self.transaction_table.rowCount():
            status_item = QTableWidgetItem("Skipped")
            status_item.setBackground(QColor(255, 255, 224))  # Light yellow
            self.transaction_table.setItem(self.current_index, 3, status_item)
        
        # Move to next transaction
        self.next_transaction()
        
        self.statusBar().showMessage("Transaction skipped")
    
    def previous_transaction(self):
        """Go to previous transaction"""
        if self.current_index > 0:
            self.current_index -= 1
            self.show_current_transaction()
    
    def next_transaction(self):
        """Go to next transaction"""
        transactions_to_show = getattr(self, 'filtered_transactions', None) or getattr(self, 'unlabeled_transactions', [])
        if self.current_index < len(transactions_to_show) - 1:
            self.current_index += 1
            self.show_current_transaction()
        else:
            # End of batch, offer to load more
            reply = QMessageBox.question(
                self, "End of Batch", 
                "You've reached the end of this batch. Load more transactions?",
                QMessageBox.Yes | QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                self.load_transaction_batch()

    def on_filter_changed(self, filter_obj: TransactionFilter):
        """Handle filter changes with background processing"""
        try:
            self.current_filter = filter_obj

            # Clear cache to ensure fresh results
            if hasattr(self.training_manager, 'clear_cache'):
                self.training_manager.clear_cache()

            # Stop any existing filter worker
            if hasattr(self, 'filter_worker') and self.filter_worker.isRunning():
                self.filter_worker.stop()
                self.filter_worker.wait(1000)  # Wait up to 1 second

            # Show loading indicator
            self.statusBar().showMessage("Applying filters...")

            # Start background filtering
            self.filter_worker = FilterWorker(
                self.training_manager,
                filter_obj,
                batch_size=self.batch_size_spin.value()
            )

            # Connect signals
            self.filter_worker.filter_completed.connect(self.on_filter_completed)
            self.filter_worker.filter_failed.connect(self.on_filter_failed)
            self.filter_worker.progress_update.connect(self.on_filter_progress)

            # Start the worker
            self.filter_worker.start()

        except Exception as e:
            self.logger.error(f"Error starting filter operation: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to start filter operation: {str(e)}")

    def on_filter_completed(self, filtered_transactions):
        """Handle filter completion"""
        try:
            # Check if data should be loaded (clean start behavior)
            if not self._should_load_stats():
                self.logger.info("Skipping filter results display - no active session or data")
                self._show_empty_transaction_display()
                return

            self.filtered_transactions = filtered_transactions

            # Update enhanced table
            if self.enhanced_table:
                self.enhanced_table.populate_table(self.filtered_transactions)

            # Debug: Log filter results
            if hasattr(self, 'filter_panel'):
                current_status = self.filter_panel.get_current_status_filter()
                labeled_count = sum(1 for txn in self.filtered_transactions if txn.is_manually_labeled)
                unlabeled_count = sum(1 for txn in self.filtered_transactions if not txn.is_manually_labeled)
                self.logger.debug(f"Filter applied - Status: {current_status}, Results: {len(self.filtered_transactions)} total, {labeled_count} labeled, {unlabeled_count} unlabeled")

            # Update status
            self.statusBar().showMessage(f"Filter applied: {len(self.filtered_transactions)} transactions found")

        except Exception as e:
            self.logger.error(f"Error handling filter results: {str(e)}")

    def on_filter_failed(self, error_message):
        """Handle filter failure"""
        self.statusBar().showMessage("Filter operation failed")
        QMessageBox.critical(self, "Filter Error", f"Failed to apply filter: {error_message}")

    def on_filter_progress(self, message, percentage):
        """Handle filter progress updates"""
        self.statusBar().showMessage(f"{message} ({percentage}%)")

    def on_filter_reset(self):
        """Handle filter reset"""
        self.current_filter = None
        self.load_transaction_batch()

    def on_selection_changed(self, selected_hash_ids: List[str]):
        """Handle selection changes in enhanced table"""
        try:
            count = len(selected_hash_ids)
            self.selection_count_label.setText(f"Selected: {count}")

            self.logger.info(f"Selection changed: {count} transactions selected")
            self.logger.info(f"Selected hash_ids: {selected_hash_ids[:5]}...")  # Show first 5 for debugging

            # Enable/disable batch operations with validation
            batch_enabled = count > 0
            similar_enabled = count == 1

            self.batch_label_button.setEnabled(batch_enabled)
            self.select_similar_button.setEnabled(similar_enabled)

            # Force refresh button states to ensure UI consistency
            self._refresh_button_states()

            # Update status bar with helpful instructions
            if count == 0:
                self.statusBar().showMessage("Select transactions using checkboxes to enable batch operations")
            elif count == 1:
                self.statusBar().showMessage("1 transaction selected - Batch Label and Select Similar buttons are available")
            else:
                self.statusBar().showMessage(f"{count} transactions selected - Batch Label button is available (Select Similar requires exactly 1 selection)")

            # Update current transaction in labeling widget if single selection
            if count == 1:
                hash_id = selected_hash_ids[0]
                self.update_labeling_widget_for_transaction(hash_id)

        except Exception as e:
            self.logger.error(f"Error in selection change handler: {str(e)}")
            # Don't show error dialog here as it might be called frequently

    def _refresh_button_states(self):
        """Force refresh button states to ensure UI consistency"""
        try:
            if hasattr(self, 'enhanced_table') and self.enhanced_table:
                selected_count = len(self.enhanced_table.selected_hash_ids)

                # Update button states
                self.batch_label_button.setEnabled(selected_count > 0)
                self.select_similar_button.setEnabled(selected_count == 1)

                # Force visual update and ensure proper styling
                self._ensure_button_styling()
                self.batch_label_button.update()
                self.select_similar_button.update()



        except Exception as e:
            self.logger.error(f"Error refreshing button states: {str(e)}")

    def _ensure_button_styling(self):
        """Ensure buttons have proper styling applied"""
        try:
            # Reapply styling to ensure it's not overridden
            if hasattr(self, 'batch_label_button'):
                self._apply_batch_button_styling()

        except Exception as e:
            self.logger.error(f"Error ensuring button styling: {str(e)}")

    def _apply_batch_button_styling(self):
        """Apply robust styling to the batch label button"""
        try:
            from PySide6.QtGui import QPalette, QColor

            # Define the complete stylesheet with higher specificity
            stylesheet = """
                QPushButton#batchLabelButton {
                    background-color: #FF9800 !important;
                    color: white !important;
                    font-weight: bold;
                    padding: 8px 16px;
                    border: 2px solid #FF9800;
                    border-radius: 4px;
                    text-align: center;
                    min-width: 120px;
                    font-size: 12px;
                }
                QPushButton#batchLabelButton:enabled {
                    background-color: #FF9800 !important;
                    color: white !important;
                    border-color: #FF9800;
                }
                QPushButton#batchLabelButton:hover:enabled {
                    background-color: #F57C00 !important;
                    color: white !important;
                    border-color: #F57C00;
                }
                QPushButton#batchLabelButton:pressed:enabled {
                    background-color: #E65100 !important;
                    color: white !important;
                    border-color: #E65100;
                }
                QPushButton#batchLabelButton:disabled {
                    background-color: #CCCCCC !important;
                    color: #666666 !important;
                    border-color: #CCCCCC;
                }
            """

            # Apply the stylesheet
            self.batch_label_button.setStyleSheet(stylesheet)

            # Also set auto fill background to ensure styling takes effect
            self.batch_label_button.setAutoFillBackground(True)

            # Ensure the button updates its appearance
            self.batch_label_button.update()



        except Exception as e:
            self.logger.error(f"Error applying batch button styling: {str(e)}")



    def on_transaction_clicked(self, hash_id: str):
        """Handle transaction click to show details in labeling widget"""
        self.update_labeling_widget_for_transaction(hash_id)

    def update_labeling_widget_for_transaction(self, hash_id: str):
        """Update labeling widget with transaction details"""
        # Find transaction in current data
        transactions_to_search = getattr(self, 'filtered_transactions', None) or getattr(self, 'unlabeled_transactions', [])
        transaction = next((txn for txn in transactions_to_search if txn.hash_id == hash_id), None)

        if transaction:
            try:
                # Validate transaction has required attributes
                if not hasattr(transaction, 'description'):
                    self.logger.warning(f"Transaction {hash_id} missing description attribute")
                    return

                description = transaction.description or "[No Description]"
                suggestions = self.training_manager.suggest_similar_labels(description)
                self.labeling_widget.set_transaction(transaction, suggestions)
                self.statusBar().showMessage(f"Showing details for: {description[:50]}...")
            except Exception as e:
                self.logger.error(f"Error updating labeling widget: {str(e)}")
                # Still show transaction even if suggestions fail
                self.labeling_widget.set_transaction(transaction, [])

    def on_similarity_requested(self, reference_hash_id: str):
        """Handle similarity search request with background processing"""
        try:
            # Stop any existing similarity worker
            if hasattr(self, 'similarity_worker') and self.similarity_worker.isRunning():
                self.similarity_worker.stop()
                self.similarity_worker.wait(1000)  # Wait up to 1 second

            # Show loading indicator
            self.statusBar().showMessage("Finding similar transactions...")

            # Start background similarity search
            self.similarity_worker = SimilarityWorker(
                self.training_manager,
                reference_hash_id,
                similarity_threshold=0.3,
                limit=20
            )

            # Connect signals
            self.similarity_worker.similarity_completed.connect(self.on_similarity_completed)
            self.similarity_worker.similarity_failed.connect(self.on_similarity_failed)
            self.similarity_worker.progress_update.connect(self.on_similarity_progress)

            # Start the worker
            self.similarity_worker.start()

        except Exception as e:
            self.logger.error(f"Error starting similarity search: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to start similarity search: {str(e)}")

    def on_similarity_completed(self, similar_transactions):
        """Handle similarity search completion"""
        try:
            if similar_transactions:
                # Extract hash IDs and similarities
                similar_hash_ids = [txn["hash_id"] for txn in similar_transactions]
                similarities = {txn["hash_id"]: txn["similarity"] for txn in similar_transactions}

                # Update table highlighting
                self.enhanced_table.set_similar_transactions(similar_hash_ids, similarities)

                self.statusBar().showMessage(f"Found {len(similar_transactions)} similar transactions")
            else:
                self.enhanced_table.clear_similarity_highlighting()
                self.statusBar().showMessage("No similar transactions found")

        except Exception as e:
            self.logger.error(f"Error handling similarity results: {str(e)}")

    def on_similarity_failed(self, error_message):
        """Handle similarity search failure"""
        self.statusBar().showMessage("Similarity search failed")
        QMessageBox.critical(self, "Similarity Error", f"Failed to find similar transactions: {error_message}")

    def on_similarity_progress(self, message, percentage):
        """Handle similarity search progress updates"""
        self.statusBar().showMessage(f"{message} ({percentage}%)")

    def on_batch_label_requested(self, transactions: List[UniqueTransaction]):
        """Handle batch labeling request"""
        self.open_batch_labeling_dialog_with_transactions(transactions)

    def on_batch_label_button_clicked(self):
        """Handle batch label button click with error handling"""
        try:
            self.open_batch_labeling_dialog()
        except Exception as e:
            self.logger.error(f"Error in batch label button click handler: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "Button Error", f"Error handling batch label button click: {str(e)}")

    def on_select_similar_button_clicked(self):
        """Handle select similar button click with error handling"""
        try:
            self.logger.info("Select Similar button clicked")
            self.select_similar_to_current()
        except Exception as e:
            self.logger.error(f"Error in select similar button click handler: {str(e)}")
            QMessageBox.critical(self, "Button Error", f"Error handling select similar button click: {str(e)}")

    def show_help_dialog(self):
        """Show help dialog explaining how to use the ML labeling interface"""
        help_text = """
<h3>ML Transaction Labeling Interface - Quick Guide</h3>

<h4>🔹 How to Select Transactions:</h4>
<ul>
<li><b>Single Selection:</b> Click the checkbox next to one transaction</li>
<li><b>Multiple Selection:</b> Click checkboxes next to multiple transactions</li>
<li><b>Select All:</b> Right-click in the table and choose "Select All"</li>
<li><b>Clear Selection:</b> Click "Clear Selection" button or right-click and choose "Clear Selection"</li>
</ul>

<h4>🔹 Button Functions:</h4>
<ul>
<li><b>Batch Label Selected:</b> Apply the same category to all selected transactions
   <br>• Requires: At least 1 transaction selected
   <br>• Opens a dialog to choose category and subcategory</li>
<li><b>Select Similar:</b> Automatically find and select similar transactions
   <br>• Requires: Exactly 1 transaction selected
   <br>• Uses AI to find transactions with similar descriptions/amounts</li>
<li><b>Clear Selection:</b> Remove all current selections</li>
</ul>

<h4>🔹 Tips:</h4>
<ul>
<li>Hover over buttons to see tooltips with requirements</li>
<li>Check the status bar for current selection information</li>
<li>Use right-click context menu for additional options</li>
<li>Buttons are automatically enabled/disabled based on your selection</li>
</ul>

<h4>🔹 Workflow Suggestion:</h4>
<ol>
<li>Select one transaction with a clear description</li>
<li>Click "Select Similar" to find related transactions</li>
<li>Review the selected transactions</li>
<li>Click "Batch Label Selected" to categorize them all at once</li>
</ol>
        """

        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("ML Labeling Interface Help")
        msg_box.setTextFormat(Qt.RichText)
        msg_box.setText(help_text)
        msg_box.setStandardButtons(QMessageBox.Ok)
        msg_box.setIcon(QMessageBox.Information)
        msg_box.exec()



    def select_similar_to_current(self):
        """Select transactions similar to currently selected one"""
        try:
            # Provide immediate visual feedback
            self.statusBar().showMessage("Searching for similar transactions...")

            selected_transactions = self.enhanced_table.get_selected_transactions()

            # Check if exactly one transaction is selected
            if len(selected_transactions) == 0:
                QMessageBox.warning(self, "No Selection",
                                  "Please select exactly one transaction to find similar ones.")
                self.statusBar().showMessage("Ready")
                return
            elif len(selected_transactions) > 1:
                QMessageBox.warning(self, "Multiple Selections",
                                  "Please select exactly one transaction to find similar ones. "
                                  f"Currently {len(selected_transactions)} transactions are selected.")
                self.statusBar().showMessage("Ready")
                return

            reference_txn = selected_transactions[0]

            # Validate that we have a training manager
            if not hasattr(self, 'training_manager') or self.training_manager is None:
                QMessageBox.critical(self, "Error", "Training manager not available.")
                self.statusBar().showMessage("Ready")
                return

            try:
                similar_transactions = self.training_manager.find_similar_transactions(
                    reference_txn.hash_id, similarity_threshold=0.3, limit=50
                )

                if similar_transactions:
                    similar_hash_ids = [txn["hash_id"] for txn in similar_transactions]
                    self.enhanced_table.select_similar_transactions_by_hash_ids(similar_hash_ids)

                    # Emit selection changed signal to update UI
                    self.enhanced_table.selection_changed.emit(list(self.enhanced_table.selected_hash_ids))

                    self.statusBar().showMessage(f"Selected {len(similar_transactions)} similar transactions")

                    # Show brief success notification
                    self.statusBar().showMessage(f"✓ Found and selected {len(similar_transactions)} similar transactions", 3000)
                else:
                    QMessageBox.information(self, "No Similar Transactions",
                                          "No similar transactions found with the current threshold.")
                    self.statusBar().showMessage("No similar transactions found")

            except Exception as e:
                self.logger.error(f"Error selecting similar transactions: {str(e)}")
                QMessageBox.critical(self, "Error", f"Failed to select similar transactions: {str(e)}")
                self.statusBar().showMessage("Error occurred")

        except Exception as e:
            self.logger.error(f"Unexpected error in select_similar_to_current: {str(e)}")
            QMessageBox.critical(self, "Unexpected Error", f"An unexpected error occurred: {str(e)}")
            self.statusBar().showMessage("Error occurred")

    def clear_all_selections(self):
        """Clear all selections"""
        try:
            if self.enhanced_table:
                self.enhanced_table.clear_all_selections()
                self.enhanced_table.clear_similarity_highlighting()
                self.statusBar().showMessage("All selections cleared")
            else:
                QMessageBox.warning(self, "Warning", "Transaction table not available.")
        except Exception as e:
            self.logger.error(f"Error clearing selections: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to clear selections: {str(e)}")

    def open_batch_labeling_dialog(self):
        """Open batch labeling dialog for selected transactions"""
        try:
            # Provide immediate visual feedback
            self.statusBar().showMessage("Opening batch labeling dialog...")

            selected_transactions = self.enhanced_table.get_selected_transactions()

            # Check if any transactions are selected
            if not selected_transactions:
                QMessageBox.warning(self, "No Selection",
                                  "Please select one or more transactions to batch label.")
                self.statusBar().showMessage("Ready")
                return

            # Validate that we have required managers
            if not hasattr(self, 'training_manager') or self.training_manager is None:
                QMessageBox.critical(self, "Error", "Training manager not available.")
                self.statusBar().showMessage("Ready")
                return

            if not hasattr(self, 'category_manager') or self.category_manager is None:
                QMessageBox.critical(self, "Error", "Category manager not available.")
                self.statusBar().showMessage("Ready")
                return

            # Show confirmation for large batches
            if len(selected_transactions) > 100:
                reply = QMessageBox.question(
                    self, "Large Batch Warning",
                    f"You are about to batch label {len(selected_transactions)} transactions. "
                    "This may take some time. Do you want to continue?",
                    QMessageBox.Yes | QMessageBox.No
                )
                if reply != QMessageBox.Yes:
                    self.statusBar().showMessage("Batch labeling cancelled")
                    return

            self.open_batch_labeling_dialog_with_transactions(selected_transactions)

        except Exception as e:
            self.logger.error(f"Error opening batch labeling dialog: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to open batch labeling dialog: {str(e)}")
            self.statusBar().showMessage("Error occurred")

    def open_batch_labeling_dialog_with_transactions(self, transactions: List[UniqueTransaction]):
        """Open batch labeling dialog with specific transactions"""
        try:
            # Validate input
            if not transactions:
                QMessageBox.warning(self, "No Transactions", "No transactions provided for batch labeling.")
                self.statusBar().showMessage("Ready")
                return

            self.statusBar().showMessage(f"Preparing batch labeling for {len(transactions)} transactions...")

            # Get batch suggestions
            hash_ids = [txn.hash_id for txn in transactions]
            suggestions = self.training_manager.get_batch_labeling_suggestions(hash_ids)

            # Create and show dialog
            dialog = BatchLabelingDialog(transactions, suggestions, self)

            # Load categories
            main_categories = [cat.name for cat in self.category_manager.get_main_categories()]
            if not main_categories:
                QMessageBox.warning(self, "No Categories",
                                  "No categories available. Please create categories first.")
                self.statusBar().showMessage("Ready")
                return

            subcategories = {}
            for cat in self.category_manager.get_main_categories():
                subcategories[cat.name] = [subcat.name for subcat in self.category_manager.get_subcategories(cat.name)]

            dialog.load_categories(main_categories, subcategories)
            dialog.batch_confirmed.connect(self.on_batch_labeling_confirmed)

            # Show dialog
            dialog.show()
            self.statusBar().showMessage(f"Batch labeling dialog opened for {len(transactions)} transactions")

        except Exception as e:
            self.logger.error(f"Error opening batch labeling dialog: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to open batch labeling dialog: {str(e)}")
            self.statusBar().showMessage("Error occurred")

    def on_batch_labeling_confirmed(self, transactions: List[UniqueTransaction],
                                   category: str, sub_category: str, confidence: float):
        """Handle confirmed batch labeling"""
        try:
            # Prepare labels for batch operation
            labels = []
            for txn in transactions:
                labels.append({
                    "hash_id": txn.hash_id,
                    "category": category,
                    "sub_category": sub_category,
                    "confidence": confidence
                })

            # Validate batch
            validation = self.training_manager.validate_batch_labeling(labels)
            if not validation["valid"]:
                error_msg = "Batch labeling validation failed:\n"
                error_msg += "\n".join(validation["errors"][:5])  # Show first 5 errors
                QMessageBox.critical(self, "Validation Error", error_msg)
                return

            # Show warnings if any
            if validation["warnings"]:
                warning_msg = "Warnings found:\n"
                warning_msg += "\n".join(validation["warnings"][:5])
                reply = QMessageBox.question(
                    self, "Warnings Found",
                    warning_msg + "\n\nContinue with batch labeling?",
                    QMessageBox.Yes | QMessageBox.No
                )
                if reply != QMessageBox.Yes:
                    return

            # Perform batch labeling
            result = self.training_manager.batch_label_transactions(labels)

            # CRITICAL FIX: Reload transaction data from storage to ensure consistency
            self.reload_transaction_data()

            # Update stats (reload_transaction_data already refreshes the UI with updated data)
            self.update_stats()

            # Show result
            success_msg = f"Batch labeling completed:\n"
            success_msg += f"Success: {result['success_count']}\n"
            success_msg += f"Errors: {result['error_count']}"

            if result['error_count'] > 0:
                success_msg += f"\n\nFirst few errors:\n"
                success_msg += "\n".join(result['errors'][:3])

            QMessageBox.information(self, "Batch Labeling Complete", success_msg)

            # Clear selections
            self.clear_all_selections()

            self.statusBar().showMessage(f"Batch labeled {result['success_count']} transactions")

        except Exception as e:
            self.logger.error(f"Error in batch labeling: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to perform batch labeling: {str(e)}")

    def reload_transaction_data(self):
        """Reload transaction data from storage to ensure UI consistency"""
        try:
            # Get current filter
            current_filter = self.filter_panel.get_current_filter()

            # Reload filtered transactions
            filtered_transactions = self.training_manager.get_filtered_transactions(current_filter)

            # Update the enhanced table with fresh data
            self.enhanced_table.refresh_transaction_data(filtered_transactions)

            self.logger.info("Transaction data reloaded successfully")

        except Exception as e:
            self.logger.error(f"Error reloading transaction data: {str(e)}")

    def open_category_editor(self):
        """Open the category editor dialog"""
        try:
            from .category_editor_dialog import CategoryEditorDialog

            dialog = CategoryEditorDialog(self.category_manager, self)
            dialog.categories_changed.connect(self.on_categories_changed)

            # Connect to enhanced category selector if available
            if hasattr(self, 'labeling_widget') and hasattr(self.labeling_widget, 'category_selector'):
                dialog.categories_changed.connect(self.labeling_widget.category_selector.refresh_categories)

            try:
                dialog.exec()
            except KeyboardInterrupt:
                # User interrupted the category editor dialog
                self.logger.info("Category editor dialog interrupted by user")
                pass

        except Exception as e:
            self.logger.error(f"Error opening category editor: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to open category editor: {str(e)}")

    def on_categories_changed(self):
        """Handle category changes from the editor using throttled refresh"""
        try:
            self.logger.info("Categories changed signal received - requesting throttled refresh")

            # Use throttled refresh manager to prevent excessive refreshes
            self.refresh_manager.request_refresh("categories", force=False)

        except Exception as e:
            self.logger.error(f"Error handling category changes: {str(e)}")
            QMessageBox.warning(self, "Warning", f"Categories updated but some UI elements may need refresh: {str(e)}")

    def _handle_throttled_refresh(self, component_name: str):
        """Handle throttled refresh requests"""
        if component_name == "categories":
            self._start_background_category_refresh()
        elif component_name == "data":
            self._refresh_transaction_data()
        else:
            self.logger.warning(f"Unknown component refresh requested: {component_name}")

    def _start_background_category_refresh(self):
        """Start category refresh in background thread"""
        try:
            # Create and configure worker
            self._refresh_worker = CategoryRefreshWorker(self.category_manager, self)

            # Connect signals
            self._refresh_worker.refresh_started.connect(self._on_refresh_started)
            self._refresh_worker.refresh_completed.connect(self._on_refresh_completed)
            self._refresh_worker.refresh_failed.connect(self._on_refresh_failed)
            self._refresh_worker.progress_update.connect(self._on_refresh_progress)

            # Start the worker
            self._refresh_worker.start()

        except Exception as e:
            self.logger.error(f"Error starting background refresh: {str(e)}")
            self._fallback_refresh()

    def _on_refresh_started(self):
        """Handle refresh started signal"""
        self.statusBar().showMessage("Refreshing categories...")
        # Disable UI elements during refresh to prevent conflicts
        if hasattr(self, 'labeling_widget'):
            self.labeling_widget.setEnabled(False)

    def _on_refresh_completed(self):
        """Handle refresh completed signal"""
        try:
            # Refresh UI components on main thread
            QTimer.singleShot(50, self._refresh_ui_components)

            # Re-enable UI elements
            if hasattr(self, 'labeling_widget'):
                self.labeling_widget.setEnabled(True)

        except Exception as e:
            self.logger.error(f"Error handling refresh completion: {str(e)}")
            # Ensure UI is re-enabled even on error
            if hasattr(self, 'labeling_widget'):
                self.labeling_widget.setEnabled(True)

    def _on_refresh_failed(self, error_message: str):
        """Handle refresh failed signal"""
        self.logger.error(f"Background category refresh failed: {error_message}")
        self.statusBar().showMessage("Category refresh failed", 3000)

        # Re-enable UI elements
        if hasattr(self, 'labeling_widget'):
            self.labeling_widget.setEnabled(True)

        # Fallback to synchronous refresh
        self._fallback_refresh()

    def _on_refresh_progress(self, message: str):
        """Handle refresh progress updates"""
        self.statusBar().showMessage(message)

    def _fallback_refresh(self):
        """Fallback to synchronous refresh if background refresh fails"""
        try:
            self.logger.info("Using fallback synchronous refresh")
            self.category_manager.refresh_categories()
            QTimer.singleShot(100, self._refresh_ui_components)
        except Exception as e:
            self.logger.error(f"Fallback refresh also failed: {str(e)}")

    @profile_operation("ui_components_refresh")
    def _refresh_ui_components(self):
        """Refresh all UI components after category changes with performance optimization"""
        try:
            components_refreshed = []

            # Reload categories in labeling widget
            if hasattr(self, 'labeling_widget') and self.labeling_widget:
                try:
                    global_profiler.start("labeling_widget_refresh")
                    self.labeling_widget.reload_categories()
                    global_profiler.end("labeling_widget_refresh")
                    components_refreshed.append("labeling_widget")
                except Exception as e:
                    self.logger.warning(f"Failed to refresh labeling widget: {str(e)}")

            # Refresh filter panel categories
            if hasattr(self, 'filter_panel') and self.filter_panel:
                try:
                    global_profiler.start("filter_panel_refresh")
                    self.load_filter_categories()
                    global_profiler.end("filter_panel_refresh")
                    components_refreshed.append("filter_panel")
                except Exception as e:
                    self.logger.warning(f"Failed to refresh filter panel: {str(e)}")

            # Refresh enhanced table if it has category-related functionality
            if hasattr(self, 'enhanced_table') and self.enhanced_table:
                try:
                    global_profiler.start("enhanced_table_refresh")
                    # Force refresh of any category-dependent data
                    if hasattr(self.enhanced_table, 'refresh_categories'):
                        self.enhanced_table.refresh_categories()
                        components_refreshed.append("enhanced_table")
                    global_profiler.end("enhanced_table_refresh")
                except Exception as e:
                    self.logger.warning(f"Failed to refresh enhanced table: {str(e)}")

            self.logger.info(f"UI components refreshed: {', '.join(components_refreshed)}")
            self.statusBar().showMessage("Categories updated - changes reflected across all components", 3000)

        except Exception as e:
            self.logger.error(f"Error refreshing UI components: {str(e)}")
            QMessageBox.warning(self, "Warning", f"Categories updated but some UI elements may need refresh: {str(e)}")

    def _refresh_transaction_data(self):
        """Refresh transaction data in background"""
        try:
            self.logger.info("Refreshing transaction data...")
            # This could be expanded to refresh transaction data if needed
            pass
        except Exception as e:
            self.logger.error(f"Error refreshing transaction data: {str(e)}")

    def load_filter_categories(self):
        """Load categories into filter panel"""
        if self.filter_panel:
            try:
                main_categories = [cat.name for cat in self.category_manager.get_main_categories()]
                subcategories = {}
                for cat in self.category_manager.get_main_categories():
                    subcategories[cat.name] = [subcat.name for subcat in self.category_manager.get_subcategories(cat.name)]

                self.filter_panel.load_categories(main_categories, subcategories)
            except Exception as e:
                self.logger.error(f"Error loading filter categories: {str(e)}")

    def open_category_manager(self):
        """Open category management dialog from main window"""
        try:
            dialog = MLCategoryDialog(self)

            # Connect to category update signals for real-time updates
            if hasattr(dialog, 'creation_widget'):
                dialog.creation_widget.category_created.connect(self.on_category_created_externally)
                dialog.creation_widget.categories_updated.connect(self.on_categories_updated_externally)

            # Connect to the enhanced category selector if available for immediate refresh
            if hasattr(self, 'labeling_widget') and hasattr(self.labeling_widget, 'category_selector'):
                if hasattr(dialog, 'creation_widget'):
                    # Connect both signals to ensure refresh happens immediately
                    dialog.creation_widget.category_created.connect(
                        lambda: self._immediate_category_refresh()
                    )
                    dialog.creation_widget.categories_updated.connect(
                        lambda: self._immediate_category_refresh()
                    )

                    # Also connect to the dialog's own signals if they exist
                    if hasattr(dialog, 'category_created'):
                        dialog.category_created.connect(
                            lambda: self._immediate_category_refresh()
                        )
                    if hasattr(dialog, 'categories_updated'):
                        dialog.categories_updated.connect(
                            lambda: self._immediate_category_refresh()
                        )

            try:
                dialog.exec()
            except KeyboardInterrupt:
                # User interrupted the category manager dialog
                self.logger.info("Category manager dialog interrupted by user")
                pass
            # Always reload categories after dialog closes (regardless of result)
            self.refresh_categories()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to open category manager: {str(e)}")

    def on_category_created_externally(self, category_id: str):
        """Handle category creation from external dialogs"""
        try:
            # Force refresh of category manager from files
            if hasattr(self.category_manager, 'force_refresh_from_files'):
                self.category_manager.force_refresh_from_files()
            else:
                self.category_manager.refresh_categories(force=True)

            # Refresh labeling widget categories immediately
            if hasattr(self, 'labeling_widget') and self.labeling_widget:
                self.labeling_widget.reload_categories()

                # Also force immediate refresh of the category selector
                if hasattr(self.labeling_widget, 'category_selector'):
                    # Reset filter to show all categories after category creation
                    self.labeling_widget.category_selector.set_transaction_type(None)
                    self.labeling_widget.category_selector.refresh_categories()

            # Refresh filter panel categories
            if hasattr(self, 'filter_panel') and self.filter_panel:
                self.load_filter_categories()

            self.logger.info(f"Category created externally: {category_id}")
            self.statusBar().showMessage("New category created - updated across all components")

        except Exception as e:
            self.logger.error(f"Error handling external category creation: {str(e)}")

    def on_categories_updated_externally(self):
        """Handle categories update from external dialogs"""
        try:
            # This is called during category creation/update from MLCategoryDialog
            # Force refresh of all components
            self.refresh_categories()

            # Additional immediate refresh for the labeling widget's category selector
            if hasattr(self, 'labeling_widget') and self.labeling_widget:
                if hasattr(self.labeling_widget, 'category_selector'):
                    # Reset filter to show all categories after category creation
                    self.labeling_widget.category_selector.set_transaction_type(None)
                    self.labeling_widget.category_selector.refresh_categories()

            self.logger.info("Categories updated externally")

        except Exception as e:
            self.logger.error(f"Error handling external categories update: {str(e)}")

    def _immediate_category_refresh(self):
        """Perform immediate category refresh for UI responsiveness"""
        try:
            # Force refresh of category manager from files
            if hasattr(self.category_manager, 'force_refresh_from_files'):
                self.category_manager.force_refresh_from_files()
            else:
                self.category_manager.refresh_categories(force=True)

            # Immediately refresh the labeling widget's category selector
            if hasattr(self, 'labeling_widget') and self.labeling_widget:
                if hasattr(self.labeling_widget, 'category_selector'):
                    # Force cache invalidation and immediate reload
                    self.labeling_widget.category_selector._invalidate_cache()

                    # Reset transaction type filter to show all categories after category creation
                    # This ensures newly created categories are visible regardless of current filter
                    self.labeling_widget.category_selector.set_transaction_type(None)
                    self.labeling_widget.category_selector.load_categories()

            # Also schedule a delayed refresh to ensure persistence
            from PySide6.QtCore import QTimer
            QTimer.singleShot(100, self._delayed_category_refresh)

            self.logger.info("Immediate category refresh completed")

        except Exception as e:
            self.logger.error(f"Error in immediate category refresh: {str(e)}")

    def _delayed_category_refresh(self):
        """Perform delayed category refresh to ensure all components are updated"""
        try:
            # Refresh all components after a short delay
            self.refresh_categories()
            self.logger.info("Delayed category refresh completed")
        except Exception as e:
            self.logger.error(f"Error in delayed category refresh: {str(e)}")

    def refresh_categories(self):
        """Refresh categories in all components"""
        try:
            # Force refresh the category manager from files
            if hasattr(self.category_manager, 'force_refresh_from_files'):
                self.category_manager.force_refresh_from_files()
            else:
                self.category_manager.refresh_categories(force=True)

            # Refresh the labeling widget's categories
            if hasattr(self, 'labeling_widget') and self.labeling_widget:
                self.labeling_widget.reload_categories()

            # Refresh filter panel categories
            if hasattr(self, 'filter_panel') and self.filter_panel:
                self.load_filter_categories()

            # Refresh any open batch labeling dialogs
            # (This would require tracking open dialogs)

            # Update status
            self.statusBar().showMessage("Categories refreshed across all components")
            self.logger.info("Categories refreshed successfully")

        except Exception as e:
            self.logger.error(f"Error refreshing categories: {str(e)}")
            QMessageBox.warning(self, "Warning", f"Error refreshing categories: {str(e)}")

    def open_training_data_manager(self):
        """Open the session-based training data manager"""
        try:
            # Use the new session-based training management interface
            self.manage_session_training()

        except Exception as e:
            self.logger.error(f"Error opening training data manager: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to open training data manager: {str(e)}")

    def closeEvent(self, event):
        """Handle window close event"""
        if self.current_session_id:
            reply = QMessageBox.question(
                self, "Active Session", 
                "You have an active labeling session. End it before closing?",
                QMessageBox.Yes | QMessageBox.No | QMessageBox.Cancel
            )
            
            if reply == QMessageBox.Yes:
                self.end_labeling_session()
                event.accept()
            elif reply == QMessageBox.No:
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()

    # Session Management Methods

    def update_session_status(self):
        """Update session status display"""
        try:
            current_session = self.transaction_data_manager.current_session

            if current_session:
                status_text = f"""
📁 Session: {current_session.session_id[-12:]}...
📊 Status: {current_session.status.value.title()}
🔢 Transactions: {current_session.transaction_count}
📅 Created: {current_session.created_at.strftime('%Y-%m-%d %H:%M')}
                """.strip()
                self.session_status_label.setStyleSheet("""
                    QLabel {
                        padding: 8px;
                        background-color: #e8f5e8;
                        border: 1px solid #4caf50;
                        border-radius: 4px;
                        font-weight: bold;
                        color: #2e7d32;
                    }
                """)
            else:
                status_text = "No active session"
                self.session_status_label.setStyleSheet("""
                    QLabel {
                        padding: 8px;
                        background-color: #f0f0f0;
                        border: 1px solid #ddd;
                        border-radius: 4px;
                        font-weight: bold;
                        color: #666;
                    }
                """)

            self.session_status_label.setText(status_text)

        except Exception as e:
            self.logger.error(f"Error updating session status: {str(e)}", exc_info=True)
            self.session_status_label.setText("Error loading session status")

    def save_current_session(self):
        """Save current labeling work as a session"""
        try:
            # Check if we have data to save
            if not hasattr(self, 'unlabeled_transactions') or not self.unlabeled_transactions:
                QMessageBox.information(self, "No Data", "No transaction data to save as session.")
                return

            # Filter out None values and invalid transactions
            valid_transactions = []
            for txn in self.unlabeled_transactions:
                if txn is not None and hasattr(txn, 'description') and hasattr(txn, 'amount_range'):
                    valid_transactions.append(txn)

            if not valid_transactions:
                QMessageBox.information(self, "No Valid Data", "No valid transaction data to save as session.")
                return

            # Use valid transactions for session creation
            self.unlabeled_transactions = valid_transactions

            # Get session description from user
            description, ok = QInputDialog.getText(
                self, "Save Session",
                "Enter a description for this labeling session:",
                text=f"ML Labeling session created on {datetime.now().strftime('%Y-%m-%d %H:%M')}"
            )

            if not ok:
                return

            # Convert unique transactions to raw transactions for session storage
            raw_transactions = []
            for unique_txn in self.unlabeled_transactions:
                try:
                    # Check if transaction is None or invalid
                    if unique_txn is None:
                        self.logger.warning("Skipping None transaction")
                        continue

                    # Check if transaction has required attributes
                    if not hasattr(unique_txn, 'description') or not hasattr(unique_txn, 'amount_range'):
                        self.logger.warning(f"Skipping invalid transaction: {unique_txn}")
                        continue

                    # Extract amount from amount_range string
                    amount_str = getattr(unique_txn, 'amount_range', '0')

                    # Handle None or empty amount_range
                    if amount_str is None:
                        amount_str = '0'

                    # Handle different amount range formats
                    if ' - ' in str(amount_str):
                        # Range format like "100 - 200", take the first value
                        amount_str = str(amount_str).split(' - ')[0]

                    # Clean the amount string (remove currency symbols, commas, etc.)
                    import re
                    amount_str = re.sub(r'[^\d.-]', '', str(amount_str))

                    # Convert to Decimal, default to 0 if conversion fails
                    try:
                        amount_value = Decimal(amount_str) if amount_str else Decimal('0')
                    except (ValueError, TypeError):
                        amount_value = Decimal('0')

                    # Get description safely
                    description = getattr(unique_txn, 'description', 'Unknown Transaction')
                    if description is None:
                        description = 'Unknown Transaction'

                    # Create a raw transaction from unique transaction data
                    raw_txn = RawTransaction(
                        date=datetime.now().date(),  # Use current date as placeholder
                        description=str(description),
                        amount=amount_value,
                        transaction_type="DEBIT",  # Default type
                        source_file="ml_labeling_session"
                    )
                    raw_transactions.append(raw_txn)

                except Exception as txn_error:
                    # Get description safely for error logging
                    desc = "Unknown"
                    try:
                        if unique_txn and hasattr(unique_txn, 'description'):
                            desc = getattr(unique_txn, 'description', 'Unknown')
                    except:
                        pass

                    self.logger.warning(f"Error converting transaction {desc}: {str(txn_error)}")
                    # Skip this transaction and continue with others
                    continue

            # Create session
            session_id = self.transaction_data_manager.create_session(
                raw_transactions,
                description=description
            )

            # Update session status
            self.update_session_status()

            QMessageBox.information(
                self, "Session Saved",
                f"Labeling session saved successfully!\n\nSession ID: {session_id}"
            )

            self.logger.info(f"Created ML labeling session: {session_id}")

        except Exception as e:
            self.logger.error(f"Error saving session: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "Save Error", f"Failed to save session:\n{str(e)}")

    def open_session_management(self):
        """Open the session management dialog"""
        try:
            from .session_management_dialog import SessionManagementDialog

            dialog = SessionManagementDialog(self.transaction_data_manager, self)
            dialog.session_selected.connect(self.load_session_data)
            try:
                dialog.exec()
            except KeyboardInterrupt:
                # User interrupted the session management dialog
                self.logger.info("Session management dialog interrupted by user")
                pass

        except Exception as e:
            self.logger.error(f"Error opening session management: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "Session Management Error", f"Failed to open session management:\n{str(e)}")

    def load_session_data(self, session_id: str, raw_transactions, processed_transactions):
        """Load session data into the labeling interface"""
        try:
            # Convert raw transactions back to unique transactions for the ML labeling interface
            # This is a simplified conversion - in practice, you'd want to preserve more metadata
            unique_transactions = []

            for raw_txn in raw_transactions:
                # Skip None transactions
                if not raw_txn:
                    self.logger.warning("Skipping None transaction in session data")
                    continue

                # Validate required attributes
                if not hasattr(raw_txn, 'description') or not hasattr(raw_txn, 'amount') or not hasattr(raw_txn, 'date'):
                    self.logger.warning(f"Skipping transaction with missing attributes: {raw_txn}")
                    continue

                # Create a unique transaction from raw transaction
                description = raw_txn.description or "[No Description]"
                unique_txn = UniqueTransaction(
                    hash_id=f"session_{len(unique_transactions)}",
                    description=description,
                    normalized_description=description.lower().strip(),
                    frequency=1,  # Default frequency
                    amount_range=str(raw_txn.amount),
                    first_seen=raw_txn.date,
                    last_seen=raw_txn.date,
                    is_manually_labeled=False,
                    category="",
                    sub_category=""
                )
                unique_transactions.append(unique_txn)

            # Set the loaded transactions
            self.unlabeled_transactions = unique_transactions
            self.filtered_transactions = unique_transactions
            self.current_index = 0

            # Mark that data has been explicitly loaded
            self._data_explicitly_loaded = True

            # Re-enable auto-reload since we're loading new data
            self.enable_auto_reload()

            # Update UI
            self.load_transaction_batch()
            self.update_session_status()

            # Show first transaction
            if unique_transactions:
                self.show_current_transaction()

            self.statusBar().showMessage(f"Loaded session: {session_id[-12:]}... ({len(unique_transactions)} transactions)")
            self.logger.info(f"Loaded ML labeling session: {session_id} ({len(unique_transactions)} transactions)")

        except Exception as e:
            self.logger.error(f"Error loading session data: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "Session Load Error", f"Failed to load session data:\n{str(e)}")

    def load_data_explicitly(self):
        """Explicitly load data and mark as loaded (for when data is imported from main window)"""
        try:
            # Mark that data has been explicitly loaded
            self._data_explicitly_loaded = True

            # Force clear all caches first to ensure fresh data
            self._force_clear_all_caches()

            # Check for new session data and offer to import it
            self._check_and_import_new_session_data()

            # Re-enable auto-reload
            self.enable_auto_reload()

            # Load initial data (stats and transaction batch)
            self.load_initial_data()

            self.statusBar().showMessage("Existing data loaded successfully", 3000)
            self.logger.info("Data explicitly loaded and interface updated")

        except Exception as e:
            self.logger.error(f"Error explicitly loading data: {str(e)}")
            self.statusBar().showMessage(f"Error loading data: {str(e)}", 5000)

    def clear_data_explicitly(self):
        """Explicitly clear data and mark as not loaded"""
        try:
            # Mark that data is no longer explicitly loaded
            self._data_explicitly_loaded = False

            # Clear current transactions
            if hasattr(self, 'current_transactions'):
                self.current_transactions = []
            if hasattr(self, 'unlabeled_transactions'):
                self.unlabeled_transactions = []
            if hasattr(self, 'filtered_transactions'):
                self.filtered_transactions = []

            # Show empty state
            self._show_empty_stats()
            self._show_empty_transaction_display()

            self.logger.info("Data explicitly cleared")

        except Exception as e:
            self.logger.error(f"Error explicitly clearing data: {str(e)}")

    def _check_and_import_new_session_data(self):
        """Check for new session data and offer to import it to training data"""
        try:
            # Get all available sessions
            sessions = self.transaction_data_manager.get_all_sessions()

            if not sessions:
                self.logger.info("No sessions found to import")
                return

            # Find the most recent session
            latest_session = max(sessions.values(), key=lambda s: s.updated_at)

            # Check if this session has new data that's not in training data
            raw_transactions, processed_transactions = self.transaction_data_manager.get_session_transactions(latest_session.session_id)

            if not raw_transactions:
                self.logger.info("No transaction data found in latest session")
                return

            # Check if we should import this data
            from PySide6.QtWidgets import QMessageBox

            # Create a custom dialog with three options
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle("Import New Data")
            msg_box.setText(
                f"Found new transaction data from session '{latest_session.session_id}' "
                f"with {len(raw_transactions)} transactions.\n\n"
                f"How would you like to handle this data?"
            )

            # Add custom buttons
            replace_btn = msg_box.addButton("Replace Current Data", QMessageBox.ActionRole)
            merge_btn = msg_box.addButton("Merge with Current Data", QMessageBox.ActionRole)
            cancel_btn = msg_box.addButton("Cancel", QMessageBox.RejectRole)

            msg_box.setDefaultButton(replace_btn)
            msg_box.exec()

            clicked_button = msg_box.clickedButton()

            if clicked_button == replace_btn:
                # Load ONLY the 147 transactions (replace current data)
                self._load_session_data_directly(raw_transactions, latest_session.session_id)
            elif clicked_button == merge_btn:
                # Merge with existing data
                self._import_session_to_training_data(raw_transactions, latest_session.session_id)

        except Exception as e:
            self.logger.error(f"Error checking for new session data: {str(e)}")

    def _load_session_data_directly(self, raw_transactions, session_id: str):
        """Load session data directly into the UI without merging with existing training data"""
        try:
            self.statusBar().showMessage("Loading session data directly...", 0)

            # Start session-based training for this data
            training_session_description = f"Training for session {session_id} with {len(raw_transactions)} transactions"
            self.start_session_based_training(session_id, training_session_description)

            # DEBUG: Log what we're actually receiving
            self.logger.info(f"_load_session_data_directly called with {len(raw_transactions)} raw transactions from session {session_id}")
            if raw_transactions:
                sample_txn = raw_transactions[0]
                self.logger.info(f"Sample transaction: {sample_txn.description[:100]}... Date: {sample_txn.date}")

            # Extract unique transactions from raw transactions
            unique_transactions = self.training_manager.data_preparator.extract_unique_transactions(raw_transactions)

            if not unique_transactions:
                self.statusBar().showMessage("No unique transactions to load", 3000)
                return

            # DEBUG: Log what unique transactions we extracted
            self.logger.info(f"Extracted {len(unique_transactions)} unique transactions")
            if unique_transactions:
                sample_unique = list(unique_transactions.values())[0]
                self.logger.info(f"Sample unique transaction: {sample_unique.description[:100]}... Frequency: {sample_unique.frequency}")

            # Convert to list format for the UI
            transaction_list = list(unique_transactions.values())

            # Sort by frequency (most common first)
            transaction_list.sort(key=lambda x: x.frequency, reverse=True)

            # Clear all UI data first
            self._force_clear_all_caches()

            # Set the transactions directly in the UI (NO merging with existing data)
            self.unlabeled_transactions = transaction_list
            self.filtered_transactions = transaction_list

            # Update the enhanced table directly
            if hasattr(self, 'enhanced_table') and self.enhanced_table:
                self.enhanced_table.populate_table(transaction_list)

            # Update statistics
            total_count = len(transaction_list)
            unlabeled_count = len([txn for txn in transaction_list if not txn.is_manually_labeled])

            self.statusBar().showMessage(
                f"Loaded {total_count} transactions from session (ONLY these transactions, no merging)",
                5000
            )

            # Show success message
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.information(
                self,
                "Session Data Loaded",
                f"Successfully loaded {total_count} transactions from session:\n"
                f"'{session_id}'\n\n"
                f"Unlabeled: {unlabeled_count}\n"
                f"Total: {total_count}\n\n"
                f"This is ONLY your new data (no old data merged)!"
            )

            self.logger.info(f"Loaded {total_count} transactions directly from session: {session_id}")

        except Exception as e:
            self.logger.error(f"Error loading session data directly: {str(e)}")
            self.statusBar().showMessage(f"Load failed: {str(e)}", 5000)

    def _import_session_to_training_data(self, raw_transactions, session_id: str):
        """Import raw transactions from session to training data"""
        try:
            self.statusBar().showMessage("Importing new transaction data...", 0)

            # Extract unique transactions from raw transactions
            unique_transactions = self.training_manager.data_preparator.extract_unique_transactions(raw_transactions)

            if not unique_transactions:
                self.statusBar().showMessage("No unique transactions to import", 3000)
                return

            # Merge new transactions with existing ones (existing data is loaded automatically)
            merged_transactions = self.training_manager.data_preparator.merge_unique_transactions(unique_transactions)

            # Save the merged data
            success = self.training_manager.data_preparator.save_unique_transactions(merged_transactions)

            if success:
                new_count = len(unique_transactions)
                total_count = len(merged_transactions)

                self.statusBar().showMessage(
                    f"Successfully imported {new_count} new transactions. Total: {total_count}",
                    5000
                )

                self.logger.info(f"Imported {new_count} transactions from session {session_id}")

                # Clear ALL caches to force complete reload
                self._force_clear_all_caches()

            else:
                self.statusBar().showMessage("Failed to import transaction data", 5000)

        except Exception as e:
            self.logger.error(f"Error importing session to training data: {str(e)}")
            self.statusBar().showMessage(f"Import failed: {str(e)}", 5000)

    def import_from_sessions(self):
        """Manually import data from available sessions"""
        try:
            # Get all available sessions
            sessions = self.transaction_data_manager.get_all_sessions()

            if not sessions:
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.information(
                    self,
                    "No Sessions Found",
                    "No processed sessions found. Please process some bank statements first."
                )
                return

            # Show session selection dialog
            from PySide6.QtWidgets import QDialog, QVBoxLayout, QListWidget, QPushButton, QHBoxLayout, QLabel

            dialog = QDialog(self)
            dialog.setWindowTitle("Import from Sessions")
            dialog.setModal(True)
            dialog.resize(500, 400)

            layout = QVBoxLayout(dialog)

            # Instructions
            label = QLabel("Select a session to import transaction data for manual labeling:")
            layout.addWidget(label)

            # Session list
            session_list = QListWidget()
            for session_id, session in sessions.items():
                item_text = f"{session_id} ({session.transaction_count} transactions) - {session.updated_at.strftime('%Y-%m-%d %H:%M')}"
                session_list.addItem(item_text)
                session_list.item(session_list.count() - 1).setData(32, session_id)  # Store session_id

            layout.addWidget(session_list)

            # Buttons
            button_layout = QHBoxLayout()
            import_btn = QPushButton("Import Selected")
            cancel_btn = QPushButton("Cancel")

            button_layout.addWidget(import_btn)
            button_layout.addWidget(cancel_btn)
            layout.addLayout(button_layout)

            # Connect buttons
            cancel_btn.clicked.connect(dialog.reject)

            def import_selected():
                current_item = session_list.currentItem()
                if current_item:
                    session_id = current_item.data(32)
                    dialog.accept()

                    # Import the selected session
                    raw_transactions, _ = self.transaction_data_manager.get_session_transactions(session_id)
                    if raw_transactions:
                        self._import_session_to_training_data(raw_transactions, session_id)
                    else:
                        self.statusBar().showMessage("No transaction data found in selected session", 3000)

            import_btn.clicked.connect(import_selected)

            # Show dialog
            dialog.exec()

        except Exception as e:
            self.logger.error(f"Error in import from sessions: {str(e)}")
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "Import Error", f"Failed to import from sessions: {str(e)}")

    def load_latest_session_directly(self):
        """Load data directly from the latest session, bypassing all training data and caches"""
        try:
            self.statusBar().showMessage("Loading latest session data directly...", 0)

            # Get all available sessions
            sessions = self.transaction_data_manager.get_all_sessions()

            if not sessions:
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.information(
                    self,
                    "No Sessions Found",
                    "No processed sessions found. Please process some bank statements first."
                )
                return

            # Find the most recent session, but prefer smaller sessions (like 147 transactions)
            # over large bulk sessions (like 684 transactions)

            # First, try to find sessions with reasonable transaction counts (< 200)
            reasonable_sessions = []
            for session_id, session in sessions.items():
                try:
                    raw_transactions, _ = self.transaction_data_manager.get_session_transactions(session_id)
                    transaction_count = len(raw_transactions)
                    if transaction_count > 0 and transaction_count < 200:  # Prefer smaller, recent sessions
                        reasonable_sessions.append((session, transaction_count))
                except Exception:
                    continue

            if reasonable_sessions:
                # Sort by update time (most recent first) among reasonable sessions
                reasonable_sessions.sort(key=lambda x: x[0].updated_at, reverse=True)
                latest_session = reasonable_sessions[0][0]
                self.logger.info(f"Selected reasonable session with {reasonable_sessions[0][1]} transactions")
            else:
                # Fallback to most recent session regardless of size
                latest_session = max(sessions.values(), key=lambda s: s.updated_at)
                self.logger.info("No reasonable sessions found, using most recent session")

            # Load raw transactions directly from session
            raw_transactions, processed_transactions = self.transaction_data_manager.get_session_transactions(latest_session.session_id)

            if not raw_transactions:
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.warning(
                    self,
                    "No Data",
                    f"No transaction data found in latest session: {latest_session.session_id}"
                )
                return

            # Convert raw transactions to unique transactions for display
            unique_transactions = self.training_manager.data_preparator.extract_unique_transactions(raw_transactions)

            # Convert to list format for the UI
            transaction_list = list(unique_transactions.values())

            # Sort by frequency (most common first)
            transaction_list.sort(key=lambda x: x.frequency, reverse=True)

            # Clear all UI data first
            self._force_clear_all_caches()

            # Set the transactions directly in the UI
            self.unlabeled_transactions = transaction_list
            self.filtered_transactions = transaction_list

            # Update the enhanced table directly
            if hasattr(self, 'enhanced_table') and self.enhanced_table:
                self.enhanced_table.populate_table(transaction_list)

            # Update statistics
            total_count = len(transaction_list)
            unlabeled_count = len([txn for txn in transaction_list if not txn.is_manually_labeled])

            self.statusBar().showMessage(
                f"Loaded {total_count} transactions directly from latest session ({unlabeled_count} unlabeled)",
                5000
            )

            # Show success message
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.information(
                self,
                "Latest Session Loaded",
                f"Successfully loaded {total_count} transactions directly from session:\n"
                f"'{latest_session.session_id}'\n\n"
                f"Unlabeled: {unlabeled_count}\n"
                f"Total: {total_count}\n\n"
                f"This data is loaded directly from the session, bypassing all caches."
            )

            self.logger.info(f"Loaded {total_count} transactions directly from latest session: {latest_session.session_id}")

        except Exception as e:
            self.logger.error(f"Error loading latest session directly: {str(e)}")
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "Load Error", f"Failed to load latest session: {str(e)}")

    def choose_and_load_session(self):
        """Show a dialog to choose which session to load"""
        try:
            sessions = self.transaction_data_manager.get_all_sessions()

            if not sessions:
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.information(
                    self,
                    "No Sessions Found",
                    "No processed sessions found. Please process some bank statements first."
                )
                return

            # Create session info for the picker
            session_options = []
            session_data = {}

            for session_id, session in sessions.items():
                try:
                    raw_transactions, _ = self.transaction_data_manager.get_session_transactions(session_id)
                    transaction_count = len(raw_transactions) if raw_transactions else 0

                    # Get sample transaction info
                    sample_info = ""
                    if raw_transactions and len(raw_transactions) > 0:
                        sample = raw_transactions[0]
                        sample_info = f" | Sample: {sample.description[:30]}..."
                        if sample.date:
                            sample_info += f" | Date: {sample.date.strftime('%Y-%m-%d')}"

                    # Create display text
                    display_text = (
                        f"{session_id} "
                        f"({transaction_count} transactions) "
                        f"| Updated: {session.updated_at.strftime('%Y-%m-%d %H:%M')}"
                        f"{sample_info}"
                    )

                    session_options.append(display_text)
                    session_data[display_text] = (session_id, session, raw_transactions)

                except Exception as e:
                    self.logger.warning(f"Error processing session {session_id}: {str(e)}")
                    continue

            if not session_options:
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.warning(self, "No Valid Sessions", "No valid sessions found.")
                return

            # Sort by update time (most recent first)
            session_options.sort(key=lambda x: session_data[x][1].updated_at, reverse=True)

            # Show selection dialog
            from PySide6.QtWidgets import QInputDialog
            selected_option, ok = QInputDialog.getItem(
                self,
                "Choose Session to Load",
                "Select a session to load into the labeling interface:",
                session_options,
                0,  # Default to first (most recent)
                False  # Not editable
            )

            if not ok or not selected_option:
                return

            # Load the selected session
            session_id, session, raw_transactions = session_data[selected_option]
            self._load_session_data_directly(raw_transactions, session_id)

        except Exception as e:
            self.logger.error(f"Error choosing session: {str(e)}")
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "Session Selection Error", f"Failed to choose session: {str(e)}")

    def manage_session_training(self):
        """Manage session-based training data"""
        try:
            from PySide6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QListWidget, QTextEdit

            dialog = QDialog(self)
            dialog.setWindowTitle("Session-Based Training Management")
            dialog.setMinimumSize(800, 600)

            layout = QVBoxLayout(dialog)

            # Current session info
            current_info = QLabel()
            if self.current_training_session_id:
                current_info.setText(f"🎯 Current Training Session: {self.current_training_session_id}")
                current_info.setStyleSheet("color: green; font-weight: bold;")
            else:
                current_info.setText("📋 No active training session")
                current_info.setStyleSheet("color: orange;")
            layout.addWidget(current_info)

            # Session list
            layout.addWidget(QLabel("Available Training Sessions:"))
            session_list = QListWidget()

            # Load session information
            sessions_info = self.session_training_manager.list_all_sessions()
            for session_id, session_data in sessions_info.items():
                status = session_data.get('training_status', 'active')
                labeled_count = session_data.get('labeled_count', 0)
                source_session = session_data.get('source_session_id', 'unknown')

                item_text = f"{session_id} | Status: {status} | Labeled: {labeled_count} | Source: {source_session}"
                session_list.addItem(item_text)

            layout.addWidget(session_list)

            # Action buttons
            button_layout = QHBoxLayout()

            combine_button = QPushButton("🔗 Combine All Labeled Data for Training")
            combine_button.clicked.connect(lambda: self._combine_session_data_for_training(dialog))
            button_layout.addWidget(combine_button)

            archive_button = QPushButton("📦 Archive Old Sessions")
            archive_button.clicked.connect(lambda: self._archive_old_sessions(dialog))
            button_layout.addWidget(archive_button)

            close_button = QPushButton("Close")
            close_button.clicked.connect(dialog.accept)
            button_layout.addWidget(close_button)

            layout.addLayout(button_layout)

            # Info text
            info_text = QTextEdit()
            info_text.setMaximumHeight(150)
            info_text.setPlainText(
                "Session-Based Training Workflow:\n"
                "1. Each time you load new transaction data, a training session is created\n"
                "2. Label transactions in each session independently\n"
                "3. Use 'Combine All Labeled Data' to train the model with data from all sessions\n"
                "4. Archive old sessions to keep only labeled data and remove unlabeled data\n\n"
                "This keeps your training data clean and organized by processing session."
            )
            info_text.setReadOnly(True)
            layout.addWidget(info_text)

            dialog.exec()

        except Exception as e:
            self.logger.error(f"Error managing session training: {str(e)}")
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "Session Management Error", f"Failed to manage session training: {str(e)}")

    def _combine_session_data_for_training(self, parent_dialog):
        """Combine labeled data from all sessions for model training"""
        try:
            combined_data = self.session_training_manager.get_combined_training_data()

            if combined_data.empty:
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.warning(parent_dialog, "No Data", "No labeled training data found in any session.")
                return

            # Show confirmation dialog
            from PySide6.QtWidgets import QMessageBox
            msg = QMessageBox(parent_dialog)
            msg.setWindowTitle("Combine Training Data")
            msg.setText(
                f"Found {len(combined_data)} labeled transactions across all sessions.\n\n"
                f"This will:\n"
                f"• Combine all labeled data from all sessions\n"
                f"• Update the main training data\n"
                f"• Trigger model retraining\n\n"
                f"Continue?"
            )
            msg.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
            msg.setDefaultButton(QMessageBox.Yes)

            if msg.exec() == QMessageBox.Yes:
                # Update the main training data with combined session data
                self._update_main_training_data_with_sessions(combined_data)

                QMessageBox.information(
                    parent_dialog,
                    "Success",
                    f"Successfully combined {len(combined_data)} labeled transactions.\n"
                    f"Training data has been updated. You can now train the model."
                )

        except Exception as e:
            self.logger.error(f"Error combining session data: {str(e)}")
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.critical(parent_dialog, "Combine Error", f"Failed to combine session data: {str(e)}")

    def _archive_old_sessions(self, parent_dialog):
        """Archive old training sessions"""
        try:
            sessions_info = self.session_training_manager.list_all_sessions()
            active_sessions = [
                session_id for session_id, data in sessions_info.items()
                if data.get('training_status', 'active') == 'active'
            ]

            if not active_sessions:
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.information(parent_dialog, "No Sessions", "No active sessions to archive.")
                return

            # Show confirmation
            from PySide6.QtWidgets import QMessageBox
            msg = QMessageBox(parent_dialog)
            msg.setWindowTitle("Archive Sessions")
            msg.setText(
                f"Archive {len(active_sessions)} active sessions?\n\n"
                f"This will:\n"
                f"• Keep all labeled data\n"
                f"• Remove unlabeled data to save space\n"
                f"• Mark sessions as archived\n\n"
                f"Continue?"
            )
            msg.setStandardButtons(QMessageBox.Yes | QMessageBox.No)

            if msg.exec() == QMessageBox.Yes:
                archived_count = 0
                for session_id in active_sessions:
                    self.session_training_manager.archive_session_unlabeled_data(session_id)
                    archived_count += 1

                QMessageBox.information(
                    parent_dialog,
                    "Archive Complete",
                    f"Successfully archived {archived_count} sessions.\n"
                    f"Labeled data preserved, unlabeled data removed."
                )

        except Exception as e:
            self.logger.error(f"Error archiving sessions: {str(e)}")
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.critical(parent_dialog, "Archive Error", f"Failed to archive sessions: {str(e)}")

    def _update_main_training_data_with_sessions(self, combined_data):
        """Update the main training data with combined session data"""
        try:
            # Convert combined session data to UniqueTransaction objects
            unique_transactions = {}

            for _, row in combined_data.iterrows():
                from ..ml.data_preparation import UniqueTransaction

                txn = UniqueTransaction(
                    description=row['description'],
                    normalized_description=row['normalized_description'],
                    hash_id=row['hash_id'],
                    frequency=row.get('frequency', 1),
                    category=row['category'],
                    sub_category=row['sub_category'],
                    confidence=row.get('confidence', 1.0),
                    is_manually_labeled=row.get('is_manually_labeled', True),
                    labeled_by=f"session_{row.get('session_id', 'unknown')}",
                    labeled_at=datetime.now().isoformat()
                )
                unique_transactions[txn.hash_id] = txn

            # Save to main training data
            success = self.training_manager.data_preparator.save_unique_transactions(unique_transactions)

            if success:
                self.logger.info(f"Successfully updated main training data with {len(unique_transactions)} transactions from sessions")

                # Refresh the UI to show updated data
                self.update_stats()

                # Clear caches to ensure fresh data
                self._force_clear_all_caches()

            else:
                raise Exception("Failed to save combined training data")

        except Exception as e:
            self.logger.error(f"Error updating main training data: {str(e)}")
            raise

    def debug_sessions(self):
        """Debug method to show detailed information about all sessions"""
        try:
            sessions = self.transaction_data_manager.get_all_sessions()

            if not sessions:
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.information(self, "Debug Sessions", "No sessions found.")
                return

            debug_info = []
            debug_info.append(f"Found {len(sessions)} sessions:\n")

            for session_id, session in sessions.items():
                try:
                    raw_transactions, _ = self.transaction_data_manager.get_session_transactions(session_id)
                    transaction_count = len(raw_transactions) if raw_transactions else 0

                    debug_info.append(f"Session: {session_id}")
                    debug_info.append(f"  Count: {transaction_count}")
                    debug_info.append(f"  Updated: {session.updated_at}")
                    debug_info.append(f"  Status: {session.status}")

                    if raw_transactions and len(raw_transactions) > 0:
                        sample = raw_transactions[0]
                        debug_info.append(f"  Sample: {sample.description[:50]}...")
                        debug_info.append(f"  Date: {sample.date}")

                    debug_info.append("")

                except Exception as e:
                    debug_info.append(f"  ERROR: {str(e)}")
                    debug_info.append("")

            from PySide6.QtWidgets import QMessageBox
            QMessageBox.information(
                self,
                "Debug Sessions",
                "\n".join(debug_info)
            )

        except Exception as e:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "Debug Error", f"Failed to debug sessions: {str(e)}")

    def replace_training_data_with_latest_session(self):
        """Replace all training data with the latest session data"""
        try:
            from PySide6.QtWidgets import QMessageBox

            # Confirm with user
            reply = QMessageBox.question(
                self,
                "Replace Training Data",
                "⚠️ This will REPLACE all existing training data with the latest session data.\n\n"
                "This action cannot be undone. Are you sure you want to continue?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            self.statusBar().showMessage("Replacing training data with latest session...", 0)

            # Get all available sessions
            sessions = self.transaction_data_manager.get_all_sessions()

            if not sessions:
                QMessageBox.information(
                    self,
                    "No Sessions Found",
                    "No processed sessions found. Please process some bank statements first."
                )
                return

            # Find the most recent session
            latest_session = max(sessions.values(), key=lambda s: s.updated_at)

            # Load raw transactions directly from session
            raw_transactions, processed_transactions = self.transaction_data_manager.get_session_transactions(latest_session.session_id)

            if not raw_transactions:
                QMessageBox.warning(
                    self,
                    "No Data",
                    f"No transaction data found in latest session: {latest_session.session_id}"
                )
                return

            # Extract unique transactions
            unique_transactions = self.training_manager.data_preparator.extract_unique_transactions(raw_transactions)

            # REPLACE (not merge) the training data
            success = self.training_manager.data_preparator.save_unique_transactions(unique_transactions)

            if success:
                # Clear all caches
                self._force_clear_all_caches()

                # Reload the interface
                self.load_initial_data()

                total_count = len(unique_transactions)
                unlabeled_count = len([txn for txn in unique_transactions.values() if not txn.is_manually_labeled])

                self.statusBar().showMessage(
                    f"Training data replaced with {total_count} transactions from latest session",
                    5000
                )

                QMessageBox.information(
                    self,
                    "Training Data Replaced",
                    f"Successfully replaced training data with latest session:\n"
                    f"'{latest_session.session_id}'\n\n"
                    f"Total transactions: {total_count}\n"
                    f"Unlabeled: {unlabeled_count}\n\n"
                    f"All old training data has been replaced."
                )

                self.logger.info(f"Replaced training data with {total_count} transactions from session: {latest_session.session_id}")

            else:
                QMessageBox.critical(self, "Save Error", "Failed to save the new training data.")

        except Exception as e:
            self.logger.error(f"Error replacing training data: {str(e)}")
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "Replace Error", f"Failed to replace training data: {str(e)}")

    def nuclear_load_session_only(self):
        """NUCLEAR OPTION: Delete all old training data and load ONLY from latest session"""
        try:
            from PySide6.QtWidgets import QMessageBox

            # Strong warning
            reply = QMessageBox.question(
                self,
                "💥 NUCLEAR OPTION - DELETE ALL OLD DATA",
                "⚠️ WARNING: This will PERMANENTLY DELETE all existing training data!\n\n"
                "This action will:\n"
                "• DELETE unique_transactions.csv (your 402 old transactions)\n"
                "• DELETE all training history\n"
                "• DELETE all manual labels\n"
                "• REPLACE everything with ONLY your latest session data\n\n"
                "This CANNOT be undone!\n\n"
                "Are you absolutely sure you want to continue?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # Second confirmation
            reply2 = QMessageBox.question(
                self,
                "💥 FINAL CONFIRMATION",
                "This is your FINAL chance to cancel!\n\n"
                "Clicking YES will PERMANENTLY DELETE all old training data.\n\n"
                "Continue with NUCLEAR option?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply2 != QMessageBox.Yes:
                return

            self.statusBar().showMessage("💥 NUCLEAR OPTION: Deleting all old data...", 0)

            # Get latest session first
            sessions = self.transaction_data_manager.get_all_sessions()
            if not sessions:
                QMessageBox.critical(self, "No Sessions", "No sessions found to load from!")
                return

            latest_session = max(sessions.values(), key=lambda s: s.updated_at)
            raw_transactions, _ = self.transaction_data_manager.get_session_transactions(latest_session.session_id)

            if not raw_transactions:
                QMessageBox.critical(self, "No Data", "No transaction data found in latest session!")
                return

            # DELETE ALL OLD TRAINING DATA FILES
            import os
            training_data_dir = Path("bank_analyzer_config/ml_data")

            files_to_delete = [
                "unique_transactions.csv",
                "labeling_history.csv",
                "training_sessions.json",
                "quality_metrics.json"
            ]

            deleted_files = []
            for filename in files_to_delete:
                file_path = training_data_dir / filename
                if file_path.exists():
                    try:
                        os.remove(file_path)
                        deleted_files.append(filename)
                        self.logger.info(f"DELETED: {filename}")
                    except Exception as e:
                        self.logger.error(f"Failed to delete {filename}: {str(e)}")

            # Extract unique transactions from session
            unique_transactions = self.training_manager.data_preparator.extract_unique_transactions(raw_transactions)

            # Save ONLY the session data as new training data
            success = self.training_manager.data_preparator.save_unique_transactions(unique_transactions)

            if success:
                # Clear ALL caches
                self._force_clear_all_caches()

                # Force reload everything
                self.load_initial_data()

                total_count = len(unique_transactions)

                self.statusBar().showMessage(
                    f"💥 NUCLEAR COMPLETE: {total_count} transactions from latest session ONLY",
                    10000
                )

                QMessageBox.information(
                    self,
                    "💥 NUCLEAR OPTION COMPLETE",
                    f"SUCCESS! All old data has been DELETED.\n\n"
                    f"Deleted files: {', '.join(deleted_files)}\n\n"
                    f"Now showing ONLY your latest session data:\n"
                    f"Session: {latest_session.session_id}\n"
                    f"Transactions: {total_count}\n\n"
                    f"Your 402 old transactions are GONE forever.\n"
                    f"Only your {len(raw_transactions)} newly processed transactions remain."
                )

                self.logger.info(f"NUCLEAR OPTION COMPLETE: Replaced all data with {total_count} transactions from session {latest_session.session_id}")

            else:
                QMessageBox.critical(self, "Save Error", "Failed to save the new training data after deletion!")

        except Exception as e:
            self.logger.error(f"Error in nuclear option: {str(e)}")
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "Nuclear Error", f"Nuclear option failed: {str(e)}")

    def _force_clear_all_caches(self):
        """Force clear all caches to ensure fresh data loading"""
        try:
            self.logger.info("Cache clearing no longer needed - caching system removed for reliability")

            # Clear any local caches that might still exist
            if hasattr(self, '_unlabeled_cache'):
                delattr(self, '_unlabeled_cache')
            if hasattr(self, '_filtered_cache'):
                delattr(self, '_filtered_cache')

            # Clear transaction lists to force reload
            self.unlabeled_transactions = []
            self.filtered_transactions = []
            if hasattr(self, 'current_transactions'):
                self.current_transactions = []

            self.logger.info("All caches cleared successfully")

        except Exception as e:
            self.logger.error(f"Error clearing caches: {str(e)}")

    def force_refresh_data(self):
        """Force refresh data by clearing all caches and reloading"""
        try:
            self.statusBar().showMessage("Force refreshing data...", 0)

            # Clear all caches
            self._force_clear_all_caches()

            # Reload data
            self.load_initial_data()

            self.statusBar().showMessage("Data force refreshed successfully", 3000)
            self.logger.info("Force refresh completed")

        except Exception as e:
            self.logger.error(f"Error in force refresh: {str(e)}")
            self.statusBar().showMessage(f"Force refresh failed: {str(e)}", 5000)

    def show_debug_data_info(self):
        """Show debug information about currently loaded data"""
        try:
            from PySide6.QtWidgets import QDialog, QVBoxLayout, QTextEdit, QPushButton

            dialog = QDialog(self)
            dialog.setWindowTitle("Debug Data Information")
            dialog.setModal(True)
            dialog.resize(600, 500)

            layout = QVBoxLayout(dialog)

            # Text area for debug info
            text_edit = QTextEdit()
            text_edit.setReadOnly(True)

            # Gather debug information
            debug_info = []
            debug_info.append("=== DEBUG DATA INFORMATION ===\n")

            # Check sessions
            try:
                sessions = self.transaction_data_manager.get_all_sessions()
                debug_info.append(f"📁 Available Sessions: {len(sessions)}")
                if sessions:
                    latest_session = max(sessions.values(), key=lambda s: s.updated_at)
                    debug_info.append(f"   Latest: {latest_session.session_id} ({latest_session.transaction_count} transactions)")
                    debug_info.append(f"   Updated: {latest_session.updated_at}")
                debug_info.append("")
            except Exception as e:
                debug_info.append(f"❌ Error checking sessions: {str(e)}\n")

            # Check training data
            try:
                unique_transactions = self.training_manager.data_preparator.load_unique_transactions()
                debug_info.append(f"🗃️ Training Data: {len(unique_transactions)} unique transactions")

                unlabeled = [txn for txn in unique_transactions.values() if not txn.is_manually_labeled]
                labeled = [txn for txn in unique_transactions.values() if txn.is_manually_labeled]
                debug_info.append(f"   Unlabeled: {len(unlabeled)}")
                debug_info.append(f"   Labeled: {len(labeled)}")

                # Show some sample descriptions
                if unlabeled:
                    debug_info.append("\n📋 Sample Unlabeled Transactions:")
                    for i, txn in enumerate(unlabeled[:5]):
                        debug_info.append(f"   {i+1}. {txn.description[:60]}... (freq: {txn.frequency})")

                debug_info.append("")
            except Exception as e:
                debug_info.append(f"❌ Error checking training data: {str(e)}\n")

            # Check current UI state
            debug_info.append(f"🖥️ Current UI State:")
            debug_info.append(f"   Unlabeled transactions loaded: {len(getattr(self, 'unlabeled_transactions', []))}")
            debug_info.append(f"   Filtered transactions: {len(getattr(self, 'filtered_transactions', []))}")
            debug_info.append(f"   Data explicitly loaded: {getattr(self, '_data_explicitly_loaded', False)}")

            # Cache status
            debug_info.append(f"\n💾 Cache Status:")
            debug_info.append(f"   Data preparator cache loaded: {getattr(self.training_manager.data_preparator, '_cache_loaded', False)}")
            debug_info.append(f"   Training manager has unlabeled cache: {hasattr(self.training_manager, '_unlabeled_cache')}")
            debug_info.append(f"   Training manager has filter cache: {hasattr(self.training_manager, '_filter_cache')}")

            text_edit.setPlainText("\n".join(debug_info))
            layout.addWidget(text_edit)

            # Close button
            close_btn = QPushButton("Close")
            close_btn.clicked.connect(dialog.accept)
            layout.addWidget(close_btn)

            dialog.exec()

        except Exception as e:
            self.logger.error(f"Error showing debug info: {str(e)}")
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "Debug Error", f"Failed to show debug info: {str(e)}")

    def show_data_file_sources(self):
        """Show exactly which files contain the data being loaded"""
        try:
            from PySide6.QtWidgets import QDialog, QVBoxLayout, QTextEdit, QPushButton
            import os

            dialog = QDialog(self)
            dialog.setWindowTitle("Data File Sources Analysis")
            dialog.setModal(True)
            dialog.resize(700, 600)

            layout = QVBoxLayout(dialog)

            # Text area for file analysis
            text_edit = QTextEdit()
            text_edit.setReadOnly(True)

            # Analyze all data files
            analysis = []
            analysis.append("=== DATA FILE SOURCES ANALYSIS ===\n")

            # Check training data files
            training_data_dir = Path("bank_analyzer_config/ml_data")
            analysis.append(f"📁 Training Data Directory: {training_data_dir}")
            analysis.append(f"   Exists: {training_data_dir.exists()}")

            if training_data_dir.exists():
                for file_name in ["unique_transactions.csv", "labeling_history.csv", "training_sessions.json"]:
                    file_path = training_data_dir / file_name
                    if file_path.exists():
                        try:
                            file_size = os.path.getsize(file_path)
                            modified_time = os.path.getmtime(file_path)
                            from datetime import datetime
                            mod_date = datetime.fromtimestamp(modified_time).strftime('%Y-%m-%d %H:%M:%S')

                            analysis.append(f"\n📄 {file_name}:")
                            analysis.append(f"   Size: {file_size:,} bytes")
                            analysis.append(f"   Modified: {mod_date}")

                            # For CSV files, count lines
                            if file_name.endswith('.csv'):
                                try:
                                    with open(file_path, 'r', encoding='utf-8') as f:
                                        lines = sum(1 for line in f)
                                    analysis.append(f"   Lines: {lines:,}")

                                    # Show first few lines
                                    with open(file_path, 'r', encoding='utf-8') as f:
                                        first_lines = [f.readline().strip() for _ in range(3)]
                                    analysis.append(f"   First lines:")
                                    for i, line in enumerate(first_lines):
                                        if line:
                                            analysis.append(f"     {i+1}: {line[:80]}...")

                                except Exception as e:
                                    analysis.append(f"   Error reading: {str(e)}")

                        except Exception as e:
                            analysis.append(f"   Error accessing: {str(e)}")
                    else:
                        analysis.append(f"\n❌ {file_name}: NOT FOUND")

            # Check session files
            analysis.append(f"\n📁 Session Data Directory: bank_analyzer_config/ml_labeling_sessions")
            sessions_dir = Path("bank_analyzer_config/ml_labeling_sessions")

            if sessions_dir.exists():
                try:
                    sessions = self.transaction_data_manager.get_all_sessions()
                    analysis.append(f"   Sessions found: {len(sessions)}")

                    if sessions:
                        latest_session = max(sessions.values(), key=lambda s: s.updated_at)
                        analysis.append(f"\n🔥 LATEST SESSION: {latest_session.session_id}")
                        analysis.append(f"   Transaction count: {latest_session.transaction_count}")
                        analysis.append(f"   Updated: {latest_session.updated_at}")
                        analysis.append(f"   Raw file: {latest_session.raw_transactions_file}")

                        # Check if raw file exists and count transactions
                        raw_file = Path(latest_session.raw_transactions_file)
                        if raw_file.exists():
                            try:
                                raw_transactions, _ = self.transaction_data_manager.get_session_transactions(latest_session.session_id)
                                analysis.append(f"   Actual raw transactions loaded: {len(raw_transactions)}")

                                if raw_transactions:
                                    analysis.append(f"   Sample descriptions:")
                                    for i, txn in enumerate(raw_transactions[:3]):
                                        analysis.append(f"     {i+1}: {txn.description[:60]}...")

                            except Exception as e:
                                analysis.append(f"   Error loading raw transactions: {str(e)}")
                        else:
                            analysis.append(f"   ❌ Raw file not found!")

                except Exception as e:
                    analysis.append(f"   Error checking sessions: {str(e)}")
            else:
                analysis.append(f"   ❌ Sessions directory not found!")

            # Show what's currently loaded in UI
            analysis.append(f"\n🖥️ CURRENTLY LOADED IN UI:")
            analysis.append(f"   Unlabeled transactions: {len(getattr(self, 'unlabeled_transactions', []))}")
            analysis.append(f"   Filtered transactions: {len(getattr(self, 'filtered_transactions', []))}")

            if hasattr(self, 'unlabeled_transactions') and self.unlabeled_transactions:
                analysis.append(f"   Sample loaded transactions:")
                for i, txn in enumerate(self.unlabeled_transactions[:3]):
                    analysis.append(f"     {i+1}: {txn.description[:60]}... (freq: {txn.frequency})")

            # Conclusion
            analysis.append(f"\n🎯 CONCLUSION:")
            analysis.append(f"The 402 transactions are likely coming from:")
            analysis.append(f"• unique_transactions.csv in the training data directory")
            analysis.append(f"• This file contains old cached training data")
            analysis.append(f"• Use the NUCLEAR option to delete this file and load only session data")

            text_edit.setPlainText("\n".join(analysis))
            layout.addWidget(text_edit)

            # Close button
            close_btn = QPushButton("Close")
            close_btn.clicked.connect(dialog.accept)
            layout.addWidget(close_btn)

            dialog.exec()

        except Exception as e:
            self.logger.error(f"Error showing file sources: {str(e)}")
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "File Sources Error", f"Failed to show file sources: {str(e)}")

    def force_clear_ui_data(self):
        """Force clear all UI data and persistent data sources"""
        try:
            self.logger.info("Starting comprehensive force clear of all data...")

            # Clear transaction lists
            unlabeled_count = len(getattr(self, 'unlabeled_transactions', []))
            filtered_count = len(getattr(self, 'filtered_transactions', []))
            self.logger.info(f"Clearing {unlabeled_count} unlabeled and {filtered_count} filtered transactions")

            self.unlabeled_transactions = []
            self.filtered_transactions = []
            self.current_index = 0

            # Prevent auto-reload to avoid data coming back
            self._prevent_auto_reload = True
            self._save_auto_reload_state()
            self.logger.info("Auto-reload disabled to prevent data restoration")

            # Clear current session data
            if hasattr(self, 'current_session_id') and self.current_session_id:
                self.logger.info(f"Clearing current session: {self.current_session_id}")
                self.current_session_id = None

            # Clear transaction data manager session completely
            if hasattr(self, 'transaction_data_manager') and self.transaction_data_manager:
                self.logger.info("Clearing transaction data manager session")
                try:
                    self.transaction_data_manager.clear_current_session(auto_save=False)
                except Exception as e:
                    self.logger.warning(f"Could not clear transaction data manager session: {e}")

                # Ensure current session is completely cleared
                self.transaction_data_manager.current_session = None
                # Force save the cleared state
                try:
                    self.transaction_data_manager._save_active_session()
                    self.logger.info("Transaction data manager session completely cleared")
                except Exception as e:
                    self.logger.warning(f"Could not save cleared session state: {e}")

            # Clear training manager cached data
            if hasattr(self, 'training_manager') and self.training_manager:
                self.logger.info("Clearing training manager cached data")
                try:
                    # Clear any cached transactions in the training manager
                    if hasattr(self.training_manager, '_cached_unlabeled_transactions'):
                        self.training_manager._cached_unlabeled_transactions = []
                    if hasattr(self.training_manager, '_transaction_cache'):
                        self.training_manager._transaction_cache.clear()
                    if hasattr(self.training_manager, 'current_session'):
                        self.training_manager.current_session = None
                except Exception as e:
                    self.logger.warning(f"Could not clear training manager cache: {e}")

            # Clear transaction tables properly
            if hasattr(self, 'enhanced_table') and self.enhanced_table:
                table_rows = self.enhanced_table.rowCount()
                self.logger.info(f"Clearing enhanced table with {table_rows} rows")
                self.enhanced_table.setRowCount(0)
                self.enhanced_table.clearContents()
                if hasattr(self.enhanced_table, 'clear_all_selections'):
                    self.enhanced_table.clear_all_selections()
                # Clear internal transaction data
                self.enhanced_table.populate_table([])

            # Also clear the legacy transaction table
            if hasattr(self, 'transaction_table') and self.transaction_table:
                table_rows = self.transaction_table.rowCount()
                self.logger.info(f"Clearing transaction table with {table_rows} rows")
                self.transaction_table.setRowCount(0)
                self.transaction_table.clearContents()

            # Clear labeling widget properly
            if hasattr(self, 'labeling_widget') and self.labeling_widget:
                self.logger.info("Clearing labeling widget display")
                self.labeling_widget.clear_transaction_display()

            # Reset filter panel if it exists
            if hasattr(self, 'filter_panel') and self.filter_panel:
                self.logger.info("Resetting filter panel")
                self.filter_panel.reset_filters()
                self.current_filter = None

            # Clear progress and stats displays
            if hasattr(self, 'progress_bar') and self.progress_bar:
                self.progress_bar.setValue(0)
            if hasattr(self, 'stats_label') and self.stats_label:
                self.stats_label.setText("No data loaded")

            # Disable navigation buttons
            if hasattr(self, 'prev_button') and self.prev_button:
                self.prev_button.setEnabled(False)
            if hasattr(self, 'next_button') and self.next_button:
                self.next_button.setEnabled(False)

            # Clear any cached data
            if hasattr(self, '_cache'):
                self._cache.clear()

            # Set flag to prevent automatic reloading
            self._prevent_auto_reload = True

            # Force refresh UI components (but don't reload data)
            self.logger.info("Refreshing UI components...")
            self.populate_transaction_table()  # This will show empty table since transactions are cleared
            self.update_session_status()

            # Clear stats manually instead of calling update_stats which might reload data
            if hasattr(self, 'progress_bar') and self.progress_bar:
                self.progress_bar.setValue(0)
            if hasattr(self, 'stats_label') and self.stats_label:
                self.stats_label.setText("No data loaded - cleared successfully")

            self.repaint()
            QApplication.processEvents()

            # Verify clearing worked
            final_unlabeled = len(getattr(self, 'unlabeled_transactions', []))
            final_filtered = len(getattr(self, 'filtered_transactions', []))
            final_table_rows = self.enhanced_table.rowCount() if hasattr(self, 'enhanced_table') and self.enhanced_table else 0

            self.logger.info(f"Force clear completed - Unlabeled: {final_unlabeled}, Filtered: {final_filtered}, Table rows: {final_table_rows}")
            return True

        except Exception as e:
            self.logger.error(f"Error force clearing UI data: {e}")
            return False

    def test_force_clear(self):
        """Test method to directly call force clear"""
        if self.force_clear_ui_data():
            QMessageBox.information(
                self, "Force Clear Test",
                "✅ Force clear completed successfully!\n\n"
                "All data has been cleared from UI and persistent storage.\n"
                "Auto-reload has been disabled.\n\n"
                "To load new data, use the Load Session or import new transactions."
            )
        else:
            QMessageBox.warning(
                self, "Force Clear Test",
                "⚠️ Force clear encountered errors.\n\n"
                "Check the console for details."
            )

    def enable_auto_reload(self):
        """Re-enable auto-reload after clearing (call this when loading new data)"""
        if hasattr(self, '_prevent_auto_reload'):
            self._prevent_auto_reload = False
            self._save_auto_reload_state()
            self.logger.info("Auto-reload re-enabled")

    def _save_auto_reload_state(self):
        """Save auto-reload prevention state to file"""
        try:
            import json
            from pathlib import Path

            config_dir = Path("bank_analyzer_config")
            config_dir.mkdir(exist_ok=True)

            state_file = config_dir / "auto_reload_state.json"
            state_data = {
                "prevent_auto_reload": getattr(self, '_prevent_auto_reload', False),
                "updated_at": datetime.now().isoformat()
            }

            with open(state_file, 'w', encoding='utf-8') as f:
                json.dump(state_data, f, indent=2)

            self.logger.debug(f"Auto-reload state saved: {state_data['prevent_auto_reload']}")

        except Exception as e:
            self.logger.warning(f"Could not save auto-reload state: {e}")

    def _load_auto_reload_state(self):
        """Load auto-reload prevention state from file"""
        try:
            import json
            from pathlib import Path

            state_file = Path("bank_analyzer_config") / "auto_reload_state.json"
            if not state_file.exists():
                return False

            with open(state_file, 'r', encoding='utf-8') as f:
                state_data = json.load(f)

            prevent_auto_reload = state_data.get("prevent_auto_reload", False)
            self.logger.debug(f"Auto-reload state loaded: {prevent_auto_reload}")

            return prevent_auto_reload

        except Exception as e:
            self.logger.warning(f"Could not load auto-reload state: {e}")
            return False

    def enable_loading_and_reload(self):
        """Re-enable loading and reload transaction data"""
        try:
            # Re-enable auto-reload
            self.enable_auto_reload()

            # Reload transaction data
            self.logger.info("Re-enabling loading and reloading transaction data...")
            self.load_transaction_batch()
            self.update_stats()

            QMessageBox.information(
                self, "Loading Re-enabled",
                "✅ Data loading has been re-enabled and transactions reloaded.\n\n"
                "The interface is now ready for normal operation."
            )

        except Exception as e:
            self.logger.error(f"Error re-enabling loading: {e}")
            QMessageBox.warning(
                self, "Error",
                f"⚠️ Error re-enabling loading:\n{e}"
            )

    def handle_clear_data_request(self):
        """Handle clear data request with fallback options"""
        try:
            # First try the regular clear method
            self.clear_current_data()
        except Exception as e:
            self.logger.error(f"Regular clear failed: {e}")

            # Offer force clear as fallback
            msg = QMessageBox()
            msg.setIcon(QMessageBox.Warning)
            msg.setWindowTitle("Clear Data Failed")
            msg.setTextFormat(Qt.RichText)
            msg.setText("""
            <h3>⚠️ Regular Clear Failed</h3>

            <p>The regular clear operation encountered an issue.</p>
            <p>Would you like to force clear the interface data?</p>

            <p><b>Force Clear will:</b></p>
            <ul>
            <li>🗑️ Clear all visible transaction data</li>
            <li>🧹 Reset the interface to empty state</li>
            <li>⚠️ Skip session backup (if session is corrupted)</li>
            </ul>
            """)
            msg.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
            msg.setDefaultButton(QMessageBox.Yes)

            yes_button = msg.button(QMessageBox.Yes)
            yes_button.setText("🔧 Force Clear")
            no_button = msg.button(QMessageBox.No)
            no_button.setText("❌ Cancel")

            try:
                reply = msg.exec()
            except KeyboardInterrupt:
                # User interrupted the force clear confirmation dialog - treat as cancellation
                self.logger.info("Force clear confirmation dialog interrupted by user")
                return

            if reply == QMessageBox.Yes:
                if self.force_clear_ui_data():
                    QMessageBox.information(
                        self, "Force Clear Successful",
                        "✅ Interface data has been cleared.\n\n"
                        "You can now load new transaction data."
                    )
                else:
                    QMessageBox.critical(
                        self, "Force Clear Failed",
                        "❌ Unable to clear interface data.\n\n"
                        "Please restart the application."
                    )

    def clear_current_data(self):
        """Clear current data with automatic backup"""
        try:
            # Clean up any None values in transaction lists first
            if hasattr(self, 'unlabeled_transactions') and self.unlabeled_transactions:
                self.unlabeled_transactions = [txn for txn in self.unlabeled_transactions if txn is not None]

            if hasattr(self, 'filtered_transactions') and self.filtered_transactions:
                self.filtered_transactions = [txn for txn in self.filtered_transactions if txn is not None]

            # Check if there's any data to clear
            has_unlabeled = hasattr(self, 'unlabeled_transactions') and self.unlabeled_transactions
            has_filtered = hasattr(self, 'filtered_transactions') and self.filtered_transactions
            has_session = self.transaction_data_manager.current_session is not None

            # Also check if there's data in the UI tables
            has_table_data = False
            if hasattr(self, 'enhanced_table') and self.enhanced_table:
                has_table_data = self.enhanced_table.rowCount() > 0
            elif hasattr(self, 'transaction_table') and self.transaction_table:
                has_table_data = self.transaction_table.rowCount() > 0

            if not (has_unlabeled or has_filtered or has_session or has_table_data):
                QMessageBox.information(
                    self, "No Data",
                    "No transaction data to clear.\n\n"
                    "The interface is already empty."
                )
                return

            # Count total data for user information
            unlabeled_count = len(self.unlabeled_transactions) if has_unlabeled else 0
            filtered_count = len(self.filtered_transactions) if has_filtered else 0
            total_count = max(unlabeled_count, filtered_count)

            # Enhanced confirmation dialog with detailed information
            msg = QMessageBox()
            msg.setIcon(QMessageBox.Question)
            msg.setWindowTitle("Clear All Data with Backup")
            msg.setTextFormat(Qt.RichText)

            msg_text = f"""
            <h3>🗑️ Clear All Transaction Data</h3>

            <p><b>This will permanently clear:</b></p>
            <ul>
            <li>📊 {total_count} transaction records from the interface</li>
            <li>🏷️ All current labeling work and progress</li>
            <li>🔍 Applied filters and selections</li>
            <li>📁 Current active session data</li>
            </ul>

            <p><b>🛡️ Safety Measures:</b></p>
            <ul>
            <li>✅ Automatic backup will be created before clearing</li>
            <li>✅ Current session will be archived (not deleted)</li>
            <li>✅ All labeling work will be preserved in backup</li>
            <li>✅ You can restore this data later from session management</li>
            </ul>

            <p><b>⚠️ Are you sure you want to continue?</b></p>
            """

            msg.setText(msg_text)
            msg.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
            msg.setDefaultButton(QMessageBox.No)

            # Custom button text
            yes_button = msg.button(QMessageBox.Yes)
            yes_button.setText("🗑️ Clear with Backup")
            no_button = msg.button(QMessageBox.No)
            no_button.setText("❌ Cancel")

            try:
                reply = msg.exec()
            except KeyboardInterrupt:
                # User interrupted the confirmation dialog - treat as cancellation
                self.logger.info("Clear data confirmation dialog interrupted by user")
                return

            if reply != QMessageBox.Yes:
                return

            # Show progress during backup and clearing
            progress_msg = QMessageBox()
            progress_msg.setIcon(QMessageBox.Information)
            progress_msg.setWindowTitle("Clearing Data...")
            progress_msg.setText("Creating backup and clearing data...\nPlease wait...")
            progress_msg.setStandardButtons(QMessageBox.NoButton)
            progress_msg.show()

            try:
                backup_id = None

                # Create backup before clearing if there's an active session
                if self.transaction_data_manager.current_session:
                    progress_msg.setText("📦 Creating backup of current session...")
                    backup_id = self.transaction_data_manager.backup_current_session(
                        f"Pre-clear backup - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                    )
                    self.logger.info(f"Created backup before clearing: {backup_id}")

                # Clear current session if one exists
                success = True
                if self.transaction_data_manager.current_session:
                    progress_msg.setText("🗑️ Clearing session data...")
                    success = self.transaction_data_manager.clear_current_session(
                        create_backup=True, archive_session=True
                    )
                    # Double-check that session is completely cleared
                    if self.transaction_data_manager.current_session is None:
                        self.logger.info("✅ Session successfully cleared from transaction data manager")
                    else:
                        self.logger.warning("⚠️ Session may not be completely cleared from transaction data manager")
                else:
                    progress_msg.setText("🗑️ Clearing interface data...")
                    self.logger.info("No active session to clear, proceeding with UI cleanup")

                # Always clear UI data regardless of session status
                if True:  # Changed from 'if success:' to always clear UI
                    # Clear UI data
                    progress_msg.setText("🧹 Clearing interface data...")
                    self.unlabeled_transactions = []
                    self.filtered_transactions = []
                    self.current_index = 0

                    # Prevent auto-reload to avoid data coming back
                    self._prevent_auto_reload = True
                    self._save_auto_reload_state()
                    self.logger.info("Auto-reload disabled to prevent data restoration")

                    # Clear transaction tables properly
                    if hasattr(self, 'enhanced_table') and self.enhanced_table:
                        # First clear the display
                        self.enhanced_table.setRowCount(0)
                        self.enhanced_table.clearContents()
                        self.enhanced_table.clear_all_selections()
                        # Then clear the internal transaction data and repopulate with empty list
                        self.enhanced_table.populate_table([])

                    # Also clear the legacy transaction table
                    if hasattr(self, 'transaction_table') and self.transaction_table:
                        self.transaction_table.setRowCount(0)
                        self.transaction_table.clearContents()

                    # Clear labeling widget
                    self.labeling_widget.set_transaction(None, [])

                    # Reset filter panel if it exists
                    if hasattr(self, 'filter_panel') and self.filter_panel:
                        self.filter_panel.reset_filters()
                        self.current_filter = None  # Clear current filter

                    # Clear progress and stats displays
                    if hasattr(self, 'progress_bar') and self.progress_bar:
                        self.progress_bar.setValue(0)
                    if hasattr(self, 'stats_label') and self.stats_label:
                        self.stats_label.setText("No data loaded")

                    # Disable navigation buttons if they exist
                    if hasattr(self, 'prev_button') and self.prev_button:
                        self.prev_button.setEnabled(False)
                    if hasattr(self, 'next_button') and self.next_button:
                        self.next_button.setEnabled(False)

                    # Clear any cached data
                    if hasattr(self, '_cache'):
                        self._cache.clear()

                    # Clear current session ID to prevent reloading
                    if hasattr(self, 'current_session_id'):
                        self.current_session_id = None
                        self.logger.info("Cleared current session ID")

                    # Note: No need to call populate_transaction_table() here as we've already
                    # cleared both tables and populated the enhanced table with empty data

                    # Update session status
                    self.update_session_status()

                    # Force update statistics (clear cache first)
                    if hasattr(self, '_get_cached_stats'):
                        # Clear the cache for stats
                        if hasattr(self._get_cached_stats, 'cache_clear'):
                            self._get_cached_stats.cache_clear()
                    self.update_stats()

                    # Force UI refresh
                    self.repaint()
                    QApplication.processEvents()

                    # Verify the clear operation worked
                    unlabeled_count = len(self.unlabeled_transactions) if self.unlabeled_transactions else 0
                    filtered_count = len(self.filtered_transactions) if self.filtered_transactions else 0
                    enhanced_table_rows = self.enhanced_table.rowCount() if hasattr(self, 'enhanced_table') and self.enhanced_table else 0
                    enhanced_table_transactions = len(self.enhanced_table.transactions) if hasattr(self, 'enhanced_table') and self.enhanced_table and hasattr(self.enhanced_table, 'transactions') else 0
                    legacy_table_rows = self.transaction_table.rowCount() if hasattr(self, 'transaction_table') and self.transaction_table else 0

                    self.logger.info(f"Clear verification - Unlabeled: {unlabeled_count}, Filtered: {filtered_count}, Enhanced table rows: {enhanced_table_rows}, Enhanced table transactions: {enhanced_table_transactions}, Legacy table rows: {legacy_table_rows}")

                    # Ensure all counts are zero
                    if unlabeled_count == 0 and filtered_count == 0 and enhanced_table_rows == 0 and enhanced_table_transactions == 0 and legacy_table_rows == 0:
                        self.logger.info("✅ Clear operation verification passed - all data successfully cleared")
                    else:
                        self.logger.warning(f"⚠️ Clear operation verification failed - some data may still be present")

                    progress_msg.close()

                    # Success message with backup information
                    success_msg = QMessageBox()
                    success_msg.setIcon(QMessageBox.Information)
                    success_msg.setWindowTitle("Data Cleared Successfully")
                    success_msg.setTextFormat(Qt.RichText)

                    success_text = f"""
                    <h3>✅ Data Cleared Successfully</h3>

                    <p><b>Completed Actions:</b></p>
                    <ul>
                    <li>🗑️ Cleared {total_count} transaction records from interface</li>
                    <li>📦 Created backup: <code>{backup_id or 'Session backup'}</code></li>
                    <li>📁 Archived current session</li>
                    <li>🧹 Reset all filters and selections</li>
                    <li>🚫 Disabled auto-reload to prevent data restoration</li>
                    </ul>

                    <p><b>💡 Next Steps:</b></p>
                    <ul>
                    <li>Load new transaction data to start fresh labeling</li>
                    <li>Use "📁 Load Session" to restore previous work</li>
                    <li>Access backups through Session Management</li>
                    <li><b>Note:</b> Data will stay cleared even after app restart</li>
                    </ul>
                    """

                    success_msg.setText(success_text)
                    try:
                        success_msg.exec()
                    except KeyboardInterrupt:
                        # Handle keyboard interrupt gracefully - the operation was successful
                        self.logger.info("Success dialog interrupted by user, but operation completed successfully")
                        pass

                    self.statusBar().showMessage("Data cleared successfully. Backup created and session archived.", 5000)
                    self.logger.info(f"ML labeling data cleared successfully. Backup: {backup_id}")

                else:
                    progress_msg.close()
                    QMessageBox.warning(self, "Clear Failed", "Failed to clear transaction data.")

            except Exception as inner_e:
                progress_msg.close()
                raise inner_e

        except Exception as e:
            self.logger.error(f"Error clearing data: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "Clear Error", f"Failed to clear data:\n{str(e)}")

    def backup_current_data(self):
        """Create backup of current labeling work"""
        try:
            if not self.transaction_data_manager.current_session:
                QMessageBox.information(self, "No Session", "No active session to backup.")
                return

            # Get backup description from user
            description, ok = QInputDialog.getText(
                self, "Backup Data",
                "Enter a description for this backup:",
                text=f"Manual backup on {datetime.now().strftime('%Y-%m-%d %H:%M')}"
            )

            if not ok:
                return

            # Create backup
            backup_id = self.transaction_data_manager.backup_current_session(description)

            QMessageBox.information(
                self, "Backup Created",
                f"Data backup created successfully!\n\nBackup ID: {backup_id}"
            )

            self.logger.info(f"Created manual backup: {backup_id}")

        except Exception as e:
            self.logger.error(f"Error creating backup: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "Backup Error", f"Failed to create backup:\n{str(e)}")

    # Additional Menu Action Methods

    def show_detailed_stats(self):
        """Show detailed labeling statistics"""
        try:
            stats = self.training_manager.get_labeling_stats()

            # Get session information
            session_info = ""
            if self.transaction_data_manager.current_session:
                session = self.transaction_data_manager.current_session
                session_info = f"""
                <h4>📁 Current Session:</h4>
                <ul>
                <li><b>Session ID:</b> {session.session_id[-12:]}...</li>
                <li><b>Created:</b> {session.created_at.strftime('%Y-%m-%d %H:%M')}</li>
                <li><b>Status:</b> {session.status.value.title()}</li>
                <li><b>Transactions:</b> {session.transaction_count}</li>
                </ul>
                """

            # Create detailed statistics dialog
            msg = QMessageBox()
            msg.setIcon(QMessageBox.Information)
            msg.setWindowTitle("Detailed Labeling Statistics")
            msg.setTextFormat(Qt.RichText)

            stats_text = f"""
            <h3>📈 ML Labeling Statistics</h3>

            <h4>📊 Progress Overview:</h4>
            <ul>
            <li><b>Total Unique Transactions:</b> {stats.total_unique_transactions}</li>
            <li><b>Labeled Transactions:</b> {stats.labeled_transactions}</li>
            <li><b>Remaining to Label:</b> {stats.unlabeled_transactions}</li>
            <li><b>Progress:</b> {stats.labeling_progress:.1f}%</li>
            </ul>

            <h4>🏷️ Current Batch:</h4>
            <ul>
            <li><b>Loaded Transactions:</b> {len(self.unlabeled_transactions) if hasattr(self, 'unlabeled_transactions') else 0}</li>
            <li><b>Filtered Transactions:</b> {len(self.filtered_transactions) if hasattr(self, 'filtered_transactions') else 0}</li>
            <li><b>Current Position:</b> {self.current_index + 1 if hasattr(self, 'current_index') and self.unlabeled_transactions else 0}</li>
            </ul>

            {session_info}

            <h4>💡 Tips:</h4>
            <ul>
            <li>Use session management to save your progress</li>
            <li>Create backups before major changes</li>
            <li>Use filters to focus on specific transaction types</li>
            </ul>
            """

            msg.setText(stats_text)
            msg.exec()

        except Exception as e:
            self.logger.error(f"Error showing detailed stats: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "Statistics Error", f"Failed to load statistics:\n{str(e)}")

    def export_labeled_data(self):
        """Export labeled transactions to CSV"""
        try:
            from PySide6.QtWidgets import QFileDialog

            # Get labeled transactions
            labeled_transactions = self.training_manager.get_labeled_transactions()

            if not labeled_transactions:
                QMessageBox.information(
                    self, "No Data",
                    "No labeled transactions to export.\n\n"
                    "Label some transactions first to use this feature."
                )
                return

            # Get export file path
            file_path, _ = QFileDialog.getSaveFileName(
                self, "Export Labeled Transactions",
                f"labeled_transactions_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if not file_path:
                return

            # Export to CSV
            import csv
            with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)

                # Write header
                writer.writerow([
                    'Description', 'Category', 'Subcategory', 'Frequency',
                    'Amount Range', 'First Seen', 'Last Seen', 'Confidence'
                ])

                # Write data
                for txn in labeled_transactions:
                    writer.writerow([
                        txn.description,
                        txn.category,
                        txn.sub_category,
                        txn.frequency,
                        txn.amount_range,
                        txn.first_seen.strftime('%Y-%m-%d') if txn.first_seen else '',
                        txn.last_seen.strftime('%Y-%m-%d') if txn.last_seen else '',
                        getattr(txn, 'confidence', 1.0)
                    ])

            QMessageBox.information(
                self, "Export Complete",
                f"Successfully exported {len(labeled_transactions)} labeled transactions to:\n\n{file_path}"
            )

            self.logger.info(f"Exported {len(labeled_transactions)} labeled transactions to {file_path}")

        except Exception as e:
            self.logger.error(f"Error exporting labeled data: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "Export Error", f"Failed to export data:\n{str(e)}")

    def show_session_help(self):
        """Show session management help"""
        help_msg = QMessageBox()
        help_msg.setIcon(QMessageBox.Information)
        help_msg.setWindowTitle("Session Management Guide")
        help_msg.setTextFormat(Qt.RichText)

        help_text = """
        <h3>📖 Session Management Guide</h3>

        <h4>🎯 What are Sessions?</h4>
        <p>Sessions allow you to save and restore your labeling work. Each session contains:</p>
        <ul>
        <li>Transaction data you were working on</li>
        <li>Your labeling progress</li>
        <li>Applied filters and settings</li>
        </ul>

        <h4>💾 Saving Sessions:</h4>
        <ul>
        <li><b>Save Current Session:</b> Saves your current work with a custom name</li>
        <li><b>Create Backup:</b> Creates a backup without changing current session</li>
        <li><b>Auto-backup:</b> Automatic backups before clearing data</li>
        </ul>

        <h4>📂 Loading Sessions:</h4>
        <ul>
        <li><b>Load Session:</b> Opens session management dialog</li>
        <li><b>Smart Detection:</b> Automatically detects previous sessions on startup</li>
        <li><b>Session Browser:</b> View all sessions with details and filters</li>
        </ul>

        <h4>🗑️ Clearing Data:</h4>
        <ul>
        <li><b>Automatic Backup:</b> Always creates backup before clearing</li>
        <li><b>Session Archiving:</b> Moves sessions to archives instead of deleting</li>
        <li><b>Safe Operation:</b> No data is permanently lost</li>
        </ul>

        <h4>🔧 Best Practices:</h4>
        <ul>
        <li>Save sessions regularly during long labeling sessions</li>
        <li>Use descriptive names for sessions</li>
        <li>Create manual backups before major changes</li>
        <li>Use session management to switch between different datasets</li>
        </ul>
        """

        help_msg.setText(help_text)
        help_msg.exec()

    def show_labeling_help(self):
        """Show labeling interface help"""
        help_msg = QMessageBox()
        help_msg.setIcon(QMessageBox.Information)
        help_msg.setWindowTitle("Labeling Interface Guide")
        help_msg.setTextFormat(Qt.RichText)

        help_text = """
        <h3>🏷️ Labeling Interface Guide</h3>

        <h4>🎯 Interface Layout:</h4>
        <ul>
        <li><b>Left Panel:</b> Filters and search options</li>
        <li><b>Middle Panel:</b> Transaction list with batch operations</li>
        <li><b>Right Panel:</b> Individual labeling and session management</li>
        </ul>

        <h4>🤖 AI Assistance:</h4>
        <ul>
        <li><b>Smart Suggestions:</b> AI suggests categories based on description</li>
        <li><b>Similar Transactions:</b> Find and label similar transactions together</li>
        <li><b>Confidence Scores:</b> AI provides confidence levels for suggestions</li>
        </ul>

        <h4>⚡ Efficient Labeling:</h4>
        <ul>
        <li><b>Batch Labeling:</b> Select multiple transactions and label at once</li>
        <li><b>Quick Categories:</b> Use preset categories for common types</li>
        <li><b>Keyboard Shortcuts:</b> Navigate quickly through transactions</li>
        </ul>

        <h4>🔍 Filtering:</h4>
        <ul>
        <li><b>Text Search:</b> Find transactions by description</li>
        <li><b>Amount Range:</b> Filter by transaction amounts</li>
        <li><b>Date Range:</b> Focus on specific time periods</li>
        <li><b>Status Filter:</b> Show only labeled or unlabeled transactions</li>
        </ul>

        <h4>💡 Tips for Success:</h4>
        <ul>
        <li>Start with clear, obvious transactions</li>
        <li>Use AI suggestions as starting points</li>
        <li>Create custom categories for your specific needs</li>
        <li>Save your work regularly using sessions</li>
        </ul>
        """

        help_msg.setText(help_text)
        help_msg.exec()
