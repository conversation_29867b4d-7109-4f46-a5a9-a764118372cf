"""
Session Management Dialog
UI for managing transaction data sessions with backup, restore, and merge capabilities
"""

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QTableWidget, 
                              QTableWidgetItem, QPushButton, QLabel, QComboBox,
                              QTextEdit, QMessageBox, QHeaderView, QGroupBox,
                              QSplitter, QFrame, QProgressBar, QInputDialog,
                              QCheckBox, QFormLayout, QSpinBox, QDateEdit)
from PySide6.QtCore import Qt, QThread, Signal, QDate
from PySide6.QtGui import QFont, QColor, QBrush
from typing import List, Dict, Any, Optional
from datetime import datetime, date

from ..core.logger import get_logger
from ..core.transaction_data_manager import (TransactionDataManager, TransactionSession,
                                           SessionStatus, DataMergeStrategy, DataMergeOptions)
from ..models.transaction import RawTransaction, ProcessedTransaction


class SessionLoadWorker(QThread):
    """Worker thread for loading session data"""

    progress_updated = Signal(int, str)  # progress, status
    session_loaded = Signal(list, list)  # raw_transactions, processed_transactions
    error_occurred = Signal(str)  # error message

    def __init__(self, data_manager: TransactionDataManager, session_id: str):
        super().__init__()
        self.data_manager = data_manager
        self.session_id = session_id
        self.logger = get_logger(__name__)
    
    def run(self):
        """Load session data"""
        try:
            self.progress_updated.emit(25, "Loading session metadata...")

            # Load session
            success = self.data_manager.load_session(self.session_id)
            if not success:
                self.error_occurred.emit(f"Failed to load session: {self.session_id}")
                return

            self.progress_updated.emit(50, "Loading transaction data...")

            # Get transactions
            raw_transactions, processed_transactions = self.data_manager.get_session_transactions(self.session_id)

            self.progress_updated.emit(100, "Session loaded successfully")
            self.session_loaded.emit(raw_transactions, processed_transactions)

        except Exception as e:
            self.logger.error(f"Error in session load worker: {str(e)}", exc_info=True)
            self.error_occurred.emit(str(e))


class SessionManagementDialog(QDialog):
    """Dialog for managing transaction data sessions"""
    
    session_selected = Signal(str, list, list)  # session_id, raw_transactions, processed_transactions
    
    def __init__(self, data_manager: TransactionDataManager, parent=None):
        super().__init__(parent)
        self.data_manager = data_manager
        self.logger = get_logger(__name__)
        
        # Data
        self.sessions = []
        self.selected_session = None
        self.load_worker = None
        
        self.setup_ui()
        self.load_sessions()
    
    def setup_ui(self):
        """Setup the user interface"""
        self.setWindowTitle("Transaction Session Management")
        self.setMinimumSize(900, 600)
        
        layout = QVBoxLayout(self)
        
        # Header
        header_label = QLabel("Manage Transaction Data Sessions")
        header_label.setFont(QFont("Arial", 16, QFont.Bold))
        header_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(header_label)
        
        # Main content splitter
        splitter = QSplitter(Qt.Horizontal)
        layout.addWidget(splitter)
        
        # Sessions list
        self.setup_sessions_panel(splitter)
        
        # Session details and actions
        self.setup_details_panel(splitter)
        
        # Control buttons
        self.setup_control_buttons(layout)
        
        # Progress bar (initially hidden)
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
    
    def setup_sessions_panel(self, splitter):
        """Setup sessions list panel"""
        sessions_frame = QFrame()
        sessions_layout = QVBoxLayout(sessions_frame)
        
        # Sessions list header
        sessions_header = QLabel("Available Sessions")
        sessions_header.setFont(QFont("Arial", 12, QFont.Bold))
        sessions_layout.addWidget(sessions_header)
        
        # Filter controls
        filter_layout = QHBoxLayout()
        
        self.status_filter = QComboBox()
        self.status_filter.addItems(["All", "Active", "Completed", "Archived", "Cleared"])
        self.status_filter.currentTextChanged.connect(self.filter_sessions)
        filter_layout.addWidget(QLabel("Status:"))
        filter_layout.addWidget(self.status_filter)
        
        self.include_archived_cb = QCheckBox("Include Archived")
        self.include_archived_cb.stateChanged.connect(self.load_sessions)
        filter_layout.addWidget(self.include_archived_cb)
        
        filter_layout.addStretch()
        sessions_layout.addLayout(filter_layout)
        
        # Sessions table
        self.sessions_table = QTableWidget()
        self.sessions_table.setColumnCount(6)
        self.sessions_table.setHorizontalHeaderLabels([
            "Session ID", "Created", "Status", "Transactions", "Description", "Last Updated"
        ])
        
        # Configure table
        header = self.sessions_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # Session ID
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # Created
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # Status
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Transactions
        header.setSectionResizeMode(4, QHeaderView.Stretch)  # Description
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Last Updated
        
        self.sessions_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.sessions_table.setAlternatingRowColors(True)
        self.sessions_table.itemSelectionChanged.connect(self.on_session_selected)
        
        sessions_layout.addWidget(self.sessions_table)
        splitter.addWidget(sessions_frame)
    
    def setup_details_panel(self, splitter):
        """Setup session details and actions panel"""
        details_frame = QFrame()
        details_layout = QVBoxLayout(details_frame)
        
        # Session details
        details_group = QGroupBox("Session Details")
        details_form = QFormLayout(details_group)
        
        self.session_id_label = QLabel("No session selected")
        self.created_at_label = QLabel("-")
        self.status_label = QLabel("-")
        self.transaction_count_label = QLabel("-")
        self.description_text = QTextEdit()
        self.description_text.setMaximumHeight(80)
        self.description_text.setReadOnly(True)
        
        details_form.addRow("Session ID:", self.session_id_label)
        details_form.addRow("Created:", self.created_at_label)
        details_form.addRow("Status:", self.status_label)
        details_form.addRow("Transactions:", self.transaction_count_label)
        details_form.addRow("Description:", self.description_text)
        
        details_layout.addWidget(details_group)
        
        # Actions
        actions_group = QGroupBox("Actions")
        actions_layout = QVBoxLayout(actions_group)
        
        # Load session
        load_layout = QHBoxLayout()
        self.load_session_btn = QPushButton("📂 Load Session")
        self.load_session_btn.clicked.connect(self.load_selected_session)
        self.load_session_btn.setEnabled(False)
        load_layout.addWidget(self.load_session_btn)

        self.load_and_close_btn = QPushButton("📂 Load & Close")
        self.load_and_close_btn.clicked.connect(self.load_session_and_close)
        self.load_and_close_btn.setEnabled(False)
        load_layout.addWidget(self.load_and_close_btn)

        actions_layout.addLayout(load_layout)
        
        # Session management
        mgmt_layout = QHBoxLayout()

        self.rename_session_btn = QPushButton("✏️ Rename")
        self.rename_session_btn.clicked.connect(self.rename_selected_session)
        self.rename_session_btn.setEnabled(False)
        mgmt_layout.addWidget(self.rename_session_btn)

        self.backup_session_btn = QPushButton("💾 Backup")
        self.backup_session_btn.clicked.connect(self.backup_selected_session)
        self.backup_session_btn.setEnabled(False)
        mgmt_layout.addWidget(self.backup_session_btn)

        self.archive_session_btn = QPushButton("📦 Archive")
        self.archive_session_btn.clicked.connect(self.archive_selected_session)
        self.archive_session_btn.setEnabled(False)
        mgmt_layout.addWidget(self.archive_session_btn)

        self.delete_session_btn = QPushButton("🗑️ Delete")
        self.delete_session_btn.clicked.connect(self.delete_selected_session)
        self.delete_session_btn.setEnabled(False)
        self.delete_session_btn.setStyleSheet("background-color: #f44336; color: white;")
        mgmt_layout.addWidget(self.delete_session_btn)
        
        actions_layout.addLayout(mgmt_layout)
        
        # Merge options (for when loading with existing data)
        merge_group = QGroupBox("Data Merge Options")
        merge_layout = QFormLayout(merge_group)
        
        self.merge_strategy_combo = QComboBox()
        self.merge_strategy_combo.addItems([
            "Replace Current Data",
            "Keep Current Data", 
            "Merge All Data",
            "Archive Current Data"
        ])
        merge_layout.addRow("Strategy:", self.merge_strategy_combo)
        
        self.preserve_categorization_cb = QCheckBox("Preserve Categorization")
        self.preserve_categorization_cb.setChecked(True)
        merge_layout.addRow("", self.preserve_categorization_cb)
        
        self.create_backup_cb = QCheckBox("Create Backup")
        self.create_backup_cb.setChecked(True)
        merge_layout.addRow("", self.create_backup_cb)
        
        actions_layout.addWidget(merge_group)
        
        details_layout.addWidget(actions_group)
        details_layout.addStretch()
        
        splitter.addWidget(details_frame)
    
    def setup_control_buttons(self, layout):
        """Setup control buttons"""
        button_layout = QHBoxLayout()
        
        self.refresh_btn = QPushButton("🔄 Refresh")
        self.refresh_btn.clicked.connect(self.load_sessions)
        button_layout.addWidget(self.refresh_btn)
        
        self.cleanup_btn = QPushButton("🧹 Cleanup Old Sessions")
        self.cleanup_btn.clicked.connect(self.cleanup_old_sessions)
        button_layout.addWidget(self.cleanup_btn)
        
        button_layout.addStretch()
        
        self.close_btn = QPushButton("Close")
        self.close_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.close_btn)
        
        layout.addLayout(button_layout)
    
    def load_sessions(self):
        """Load available sessions"""
        try:
            include_archived = self.include_archived_cb.isChecked()
            self.sessions = self.data_manager.get_available_sessions(include_archived)
            self.populate_sessions_table()
            
            self.logger.info(f"Loaded {len(self.sessions)} sessions")
            
        except Exception as e:
            self.logger.error(f"Error loading sessions: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "Error", f"Failed to load sessions:\n{str(e)}")
    
    def populate_sessions_table(self):
        """Populate the sessions table"""
        try:
            # Filter sessions based on status filter
            filtered_sessions = self.get_filtered_sessions()
            
            self.sessions_table.setRowCount(len(filtered_sessions))
            
            for row, session in enumerate(filtered_sessions):
                # Session ID (truncated)
                session_id_item = QTableWidgetItem(session.session_id[-12:] + "...")
                session_id_item.setToolTip(session.session_id)
                self.sessions_table.setItem(row, 0, session_id_item)
                
                # Created date
                created_item = QTableWidgetItem(session.created_at.strftime("%Y-%m-%d %H:%M"))
                self.sessions_table.setItem(row, 1, created_item)
                
                # Status
                status_item = QTableWidgetItem(session.status.value.title())
                # Color code based on status
                if session.status == SessionStatus.ACTIVE:
                    status_item.setBackground(QBrush(QColor(200, 255, 200)))  # Light green
                elif session.status == SessionStatus.COMPLETED:
                    status_item.setBackground(QBrush(QColor(200, 200, 255)))  # Light blue
                elif session.status == SessionStatus.ARCHIVED:
                    status_item.setBackground(QBrush(QColor(255, 255, 200)))  # Light yellow
                elif session.status == SessionStatus.CLEARED:
                    status_item.setBackground(QBrush(QColor(255, 200, 200)))  # Light red
                self.sessions_table.setItem(row, 2, status_item)
                
                # Transaction count
                count_item = QTableWidgetItem(str(session.transaction_count))
                count_item.setTextAlignment(Qt.AlignCenter)
                self.sessions_table.setItem(row, 3, count_item)
                
                # Description (truncated)
                desc_text = session.description[:50] + "..." if len(session.description) > 50 else session.description
                desc_item = QTableWidgetItem(desc_text)
                desc_item.setToolTip(session.description)
                self.sessions_table.setItem(row, 4, desc_item)
                
                # Last updated
                updated_item = QTableWidgetItem(session.updated_at.strftime("%Y-%m-%d %H:%M"))
                self.sessions_table.setItem(row, 5, updated_item)
            
        except Exception as e:
            self.logger.error(f"Error populating sessions table: {str(e)}", exc_info=True)
    
    def get_filtered_sessions(self) -> List[TransactionSession]:
        """Get sessions based on current filter"""
        filter_text = self.status_filter.currentText()
        
        if filter_text == "All":
            return self.sessions
        elif filter_text == "Active":
            return [s for s in self.sessions if s.status == SessionStatus.ACTIVE]
        elif filter_text == "Completed":
            return [s for s in self.sessions if s.status == SessionStatus.COMPLETED]
        elif filter_text == "Archived":
            return [s for s in self.sessions if s.status == SessionStatus.ARCHIVED]
        elif filter_text == "Cleared":
            return [s for s in self.sessions if s.status == SessionStatus.CLEARED]
        else:
            return self.sessions

    def filter_sessions(self):
        """Filter sessions based on selected filter"""
        self.populate_sessions_table()

    def on_session_selected(self):
        """Handle session selection"""
        try:
            current_row = self.sessions_table.currentRow()
            if current_row < 0:
                self.selected_session = None
                self.update_session_details(None)
                return

            filtered_sessions = self.get_filtered_sessions()
            if current_row < len(filtered_sessions):
                self.selected_session = filtered_sessions[current_row]
                self.update_session_details(self.selected_session)

        except Exception as e:
            self.logger.error(f"Error handling session selection: {str(e)}", exc_info=True)

    def update_session_details(self, session: Optional[TransactionSession]):
        """Update session details panel"""
        if session is None:
            self.session_id_label.setText("No session selected")
            self.created_at_label.setText("-")
            self.status_label.setText("-")
            self.transaction_count_label.setText("-")
            self.description_text.setText("")

            # Disable action buttons
            self.load_session_btn.setEnabled(False)
            self.load_and_close_btn.setEnabled(False)
            self.rename_session_btn.setEnabled(False)
            self.backup_session_btn.setEnabled(False)
            self.archive_session_btn.setEnabled(False)
            self.delete_session_btn.setEnabled(False)
        else:
            self.session_id_label.setText(session.session_id)
            self.created_at_label.setText(session.created_at.strftime("%Y-%m-%d %H:%M:%S"))
            self.status_label.setText(session.status.value.title())
            self.transaction_count_label.setText(str(session.transaction_count))
            self.description_text.setText(session.description)

            # Enable action buttons based on session status
            can_load = session.status in [SessionStatus.ACTIVE, SessionStatus.COMPLETED, SessionStatus.ARCHIVED]
            can_backup = session.status in [SessionStatus.ACTIVE, SessionStatus.COMPLETED]
            can_archive = session.status == SessionStatus.ACTIVE
            can_rename = True  # Can rename any session
            can_delete = True

            self.load_session_btn.setEnabled(can_load)
            self.load_and_close_btn.setEnabled(can_load)
            self.rename_session_btn.setEnabled(can_rename)
            self.backup_session_btn.setEnabled(can_backup)
            self.archive_session_btn.setEnabled(can_archive)
            self.delete_session_btn.setEnabled(can_delete)

    def load_selected_session(self):
        """Load the selected session"""
        if not self.selected_session:
            QMessageBox.warning(self, "No Selection", "Please select a session to load.")
            return

        self.start_session_loading(self.selected_session.session_id, close_after=False)

    def load_session_and_close(self):
        """Load the selected session and close dialog"""
        if not self.selected_session:
            QMessageBox.warning(self, "No Selection", "Please select a session to load.")
            return

        self.start_session_loading(self.selected_session.session_id, close_after=True)

    def start_session_loading(self, session_id: str, close_after: bool = False):
        """Start loading session in worker thread"""
        try:
            # Show progress bar
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)

            # Disable buttons during loading
            self.load_session_btn.setEnabled(False)
            self.load_and_close_btn.setEnabled(False)

            # Create and start worker
            self.load_worker = SessionLoadWorker(self.data_manager, session_id)
            self.load_worker.progress_updated.connect(self.update_progress)
            self.load_worker.session_loaded.connect(
                lambda raw, processed: self.on_session_loaded(session_id, raw, processed, close_after)
            )
            self.load_worker.error_occurred.connect(self.on_session_load_error)
            self.load_worker.start()

        except Exception as e:
            self.logger.error(f"Error starting session loading: {str(e)}", exc_info=True)
            self.on_session_load_error(str(e))

    def update_progress(self, progress: int, status: str):
        """Update progress bar"""
        self.progress_bar.setValue(progress)
        self.progress_bar.setFormat(f"{status} - {progress}%")

    def on_session_loaded(self, session_id: str, raw_transactions: List[RawTransaction],
                         processed_transactions: List[ProcessedTransaction], close_after: bool):
        """Handle session loading completion"""
        try:
            # Hide progress bar
            self.progress_bar.setVisible(False)

            # Re-enable buttons
            self.update_session_details(self.selected_session)

            # Emit signal with loaded data
            self.session_selected.emit(session_id, raw_transactions, processed_transactions)

            # Show success message
            QMessageBox.information(
                self, "Session Loaded",
                f"Successfully loaded session: {session_id}\n\n"
                f"Raw transactions: {len(raw_transactions)}\n"
                f"Processed transactions: {len(processed_transactions)}"
            )

            # Close dialog if requested
            if close_after:
                self.accept()

        except Exception as e:
            self.logger.error(f"Error handling session load completion: {str(e)}", exc_info=True)

    def on_session_load_error(self, error_message: str):
        """Handle session loading error"""
        # Hide progress bar
        self.progress_bar.setVisible(False)

        # Re-enable buttons
        self.update_session_details(self.selected_session)

        # Show error message
        QMessageBox.critical(self, "Session Load Error", f"Failed to load session:\n{error_message}")

    def backup_selected_session(self):
        """Create backup of selected session"""
        try:
            if not self.selected_session:
                QMessageBox.warning(self, "No Selection", "Please select a session to backup.")
                return

            # Get backup description from user
            description, ok = QInputDialog.getText(
                self, "Backup Session",
                "Enter a description for this backup:",
                text=f"Backup of {self.selected_session.session_id} on {datetime.now().strftime('%Y-%m-%d')}"
            )

            if not ok:
                return

            # Create backup
            backup_id = self.data_manager.backup_current_session(description)

            QMessageBox.information(
                self, "Backup Created",
                f"Session backup created successfully!\n\nBackup ID: {backup_id}"
            )

            # Refresh sessions list
            self.load_sessions()

        except Exception as e:
            self.logger.error(f"Error creating session backup: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "Backup Error", f"Failed to create backup:\n{str(e)}")

    def archive_selected_session(self):
        """Archive the selected session"""
        try:
            if not self.selected_session:
                QMessageBox.warning(self, "No Selection", "Please select a session to archive.")
                return

            reply = QMessageBox.question(
                self, "Archive Session",
                f"Are you sure you want to archive session:\n{self.selected_session.session_id}?\n\n"
                f"This will move the session to the archives directory.",
                QMessageBox.Yes | QMessageBox.No, QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # Archive session
            success = self.data_manager._archive_session(self.selected_session.session_id)

            if success:
                QMessageBox.information(self, "Session Archived", "Session has been archived successfully.")
                self.load_sessions()
            else:
                QMessageBox.warning(self, "Archive Failed", "Failed to archive the session.")

        except Exception as e:
            self.logger.error(f"Error archiving session: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "Archive Error", f"Failed to archive session:\n{str(e)}")

    def delete_selected_session(self):
        """Delete the selected session"""
        try:
            if not self.selected_session:
                QMessageBox.warning(self, "No Selection", "Please select a session to delete.")
                return

            reply = QMessageBox.question(
                self, "Delete Session",
                f"Are you sure you want to delete session:\n{self.selected_session.session_id}?\n\n"
                f"This action cannot be undone!\n"
                f"All transaction data and files will be permanently deleted.",
                QMessageBox.Yes | QMessageBox.No, QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # Delete session
            success = self.data_manager.delete_session(self.selected_session.session_id, delete_files=True)

            if success:
                QMessageBox.information(self, "Session Deleted", "Session has been deleted successfully.")
                self.load_sessions()
                self.selected_session = None
                self.update_session_details(None)
            else:
                QMessageBox.warning(self, "Delete Failed", "Failed to delete the session.")

        except Exception as e:
            self.logger.error(f"Error deleting session: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "Delete Error", f"Failed to delete session:\n{str(e)}")

    def rename_selected_session(self):
        """Rename the selected session"""
        if not self.selected_session:
            QMessageBox.warning(self, "No Selection", "Please select a session to rename.")
            return

        try:
            # Get new name from user
            current_name = self.selected_session.description
            new_name, ok = QInputDialog.getText(
                self, "Rename Session",
                "Enter new session name:",
                text=current_name
            )

            if not ok or not new_name.strip():
                return

            new_name = new_name.strip()
            if new_name == current_name:
                return  # No change

            # Rename the session
            success = self.data_manager.rename_session(self.selected_session.session_id, new_name)

            if success:
                QMessageBox.information(self, "Rename Successful", f"Session renamed to: {new_name}")

                # Refresh sessions list
                self.load_sessions()

                # Try to reselect the renamed session
                for row in range(self.sessions_table.rowCount()):
                    session_id_item = self.sessions_table.item(row, 0)
                    if session_id_item and session_id_item.text() == self.selected_session.session_id:
                        self.sessions_table.selectRow(row)
                        break
            else:
                QMessageBox.warning(self, "Rename Failed", "Failed to rename the session.")

        except Exception as e:
            self.logger.error(f"Error renaming session: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "Rename Error", f"Failed to rename session:\n{str(e)}")

    def cleanup_old_sessions(self):
        """Cleanup old sessions"""
        try:
            # Get cleanup parameters from user
            days, ok = QInputDialog.getInt(
                self, "Cleanup Old Sessions",
                "Delete sessions older than how many days?",
                value=30, min=1, max=365
            )

            if not ok:
                return

            # Find old sessions
            cutoff_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            cutoff_date = cutoff_date.replace(day=cutoff_date.day - days)

            old_sessions = []
            for session in self.sessions:
                if session.created_at < cutoff_date and session.status in [SessionStatus.ARCHIVED, SessionStatus.CLEARED]:
                    old_sessions.append(session)

            if not old_sessions:
                QMessageBox.information(self, "Cleanup Complete", f"No sessions older than {days} days found.")
                return

            reply = QMessageBox.question(
                self, "Cleanup Confirmation",
                f"Found {len(old_sessions)} sessions older than {days} days.\n\n"
                f"Are you sure you want to delete them permanently?",
                QMessageBox.Yes | QMessageBox.No, QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # Delete old sessions
            deleted_count = 0
            for session in old_sessions:
                if self.data_manager.delete_session(session.session_id, delete_files=True):
                    deleted_count += 1

            QMessageBox.information(
                self, "Cleanup Complete",
                f"Successfully deleted {deleted_count} old sessions."
            )

            # Refresh sessions list
            self.load_sessions()

        except Exception as e:
            self.logger.error(f"Error during cleanup: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "Cleanup Error", f"Failed to cleanup sessions:\n{str(e)}")

    def closeEvent(self, event):
        """Handle dialog close event"""
        # Stop worker if running
        if self.load_worker and self.load_worker.isRunning():
            self.load_worker.quit()
            self.load_worker.wait()

        event.accept()
