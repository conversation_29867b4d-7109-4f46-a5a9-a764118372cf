"""
Simple Enhanced ML Window for AI-Assisted Labeling
Standalone window that doesn't inherit from complex parent classes
"""

from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QPushButton, QLabel, QComboBox, QLineEdit, QProgressBar, QTextEdit,
    QSplitter, QGroupBox, QFormLayout, QMessageBox, QHeaderView,
    QCheckBox, QSpinBox, QTabWidget, QFrame, QScrollArea, QDialog,
    QInputDialog, QToolButton, QMenu, QButtonGroup, QRadioButton
)
from PySide6.QtCore import Qt, QThread, QTimer, Signal, QSize
from PySide6.QtGui import QFont, QColor, QPalette, QIcon

import pandas as pd
from typing import List, Dict, Optional, Any
from datetime import datetime
import logging
import time

from ..ml.hybrid_ml_categorizer import HybridMLCategorizer, OperationMode, DataSource
from ..ml.training_data_manager import TrainingDataManager
from ..core.logger import get_logger


class SimpleEnhancedMLWindow(QMainWindow):
    """
    Simple Enhanced ML window for AI-assisted labeling
    Standalone implementation without complex inheritance
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger(__name__)
        
        # Initialize components
        self.hybrid_categorizer = HybridMLCategorizer()
        self._training_manager = None  # Lazy initialization to prevent automatic data loading
        self.ai_suggestions = {}
        
        # Setup UI
        self.setup_ui()
        self.setup_connections()

        # Update initial status (with delay to ensure UI is ready)
        QTimer.singleShot(100, self.update_budget_status)
        QTimer.singleShot(200, self.update_statistics)
        
        self.logger.info("Simple Enhanced ML window initialized")

    @property
    def training_manager(self):
        """Lazy initialization of training manager to prevent automatic data loading"""
        if self._training_manager is None:
            self._training_manager = TrainingDataManager()
        return self._training_manager

    def setup_ui(self):
        """Setup the user interface"""
        self.setWindowTitle("🤖 Manual Labeling with AI Assistant")
        self.setGeometry(100, 100, 1200, 800)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QHBoxLayout(central_widget)
        
        # Left panel - Controls and info
        left_panel = self.create_left_panel()
        main_layout.addWidget(left_panel, 1)
        
        # Right panel - Transaction processing
        right_panel = self.create_right_panel()
        main_layout.addWidget(right_panel, 2)
    
    def create_left_panel(self):
        """Create left control panel"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Title
        title = QLabel("🤖 Manual Labeling\nwith AI Assistant")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # Operation mode selection
        mode_group = QGroupBox("Operation Mode")
        mode_layout = QVBoxLayout(mode_group)
        
        self.mode_group = QButtonGroup()
        modes = [
            ("hybrid", "⚡ Hybrid", "Intelligently combine all approaches"),
            ("ai_first", "🚀 AI First", "Use AI first, fall back to local ML"),
            ("ml_first", "🤖 ML First", "Use local ML first, AI for uncertain cases"),
            ("manual_only", "✋ Manual Only", "Disable AI, use only local ML")
        ]
        
        for mode_value, mode_name, description in modes:
            radio = QRadioButton(mode_name)
            radio.setToolTip(description)
            radio.toggled.connect(lambda checked, mode=mode_value: self.on_mode_changed(mode, checked))
            self.mode_group.addButton(radio)
            mode_layout.addWidget(radio)
        
        # Set default to hybrid
        self.mode_group.buttons()[0].setChecked(True)
        layout.addWidget(mode_group)
        
        # Budget status
        budget_group = QGroupBox("Budget Status")
        budget_layout = QVBoxLayout(budget_group)
        
        self.budget_label = QLabel("Budget: Loading...")
        budget_layout.addWidget(self.budget_label)
        
        layout.addWidget(budget_group)
        
        # Statistics
        stats_group = QGroupBox("Session Statistics")
        stats_layout = QVBoxLayout(stats_group)
        
        self.stats_label = QLabel("No statistics available")
        stats_layout.addWidget(self.stats_label)
        
        layout.addWidget(stats_group)
        
        # AI Suggestion panel
        suggestion_group = QGroupBox("🤖 AI Suggestion")
        suggestion_layout = QVBoxLayout(suggestion_group)
        
        self.suggestion_text = QTextEdit()
        self.suggestion_text.setMaximumHeight(150)
        self.suggestion_text.setPlainText("No suggestion available")
        suggestion_layout.addWidget(self.suggestion_text)
        
        # Suggestion buttons
        button_layout = QHBoxLayout()
        
        self.accept_btn = QPushButton("✅ Accept")
        self.accept_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; }")
        self.accept_btn.setEnabled(False)
        button_layout.addWidget(self.accept_btn)
        
        self.reject_btn = QPushButton("❌ Reject")
        self.reject_btn.setStyleSheet("QPushButton { background-color: #f44336; color: white; }")
        self.reject_btn.setEnabled(False)
        button_layout.addWidget(self.reject_btn)
        
        suggestion_layout.addLayout(button_layout)
        layout.addWidget(suggestion_group)
        
        # Spacer
        layout.addStretch()
        
        return panel
    
    def create_right_panel(self):
        """Create right processing panel"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Title
        title = QLabel("Transaction Processing")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        layout.addWidget(title)
        
        # Test transaction input
        input_group = QGroupBox("Test Transaction")
        input_layout = QFormLayout(input_group)
        
        self.description_input = QLineEdit()
        self.description_input.setPlaceholderText("Enter transaction description...")
        input_layout.addRow("Description:", self.description_input)
        
        self.amount_input = QLineEdit()
        self.amount_input.setPlaceholderText("Enter amount (e.g., -45.67)")
        input_layout.addRow("Amount:", self.amount_input)
        
        layout.addWidget(input_group)
        
        # Action buttons
        button_layout = QHBoxLayout()
        
        self.get_suggestion_btn = QPushButton("🤖 Get AI Suggestion")
        self.get_suggestion_btn.clicked.connect(self.get_ai_suggestion)
        button_layout.addWidget(self.get_suggestion_btn)
        
        self.categorize_btn = QPushButton("🔄 Categorize")
        self.categorize_btn.clicked.connect(self.categorize_transaction)
        button_layout.addWidget(self.categorize_btn)
        
        layout.addLayout(button_layout)
        
        # Results display
        results_group = QGroupBox("Results")
        results_layout = QVBoxLayout(results_group)
        
        self.results_text = QTextEdit()
        self.results_text.setPlainText("No results yet. Enter a transaction and click 'Categorize' or 'Get AI Suggestion'.")
        results_layout.addWidget(self.results_text)
        
        layout.addWidget(results_group)
        
        return panel
    
    def setup_connections(self):
        """Setup signal connections"""
        self.accept_btn.clicked.connect(self.accept_suggestion)
        self.reject_btn.clicked.connect(self.reject_suggestion)
        
        # Update timer
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_statistics)
        self.update_timer.start(10000)  # Update every 10 seconds
    
    def on_mode_changed(self, mode: str, checked: bool):
        """Handle operation mode change"""
        if checked:
            try:
                operation_mode = OperationMode(mode)
                self.hybrid_categorizer.switch_operation_mode(operation_mode)
                self.logger.info(f"Switched to {mode} mode")
                
                # Update budget status if available
                if hasattr(self, 'budget_label'):
                    self.update_budget_status()
                
                # Show message
                mode_names = {
                    "ai_first": "🚀 AI First",
                    "ml_first": "🤖 ML First", 
                    "hybrid": "⚡ Hybrid",
                    "manual_only": "✋ Manual Only"
                }
                
                if hasattr(self, 'results_text'):
                    self.results_text.append(f"\n🔧 Switched to {mode_names.get(mode, mode)} mode")
                
            except Exception as e:
                self.logger.error(f"Error changing operation mode: {str(e)}")
                QMessageBox.warning(self, "Error", f"Failed to change operation mode: {str(e)}")
    
    def get_ai_suggestion(self):
        """Get AI suggestion for the entered transaction"""
        try:
            description = self.description_input.text().strip()
            amount_text = self.amount_input.text().strip()
            
            if not description:
                QMessageBox.warning(self, "Input Required", "Please enter a transaction description.")
                return
            
            if not amount_text:
                amount = -50.0  # Default amount
            else:
                try:
                    amount = float(amount_text)
                except ValueError:
                    QMessageBox.warning(self, "Invalid Amount", "Please enter a valid amount (e.g., -45.67).")
                    return
            
            # Create test transaction
            from ..models.transaction import RawTransaction
            test_transaction = RawTransaction(
                date="2024-01-01",
                description=description,
                amount=amount
            )
            
            # Get AI suggestions
            self.get_suggestion_btn.setEnabled(False)
            self.get_suggestion_btn.setText("🔄 Getting Suggestion...")
            
            suggestions = self.hybrid_categorizer.get_ai_suggestions_for_manual_labeling([test_transaction])
            
            if suggestions and description in suggestions:
                suggestion = suggestions[description]
                self.current_suggestion = suggestion
                
                suggestion_text = f"""
🤖 AI Suggestion:
• Category: {suggestion['category']}
• Subcategory: {suggestion['subcategory']}
• Confidence: {suggestion['confidence']:.1%}
• Source: {suggestion['source']}
• Notes: {suggestion['notes']}
• Recommendation: {suggestion['recommendation']}
                """
                
                self.suggestion_text.setPlainText(suggestion_text.strip())
                self.accept_btn.setEnabled(True)
                self.reject_btn.setEnabled(True)
                
                self.results_text.append(f"\n✅ Got AI suggestion for: {description}")
                
            else:
                self.suggestion_text.setPlainText("No AI suggestion available (likely due to budget constraints or filtering).")
                self.accept_btn.setEnabled(False)
                self.reject_btn.setEnabled(False)
                
                self.results_text.append(f"\n⚠️ No AI suggestion available for: {description}")
            
        except Exception as e:
            self.logger.error(f"Error getting AI suggestion: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to get AI suggestion: {str(e)}")
            self.suggestion_text.setPlainText(f"Error: {str(e)}")
        
        finally:
            self.get_suggestion_btn.setEnabled(True)
            self.get_suggestion_btn.setText("🤖 Get AI Suggestion")
            self.update_budget_status()
    
    def categorize_transaction(self):
        """Categorize the entered transaction using hybrid approach"""
        try:
            description = self.description_input.text().strip()
            amount_text = self.amount_input.text().strip()
            
            if not description:
                QMessageBox.warning(self, "Input Required", "Please enter a transaction description.")
                return
            
            if not amount_text:
                amount = -50.0  # Default amount
            else:
                try:
                    amount = float(amount_text)
                except ValueError:
                    QMessageBox.warning(self, "Invalid Amount", "Please enter a valid amount (e.g., -45.67).")
                    return
            
            # Create test transaction
            from ..models.transaction import RawTransaction
            test_transaction = RawTransaction(
                date="2024-01-01",
                description=description,
                amount=amount
            )
            
            # Categorize using hybrid approach
            self.categorize_btn.setEnabled(False)
            self.categorize_btn.setText("🔄 Categorizing...")
            
            processed, hybrid_pred = self.hybrid_categorizer.categorize_transaction(test_transaction)
            
            result_text = f"""
🔄 Categorization Result:
• Description: {description}
• Amount: ${abs(amount):.2f}
• Category: {processed.category}
• Subcategory: {processed.sub_category}
• Confidence: {processed.confidence_score:.2f}
• Source: {hybrid_pred.source}
• Data Source: {hybrid_pred.data_source.value}
• Should Add to Training: {hybrid_pred.should_add_to_training}
• Recommendation: {hybrid_pred.recommendation}
            """
            
            self.results_text.append(result_text.strip())
            
        except Exception as e:
            self.logger.error(f"Error categorizing transaction: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to categorize transaction: {str(e)}")
            self.results_text.append(f"\n❌ Error: {str(e)}")
        
        finally:
            self.categorize_btn.setEnabled(True)
            self.categorize_btn.setText("🔄 Categorize")
            self.update_budget_status()
            self.update_statistics()
    
    def accept_suggestion(self):
        """Accept the current AI suggestion"""
        if hasattr(self, 'current_suggestion'):
            self.results_text.append(f"\n✅ Accepted AI suggestion: {self.current_suggestion['category']}/{self.current_suggestion['subcategory']}")
            self.clear_suggestion()
    
    def reject_suggestion(self):
        """Reject the current AI suggestion"""
        if hasattr(self, 'current_suggestion'):
            self.results_text.append(f"\n❌ Rejected AI suggestion: {self.current_suggestion['category']}/{self.current_suggestion['subcategory']}")
            self.clear_suggestion()
    
    def clear_suggestion(self):
        """Clear the current suggestion"""
        self.suggestion_text.setPlainText("No suggestion available")
        self.accept_btn.setEnabled(False)
        self.reject_btn.setEnabled(False)
        if hasattr(self, 'current_suggestion'):
            delattr(self, 'current_suggestion')
    
    def update_budget_status(self):
        """Update budget status display"""
        if not hasattr(self, 'budget_label'):
            return

        try:
            budget_status = self.hybrid_categorizer._get_ai_budget_status()
            remaining = budget_status.get('total_remaining', 0)
            usage_pct = budget_status.get('total_usage_pct', 0)
            warning_level = budget_status.get('warning_level', 'unknown')

            if warning_level == 'safe':
                color = "green"
                icon = "✅"
            elif warning_level == 'warning':
                color = "orange"
                icon = "⚠️"
            elif warning_level == 'critical':
                color = "red"
                icon = "🚨"
            else:
                color = "gray"
                icon = "❓"

            budget_text = f"{icon} Budget: ${remaining:.4f} ({usage_pct:.1f}% used)"
            self.budget_label.setText(f'<span style="color: {color};">{budget_text}</span>')

        except Exception as e:
            self.budget_label.setText("💰 Budget: Status unavailable")
            self.logger.debug(f"Budget status update failed: {str(e)}")
    
    def update_statistics(self):
        """Update session statistics"""
        if not hasattr(self, 'stats_label'):
            return

        try:
            stats = self.hybrid_categorizer.stats

            stats_text = f"""
<b>Session Statistics:</b><br>
• Total Categorized: {stats.get('total_categorized', 0)}<br>
• ML Used: {stats.get('ml_used', 0)}<br>
• AI Used: {stats.get('ai_used', 0)}<br>
• Rules Used: {stats.get('rules_used', 0)}<br>
• Hybrid Used: {stats.get('hybrid_used', 0)}<br>
• Added to Training: {stats.get('added_to_training', 0)}<br>
• Uncertain Cases: {stats.get('uncertain_cases', 0)}<br>
            """

            self.stats_label.setText(stats_text.strip())

        except Exception as e:
            self.stats_label.setText("Statistics unavailable")
            self.logger.debug(f"Statistics update failed: {str(e)}")
    
    def closeEvent(self, event):
        """Handle window close event"""
        if hasattr(self, 'update_timer'):
            self.update_timer.stop()
        event.accept()
