"""
Transaction Categorization Dialog
Shows the results of three-way transaction categorization
"""

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                              QTableWidget, QTableWidgetItem, QPushButton,
                              QTabWidget, QWidget, QTextEdit, QGroupBox,
                              QHeaderView, QMessageBox, QSplitter, QCheckBox)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont, QColor
from typing import List, Dict, Any
from decimal import Decimal

from ..core.logger import get_logger
from ..ml.transaction_categorization_service import TransactionCategorizationResult
from ..ml.data_preparation import UniqueTransaction


class TransactionCategorizationDialog(QDialog):
    """Dialog to display three-way transaction categorization results"""
    
    proceed_with_selection = Signal(object)  # TransactionCategorizationResult with user selections
    
    def __init__(self, categorization_result: TransactionCategorizationResult, parent=None):
        super().__init__(parent)
        self.categorization_result = categorization_result
        self.logger = get_logger(__name__)
        
        self.setWindowTitle("Transaction Categorization Results")
        self.setModal(True)
        self.resize(1200, 800)
        
        # User selections
        self.include_already_labeled = False
        self.include_unlabeled = True
        self.include_new = True
        
        self.setup_ui()
        self.populate_data()
    
    def setup_ui(self):
        """Setup the user interface"""
        layout = QVBoxLayout(self)
        
        # Summary section
        summary_group = QGroupBox("Categorization Summary")
        summary_layout = QVBoxLayout(summary_group)
        
        # Summary label
        self.summary_label = QLabel()
        self.summary_label.setWordWrap(True)
        font = QFont()
        font.setPointSize(10)
        self.summary_label.setFont(font)
        summary_layout.addWidget(self.summary_label)
        
        layout.addWidget(summary_group)
        
        # Selection options
        selection_group = QGroupBox("Select Categories to Include in Training")
        selection_layout = QVBoxLayout(selection_group)
        
        self.already_labeled_cb = QCheckBox("Include Already Labeled transactions (for reference)")
        self.already_labeled_cb.setChecked(self.include_already_labeled)
        self.already_labeled_cb.setToolTip("Show already labeled transactions for reference (won't be retrained)")
        self.already_labeled_cb.toggled.connect(self.on_selection_changed)
        
        self.unlabeled_cb = QCheckBox("Include Unlabeled transactions (recommended)")
        self.unlabeled_cb.setChecked(self.include_unlabeled)
        self.unlabeled_cb.setToolTip("Include transactions that exist but haven't been labeled yet")
        self.unlabeled_cb.toggled.connect(self.on_selection_changed)
        
        self.new_cb = QCheckBox("Include New transactions (recommended)")
        self.new_cb.setChecked(self.include_new)
        self.new_cb.setToolTip("Include completely new transactions")
        self.new_cb.toggled.connect(self.on_selection_changed)
        
        selection_layout.addWidget(self.already_labeled_cb)
        selection_layout.addWidget(self.unlabeled_cb)
        selection_layout.addWidget(self.new_cb)
        
        layout.addWidget(selection_group)
        
        # Main content with tabs
        self.tab_widget = QTabWidget()
        
        # Already labeled tab
        self.already_labeled_tab = self.create_transactions_tab("Already Labeled")
        self.tab_widget.addTab(self.already_labeled_tab, 
                              f"Already Labeled ({self.categorization_result.total_already_labeled})")
        
        # Unlabeled tab
        self.unlabeled_tab = self.create_transactions_tab("Unlabeled")
        self.tab_widget.addTab(self.unlabeled_tab, 
                              f"Unlabeled ({self.categorization_result.total_unlabeled})")
        
        # New tab
        self.new_tab = self.create_transactions_tab("New")
        self.tab_widget.addTab(self.new_tab, 
                              f"New ({self.categorization_result.total_new})")
        
        # Details tab
        self.details_tab = self.create_details_tab()
        self.tab_widget.addTab(self.details_tab, "Details")
        
        layout.addWidget(self.tab_widget)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.proceed_btn = QPushButton("Proceed with Selected Categories")
        self.proceed_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        self.proceed_btn.clicked.connect(self.on_proceed)
        
        self.cancel_btn = QPushButton("Cancel")
        self.cancel_btn.clicked.connect(self.reject)
        
        button_layout.addWidget(self.proceed_btn)
        button_layout.addStretch()
        button_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(button_layout)
    
    def create_transactions_tab(self, title: str) -> QWidget:
        """Create a tab for displaying transactions"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Info label
        info_label = QLabel()
        info_label.setWordWrap(True)
        layout.addWidget(info_label)
        
        # Table
        table = QTableWidget()
        table.setColumnCount(6)
        table.setHorizontalHeaderLabels(["Description", "Hash ID", "Category", "Sub-category", "Frequency", "Status"])
        
        # Configure table
        header = table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # Description column
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # Hash ID
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # Category
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Sub-category
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Frequency
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Status
        
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectRows)
        
        layout.addWidget(table)
        
        # Store references
        if "Already Labeled" in title:
            self.already_labeled_info_label = info_label
            self.already_labeled_table = table
        elif "Unlabeled" in title:
            self.unlabeled_info_label = info_label
            self.unlabeled_table = table
        else:  # New
            self.new_info_label = info_label
            self.new_table = table
        
        return widget
    
    def create_details_tab(self) -> QWidget:
        """Create the details tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Details text
        self.details_text = QTextEdit()
        self.details_text.setReadOnly(True)
        self.details_text.setFont(QFont("Courier", 9))
        
        layout.addWidget(QLabel("Detailed Categorization Information:"))
        layout.addWidget(self.details_text)
        
        return widget
    
    def populate_data(self):
        """Populate the dialog with data"""
        # Summary
        summary_text = f"<b>Categorization Results:</b><br>"
        summary_text += f"<b>Already Labeled:</b> {self.categorization_result.total_already_labeled}<br>"
        summary_text += f"<b>Unlabeled:</b> {self.categorization_result.total_unlabeled}<br>"
        summary_text += f"<b>New:</b> {self.categorization_result.total_new}<br><br>"
        summary_text += f"<b>Summary:</b><br>{self.categorization_result.categorization_summary.replace(chr(10), '<br>')}"
        
        self.summary_label.setText(summary_text)
        
        # Already labeled transactions
        self.populate_transaction_table(
            self.already_labeled_table,
            self.categorization_result.already_labeled,
            self.categorization_result.already_labeled_details,
            self.already_labeled_info_label,
            "These transactions have been previously labeled and categorized."
        )
        
        # Unlabeled transactions
        self.populate_transaction_table(
            self.unlabeled_table,
            self.categorization_result.unlabeled,
            self.categorization_result.unlabeled_details,
            self.unlabeled_info_label,
            "These transactions exist in the system but haven't been labeled yet."
        )
        
        # New transactions
        self.populate_transaction_table(
            self.new_table,
            self.categorization_result.new,
            self.categorization_result.new_details,
            self.new_info_label,
            "These are completely new transactions not seen before."
        )
        
        # Details
        self.populate_details()
        
        # Update button state
        self.update_proceed_button()
    
    def populate_transaction_table(self, table: QTableWidget, transactions: List[UniqueTransaction],
                                 details: List[Dict], info_label: QLabel, info_text: str):
        """Populate a transaction table"""
        info_label.setText(f"{info_text} ({len(transactions)} transactions)")
        
        table.setRowCount(len(transactions))
        
        for row, (txn, detail) in enumerate(zip(transactions, details)):
            # Description
            desc_item = QTableWidgetItem(txn.description)
            table.setItem(row, 0, desc_item)
            
            # Hash ID
            hash_item = QTableWidgetItem(txn.hash_id)
            table.setItem(row, 1, hash_item)
            
            # Category
            category = detail.get('existing_category', txn.category if hasattr(txn, 'category') else '')
            category_item = QTableWidgetItem(category or 'Not labeled')
            if category:
                category_item.setBackground(QColor(200, 255, 200))  # Light green for labeled
            table.setItem(row, 2, category_item)
            
            # Sub-category
            sub_category = detail.get('existing_sub_category', txn.sub_category if hasattr(txn, 'sub_category') else '')
            sub_category_item = QTableWidgetItem(sub_category or 'Not labeled')
            if sub_category:
                sub_category_item.setBackground(QColor(200, 255, 200))  # Light green for labeled
            table.setItem(row, 3, sub_category_item)
            
            # Frequency
            frequency = detail.get('frequency', txn.frequency)
            frequency_item = QTableWidgetItem(str(frequency))
            table.setItem(row, 4, frequency_item)
            
            # Status
            reason = detail.get('reason', 'Unknown')
            status_item = QTableWidgetItem(reason)
            table.setItem(row, 5, status_item)
    
    def populate_details(self):
        """Populate the details tab"""
        details = []
        details.append("Transaction Categorization Details")
        details.append("=" * 50)
        details.append(f"Total transactions processed: {self.categorization_result.total_already_labeled + self.categorization_result.total_unlabeled + self.categorization_result.total_new}")
        details.append("")
        details.append(self.categorization_result.categorization_summary)
        details.append("")
        
        # Add sample details for each category
        if self.categorization_result.already_labeled_details:
            details.append("Sample Already Labeled Transactions:")
            details.append("-" * 40)
            for i, detail in enumerate(self.categorization_result.already_labeled_details[:3], 1):
                txn = detail['transaction']
                details.append(f"{i}. {txn.description}")
                details.append(f"   Category: {detail.get('existing_category', 'N/A')}")
                details.append(f"   Sub-category: {detail.get('existing_sub_category', 'N/A')}")
                details.append(f"   Labeled by: {detail.get('labeled_by', 'N/A')}")
                details.append("")
        
        if self.categorization_result.unlabeled_details:
            details.append("Sample Unlabeled Transactions:")
            details.append("-" * 40)
            for i, detail in enumerate(self.categorization_result.unlabeled_details[:3], 1):
                txn = detail['transaction']
                details.append(f"{i}. {txn.description}")
                details.append(f"   Frequency: {detail.get('frequency', 1)}")
                details.append(f"   First seen: {detail.get('first_seen', 'N/A')}")
                details.append("")
        
        if self.categorization_result.new_details:
            details.append("Sample New Transactions:")
            details.append("-" * 40)
            for i, detail in enumerate(self.categorization_result.new_details[:3], 1):
                txn = detail['transaction']
                details.append(f"{i}. {txn.description}")
                details.append(f"   Hash ID: {detail['hash_id']}")
                details.append("")
        
        self.details_text.setPlainText("\n".join(details))
    
    def on_selection_changed(self):
        """Handle selection checkbox changes"""
        self.include_already_labeled = self.already_labeled_cb.isChecked()
        self.include_unlabeled = self.unlabeled_cb.isChecked()
        self.include_new = self.new_cb.isChecked()
        
        self.update_proceed_button()
    
    def update_proceed_button(self):
        """Update the proceed button state"""
        has_selection = self.include_already_labeled or self.include_unlabeled or self.include_new
        self.proceed_btn.setEnabled(has_selection)
        
        if not has_selection:
            self.proceed_btn.setText("Select at least one category")
        else:
            selected_count = 0
            if self.include_already_labeled:
                selected_count += self.categorization_result.total_already_labeled
            if self.include_unlabeled:
                selected_count += self.categorization_result.total_unlabeled
            if self.include_new:
                selected_count += self.categorization_result.total_new
            
            self.proceed_btn.setText(f"Proceed with {selected_count} transactions")
    
    def on_proceed(self):
        """Handle proceed button click"""
        if not (self.include_already_labeled or self.include_unlabeled or self.include_new):
            QMessageBox.warning(self, "No Selection", "Please select at least one category to proceed.")
            return
        
        # Create filtered result based on user selection
        filtered_result = TransactionCategorizationResult(
            already_labeled=self.categorization_result.already_labeled if self.include_already_labeled else [],
            unlabeled=self.categorization_result.unlabeled if self.include_unlabeled else [],
            new=self.categorization_result.new if self.include_new else [],
            already_labeled_details=self.categorization_result.already_labeled_details if self.include_already_labeled else [],
            unlabeled_details=self.categorization_result.unlabeled_details if self.include_unlabeled else [],
            new_details=self.categorization_result.new_details if self.include_new else [],
            total_already_labeled=self.categorization_result.total_already_labeled if self.include_already_labeled else 0,
            total_unlabeled=self.categorization_result.total_unlabeled if self.include_unlabeled else 0,
            total_new=self.categorization_result.total_new if self.include_new else 0,
            categorization_summary=f"User selected: Already Labeled={self.include_already_labeled}, Unlabeled={self.include_unlabeled}, New={self.include_new}"
        )
        
        self.proceed_with_selection.emit(filtered_result)
        self.accept()
