"""
Transaction preview and review interface
Allows users to review, edit, and validate transactions before import
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QPushButton, QLabel, QComboBox, QLineEdit, QTextEdit, QDateEdit,
    QDoubleSpinBox, QHeaderView, QAbstractItemView, QSplitter,
    QGroupBox, QFormLayout, QCheckBox, QProgressBar, QMessageBox,
    QDialog, QDialogButtonBox, QFrame, QScrollArea
)
from PySide6.QtCore import Qt, Signal, QDate, QTimer
from PySide6.QtGui import QFont, QColor, QPalette

from datetime import datetime, date
from typing import List, Dict, Any, Optional
from decimal import Decimal

from ..models.transaction import ProcessedTransaction
from ..core.logger import get_logger


class TransactionEditDialog(QDialog):
    """Dialog for editing individual transaction details"""
    
    transaction_updated = Signal(ProcessedTransaction)
    
    def __init__(self, transaction: ProcessedTransaction, categories: Dict[str, List[str]], parent=None):
        super().__init__(parent)
        self.transaction = transaction
        self.categories = categories
        self.logger = get_logger(__name__)
        
        self.setWindowTitle("Edit Transaction")
        self.setModal(True)
        self.resize(500, 400)
        
        self.setup_ui()
        self.populate_fields()
    
    def setup_ui(self):
        """Setup the dialog UI"""
        layout = QVBoxLayout(self)
        
        # Create form layout
        form_layout = QFormLayout()
        
        # Date field
        self.date_edit = QDateEdit()
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setDate(QDate.currentDate())
        form_layout.addRow("Date:", self.date_edit)
        
        # Type field
        self.type_combo = QComboBox()
        self.type_combo.addItems(["Expense", "Income", "Transfer"])
        form_layout.addRow("Type:", self.type_combo)
        
        # Category field
        self.category_combo = QComboBox()
        self.category_combo.currentTextChanged.connect(self.on_category_changed)
        form_layout.addRow("Category:", self.category_combo)
        
        # Sub-category field
        self.subcategory_combo = QComboBox()
        form_layout.addRow("Sub-category:", self.subcategory_combo)
        
        # Transaction mode field
        self.transaction_mode_combo = QComboBox()
        self.transaction_mode_combo.addItems([
            "Cash", "Credit Card", "Debit Card", "UPI", "Net Banking",
            "Wallet", "Cheque", "Bank Transfer", "Other"
        ])
        form_layout.addRow("Transaction Mode:", self.transaction_mode_combo)
        
        # Amount field
        self.amount_spinbox = QDoubleSpinBox()
        self.amount_spinbox.setRange(0.01, 999999.99)
        self.amount_spinbox.setDecimals(2)
        self.amount_spinbox.setSuffix(" ₹")
        form_layout.addRow("Amount:", self.amount_spinbox)
        
        # Notes field
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(100)
        form_layout.addRow("Notes:", self.notes_edit)
        
        # Original description (read-only)
        self.original_desc_label = QLabel()
        self.original_desc_label.setWordWrap(True)
        self.original_desc_label.setStyleSheet("color: gray; font-style: italic;")
        form_layout.addRow("Original Description:", self.original_desc_label)
        
        layout.addLayout(form_layout)
        
        # Confidence score display
        confidence_layout = QHBoxLayout()
        confidence_layout.addWidget(QLabel("Confidence Score:"))
        self.confidence_label = QLabel()
        self.confidence_label.setStyleSheet("font-weight: bold;")
        confidence_layout.addWidget(self.confidence_label)
        confidence_layout.addStretch()
        layout.addLayout(confidence_layout)
        
        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept_changes)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
        
        # Populate categories
        self.populate_categories()
    
    def populate_categories(self):
        """Populate category dropdown"""
        self.category_combo.clear()
        self.category_combo.addItems(sorted(self.categories.keys()))
    
    def on_category_changed(self, category: str):
        """Handle category change"""
        self.subcategory_combo.clear()
        if category in self.categories:
            self.subcategory_combo.addItems(sorted(self.categories[category]))
    
    def populate_fields(self):
        """Populate fields with transaction data"""
        if self.transaction.date:
            self.date_edit.setDate(QDate.fromString(self.transaction.date.isoformat(), Qt.ISODate))
        
        self.type_combo.setCurrentText(self.transaction.type)
        
        if self.transaction.category:
            self.category_combo.setCurrentText(self.transaction.category)
            self.on_category_changed(self.transaction.category)
        
        if self.transaction.sub_category:
            self.subcategory_combo.setCurrentText(self.transaction.sub_category)
        
        if self.transaction.transaction_mode:
            self.transaction_mode_combo.setCurrentText(self.transaction.transaction_mode)
        
        self.amount_spinbox.setValue(float(self.transaction.amount))
        self.notes_edit.setPlainText(self.transaction.notes)
        self.original_desc_label.setText(self.transaction.original_description)
        
        # Update confidence score display
        confidence_text = f"{self.transaction.confidence_score:.1%}"
        if self.transaction.confidence_score >= 0.8:
            color = "green"
        elif self.transaction.confidence_score >= 0.5:
            color = "orange"
        else:
            color = "red"
        
        self.confidence_label.setText(confidence_text)
        self.confidence_label.setStyleSheet(f"color: {color}; font-weight: bold;")
    
    def accept_changes(self):
        """Accept and apply changes"""
        try:
            # Update transaction with form data
            self.transaction.date = self.date_edit.date().toPython()
            self.transaction.type = self.type_combo.currentText()
            self.transaction.category = self.category_combo.currentText()
            self.transaction.sub_category = self.subcategory_combo.currentText()
            self.transaction.transaction_mode = self.transaction_mode_combo.currentText()
            self.transaction.amount = Decimal(str(self.amount_spinbox.value()))
            self.transaction.notes = self.notes_edit.toPlainText()
            self.transaction.is_manually_reviewed = True
            self.transaction.updated_at = datetime.now()
            
            # Validate
            errors = self.transaction.validate()
            if errors:
                QMessageBox.warning(self, "Validation Error", "\n".join(errors))
                return
            
            self.transaction_updated.emit(self.transaction)
            self.accept()
            
        except Exception as e:
            self.logger.error(f"Error updating transaction: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to update transaction: {str(e)}")


class TransactionPreviewWidget(QWidget):
    """
    Widget for previewing and editing transactions before import
    """
    
    transactions_ready = Signal(list)  # Emitted when transactions are ready for import
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger(__name__)
        
        self.transactions = []
        self.categories = {}
        self.filtered_transactions = []
        
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the preview widget UI"""
        layout = QVBoxLayout(self)
        
        # Header with statistics
        self.create_header_section(layout)
        
        # Filter and search section
        self.create_filter_section(layout)
        
        # Main content area with splitter
        splitter = QSplitter(Qt.Horizontal)
        
        # Left side - transaction table
        self.create_transaction_table(splitter)
        
        # Right side - transaction details
        self.create_details_panel(splitter)
        
        splitter.setSizes([700, 300])
        layout.addWidget(splitter)
        
        # Bottom buttons
        self.create_action_buttons(layout)
    
    def create_header_section(self, layout):
        """Create header section with statistics"""
        header_frame = QFrame()
        header_frame.setFrameStyle(QFrame.StyledPanel)
        header_layout = QHBoxLayout(header_frame)
        
        # Statistics labels
        self.total_transactions_label = QLabel("Total: 0")
        self.total_amount_label = QLabel("Amount: ₹0.00")
        self.high_confidence_label = QLabel("High Confidence: 0")
        self.needs_review_label = QLabel("Needs Review: 0")
        
        # Style the labels
        for label in [self.total_transactions_label, self.total_amount_label,
                     self.high_confidence_label, self.needs_review_label]:
            label.setStyleSheet("font-weight: bold; padding: 5px;")
        
        header_layout.addWidget(self.total_transactions_label)
        header_layout.addWidget(self.total_amount_label)
        header_layout.addWidget(self.high_confidence_label)
        header_layout.addWidget(self.needs_review_label)
        header_layout.addStretch()
        
        layout.addWidget(header_frame)
    
    def create_filter_section(self, layout):
        """Create filter and search section"""
        filter_frame = QFrame()
        filter_layout = QHBoxLayout(filter_frame)
        
        # Category filter
        filter_layout.addWidget(QLabel("Category:"))
        self.category_filter = QComboBox()
        self.category_filter.currentTextChanged.connect(self.apply_filters)
        filter_layout.addWidget(self.category_filter)
        
        # Type filter
        filter_layout.addWidget(QLabel("Type:"))
        self.type_filter = QComboBox()
        self.type_filter.addItems(["All", "Expense", "Income", "Transfer"])
        self.type_filter.currentTextChanged.connect(self.apply_filters)
        filter_layout.addWidget(self.type_filter)
        
        # Confidence filter
        filter_layout.addWidget(QLabel("Confidence:"))
        self.confidence_filter = QComboBox()
        self.confidence_filter.addItems(["All", "High (>80%)", "Medium (50-80%)", "Low (<50%)"])
        self.confidence_filter.currentTextChanged.connect(self.apply_filters)
        filter_layout.addWidget(self.confidence_filter)
        
        # Search box
        filter_layout.addWidget(QLabel("Search:"))
        self.search_box = QLineEdit()
        self.search_box.setPlaceholderText("Search descriptions...")
        self.search_box.textChanged.connect(self.apply_filters)
        filter_layout.addWidget(self.search_box)
        
        filter_layout.addStretch()
        
        layout.addWidget(filter_frame)

    def create_transaction_table(self, parent):
        """Create transaction table"""
        table_widget = QWidget()
        table_layout = QVBoxLayout(table_widget)

        # Table
        self.transaction_table = QTableWidget()
        self.transaction_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.transaction_table.setAlternatingRowColors(True)
        self.transaction_table.itemSelectionChanged.connect(self.on_selection_changed)
        self.transaction_table.itemDoubleClicked.connect(self.edit_selected_transaction)

        # Set up columns
        columns = ["Date", "Description", "Category", "Sub-Category", "Amount", "Type", "Mode", "Confidence"]
        self.transaction_table.setColumnCount(len(columns))
        self.transaction_table.setHorizontalHeaderLabels(columns)

        # Configure column widths
        header = self.transaction_table.horizontalHeader()
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # Description column
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # Category
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Sub-category

        table_layout.addWidget(self.transaction_table)

        # Table controls
        table_controls = QHBoxLayout()

        self.select_all_btn = QPushButton("Select All")
        self.select_all_btn.clicked.connect(self.select_all_transactions)
        table_controls.addWidget(self.select_all_btn)

        self.select_none_btn = QPushButton("Select None")
        self.select_none_btn.clicked.connect(self.select_no_transactions)
        table_controls.addWidget(self.select_none_btn)

        self.edit_selected_btn = QPushButton("Edit Selected")
        self.edit_selected_btn.clicked.connect(self.edit_selected_transaction)
        table_controls.addWidget(self.edit_selected_btn)

        self.delete_selected_btn = QPushButton("Delete Selected")
        self.delete_selected_btn.clicked.connect(self.delete_selected_transactions)
        table_controls.addWidget(self.delete_selected_btn)

        table_controls.addStretch()
        table_layout.addLayout(table_controls)

        parent.addWidget(table_widget)

    def create_details_panel(self, parent):
        """Create transaction details panel"""
        details_widget = QWidget()
        details_layout = QVBoxLayout(details_widget)

        # Details group
        details_group = QGroupBox("Transaction Details")
        details_form = QFormLayout(details_group)

        self.detail_date = QLabel()
        self.detail_amount = QLabel()
        self.detail_type = QLabel()
        self.detail_category = QLabel()
        self.detail_subcategory = QLabel()
        self.detail_mode = QLabel()
        self.detail_confidence = QLabel()
        self.detail_description = QLabel()
        self.detail_description.setWordWrap(True)

        details_form.addRow("Date:", self.detail_date)
        details_form.addRow("Amount:", self.detail_amount)
        details_form.addRow("Type:", self.detail_type)
        details_form.addRow("Category:", self.detail_category)
        details_form.addRow("Sub-category:", self.detail_subcategory)
        details_form.addRow("Mode:", self.detail_mode)
        details_form.addRow("Confidence:", self.detail_confidence)
        details_form.addRow("Description:", self.detail_description)

        details_layout.addWidget(details_group)

        # Suggestions group
        suggestions_group = QGroupBox("Category Suggestions")
        suggestions_layout = QVBoxLayout(suggestions_group)

        self.suggestions_list = QTableWidget()
        self.suggestions_list.setColumnCount(3)
        self.suggestions_list.setHorizontalHeaderLabels(["Category", "Sub-Category", "Confidence"])
        self.suggestions_list.setMaximumHeight(150)
        suggestions_layout.addWidget(self.suggestions_list)

        details_layout.addWidget(suggestions_group)
        details_layout.addStretch()

        parent.addWidget(details_widget)

    def create_action_buttons(self, layout):
        """Create action buttons"""
        button_layout = QHBoxLayout()

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        button_layout.addWidget(self.progress_bar)

        button_layout.addStretch()

        # Action buttons
        self.validate_btn = QPushButton("Validate All")
        self.validate_btn.clicked.connect(self.validate_all_transactions)
        button_layout.addWidget(self.validate_btn)

        self.auto_categorize_btn = QPushButton("Auto-Categorize")
        self.auto_categorize_btn.clicked.connect(self.auto_categorize_all)
        button_layout.addWidget(self.auto_categorize_btn)

        self.import_btn = QPushButton("Import Transactions")
        self.import_btn.clicked.connect(self.import_transactions)
        self.import_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        button_layout.addWidget(self.import_btn)

        layout.addLayout(button_layout)

    def set_transactions(self, transactions: List[ProcessedTransaction], categories: Dict[str, List[str]]):
        """Set transactions to preview"""
        self.transactions = transactions
        self.categories = categories
        self.filtered_transactions = transactions.copy()

        self.populate_category_filter()
        self.populate_table()
        self.update_statistics()

    def populate_category_filter(self):
        """Populate category filter dropdown"""
        self.category_filter.clear()
        self.category_filter.addItem("All")
        self.category_filter.addItems(sorted(self.categories.keys()))

    def populate_table(self):
        """Populate the transaction table"""
        self.transaction_table.setRowCount(len(self.filtered_transactions))

        for row, transaction in enumerate(self.filtered_transactions):
            # Date
            date_item = QTableWidgetItem(transaction.date.strftime("%Y-%m-%d") if transaction.date else "")
            self.transaction_table.setItem(row, 0, date_item)

            # Description
            desc_item = QTableWidgetItem(transaction.original_description[:50] + "..." if len(transaction.original_description) > 50 else transaction.original_description)
            self.transaction_table.setItem(row, 1, desc_item)

            # Category
            cat_item = QTableWidgetItem(transaction.category)
            self.transaction_table.setItem(row, 2, cat_item)

            # Sub-category
            subcat_item = QTableWidgetItem(transaction.sub_category)
            self.transaction_table.setItem(row, 3, subcat_item)

            # Amount
            amount_item = QTableWidgetItem(f"₹{transaction.amount:.2f}")
            amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.transaction_table.setItem(row, 4, amount_item)

            # Type
            type_item = QTableWidgetItem(transaction.type)
            self.transaction_table.setItem(row, 5, type_item)

            # Mode
            mode_item = QTableWidgetItem(transaction.transaction_mode)
            self.transaction_table.setItem(row, 6, mode_item)

            # Confidence
            confidence_item = QTableWidgetItem(f"{transaction.confidence_score:.1%}")
            confidence_item.setTextAlignment(Qt.AlignCenter)

            # Color code confidence
            if transaction.confidence_score >= 0.8:
                confidence_item.setBackground(QColor(200, 255, 200))  # Light green
            elif transaction.confidence_score >= 0.5:
                confidence_item.setBackground(QColor(255, 255, 200))  # Light yellow
            else:
                confidence_item.setBackground(QColor(255, 200, 200))  # Light red

            self.transaction_table.setItem(row, 7, confidence_item)

    def apply_filters(self):
        """Apply filters to transaction list"""
        self.filtered_transactions = []

        category_filter = self.category_filter.currentText()
        type_filter = self.type_filter.currentText()
        confidence_filter = self.confidence_filter.currentText()
        search_text = self.search_box.text().lower()

        for transaction in self.transactions:
            # Category filter
            if category_filter != "All" and transaction.category != category_filter:
                continue

            # Type filter
            if type_filter != "All" and transaction.type != type_filter:
                continue

            # Confidence filter
            if confidence_filter != "All":
                if confidence_filter == "High (>80%)" and transaction.confidence_score <= 0.8:
                    continue
                elif confidence_filter == "Medium (50-80%)" and (transaction.confidence_score <= 0.5 or transaction.confidence_score > 0.8):
                    continue
                elif confidence_filter == "Low (<50%)" and transaction.confidence_score > 0.5:
                    continue

            # Search filter
            if search_text and search_text not in transaction.original_description.lower():
                continue

            self.filtered_transactions.append(transaction)

        self.populate_table()
        self.update_statistics()

    def update_statistics(self):
        """Update statistics display"""
        total_count = len(self.filtered_transactions)
        total_amount = sum(t.amount for t in self.filtered_transactions)
        high_confidence = sum(1 for t in self.filtered_transactions if t.confidence_score >= 0.8)
        needs_review = sum(1 for t in self.filtered_transactions if t.confidence_score < 0.5)

        self.total_transactions_label.setText(f"Total: {total_count}")
        self.total_amount_label.setText(f"Amount: ₹{total_amount:.2f}")
        self.high_confidence_label.setText(f"High Confidence: {high_confidence}")
        self.needs_review_label.setText(f"Needs Review: {needs_review}")

    def on_selection_changed(self):
        """Handle table selection change"""
        current_row = self.transaction_table.currentRow()
        if 0 <= current_row < len(self.filtered_transactions):
            transaction = self.filtered_transactions[current_row]
            self.show_transaction_details(transaction)

    def show_transaction_details(self, transaction: ProcessedTransaction):
        """Show transaction details in the details panel"""
        self.detail_date.setText(transaction.date.strftime("%Y-%m-%d") if transaction.date else "")
        self.detail_amount.setText(f"₹{transaction.amount:.2f}")
        self.detail_type.setText(transaction.type)
        self.detail_category.setText(transaction.category)
        self.detail_subcategory.setText(transaction.sub_category)
        self.detail_mode.setText(transaction.transaction_mode)

        # Confidence with color
        confidence_text = f"{transaction.confidence_score:.1%}"
        if transaction.confidence_score >= 0.8:
            color = "green"
        elif transaction.confidence_score >= 0.5:
            color = "orange"
        else:
            color = "red"
        self.detail_confidence.setText(f'<span style="color: {color}; font-weight: bold;">{confidence_text}</span>')

        self.detail_description.setText(transaction.original_description)

        # Show suggestions
        self.suggestions_list.setRowCount(len(transaction.suggested_categories))
        for row, suggestion in enumerate(transaction.suggested_categories):
            self.suggestions_list.setItem(row, 0, QTableWidgetItem(suggestion['category']))
            self.suggestions_list.setItem(row, 1, QTableWidgetItem(suggestion['sub_category']))
            confidence_item = QTableWidgetItem(f"{suggestion['confidence']:.1%}")
            confidence_item.setTextAlignment(Qt.AlignCenter)
            self.suggestions_list.setItem(row, 2, confidence_item)

    def select_all_transactions(self):
        """Select all transactions in table"""
        self.transaction_table.selectAll()

    def select_no_transactions(self):
        """Clear all selections"""
        self.transaction_table.clearSelection()

    def edit_selected_transaction(self):
        """Edit the selected transaction"""
        current_row = self.transaction_table.currentRow()
        if 0 <= current_row < len(self.filtered_transactions):
            transaction = self.filtered_transactions[current_row]

            dialog = TransactionEditDialog(transaction, self.categories, self)
            dialog.transaction_updated.connect(self.on_transaction_updated)
            dialog.exec()

    def on_transaction_updated(self, updated_transaction: ProcessedTransaction):
        """Handle transaction update"""
        # Find and update the transaction in the main list
        for i, transaction in enumerate(self.transactions):
            if transaction.id == updated_transaction.id:
                self.transactions[i] = updated_transaction
                break

        # Refresh the display
        self.apply_filters()
        self.show_transaction_details(updated_transaction)

    def delete_selected_transactions(self):
        """Delete selected transactions"""
        selected_rows = set()
        for item in self.transaction_table.selectedItems():
            selected_rows.add(item.row())

        if not selected_rows:
            QMessageBox.information(self, "No Selection", "Please select transactions to delete.")
            return

        reply = QMessageBox.question(
            self, "Confirm Delete",
            f"Are you sure you want to delete {len(selected_rows)} transaction(s)?",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Get transactions to delete
            transactions_to_delete = [self.filtered_transactions[row] for row in sorted(selected_rows)]

            # Remove from main list
            for transaction in transactions_to_delete:
                if transaction in self.transactions:
                    self.transactions.remove(transaction)

            # Refresh display
            self.apply_filters()

    def validate_all_transactions(self):
        """Validate all transactions"""
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, len(self.transactions))

        validation_errors = []

        for i, transaction in enumerate(self.transactions):
            self.progress_bar.setValue(i + 1)

            errors = transaction.validate()
            if errors:
                validation_errors.append(f"Transaction {i+1}: {', '.join(errors)}")

        self.progress_bar.setVisible(False)

        if validation_errors:
            error_dialog = QMessageBox(self)
            error_dialog.setWindowTitle("Validation Errors")
            error_dialog.setText(f"Found {len(validation_errors)} validation errors:")
            error_dialog.setDetailedText("\n".join(validation_errors))
            error_dialog.setIcon(QMessageBox.Warning)
            error_dialog.exec()
        else:
            QMessageBox.information(self, "Validation Complete", "All transactions are valid!")

    def auto_categorize_all(self):
        """Auto-categorize all transactions with low confidence"""
        # This would trigger re-categorization for low confidence transactions
        # Implementation would depend on having access to the categorizer
        QMessageBox.information(self, "Auto-Categorize", "Auto-categorization feature would be implemented here.")

    def import_transactions(self):
        """Import transactions to main application"""
        if not self.transactions:
            QMessageBox.information(self, "No Transactions", "No transactions to import.")
            return

        # Validate all transactions first
        validation_errors = []
        for i, transaction in enumerate(self.transactions):
            errors = transaction.validate()
            if errors:
                validation_errors.extend([f"Transaction {i+1}: {error}" for error in errors])

        if validation_errors:
            reply = QMessageBox.question(
                self, "Validation Errors",
                f"Found {len(validation_errors)} validation errors. Import anyway?",
                QMessageBox.Yes | QMessageBox.No
            )
            if reply == QMessageBox.No:
                return

        # Emit signal with transactions ready for import
        self.transactions_ready.emit(self.transactions)
