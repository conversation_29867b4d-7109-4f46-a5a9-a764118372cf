{"timestamp": "20250629_131602", "session_id": "20250629_131602", "pattern_count": 24, "patterns": {"f094a9bbc95d2162": {"pattern": "ZOMATO", "category": "Food & Dining", "sub_category": "Delivery", "confidence": 0.9, "transaction_count": 2, "last_seen": "2025-06-27T19:09:25.235409", "created_at": "2025-06-27T16:57:13.273016", "pattern_type": "contains", "transaction_types": ["DEBIT"], "amount_ranges": [[250.0, 250.0]], "source": "ai"}, "d7c8f21184ae33d9": {"pattern": "PURCHASE RELIANCE FR", "category": "Supermarket", "sub_category": "Miscellaneous", "confidence": 0.7, "transaction_count": 2, "last_seen": "2025-06-27T17:55:37.548195", "created_at": "2025-06-27T16:57:19.304160", "pattern_type": "contains", "transaction_types": ["DEBIT"], "amount_ranges": [[1200.0, 1200.0]], "source": "ai"}, "fb2fe3d9b958cc36": {"pattern": "CREDIT SALARY", "category": "Salary", "sub_category": "Miscellaneous", "confidence": 0.7, "transaction_count": 2, "last_seen": "2025-06-27T17:55:37.548195", "created_at": "2025-06-27T16:57:19.310271", "pattern_type": "contains", "transaction_types": ["CREDIT"], "amount_ranges": [[50000.0, 50000.0]], "source": "ai"}, "1c68dc257c2c4c39": {"pattern": "TRANSFER", "category": "Personal", "sub_category": "Transfers", "confidence": 0.8, "transaction_count": 2, "last_seen": "2025-06-27T17:45:22.838659", "created_at": "2025-06-27T16:57:19.317553", "pattern_type": "contains", "transaction_types": ["DEBIT"], "amount_ranges": [[5000.0, 5000.0]], "source": "ai"}, "50ac96710720e0b2": {"pattern": "PURCHASE AMAZON", "category": "Shopping", "sub_category": "Online Shopping", "confidence": 0.8, "transaction_count": 2, "last_seen": "2025-06-27T16:59:32.291360", "created_at": "2025-06-27T16:57:19.324088", "pattern_type": "contains", "transaction_types": ["DEBIT"], "amount_ranges": [[15000.0, 15000.0]], "source": "ai"}, "aae118cb5267936e": {"pattern": "SWIGGY", "category": "Takeout/Delivery", "sub_category": "Miscellaneous", "confidence": 0.7, "transaction_count": 2, "last_seen": "2025-06-27T18:22:18.940722", "created_at": "2025-06-27T16:57:19.331068", "pattern_type": "contains", "transaction_types": ["DEBIT"], "amount_ranges": [[280.0, 300.0]], "source": "ai"}, "1aad4ca5c0bfa1ea": {"pattern": "ZOMATO", "category": "Online Food Delivery", "sub_category": "Miscellaneous", "confidence": 0.7, "transaction_count": 2, "last_seen": "2025-06-27T16:57:21.075461", "created_at": "2025-06-27T16:57:19.338049", "pattern_type": "contains", "transaction_types": ["DEBIT"], "amount_ranges": [[250.0, 250.0]], "source": "ai"}, "31b84d0213fb7fb0": {"pattern": "PAYMENT", "category": "Shopping", "sub_category": "Retail/Store Purchases", "confidence": 0.8, "transaction_count": 2, "last_seen": "2025-06-27T18:22:18.942904", "created_at": "2025-06-27T16:57:19.342038", "pattern_type": "contains", "transaction_types": ["DEBIT"], "amount_ranges": [[150.0, 150.0]], "source": "ai"}, "8db7ef701dc5a0cc": {"pattern": "ATM-", "category": "Test Category", "sub_category": "Test Sub-Category", "confidence": 0.85, "transaction_count": 1, "last_seen": "2025-06-27T17:55:37.545660", "created_at": "2025-06-27T17:30:25.387512", "pattern_type": "contains", "transaction_types": ["DEBIT"], "amount_ranges": [[500.0, 500.0]], "source": "test"}, "d116ba7baf8ecd5b": {"pattern": "ZOMATO", "category": "Test Category", "sub_category": "Test Sub-Category", "confidence": 0.85, "transaction_count": 1, "last_seen": "2025-06-27T17:30:25.389954", "created_at": "2025-06-27T17:30:25.389954", "pattern_type": "contains", "transaction_types": ["DEBIT"], "amount_ranges": [[250.0, 250.0]], "source": "test"}, "238e220233474cd0": {"pattern": "PURCHASE RELIANCE FR", "category": "Test Category", "sub_category": "Test Sub-Category", "confidence": 0.85, "transaction_count": 1, "last_seen": "2025-06-27T17:30:25.392347", "created_at": "2025-06-27T17:30:25.392347", "pattern_type": "contains", "transaction_types": ["DEBIT"], "amount_ranges": [[1200.0, 1200.0]], "source": "test"}, "8af438d9a921af82": {"pattern": "CREDIT SALARY COMPAN", "category": "Test Category", "sub_category": "Test Sub-Category", "confidence": 0.85, "transaction_count": 1, "last_seen": "2025-06-27T17:30:25.393344", "created_at": "2025-06-27T17:30:25.393344", "pattern_type": "contains", "transaction_types": ["CREDIT"], "amount_ranges": [[50000.0, 50000.0]], "source": "test"}, "b131a4f26d7c02e4": {"pattern": "INTEREST", "category": "Test Category", "sub_category": "Test Sub-Category", "confidence": 0.85, "transaction_count": 1, "last_seen": "2025-06-27T17:45:22.842740", "created_at": "2025-06-27T17:30:25.395338", "pattern_type": "contains", "transaction_types": ["CREDIT"], "amount_ranges": [[125.5, 125.5]], "source": "test"}, "2634ae116089e509": {"pattern": "PURCHASE FLIPKART", "category": "Shopping", "sub_category": "Online Shopping", "confidence": 0.9, "transaction_count": 2, "last_seen": "2025-06-27T17:30:40.370586", "created_at": "2025-06-27T17:30:30.178779", "pattern_type": "contains", "transaction_types": ["DEBIT"], "amount_ranges": [[2000.0, 2000.0]], "source": "ai"}, "f4d01a1cb0c27dd7": {"pattern": "UNUSUAL", "category": "Unknown/Unidentified", "sub_category": "Miscellaneous", "confidence": 0.85, "transaction_count": 2, "last_seen": "2025-06-27T17:30:40.371583", "created_at": "2025-06-27T17:30:31.108052", "pattern_type": "contains", "transaction_types": ["DEBIT"], "amount_ranges": [[7777.77, 7777.77]], "source": "ai"}, "4fc1b0d7a2a9004f": {"pattern": "ANNUAL", "category": "Finance", "sub_category": "Credit Card Fees", "confidence": 0.8, "transaction_count": 2, "last_seen": "2025-06-27T17:30:40.368081", "created_at": "2025-06-27T17:30:31.110206", "pattern_type": "contains", "transaction_types": ["DEBIT"], "amount_ranges": [[590.0, 590.0]], "source": "ai"}, "ac1e86b7705ea858": {"pattern": "INTEREST", "category": "Interest Income", "sub_category": "Miscellaneous", "confidence": 0.7, "transaction_count": 6, "last_seen": "2025-06-27T17:45:23.750754", "created_at": "2025-06-27T17:30:31.111357", "pattern_type": "contains", "transaction_types": ["CREDIT"], "amount_ranges": [[125.5, 125.5]], "source": "ai"}, "86ba4c1794fd3e5f": {"pattern": "SMS", "category": "Communications", "sub_category": "Mobile Phone Bill", "confidence": 0.8, "transaction_count": 2, "last_seen": "2025-06-27T17:30:40.369586", "created_at": "2025-06-27T17:30:31.114834", "pattern_type": "contains", "transaction_types": ["DEBIT"], "amount_ranges": [[25.0, 25.0]], "source": "ai"}, "a63317889183c286": {"pattern": "PETROL", "category": "Transportation", "sub_category": "Fuel", "confidence": 0.9, "transaction_count": 2, "last_seen": "2025-06-27T19:11:58.727186", "created_at": "2025-06-27T17:45:15.673962", "pattern_type": "contains", "transaction_types": ["DEBIT"], "amount_ranges": [[2000.0, 2000.0]], "source": "ai"}, "71376274bb94f231": {"pattern": "AMAZON", "category": "Shopping", "sub_category": "Online Shopping", "confidence": 0.9, "transaction_count": 2, "last_seen": "2025-06-27T17:45:23.747577", "created_at": "2025-06-27T17:45:16.263369", "pattern_type": "contains", "transaction_types": ["DEBIT"], "amount_ranges": [[1500.0, 1500.0]], "source": "ai"}, "82f2aedc490aae60": {"pattern": "SALARY", "category": "Income", "sub_category": "Salary", "confidence": 0.9, "transaction_count": 2, "last_seen": "2025-06-27T19:11:58.722831", "created_at": "2025-06-27T18:20:37.515905", "pattern_type": "contains", "transaction_types": ["CREDIT"], "amount_ranges": [[50000.0, 50000.0]], "source": "ai"}, "44fbd54526bf6d27": {"pattern": "SHOPPING", "category": "Shopping", "sub_category": "Online Shopping", "confidence": 0.9, "transaction_count": 2, "last_seen": "2025-06-27T18:22:19.720092", "created_at": "2025-06-27T18:20:37.960801", "pattern_type": "contains", "transaction_types": ["DEBIT"], "amount_ranges": [[1200.0, 1200.0]], "source": "ai"}, "85a9e56a52ca89c7": {"pattern": "GROCERY", "category": "**Shopping**", "sub_category": "**Groceries**", "confidence": 0.9, "transaction_count": 1, "last_seen": "2025-06-27T19:11:58.716690", "created_at": "2025-06-27T18:30:30.382652", "pattern_type": "contains", "transaction_types": ["DEBIT"], "amount_ranges": [[1200.0, 1200.0]], "source": "ai"}, "446fa39842dc2afb": {"pattern": "AMAZON", "category": "Online Shopping", "sub_category": "Miscellaneous", "confidence": 0.7, "transaction_count": 1, "last_seen": "2025-06-27T19:11:58.718749", "created_at": "2025-06-27T18:30:30.807070", "pattern_type": "contains", "transaction_types": ["DEBIT"], "amount_ranges": [[899.0, 899.0]], "source": "ai"}}, "statistics": {"total_patterns": 22, "ai_learned": 19, "manual_learned": 0, "cache_hits": 144, "cache_misses": 75}}