{"dd7a5171a473": {"hash_id": "dd7a5171a473", "original_description": "BY UPI CREDIT UPI/446567189847/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "normalized_description": "BY UPI CREDIT UPI//UPI XXXXX16269/V.ELUMALAI. ABI 1@OKAXIS IDIB000P152/MR V ELUMALAI", "amount": 128.89, "suggested_category": "Shared Money", "suggested_sub_category": "From Dad", "confidence_score": 0.9, "matched_transaction_id": "dd7a5171a473", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:48.436268", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "1c2cf891cee4": {"hash_id": "1c2cf891cee4", "original_description": "BY UPI CREDIT UPI/************/UPI XXXXX08597 /deepulakshmi2050@okhdfcbank CNRB0000033/S DEEPALAKSHMI", "normalized_description": "BY UPI CREDIT UPI//UPI XXXXX08597 /DEEPULAKSHMI2050@OKHDFCBANK CNRB0000033/S DEEPALAKSHMI", "amount": 272.23, "suggested_category": "Borrowed", "suggested_sub_category": "From GF", "confidence_score": 0.9, "matched_transaction_id": "1c2cf891cee4", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:46.607822", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "69551f7a3b17": {"hash_id": "69551f7a3b17", "original_description": "THRU UPI DEBIT UPI/************/UPI XXXXX /shadowfax@icici ICIC0DC0099/Shadowfax", "normalized_description": "THRU UPI DEBIT UPI//UPI XXXXX /SHADOWFAX@ICICI ICIC0DC0099/SHADOWFAX", "amount": -238.0, "suggested_category": "Cash limit", "suggested_sub_category": "shadowFax CashLimit", "confidence_score": 0.9, "matched_transaction_id": "69551f7a3b17", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:48.427748", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "5b9ced220278": {"hash_id": "5b9ced220278", "original_description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "normalized_description": "THRU UPI DEBIT UPI//FOODORDERING XXXXX /ZOMATOINDIA.RZP@AXISBANK UTIB0001507/ZOMATO PRIVATE", "amount": -210.0, "suggested_category": "Cash limit", "suggested_sub_category": "Zomato cash limit", "confidence_score": 0.9, "matched_transaction_id": "5b9ced220278", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:48.427748", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "b21e0939f424": {"hash_id": "b21e0939f424", "original_description": "THRU UPI DEBIT UPI/************/UPI XXXXX /kalassri1984@oksbi SBIN0010482/SRIRAM S", "normalized_description": "THRU UPI DEBIT UPI//UPI XXXXX /KALASSRI1984@OKSBI SBIN0010482/SRIRAM S", "amount": -400.0, "suggested_category": "Lend To", "suggested_sub_category": "Friends", "confidence_score": 0.9, "matched_transaction_id": "b21e0939f424", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:21.085890", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "c64dfcbbcc3d": {"hash_id": "c64dfcbbcc3d", "original_description": "BY UPI CREDIT UPI/408970859843/Rewarded for doing a XXXXX22222/goog payment@okaxis UTIB0000553/GOOGLEPAY", "normalized_description": "BY UPI CREDIT UPI//REWARDED FOR DOING A XXXXX22222/GOOG PAYMENT@OKAXIS UTIB0000553/GOOGLEPAY", "amount": 1.0, "suggested_category": "Gifts & Donations", "suggested_sub_category": "Social", "confidence_score": 0.9, "matched_transaction_id": "c64dfcbbcc3d", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:19.807410", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "e64b38ec76a2": {"hash_id": "e64b38ec76a2", "original_description": "THRU UPI DEBIT UPI/417736088460/UPI XXXXX /euronetgpay. rch@icici ICIC0DC0099/EURONETGPAY", "normalized_description": "THRU UPI DEBIT UPI//UPI XXXXX /EURONETGPAY. RCH@ICICI ICIC0DC0099/EURONETGPAY", "amount": -270.9, "suggested_category": "Bills & Utilities", "suggested_sub_category": "Recharge", "confidence_score": 0.9, "matched_transaction_id": "e64b38ec76a2", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:37.030860", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "6e3c11885ad2": {"hash_id": "6e3c11885ad2", "original_description": "THRU UPI DEBIT UPI/************/UPI XXXXX /gpaybillpay. rchrg@okpayaxis UTIB0000553/Google India Digital Services", "normalized_description": "THRU UPI DEBIT UPI//UPI XXXXX /GPAYBILLPAY. RCHRG@OKPAYAXIS UTIB0000553/GOOGLE INDIA DIGITAL SERVICES", "amount": -58.0, "suggested_category": "Bills & Utilities", "suggested_sub_category": "LandLine", "confidence_score": 0.9, "matched_transaction_id": "6e3c11885ad2", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:28.475242", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "09e87f12d70b": {"hash_id": "09e87f12d70b", "original_description": "THRU UPI DEBIT UPI/************/UPI XXXXX /v.elumalai.abi 1@oksbi IDIB000P152/Mr V ELUMALAI", "normalized_description": "THRU UPI DEBIT UPI//UPI XXXXX /V.ELUMALAI.ABI 1@OKSBI IDIB000P152/MR V ELUMALAI", "amount": -24.0, "suggested_category": "House Hold Expenses", "suggested_sub_category": "Daily expenses", "confidence_score": 0.9, "matched_transaction_id": "09e87f12d70b", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:48.910767", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "ac31229ac6c5": {"hash_id": "ac31229ac6c5", "original_description": "BY UPI CREDIT UPI/409877232432/Payment from PhonePe XXXXX34269/vasanthansbi01@ybl SBIN0070601/VASANTHAN", "normalized_description": "BY UPI CREDIT UPI//PAYMENT FROM PHONEPE XXXXX34269/VASANTHANSBI01@YBL SBIN0070601/VASANTHAN", "amount": 24.28, "suggested_category": "Salary", "suggested_sub_category": "shadowfax payout", "confidence_score": 0.9, "matched_transaction_id": "ac31229ac6c5", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:48.910767", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "1ac7ed571985": {"hash_id": "1ac7ed571985", "original_description": "BY UPI CREDIT UPI/************/poda venna XXXXX08597 /deepulakshmi2050@okhdfcbank CNRB0000033/S DEEPALAKSHMI", "normalized_description": "BY UPI CREDIT UPI//PODA VENNA XXXXX08597 /DEEPULAKSHMI2050@OKHDFCBANK CNRB0000033/S DEEPALAKSHMI", "amount": 400.0, "suggested_category": "Borrowed", "suggested_sub_category": "From GF", "confidence_score": 0.9, "matched_transaction_id": "1ac7ed571985", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:20.284023", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "cf29fae34d81": {"hash_id": "cf29fae34d81", "original_description": "THRU UPI DEBIT UPI/************/UPI XXXXX /<EMAIL> sbin0070601/Mr VASANTHAN", "normalized_description": "THRU UPI DEBIT UPI//UPI XXXXX /@SBIN0070601.IFSC.NPCI SBIN0070601/MR VASANTHAN", "amount": -1200.0, "suggested_category": "EMI", "suggested_sub_category": "ola s1 pro", "confidence_score": 0.9, "matched_transaction_id": "cf29fae34d81", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:48.974517", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "89a60bd14fd5": {"hash_id": "89a60bd14fd5", "original_description": "THRU UPI DEBIT UPI/************/UPI XXXXX /deepulakshmi2050@okhdfcbank CNRB0000033/S DEEPALAKSHMI", "normalized_description": "THRU UPI DEBIT UPI//UPI XXXXX /DEEPULAKSHMI2050@OKHDFCBANK CNRB0000033/S DEEPALAKSHMI", "amount": -100.0, "suggested_category": "House Hold Expenses", "suggested_sub_category": "Personal Expenses", "confidence_score": 0.9, "matched_transaction_id": "89a60bd14fd5", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:48.436268", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "fe17a3c4aa2d": {"hash_id": "fe17a3c4aa2d", "original_description": "THRU UPI DEBIT UPI/************/UPI XXXXX /paytm ********@paytm YESB0PTMUPI/NRTraders", "normalized_description": "THRU UPI DEBIT UPI//UPI XXXXX /PAYTM ********@PAYTM YESB0PTMUPI/NRTRADERS", "amount": -180.0, "suggested_category": "Shopping", "suggested_sub_category": "oil", "confidence_score": 0.9, "matched_transaction_id": "fe17a3c4aa2d", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:20.615892", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "3365c33ed7af": {"hash_id": "3365c33ed7af", "original_description": "THRU UPI DEBIT UPI/411159366326/UPI XXXXX /v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@oksbi SBIN0016563/YOGESH H", "normalized_description": "THRU UPI DEBIT UPI//UPI XXXXX /VADHANAHARIKRISHNAN@OKSBI SBIN0016563/YOGESH H", "amount": -100.0, "suggested_category": "Returned", "suggested_sub_category": "To Friends", "confidence_score": 0.9, "matched_transaction_id": "3365c33ed7af", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:47.293141", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "27c441159331": {"hash_id": "27c441159331", "original_description": "THRU UPI DEBIT UPI/410381224811/UPI XXXXX /balaramanmadhavan28@okicici UBIN0904422/MADHAVAN B", "normalized_description": "THRU UPI DEBIT UPI//UPI XXXXX /BALARAMANMADHAVAN28@OKICICI UBIN0904422/MADHAVAN B", "amount": -200.0, "suggested_category": "Lend To", "suggested_sub_category": "Friends", "confidence_score": 0.9, "matched_transaction_id": "27c441159331", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:48.168747", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "d8cdebea533c": {"hash_id": "d8cdebea533c", "original_description": "THRU UPI DEBIT UPI/408070868935/UPI XXXXX /gpay 11180489334@okbizaxis UTIB0000000/VILORA TECHNOLOGIES", "normalized_description": "THRU UPI DEBIT UPI//UPI XXXXX /GPAY @OKBIZAXIS UTIB0000000/VILORA TECHNOLOGIES", "amount": -60.0, "suggested_category": "Bills & Utilities", "suggested_sub_category": "Education", "confidence_score": 0.9, "matched_transaction_id": "d8cdebea533c", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:20.623404", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "15422bf2b6f4": {"hash_id": "15422bf2b6f4", "original_description": "BY UPI CREDIT UPI/406705319403/UPI XXXXX69717 /balaramanmadhavan28@okicici UBIN0904422/MADHAVAN B", "normalized_description": "BY UPI CREDIT UPI//UPI XXXXX69717 /BALARAMANMADHAVAN28@OKICICI UBIN0904422/MADHAVAN B", "amount": 2000.0, "suggested_category": "Borrowed", "suggested_sub_category": "From Friends", "confidence_score": 0.9, "matched_transaction_id": "15422bf2b6f4", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:22.656882", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "1ec63d7160ea": {"hash_id": "1ec63d7160ea", "original_description": "THRU UPI DEBIT UPI/444535447171/UPI XXXXX /paytm 80306317@paytm YESB0PTMUPI/Lakshmi Narayana Agency", "normalized_description": "THRU UPI DEBIT UPI//UPI XXXXX /PAYTM 80306317@PAYTM YESB0PTMUPI/LAKSHMI NARAYANA AGENCY", "amount": -300.0, "suggested_category": "Transportation", "suggested_sub_category": "Petrol", "confidence_score": 0.9, "matched_transaction_id": "1ec63d7160ea", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:20.623909", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "0211f5b6ec02": {"hash_id": "0211f5b6ec02", "original_description": "THRU UPI DEBIT UPI/407402307248/UPI XXXXX /paytmqr4hx2pf7m48@paytm PYTM0123456/SRI Bharath Sweet and Bakery", "normalized_description": "THRU UPI DEBIT UPI//UPI XXXXX /PAYTMQR4HX2PF7M48@PAYTM PYTM0123456/SRI BHARATH SWEET AND BAKERY", "amount": -15.0, "suggested_category": "House Hold Expenses", "suggested_sub_category": "Personal Expenses", "confidence_score": 0.9, "matched_transaction_id": "0211f5b6ec02", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:21.086891", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "2fa2be8a4b95": {"hash_id": "2fa2be8a4b95", "original_description": "THRU UPI DEBIT UPI/************/UPI XXXXX /gpaybillpay. insur@okpayaxis UTIB0000553/Google India Digital Services", "normalized_description": "THRU UPI DEBIT UPI//UPI XXXXX /GPAYBILLPAY. INSUR@OKPAYAXIS UTIB0000553/GOOGLE INDIA DIGITAL SERVICES", "amount": -2225.02, "suggested_category": "Bills & Utilities", "suggested_sub_category": "Electricity", "confidence_score": 0.9, "matched_transaction_id": "2fa2be8a4b95", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:21.086891", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "73aef582e30e": {"hash_id": "73aef582e30e", "original_description": "THRU UPI DEBIT UPI/************/Pay to BharatPe Merc XXXXX /bharatpe09916629172@yesbankltd YESB0YESUPI/MS UDHAYA COMPUTERS", "normalized_description": "THRU UPI DEBIT UPI//PAY TO BHARATPE MERC XXXXX /BHARATPE09916629172@YESBANKLTD YESB0YESUPI/MS UDHAYA COMPUTERS", "amount": -60.0, "suggested_category": "Appliances", "suggested_sub_category": "Computer Upgrades", "confidence_score": 0.9, "matched_transaction_id": "73aef582e30e", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:42.646334", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "4ec4d9123849": {"hash_id": "4ec4d9123849", "original_description": "THRU UPI DEBIT UPI/************/UPI XXXXX /paytmqr281005050101tvhf2i3bnm4a@paytm PYTM0123456/C A K TYRES", "normalized_description": "THRU UPI DEBIT UPI//UPI XXXXX /PAYTMQR281005050101TVHF2I3BNM4A@PAYTM PYTM0123456/C A K TYRES", "amount": -3800.0, "suggested_category": "Transportation", "suggested_sub_category": "Vehicle Maintenance", "confidence_score": 0.9, "matched_transaction_id": "4ec4d9123849", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:22.656882", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "0e06c513a672": {"hash_id": "0e06c513a672", "original_description": "THRU UPI DEBIT UPI/413818543965/UPI XXXXX /kkamataj 1@oksbi SBIN0009584/Kamaraj <PERSON>", "normalized_description": "THRU UPI DEBIT UPI//UPI XXXXX /KKAMATAJ 1@OKSBI SBIN0009584/KAMARAJ GANESAN", "amount": -100.0, "suggested_category": "EMI", "suggested_sub_category": "Intial Amount", "confidence_score": 0.9, "matched_transaction_id": "0e06c513a672", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:42.646334", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "d5a3b527eb14": {"hash_id": "d5a3b527eb14", "original_description": "BY UPI CREDIT UPI/************/GPlinks Payments XXXXX92995 /poweraccess.cashfree@axisbank utib0001920/Cashfree Payments", "normalized_description": "BY UPI CREDIT UPI//GPL<PERSON>KS PAYMENTS XXXXX92995 /POWERACCESS.CASHFREE@AXISBANK UTIB0001920/CASHFREE PAYMENTS", "amount": 439.54, "suggested_category": "Youtube Income", "suggested_sub_category": "gp links", "confidence_score": 0.9, "matched_transaction_id": "d5a3b527eb14", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:23.875686", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "8a966bf30b1b": {"hash_id": "8a966bf30b1b", "original_description": "THRU UPI DEBIT UPI/************/UPIIntent XXXXX /idfcbankloanrepayment.payu@hdfcbank HDFC0000499/WWW IDFCBANK COM", "normalized_description": "THRU UPI DEBIT UPI//UPIINTENT XXXXX /IDFCBANKLOANREPAYMENT.PAYU@HDFCBANK HDFC0000499/WWW IDFCBANK COM", "amount": -972.0, "suggested_category": "EMI", "suggested_sub_category": "Flipkart paylater", "confidence_score": 0.9, "matched_transaction_id": "8a966bf30b1b", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:24.139139", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "08504da61c6f": {"hash_id": "08504da61c6f", "original_description": "BY UPI CREDIT UPI/************/UPI XXXXX57133 /atk23592@okicici UBIN0830054/PURUSHOTHAMAN A", "normalized_description": "BY UPI CREDIT UPI//UPI XXXXX57133 /ATK23592@OKICICI UBIN0830054/PURUSHOTHAMAN A", "amount": 250.0, "suggested_category": "Youtube Income", "suggested_sub_category": "Id sales", "confidence_score": 0.9, "matched_transaction_id": "08504da61c6f", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:29.178968", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "dea655e831d0": {"hash_id": "dea655e831d0", "original_description": "THRU UPI DEBIT UPI/441375394512/UPI XXXXX /mugumugundaram507@okicici IDIB000L007 /mugumugundaram507@okicici", "normalized_description": "THRU UPI DEBIT UPI//UPI XXXXX /MUGUMUGUNDARAM507@OKICICI IDIB000L007 /MUGUMUGUNDARAM507@OKICICI", "amount": -500.0, "suggested_category": "Lend To", "suggested_sub_category": "Friends", "confidence_score": 0.9, "matched_transaction_id": "dea655e831d0", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:26.037918", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "1aead1c7d127": {"hash_id": "1aead1c7d127", "original_description": "BY UPI CREDIT UPI/************/UPI XXXXX91759 /blackloverz333@okhdfcbank IDIB000P152/Mrs E Abirami", "normalized_description": "BY UPI CREDIT UPI//UPI XXXXX91759 /BLACKLOVERZ333@OKHDFCBANK IDIB000P152/MRS E ABIRAMI", "amount": 100.0, "suggested_category": "Shared Money", "suggested_sub_category": "From <PERSON><PERSON><PERSON>", "confidence_score": 0.9, "matched_transaction_id": "1aead1c7d127", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:26.362137", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "0458feb63673": {"hash_id": "0458feb63673", "original_description": "THRU UPI DEBIT UPI/************/UPI XXXXX /jagansundar0. 0001@okicici SBIN0007545/Jagansundar M", "normalized_description": "THRU UPI DEBIT UPI//UPI XXXXX /JAGANSUNDAR0. 0001@OKICICI SBIN0007545/JAGANSUNDAR M", "amount": -200.0, "suggested_category": "Returned", "suggested_sub_category": "To Friends", "confidence_score": 0.9, "matched_transaction_id": "0458feb63673", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:26.378289", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "c02339c87aa1": {"hash_id": "c02339c87aa1", "original_description": "THRU UPI DEBIT UPI/************/UPIIntent XXXXX /idfcfirstbank.payu@mairtel AIRP0000001/IDFC FIRST BANK LIMITED", "normalized_description": "THRU UPI DEBIT UPI//UPIINTENT XXXXX /IDFCFIRSTBANK.PAYU@MAIRTEL AIRP0000001/IDFC FIRST BANK", "amount": -498.0, "suggested_category": "Bills & Utilities", "suggested_sub_category": "Recharge", "confidence_score": 0.9, "matched_transaction_id": "c02339c87aa1", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:26.378289", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "a3e89002d74e": {"hash_id": "a3e89002d74e", "original_description": "THRU UPI DEBIT UPI/************/UPI XXXXX /paytmqr281005050101gek6lugn31vz@paytm PYTM0123456/SAI VIKA AGENCIES", "normalized_description": "THRU UPI DEBIT UPI//UPI XXXXX /PAYTMQR281005050101GEK6LUGN31VZ@PAYTM PYTM0123456/SAI VIKA AGENCIES", "amount": -100.0, "suggested_category": "Transportation", "suggested_sub_category": "Petrol", "confidence_score": 0.9, "matched_transaction_id": "a3e89002d74e", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:26.385823", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "57219a24fc02": {"hash_id": "57219a24fc02", "original_description": "BY UPI CREDIT UPI/440770414764/UPI XXXXX08597 /deepulakshmi2050@okicici CNRB0000033/S DEEPALAKSHMI", "normalized_description": "BY UPI CREDIT UPI//UPI XXXXX08597 /DEEPULAKSHMI2050@OKICICI CNRB0000033/S DEEPALAKSHMI", "amount": 350.0, "suggested_category": "Borrowed", "suggested_sub_category": "From GF", "confidence_score": 0.9, "matched_transaction_id": "57219a24fc02", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:26.725877", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "fba29c26a334": {"hash_id": "fba29c26a334", "original_description": "THRU UPI DEBIT UPI/404086302257/UPI XXXXX /thalapathygokul2407@okicici IDIB000V022/Mr <PERSON>", "normalized_description": "THRU UPI DEBIT UPI//UPI XXXXX /THALAPATHYGOKUL2407@OKICICI IDIB000V022/MR S GOKULAKRISHNAN", "amount": -50.0, "suggested_category": "Lend To", "suggested_sub_category": "Friends", "confidence_score": 0.9, "matched_transaction_id": "fba29c26a334", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:26.726877", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "0e88bc86af82": {"hash_id": "0e88bc86af82", "original_description": "THRU UPI DEBIT UPI/************/UPI XXXXX /blackloverz333@okhdfcbank IDIB000P152/Mrs E Abirami", "normalized_description": "THRU UPI DEBIT UPI//UPI XXXXX /BLACKLOVERZ333@OKHDFCBANK IDIB000P152/MRS E ABIRAMI", "amount": -11.0, "suggested_category": "House Hold Expenses", "suggested_sub_category": "Daily expenses", "confidence_score": 0.9, "matched_transaction_id": "0e88bc86af82", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:28.921218", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "e56fd482164c": {"hash_id": "e56fd482164c", "original_description": "THRU UPI DEBIT UPI/************/UPI XXXXX /paytmqrzwm016a0ey@paytm PYTM0123456/Badmavathy Agencies", "normalized_description": "THRU UPI DEBIT UPI//UPI XXXXX /PAYTMQRZWM016A0EY@PAYTM PYTM0123456/BADMAVATHY AGENCIES", "amount": -100.0, "suggested_category": "Transportation", "suggested_sub_category": "Petrol", "confidence_score": 0.9, "matched_transaction_id": "e56fd482164c", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:27.239148", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "8a926efe88dd": {"hash_id": "8a926efe88dd", "original_description": "THRU UPI DEBIT UPI/403314098374/UPI XXXXX /paytmqr2810050501011mwbekd7l0zf@paytm PYTM0123456 /IPOUR GKC And RKC And SONS PET", "normalized_description": "THRU UPI DEBIT UPI//UPI XXXXX /PAYTMQR2810050501011MWBEKD7L0ZF@PAYTM PYTM0123456 /IPOUR GKC AND RKC AND SONS PET", "amount": -100.0, "suggested_category": "House Hold Expenses", "suggested_sub_category": "Home Setup", "confidence_score": 0.9, "matched_transaction_id": "8a926efe88dd", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:27.828257", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "fc0e70979818": {"hash_id": "fc0e70979818", "original_description": "THRU UPI DEBIT UPI/402399273969/UPI XXXXX /paytmqr2810050501011fowx9i92gey@paytm PYTM0123456 /SRINIVASA AUTO SPARES", "normalized_description": "THRU UPI DEBIT UPI//UPI XXXXX /PAYTMQR2810050501011FOWX9I92GEY@PAYTM PYTM0123456 /SRINIVASA AUTO SPARES", "amount": -100.0, "suggested_category": "Transportation", "suggested_sub_category": "Vehicle Maintenance", "confidence_score": 0.9, "matched_transaction_id": "fc0e70979818", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:28.703296", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "fd9d02f9e15c": {"hash_id": "fd9d02f9e15c", "original_description": "THRU UPI DEBIT UPI/401994226440/UPI XXXXX /gpay 11216505112@okbizaxis UTIB0000000/Suba Auto Spares", "normalized_description": "THRU UPI DEBIT UPI//UPI XXXXX /GPAY @OKBIZAXIS UTIB0000000/SUBA AUTO SPARES", "amount": -560.0, "suggested_category": "Bike <PERSON>ce", "suggested_sub_category": "R15 V3", "confidence_score": 0.9, "matched_transaction_id": "fd9d02f9e15c", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:29.178968", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "57de3d547a55": {"hash_id": "57de3d547a55", "original_description": "BY UPI CREDIT UPI/400567280499/UPI XXXXX16269/v.elumalai. abi 1@oksbi IDIB000P152/Mr V ELUMALAI", "normalized_description": "BY UPI CREDIT UPI//UPI XXXXX16269/V.ELUMALAI. ABI 1@OKSBI IDIB000P152/MR V ELUMALAI", "amount": 100.0, "suggested_category": "Shared Money", "suggested_sub_category": "From Dad", "confidence_score": 0.8568688078388677, "matched_transaction_id": "dd7a5171a473", "similarity_score": 0.7614480130647794, "auto_labeled_at": "2025-06-29T14:05:30.019135", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "6627aab0927b": {"hash_id": "6627aab0927b", "original_description": "THRU UPI DEBIT UPI/422818833726/UPI XXXXX /mailtovara022@oksbi IDIB000K049/Ms Varalakshmi", "normalized_description": "THRU UPI DEBIT UPI//UPI XXXXX /MAILTOVARA022@OKSBI IDIB000K049/MS VARALAKSHMI", "amount": -1500.0, "suggested_category": "Returned", "suggested_sub_category": "To Friends", "confidence_score": 0.9, "matched_transaction_id": "6627aab0927b", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:30.236800", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "7452cf80b5ea": {"hash_id": "7452cf80b5ea", "original_description": "BY UPI CREDIT UPI/459403238645/UPI XXXXX91759 /abirami9159@okaxis IDIB000P152/Mrs E Abirami", "normalized_description": "BY UPI CREDIT UPI//UPI XXXXX91759 /ABIRAMI9159@OKAXIS IDIB000P152/MRS E ABIRAMI", "amount": 430.0, "suggested_category": "Shared Money", "suggested_sub_category": "From Abirami", "confidence_score": 0.9, "matched_transaction_id": "7452cf80b5ea", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:30.236800", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "80b30f093b28": {"hash_id": "80b30f093b28", "original_description": "BY UPI CREDIT UPI/420994192384/UPI XXXXX16269/v.elumalai. abi@oksbi SBIN0070601/V ELUMALAI", "normalized_description": "BY UPI CREDIT UPI//UPI XXXXX16269/V.ELUMALAI. ABI@OKSBI SBIN0070601/V ELUMALAI", "amount": 100.0, "suggested_category": "Shared Money", "suggested_sub_category": "From Dad", "confidence_score": 0.9, "matched_transaction_id": "80b30f093b28", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:32.219270", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "99da2f35750f": {"hash_id": "99da2f35750f", "original_description": "THRU UPI DEBIT UPI/************/UPI XXXXX /balaramanmadhavan28@okhdfcbank UBIN0904422/MADHAVAN B", "normalized_description": "THRU UPI DEBIT UPI//UPI XXXXX /BALARAMANMADHAVAN28@OKHDFCBANK UBIN0904422/MADHAVAN B", "amount": -10.0, "suggested_category": "Returned", "suggested_sub_category": "To Friends", "confidence_score": 0.9, "matched_transaction_id": "99da2f35750f", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:32.219270", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "92a9d3d1f177": {"hash_id": "92a9d3d1f177", "original_description": "THRU UPI DEBIT UPI/************/Upi Transaction XXXXX48779 /magma.hdi.payu@icici ICIC0DC0099/Magma HDI General Insurance Co", "normalized_description": "THRU UPI DEBIT UPI//UPI TRANSACTION XXXXX48779 /MAGMA.HDI.PAYU@ICICI ICIC0DC0099/MAGMA HDI GENERAL INSURANCE CO", "amount": -59.0, "suggested_category": "Bills & Utilities", "suggested_sub_category": "Insurance", "confidence_score": 0.9, "matched_transaction_id": "92a9d3d1f177", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:33.896673", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "7845a0a88fe3": {"hash_id": "7845a0a88fe3", "original_description": "BY UPI CREDIT UPI/420463858663/UPI XXXXX86489 /jagansundar0.0001 1@oksbi SBIN0007545/Jagansundar M", "normalized_description": "BY UPI CREDIT UPI//UPI XXXXX86489 /JAGANSUNDAR0.0001 1@OKSBI SBIN0007545/JAGANSUNDAR M", "amount": 1000.0, "suggested_category": "Borrowed", "suggested_sub_category": "From Friends", "confidence_score": 0.9, "matched_transaction_id": "7845a0a88fe3", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:34.116721", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "7106c369471a": {"hash_id": "7106c369471a", "original_description": "THRU UPI DEBIT UPI/410239784510/UPI Intent XXXXX /paytm 17731298@paytm YESB0PTMUPI/Meesho", "normalized_description": "THRU UPI DEBIT UPI//UPI INTENT XXXXX /PAYTM 17731298@PAYTM YESB0PTMUPI/MEESHO", "amount": -267.0, "suggested_category": "Online Stores", "suggested_sub_category": "meesho", "confidence_score": 0.9, "matched_transaction_id": "7106c369471a", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:48.168747", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "9b45a589eb0a": {"hash_id": "9b45a589eb0a", "original_description": "BY UPI CREDIT UPI/417025074714/RefundRef OD33151646 XXXXX00000/DEUT2050243000@dbag DEUT0797BGL/Flipkart Internet Private Limi", "normalized_description": "BY UPI CREDIT UPI// OD33151646 XXXXX00000/DEUT2050243000@DBAG DEUT0797BGL/FLIPKART INTERNET PRIVATE LIMI", "amount": 129.01, "suggested_category": "Refunds", "suggested_sub_category": "flipkart refunds", "confidence_score": 0.9, "matched_transaction_id": "9b45a589eb0a", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:38.463440", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "8cd85dc8f528": {"hash_id": "8cd85dc8f528", "original_description": "BY UPI CREDIT UPI/************/CASHFREE XXXXX92995 /poweraccess.cashfree@axisbank utib0001920/Cashfree Payments", "normalized_description": "BY UPI CREDIT UPI//CASHFREE XXXXX92995 /POWERACCESS.CASHFREE@AXISBANK UTIB0001920/CASHFREE PAYMENTS", "amount": 448.23, "suggested_category": "Refunds", "suggested_sub_category": "SnapMint", "confidence_score": 0.9, "matched_transaction_id": "8cd85dc8f528", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:46.607822", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "779ca3f9b407": {"hash_id": "779ca3f9b407", "original_description": "THRU UPI DEBIT UPI/************/UPI XXXXX /paytmqr2810050501014r27cbgnsopq@paytm YESB0PTMUPI/SRI VENU CORPORATION", "normalized_description": "THRU UPI DEBIT UPI//UPI XXXXX /PAYTMQR2810050501014R27CBGNSOPQ@PAYTM YESB0PTMUPI/SRI VENU CORPORATION", "amount": -50.0, "suggested_category": "Transportation", "suggested_sub_category": "Petrol", "confidence_score": 0.9, "matched_transaction_id": "779ca3f9b407", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:40.938818", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "66f69dfecb9d": {"hash_id": "66f69dfecb9d", "original_description": "BY UPI CREDIT UPI/452303366884/collect XXXXX01028/paytm 31893797@paytm YESB0PTMUPI/Snapmint Credit Advisory Pvt L", "normalized_description": "BY UPI CREDIT UPI//COLLECT XXXXX01028/PAYTM 31893797@PAYTM YESB0PTMUPI/SNAPMINT CREDIT ADVISORY L", "amount": 4959.1, "suggested_category": "Refunds", "suggested_sub_category": "SnapMint", "confidence_score": 0.9, "matched_transaction_id": "66f69dfecb9d", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:40.938818", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "ac73f56b36d8": {"hash_id": "ac73f56b36d8", "original_description": "THRU UPI DEBIT UPI/415590697689/UPIIntent XXXXX /snapmintfinancialserv.payu@mairtel AIRP0000001/SNAPMINT FINANCIAL SERVICES PR", "normalized_description": "THRU UPI DEBIT UPI//UPIINTENT XXXXX /SNAPMINTFINANCIALSERV.PAYU@MAIRTEL AIRP0000001/SNAPMINT FINANCIAL SERVICES PR", "amount": -1303.0, "suggested_category": "EMI", "suggested_sub_category": "snapmint", "confidence_score": 0.9, "matched_transaction_id": "ac73f56b36d8", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:41.159650", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "b727420f874c": {"hash_id": "b727420f874c", "original_description": "BY UPI CREDIT UPI/415111219772/Payment from PhonePe XXXXX91759/9489391759@ybl IDIB000P152/Mrs E Abirami", "normalized_description": "BY UPI CREDIT UPI//PAYMENT FROM PHONEPE XXXXX91759/@YBL IDIB000P152/MRS E ABIRAMI", "amount": 246.1, "suggested_category": "Shared Money", "suggested_sub_category": "From Abirami", "confidence_score": 0.9, "matched_transaction_id": "b727420f874c", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:41.403753", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "0c1ac48877fb": {"hash_id": "0c1ac48877fb", "original_description": "BY UPI CREDIT UPI/415199818024/UPI XXXXX37899/info. krishnakumar2004@oksbi SBIN0012797/S <PERSON>", "normalized_description": "BY UPI CREDIT UPI//UPI XXXXX37899/INFO. KRISHNAKUMAR2004@OKSBI SBIN0012797/S KRISHNAKUMAR", "amount": 1489.1, "suggested_category": "Borrowed", "suggested_sub_category": "From Friends", "confidence_score": 0.9, "matched_transaction_id": "0c1ac48877fb", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:41.403248", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "74a69e08d2ad": {"hash_id": "74a69e08d2ad", "original_description": "BY UPI CREDIT UPI/415029648634/Additional XXXXX91759 /9489391759@ybl IDIB000P152/Mrs E Abirami", "normalized_description": "BY UPI CREDIT UPI//ADDITIONAL XXXXX91759 /@YBL IDIB000P152/MRS E ABIRAMI", "amount": 20347.1, "suggested_category": "Shared Money", "suggested_sub_category": "From Abirami", "confidence_score": 0.9, "matched_transaction_id": "74a69e08d2ad", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:41.754836", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "547ed75e96fa": {"hash_id": "547ed75e96fa", "original_description": "BY UPI CREDIT UPI/414018283898/UPI XXXXX28268 /ramachandran1854@oksbi SBIN0000900/RAMACHANDRAN G", "normalized_description": "BY UPI CREDIT UPI//UPI XXXXX28268 /RAMACHANDRAN1854@OKSBI SBIN0000900/RAMACHANDRAN G", "amount": 335.94, "suggested_category": "Youtube Income", "suggested_sub_category": "Id sales", "confidence_score": 0.9, "matched_transaction_id": "547ed75e96fa", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:42.646334", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "612b6394a80b": {"hash_id": "612b6394a80b", "original_description": "BY UPI CREDIT UPI/413363105139/Payment from PhonePe XXXXX09009/8870409009 2@axl HDFC0000407/MALATHI MAYAKRISHNAN", "normalized_description": "BY UPI CREDIT UPI//PAYMENT FROM PHONEPE XXXXX09009/ 2@AXL HDFC0000407/MALATHI MAYAKRISHNAN", "amount": 1130.26, "suggested_category": "Refunds", "suggested_sub_category": "SnapMint", "confidence_score": 0.9, "matched_transaction_id": "612b6394a80b", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:44.497490", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "b1a04a881332": {"hash_id": "b1a04a881332", "original_description": "BY UPI CREDIT UPI/410380303135/UPI XXXXX22530 /v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@oksbi SBIN0016563/YOGESH H", "normalized_description": "BY UPI CREDIT UPI//UPI XXXXX22530 /VADHANAHARIKRISHNAN@OKSBI SBIN0016563/YOGESH H", "amount": 190.49, "suggested_category": "Borrowed", "suggested_sub_category": "From Friends", "confidence_score": 0.9, "matched_transaction_id": "b1a04a881332", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:48.168747", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "72448d396f5c": {"hash_id": "72448d396f5c", "original_description": "THRU UPI DEBIT UPI/409871673284/balance amount XXXXX /balaramanmadhavan28@okicici UBIN0904422/MADHAVAN B", "normalized_description": "THRU UPI DEBIT UPI//BALANCE AMOUNT XXXXX /BALARAMANMADHAVAN28@OKICICI UBIN0904422/MADHAVAN B", "amount": -500.0, "suggested_category": "Lend To", "suggested_sub_category": "Friends", "confidence_score": 0.8841236994219653, "matched_transaction_id": "27c441159331", "similarity_score": 0.8068728323699422, "auto_labeled_at": "2025-06-29T14:05:48.646020", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}, "5ff99ad8b8d8": {"hash_id": "5ff99ad8b8d8", "original_description": "THRU UPI DEBIT UPI/409871609400/emi XXXXX /balaramanmadhavan28@okicici UBIN0904422/MADHAVAN B", "normalized_description": "THRU UPI DEBIT UPI//EMI XXXXX /BALARAMANMADHAVAN28@OKICICI UBIN0904422/MADHAVAN B", "amount": -500.0, "suggested_category": "Returned", "suggested_sub_category": "To Friends", "confidence_score": 0.9, "matched_transaction_id": "5ff99ad8b8d8", "similarity_score": 0.****************, "auto_labeled_at": "2025-06-29T14:05:48.646020", "status": "pending_review", "reviewed_by": null, "reviewed_at": null, "final_category": null, "final_sub_category": null}}