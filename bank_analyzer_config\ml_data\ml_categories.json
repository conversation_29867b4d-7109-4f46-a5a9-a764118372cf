[{"id": "ml_zomato_cash_limit_1750696607", "name": "Cash limit", "parent_id": null, "description": "zomato restriction clearance", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-23T22:06:47.675894", "updated_at": "2025-06-26T11:35:59.310308"}, {"id": "ml_cash_limit_1750696630", "name": "Zomato cash limit", "parent_id": "ml_zomato_cash_limit_1750696607", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-23T22:07:10.384256", "updated_at": "2025-06-26T11:36:24.624054"}, {"id": "ml_gf_1750697254", "name": "GF", "parent_id": "main_cat_other", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-23T22:17:34.575241", "updated_at": "2025-06-23T22:17:34.575241"}, {"id": "ml_investment_1750697273", "name": "Investment", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-23T22:17:53.089842", "updated_at": "2025-06-23T22:17:53.089842"}, {"id": "ml_zerodha_1750697281", "name": "zerodha", "parent_id": "ml_investment_1750697273", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-23T22:18:01.699300", "updated_at": "2025-06-23T22:18:01.699300"}, {"id": "ml_aura_gold_1750697292", "name": "aura gold", "parent_id": "ml_investment_1750697273", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-23T22:18:12.310111", "updated_at": "2025-06-23T22:18:12.310111"}, {"id": "ml_aura_silver_1750697303", "name": "aura silver", "parent_id": "ml_investment_1750697273", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-23T22:18:23.855520", "updated_at": "2025-06-23T22:18:23.855520"}, {"id": "ml_emi_1750697955", "name": "EMI", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-23T22:29:15.652249", "updated_at": "2025-06-23T22:29:15.652249"}, {"id": "ml_housing_loan_1750697969", "name": "Housing Loan", "parent_id": "ml_emi_1750697955", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-23T22:29:29.569581", "updated_at": "2025-06-23T22:29:29.569581"}, {"id": "ml_ola_s1_x_1_1750697982", "name": "Ola s1 x 1", "parent_id": "ml_emi_1750697955", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-23T22:29:42.251607", "updated_at": "2025-06-23T22:29:42.251607"}, {"id": "ml_ola_s1_x_2_1750697992", "name": "ola s1 x 2", "parent_id": "ml_emi_1750697955", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-23T22:29:52.398484", "updated_at": "2025-06-23T22:29:52.398484"}, {"id": "ml_ola_s1_pro_1750698000", "name": "ola s1 pro", "parent_id": "ml_emi_1750697955", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-23T22:30:00.064156", "updated_at": "2025-06-23T22:30:00.064156"}, {"id": "ml_iphone_16_1750698013", "name": "iphone 16", "parent_id": "ml_emi_1750697955", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-23T22:30:13.832012", "updated_at": "2025-06-23T22:30:13.832012"}, {"id": "ml_dji_osmo_action_2_1750698025", "name": "dji osmo action 2", "parent_id": "ml_emi_1750697955", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-23T22:30:25.885169", "updated_at": "2025-06-23T22:30:25.885169"}, {"id": "ml_moneyview_app_1750698039", "name": "Moneyview app", "parent_id": "ml_emi_1750697955", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-23T22:30:39.329172", "updated_at": "2025-06-23T22:30:39.329172"}, {"id": "ml_kreditbee_1750698081", "name": "<PERSON><PERSON><PERSON><PERSON>", "parent_id": "ml_emi_1750697955", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-23T22:31:21.096728", "updated_at": "2025-06-23T22:31:21.096728"}, {"id": "ml_slice_1750698088", "name": "slice", "parent_id": "ml_emi_1750697955", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-23T22:31:28.732455", "updated_at": "2025-06-23T22:31:28.732455"}, {"id": "ml_mpokket_1750698100", "name": "Mpokket", "parent_id": "ml_emi_1750697955", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-23T22:31:40.008233", "updated_at": "2025-06-23T22:31:40.008233"}, {"id": "ml_truebalance_1750698110", "name": "TrueBalance", "parent_id": "ml_emi_1750697955", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-23T22:31:50.521984", "updated_at": "2025-06-23T22:31:50.521984"}, {"id": "ml_youtube_1750698136", "name": "Youtube", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-23T22:32:16.201618", "updated_at": "2025-06-23T22:32:16.201618"}, {"id": "ml_adsence_1750698143", "name": "Adsence", "parent_id": "ml_youtube_1750698136", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-23T22:32:23.558422", "updated_at": "2025-06-23T22:32:23.558422"}, {"id": "ml_setttings_1750698152", "name": "setttings", "parent_id": "ml_youtube_1750698136", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-23T22:32:32.705642", "updated_at": "2025-06-23T22:32:32.705642"}, {"id": "ml_gplinks_1750698176", "name": "gplinks", "parent_id": "ml_youtube_1750698136", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-23T22:32:56.198810", "updated_at": "2025-06-23T22:32:56.198810"}, {"id": "ml_id_sales_1750698188", "name": "id sales", "parent_id": "ml_youtube_1750698136", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-23T22:33:08.825265", "updated_at": "2025-06-23T22:33:08.825265"}, {"id": "ml_card_account_sales_1750698199", "name": "card account sales", "parent_id": "ml_youtube_1750698136", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-23T22:33:19.919949", "updated_at": "2025-06-23T22:33:19.919949"}, {"id": "ml_house_hold_expenses_1750698318", "name": "House Hold Expenses", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-23T22:35:18.226808", "updated_at": "2025-06-23T22:35:18.226808"}, {"id": "ml_daily_expenses_1750698338", "name": "Daily expenses", "parent_id": "ml_house_hold_expenses_1750698318", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-23T22:35:38.111369", "updated_at": "2025-06-23T22:35:38.111369"}, {"id": "ml_online_stores_1750698562", "name": "Online Stores", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-23T22:39:22.637956", "updated_at": "2025-06-23T22:39:22.637956"}, {"id": "ml_amazon_1750698581", "name": "amazon", "parent_id": "ml_online_stores_1750698562", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-23T22:39:41.646691", "updated_at": "2025-06-23T22:39:41.646691"}, {"id": "ml_meesho_1750698588", "name": "meesho", "parent_id": "ml_online_stores_1750698562", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-23T22:39:48.527801", "updated_at": "2025-06-23T22:39:48.527801"}, {"id": "ml_flipkart_1750698597", "name": "<PERSON><PERSON>t", "parent_id": "ml_online_stores_1750698562", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-23T22:39:57.354320", "updated_at": "2025-06-23T22:39:57.354320"}, {"id": "ml_appliances_1750698615", "name": "Appliances", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-23T22:40:15.290987", "updated_at": "2025-06-23T22:40:15.290987"}, {"id": "ml_house_hold_1750698625", "name": "House Hold", "parent_id": "ml_appliances_1750698615", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-23T22:40:25.205312", "updated_at": "2025-06-23T22:40:25.205312"}, {"id": "ml_vasanth_needs_1750698648", "name": "<PERSON><PERSON><PERSON><PERSON>s", "parent_id": "ml_appliances_1750698615", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-23T22:40:48.055809", "updated_at": "2025-06-23T22:40:48.055809"}, {"id": "ml_common_needs_1750698659", "name": "Common Needs", "parent_id": "ml_appliances_1750698615", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-23T22:40:59.909718", "updated_at": "2025-06-23T22:40:59.909718"}, {"id": "ml_initial_payment_1750698918", "name": "Initial Payment", "parent_id": "main_cat_other", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-23T22:45:18.015814", "updated_at": "2025-06-23T22:45:18.015814"}, {"id": "ml_gas_1750916189", "name": "Gas", "parent_id": "main_cat_transportation", "description": "Test gas subcategory", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-26T11:06:29.493435", "updated_at": "2025-06-26T11:06:29.493435"}, {"id": "ml_public_transit_1750916189", "name": "Public Transit", "parent_id": "main_cat_transportation", "description": "Test public transit subcategory", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-26T11:06:29.495521", "updated_at": "2025-06-26T11:06:29.495521"}, {"id": "ml_concerts_1750916189", "name": "Concerts", "parent_id": "main_cat_entertainment", "description": "Test concerts subcategory", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-26T11:06:29.497514", "updated_at": "2025-06-26T11:06:29.497514"}, {"id": "ml_swiggy_cash_limit_1750918121", "name": "Swiggy <PERSON>it", "parent_id": "ml_zomato_cash_limit_1750696607", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-26T11:38:41.731612", "updated_at": "2025-06-26T11:38:41.731612"}, {"id": "ml_salary_1750919564", "name": "Salary", "parent_id": null, "description": "Test income category", "is_active": true, "is_system": false, "color": "#4CAF50", "icon": "folder", "category_type": "income", "created_at": "2025-06-26T12:02:44.664607", "updated_at": "2025-06-26T12:02:44.664607"}, {"id": "ml_investment_returns_1750919564", "name": "Investment Returns", "parent_id": null, "description": "Test income category", "is_active": true, "is_system": false, "color": "#2196F3", "icon": "folder", "category_type": "income", "created_at": "2025-06-26T12:02:44.667607", "updated_at": "2025-06-26T12:02:44.667607"}, {"id": "ml_freelance_income_1750919564", "name": "Freelance Income", "parent_id": null, "description": "Test income category", "is_active": true, "is_system": false, "color": "#00BCD4", "icon": "folder", "category_type": "income", "created_at": "2025-06-26T12:02:44.670176", "updated_at": "2025-06-26T12:02:44.670176"}, {"id": "ml_refunds_1750919564", "name": "Refunds", "parent_id": null, "description": "Test income category", "is_active": true, "is_system": false, "color": "#8BC34A", "icon": "folder", "category_type": "income", "created_at": "2025-06-26T12:02:44.677291", "updated_at": "2025-06-26T12:02:44.677291"}, {"id": "ml_zomato_payout_1750924176", "name": "zomato payout", "parent_id": "ml_salary_1750919564", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-26T13:19:36.378426", "updated_at": "2025-06-26T13:19:36.378426"}, {"id": "ml_shared_money_1750925842", "name": "Shared Money", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-06-26T13:47:22.489070", "updated_at": "2025-06-26T22:37:47.223629"}, {"id": "ml_computer_upgrades_1750951839", "name": "Computer Upgrades", "parent_id": "ml_appliances_1750698615", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-26T21:00:39.269109", "updated_at": "2025-06-26T21:00:39.269109"}, {"id": "ml_computer_maintenance_1750951883", "name": "Computer Maintenance", "parent_id": "ml_appliances_1750698615", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-26T21:01:23.679110", "updated_at": "2025-06-26T21:01:23.679110"}, {"id": "ml_from_dad_1750954300", "name": "From Dad", "parent_id": "ml_shared_money_1750925842", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-26T21:41:40.375074", "updated_at": "2025-06-26T21:41:40.375074"}, {"id": "ml_swiggy_payout_1750957835", "name": "swiggy payout", "parent_id": "ml_salary_1750919564", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-26T22:40:35.290669", "updated_at": "2025-06-26T22:40:35.290669"}, {"id": "ml_swiggy_tips_1750957845", "name": "swiggy tips", "parent_id": "ml_salary_1750919564", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-26T22:40:45.917092", "updated_at": "2025-06-26T22:40:45.917092"}, {"id": "ml_zomato_tips_1750957855", "name": "zomato tips", "parent_id": "ml_salary_1750919564", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-26T22:40:55.558782", "updated_at": "2025-06-26T22:40:55.558782"}, {"id": "ml_shadowfax_payout_1750957871", "name": "shadowfax payout", "parent_id": "ml_salary_1750919564", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-26T22:41:11.190641", "updated_at": "2025-06-26T22:41:11.190641"}, {"id": "ml_shadow_fax_tips_1750957884", "name": "shadow fax tips", "parent_id": "ml_salary_1750919564", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-26T22:41:24.125456", "updated_at": "2025-06-26T22:41:24.125456"}, {"id": "ml_rapido_payout_1750957901", "name": "rapido payout", "parent_id": "ml_salary_1750919564", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-26T22:41:41.844282", "updated_at": "2025-06-26T22:41:41.844282"}, {"id": "ml_rapido_tips_1750957914", "name": "rapido tips", "parent_id": "ml_salary_1750919564", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-26T22:41:54.382226", "updated_at": "2025-06-26T22:41:54.382226"}, {"id": "ml_shadowfax_cashlimit_1750962518", "name": "shadowFax CashLimit", "parent_id": "ml_zomato_cash_limit_1750696607", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-26T23:58:38.999346", "updated_at": "2025-06-26T23:58:38.999346"}, {"id": "ml_from_vasanth_1751037225", "name": "From Vasanth", "parent_id": "ml_shared_money_1750925842", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-27T20:43:45.137582", "updated_at": "2025-06-27T20:43:45.137582"}, {"id": "ml_from_a<PERSON><PERSON>_1751037234", "name": "From <PERSON><PERSON><PERSON>", "parent_id": "ml_shared_money_1750925842", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-27T20:43:54.718583", "updated_at": "2025-06-27T20:43:54.718583"}, {"id": "ml_from_abirami_1751037242", "name": "From Abirami", "parent_id": "ml_shared_money_1750925842", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-27T20:44:02.310519", "updated_at": "2025-06-27T20:44:02.310519"}, {"id": "ml_lend_from_1751039357", "name": "Borrowed", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-06-27T21:19:17.009392", "updated_at": "2025-06-27T21:19:45.824784"}, {"id": "ml_from_dad_1751039411", "name": "From Dad", "parent_id": "ml_lend_from_1751039357", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-27T21:20:11.274679", "updated_at": "2025-06-27T21:20:11.274679"}, {"id": "ml_from_friends_1751039434", "name": "From Friends", "parent_id": "ml_lend_from_1751039357", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-27T21:20:34.555667", "updated_at": "2025-06-27T21:20:34.555667"}, {"id": "ml_from_gf_1751039446", "name": "From GF", "parent_id": "ml_lend_from_1751039357", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-27T21:20:46.749996", "updated_at": "2025-06-27T21:20:46.749996"}, {"id": "ml_returned_1751039458", "name": "Returned", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-27T21:20:58.290224", "updated_at": "2025-06-27T21:20:58.290224"}, {"id": "ml_to_dad_1751039468", "name": "To <PERSON>", "parent_id": "ml_returned_1751039458", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-27T21:21:08.657863", "updated_at": "2025-06-27T21:21:08.657863"}, {"id": "ml_to_friends_1751039481", "name": "To Friends", "parent_id": "ml_returned_1751039458", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-27T21:21:21.285452", "updated_at": "2025-06-27T21:21:21.285452"}, {"id": "ml_to_gf_1751039489", "name": "To GF", "parent_id": "ml_returned_1751039458", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-27T21:21:29.520274", "updated_at": "2025-06-27T21:21:29.520274"}, {"id": "ml_recharge_1751039618", "name": "Recharge", "parent_id": "main_cat_bills_&_utilities", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-27T21:23:38.358347", "updated_at": "2025-06-27T21:23:38.358347"}, {"id": "ml_lend_to_1751039767", "name": "Lend To", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-27T21:26:07.663312", "updated_at": "2025-06-27T21:26:07.663312"}, {"id": "ml_friends_1751039776", "name": "Friends", "parent_id": "ml_lend_to_1751039767", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-27T21:26:16.938269", "updated_at": "2025-06-27T21:26:16.938269"}, {"id": "ml_bro_1751039789", "name": "<PERSON><PERSON>", "parent_id": "ml_lend_to_1751039767", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-27T21:26:29.001669", "updated_at": "2025-06-27T21:26:29.001669"}, {"id": "ml_relations_1751039799", "name": "Relations", "parent_id": "ml_lend_to_1751039767", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-27T21:26:39.034368", "updated_at": "2025-06-27T21:26:39.034368"}, {"id": "ml_gf_1751039807", "name": "GF", "parent_id": "ml_lend_to_1751039767", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-27T21:26:47.384026", "updated_at": "2025-06-27T21:26:47.384026"}, {"id": "ml_intial_amount_1751039882", "name": "Intial Amount", "parent_id": "ml_emi_1750697955", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-27T21:28:02.924782", "updated_at": "2025-06-27T21:28:02.924782"}, {"id": "ml_landline_1751039952", "name": "LandLine", "parent_id": "main_cat_bills_&_utilities", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-27T21:29:12.772720", "updated_at": "2025-06-27T21:29:12.772720"}, {"id": "ml_flipkart_refunds_1751047600", "name": "flipkart refunds", "parent_id": "ml_refunds_1750919564", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-27T23:36:40.331094", "updated_at": "2025-06-27T23:36:40.331094"}, {"id": "ml_business_1751048621", "name": "Business", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-06-27T23:53:41.226465", "updated_at": "2025-06-27T23:53:41.226465"}, {"id": "ml_swiggy_restaurent_1751048632", "name": "<PERSON><PERSON><PERSON>", "parent_id": "ml_business_1751048621", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-27T23:53:52.987662", "updated_at": "2025-06-27T23:53:52.987662"}, {"id": "ml_zomato_resturent_1751048642", "name": "<PERSON><PERSON><PERSON>", "parent_id": "ml_business_1751048621", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-27T23:54:02.079840", "updated_at": "2025-06-27T23:54:02.079840"}, {"id": "ml_youtube_income_1751048663", "name": "Youtube Income", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-06-27T23:54:23.209096", "updated_at": "2025-06-27T23:54:45.715396"}, {"id": "ml_id_sales_1751048694", "name": "Id sales", "parent_id": "ml_youtube_income_1751048663", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-27T23:54:54.432130", "updated_at": "2025-06-27T23:54:54.432130"}, {"id": "ml_gp_links_1751048702", "name": "gp links", "parent_id": "ml_youtube_income_1751048663", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-27T23:55:02.794445", "updated_at": "2025-06-27T23:55:02.794445"}, {"id": "ml_adsense_1751048728", "name": "Adsense", "parent_id": "ml_youtube_income_1751048663", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-27T23:55:28.849696", "updated_at": "2025-06-27T23:55:28.849696"}, {"id": "ml_bank_intrest_1751049870", "name": "Bank Intrest", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-06-28T00:14:30.338969", "updated_at": "2025-06-28T00:14:38.701277"}, {"id": "ml_indian_bank_1751049897", "name": "Indian Bank Intrest", "parent_id": "ml_bank_intrest_1751049870", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-28T00:14:57.368388", "updated_at": "2025-06-28T00:15:27.379510"}, {"id": "ml_sbi_intrest_1751049911", "name": "SBI Intrest", "parent_id": "ml_bank_intrest_1751049870", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-28T00:15:11.869717", "updated_at": "2025-06-28T00:15:11.869717"}, {"id": "ml_snapmint_1751050231", "name": "SnapMint", "parent_id": "ml_refunds_1750919564", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-28T00:20:31.703329", "updated_at": "2025-06-28T00:20:31.703329"}, {"id": "ml_oil_1751052626", "name": "oil", "parent_id": "main_cat_shopping", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-28T01:00:26.752680", "updated_at": "2025-06-28T01:00:26.752680"}, {"id": "ml_veltech_institute_1751052683", "name": "VelTech Institute", "parent_id": "ml_coaching_1751052666", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-28T01:01:23.361720", "updated_at": "2025-06-28T01:01:23.361720"}, {"id": "ml_flipkart_paylater_1751052776", "name": "Flipkart paylater", "parent_id": "ml_emi_1750697955", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-28T01:02:56.512604", "updated_at": "2025-06-28T01:02:56.512604"}, {"id": "ml_bike_tyre_1751052856", "name": "Bike <PERSON>", "parent_id": "ml_bikes_1751052842", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-28T01:04:16.263754", "updated_at": "2025-06-28T01:04:16.263754"}, {"id": "ml_bike_spares_1751052868", "name": "bike Spares", "parent_id": "ml_bikes_1751052842", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-28T01:04:28.937602", "updated_at": "2025-06-28T01:04:28.937602"}, {"id": "ml_bike_oil_1751052883", "name": "Bike oil", "parent_id": "ml_bikes_1751052842", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-28T01:04:43.507573", "updated_at": "2025-06-28T01:04:43.507573"}, {"id": "ml_petrol_1751053099", "name": "Petrol", "parent_id": "main_cat_transportation", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-28T01:08:19.411413", "updated_at": "2025-06-28T01:08:19.411413"}, {"id": "ml_diesel_1751053109", "name": "Diesel", "parent_id": "main_cat_transportation", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-28T01:08:29.979117", "updated_at": "2025-06-28T01:08:29.979117"}, {"id": "ml_charging_station_1751053120", "name": "charging station", "parent_id": "main_cat_transportation", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-28T01:08:40.790611", "updated_at": "2025-06-28T01:08:40.790611"}, {"id": "ml_basic_setup_1751053354", "name": "basic setup", "parent_id": "ml_aquarium_1751053341", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-28T01:12:34.504792", "updated_at": "2025-06-28T01:12:34.504792"}, {"id": "ml_bank_charges_1751053627", "name": "Bank charges", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-28T01:17:07.353189", "updated_at": "2025-06-28T01:17:07.353189"}, {"id": "ml_indian_cleaning_corporation_1751053648", "name": "Indian Cleaning Corporation", "parent_id": "ml_bank_charges_1751053627", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-28T01:17:28.406347", "updated_at": "2025-06-28T01:17:28.406347"}, {"id": "ml_snapmint_1751053776", "name": "snapmint", "parent_id": "ml_emi_1750697955", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-28T01:19:36.716099", "updated_at": "2025-06-28T01:19:36.716099"}, {"id": "ml_bike_maintence_1751120197", "name": "Bike <PERSON>ce", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-28T19:46:37.084027", "updated_at": "2025-06-28T19:46:37.084027"}, {"id": "ml_r15_v3_1751120213", "name": "R15 V3", "parent_id": "ml_bike_maintence_1751120197", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-28T19:46:53.169272", "updated_at": "2025-06-28T19:46:53.169272"}, {"id": "ml_ola_s1_pro_1751120224", "name": "OLA S1 Pro", "parent_id": "ml_bike_maintence_1751120197", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-28T19:47:04.384578", "updated_at": "2025-06-28T19:47:04.384578"}, {"id": "ml_ola_s1_x_1751120237", "name": "OLA S1 X", "parent_id": "ml_bike_maintence_1751120197", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-28T19:47:17.426160", "updated_at": "2025-06-28T19:47:17.426160"}, {"id": "ml_stunner_1751120256", "name": "<PERSON><PERSON><PERSON>", "parent_id": "ml_bike_maintence_1751120197", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-28T19:47:36.170690", "updated_at": "2025-06-28T19:47:36.170690"}, {"id": "ml_school_fees_1751232937", "name": "School Fees", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-30T03:05:37.186845", "updated_at": "2025-06-30T03:05:37.186845"}, {"id": "ml_asmetha_1751232944", "name": "<PERSON><PERSON><PERSON>", "parent_id": "ml_school_fees_1751232937", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-06-30T03:05:44.869845", "updated_at": "2025-06-30T03:05:44.869845"}]