{"description": "Optimized router configuration for better AI utilization", "version": "2.0", "merchant_cache_confidence_threshold": 0.6, "ai_cost_per_transaction": 0.02, "max_ai_transactions_per_batch": 100, "high_amount_threshold": 500.0, "max_budget_per_session": 2.0, "enable_cost_optimization": true, "prioritize_high_amounts": true, "enable_aggressive_ai_routing": true, "ai_preference_multiplier": 1.5, "reduce_manual_routing": true}