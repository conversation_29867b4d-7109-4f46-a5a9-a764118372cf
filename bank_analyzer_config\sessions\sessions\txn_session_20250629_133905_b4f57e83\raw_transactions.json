[{"date": "2024-03-31", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "100.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R3", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 3, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-31", "description": "THRU UPI DEBIT UPI/************/90wnl90Kk1vmopIQMIt2 XXXXX00037/bsestarmfrzp@icici ICIC0DC0099 /IndianClearingCorporation", "amount": "-100.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R4", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 4, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-31", "description": "ONUS BNA DEP BNA SEQ NO1220 ATM ID S1C027371 TRAN DATE (MMDD) 0401 TRAN TIME (HHMMSS) 001451", "amount": "-1100.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R5", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 5, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-31", "description": "THRU UPI DEBIT UPI/************/nandrii XXXXX /deepulakshmi2050@okhdfcbank CNRB0000033/S DEEPALAKSHMI", "amount": "-100.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R6", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 6, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-31", "description": "BY UPI CREDIT UPI/************/UPI XXXXX08597 /deepulakshmi2050@okhdfcbank CNRB0000033/S DEEPALAKSHMI", "amount": "100.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R7", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 7, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-31", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /007dipan.z 1@oksbi KKBK0000958/DIPAN DIPANKAR MANDAL", "amount": "-19.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R8", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 8, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-31", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /vineethvinee62 1@okicici UBIN0905992/VINEETH V", "amount": "-85.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R9", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 9, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-31", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /shadowfax@icici ICIC0DC0099/Shadowfax", "amount": "-399.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R10", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 10, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-31", "description": "ONUS BNA DEP BNA SEQ NO1944 ATM ID HIT015271 TRAN DATE (MMDD) 0331 TRAN TIME (HHMMSS) 001913", "amount": "-500.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R11", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 11, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-30", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-500.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R12", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 12, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-30", "description": "ONUS BNA DEP BNA SEQ NO2632 ATM ID S1C015272 TRAN DATE (MMDD) 0330 TRAN TIME (HHMMSS) 162755", "amount": "-500.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R13", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 13, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-30", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /paytmqr7c322tf38b@paytm YESB0PTMUPI/VANJULA VALLI", "amount": "-20.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R14", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 14, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-30", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /kalassri1984@oksbi SBIN0010482/SRIRAM S", "amount": "-900.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R15", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 15, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-29", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-180.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R16", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 16, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-29", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-380.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R17", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 17, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-29", "description": "ONUS BNA DEP BNA SEQ NO2355 ATM ID S1C015272 TRAN DATE (MMDD) 0329 TRAN TIME (HHMMSS) 211332", "amount": "-1400.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R18", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 18, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-29", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-450.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R19", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 19, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-29", "description": "BY UPI CREDIT UPI/************/UPI XXXXX08597 /deepulakshmi2050@okhdfcbank CNRB0000033/S DEEPALAKSHMI", "amount": "500.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R20", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 20, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-29", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-180.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R21", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 21, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-29", "description": "BY UPI CREDIT UPI/************/UPI XXXXX27753 /sivarishi17@oksbi SBIN0070009/B <PERSON><PERSON>shore", "amount": "208.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R22", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 22, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-29", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-40.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R23", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 23, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-29", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /raj<PERSON><PERSON>lipu7@okicici IDIB000P134/Mr RAJIB KUMAR PAL", "amount": "-240.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R24", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 24, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-29", "description": "BY UPI CREDIT UPI/************/Rewarded for doing a XXXXX22222/goog payment@okaxis UTIB0000553/GOOGLEPAY", "amount": "1.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R25", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 25, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-29", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /euronetgpay. rch@icici ICIC0DC0099/EURONETGPAY", "amount": "-270.9", "balance": null, "transaction_type": "DEBIT", "reference_number": "R26", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 26, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-29", "description": "BY UPI CREDIT UPI/************/UPI XXXXX98977 /muthustr94@okaxis IDIB000P042/Mr <PERSON>u", "amount": "20.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R27", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 27, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-28", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-140.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R28", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 28, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-28", "description": "BY UPI CREDIT UPI/************/UPI XXXXX33887/katkarrajat10 1@okhdfcbank HDFC0009352/RAJAT RAMESH KATKAR", "amount": "254.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R29", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 29, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-28", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-50.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R30", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 30, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-28", "description": "ONUS BNA DEP BNA SEQ NO9171 ATM ID S1C027371 TRAN DATE (MMDD) 0328 TRAN TIME (HHMMSS) 001351", "amount": "-500.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R31", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 31, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-27", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /swiggy. ********@icici ICIC0DC0099/swiggyupi", "amount": "-6.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R32", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 32, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-26", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /kalassri1984@oksbi SBIN0010482/SRIRAM S", "amount": "-1100.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R33", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 33, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-26", "description": "ONUS BNA DEP BNA SEQ NO4985 ATM ID S1T014151 TRAN DATE (MMDD) 0326 TRAN TIME (HHMMSS) 202821", "amount": "-200.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R34", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 34, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-26", "description": "ONUS BNA DEP BNA SEQ NO4983 ATM ID S1T014151 TRAN DATE (MMDD) 0326 TRAN TIME (HHMMSS) 202717", "amount": "-500.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R35", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 35, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-26", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /gpaybillpay. rchrg@okpayaxis UTIB0000553/Google India Digital Services", "amount": "-200.9", "balance": null, "transaction_type": "DEBIT", "reference_number": "R36", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 36, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-26", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /v.elumalai.abi 1@oksbi IDIB000P152/Mr V ELUMALAI", "amount": "-100.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R37", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 37, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-26", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /q987755226@ybl YESB0YBLUPI/MOHAMED SYED", "amount": "-30.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R38", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 38, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-26", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "700.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R39", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 39, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-25", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /v.elumalai.abi 1@oksbi IDIB000P152/Mr V ELUMALAI", "amount": "-150.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R40", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 40, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-25", "description": "BY UPI CREDIT UPI/************/UPI XXXXX66316 /ambika05mom@okicici PSIB0021233/Jayaprakash V", "amount": "167.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R41", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 41, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-25", "description": "POS PRCH POS TXN SEQ NO ************ POS ID ******** Richman PONDICH DATE (MMDD) 0325 TIME (HHMMSS) 192350", "amount": "-790.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R42", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 42, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-25", "description": "BY UPI CREDIT UPI/************/Payment from PhonePe XXXXX34269/vasanthansbi01@ybl SBIN0070601/VASANTHAN", "amount": "126.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R43", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 43, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-25", "description": "BY TRANSFER NEFT/CITI/CITIN24441838875 /ZOMATO LIMIT/ TRANSFER FROM ***********", "amount": "-608.11", "balance": null, "transaction_type": "DEBIT", "reference_number": "R44", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 44, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-25", "description": "BY TRANSFER NEFT/CITI/CITIN24441745121 /ZOMATO LIMIT/ TRANSFER FROM ***********", "amount": "-30.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R45", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 45, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-24", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /v.elumalai.abi 1@oksbi IDIB000P152/Mr V ELUMALAI", "amount": "-400.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R46", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 46, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-24", "description": "BY UPI CREDIT UPI/************/poda venna XXXXX08597 /deepulakshmi2050@okhdfcbank CNRB0000033/S DEEPALAKSHMI", "amount": "400.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R47", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 47, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-23", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /q987755226@ybl YESB0YBLUPI/MOHAMED SYED", "amount": "-30.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R48", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 48, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-23", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /<EMAIL> sbin0070601/Mr VASANTHAN", "amount": "-300.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R49", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 49, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-22", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /deepulakshmi2050@okhdfcbank CNRB0000033/S DEEPALAKSHMI", "amount": "-500.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R50", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 50, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-22", "description": "ONUS BNA DEP BNA SEQ NO8310 ATM ID HIT015271 TRAN DATE (MMDD) 0322 TRAN TIME (HHMMSS) 203248", "amount": "-700.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R51", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 51, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-22", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-400.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R52", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 52, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-22", "description": "BY UPI CREDIT UPI/************/UPI XXXXX08597 /deepulakshmi2050@okhdfcbank CNRB0000033/S DEEPALAKSHMI", "amount": "500.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R53", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 53, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-21", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-630.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R54", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 54, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-21", "description": "BY UPI CREDIT UPI/************/UPI XXXXX21294 /monishinr@oksbi IDIB000T070/Mr <PERSON>", "amount": "704.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R55", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 55, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-21", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-110.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R56", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 56, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-21", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "35.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R57", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 57, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-21", "description": "BY TRANSFER /IMPS/P2A/************/I47448770546/ZOMATO LIMIT TRANSFER FROM ***********", "amount": "-15.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R58", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 58, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-20", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /paytm ********@paytm YESB0PTMUPI/NRTraders", "amount": "-180.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R59", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 59, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-20", "description": "BY UPI CREDIT UPI/************/UPI XXXXX08597 /deepulakshmi2050@okhdfcbank CNRB0000033/S DEEPALAKSHMI", "amount": "200.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R60", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 60, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-20", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-40.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R61", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 61, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-20", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-79.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R62", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 62, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-20", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-40.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R63", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 63, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-20", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-165.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R64", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 64, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-20", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "200.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R65", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 65, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-20", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@oksbi SBIN0016563/YOGESH H", "amount": "-2000.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R66", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 66, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-20", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /balaramanmadhavan28@okicici UBIN0904422/MADHAVAN B", "amount": "-1000.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R67", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 67, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-20", "description": "ONUS BNA DEP BNA SEQ NO3050 ATM ID S1T014151 TRAN DATE (MMDD) 0320 TRAN TIME (HHMMSS) 155259", "amount": "-3000.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R68", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 68, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-20", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /gpay ***********@okbizaxis UTIB0000000/VILORA TECHNOLOGIES", "amount": "-60.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R69", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 69, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-20", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-894.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R70", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 70, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-20", "description": "BY UPI CREDIT UPI/************/UPI XXXXX69717 /balaramanmadhavan28@okicici UBIN0904422/MADHAVAN B", "amount": "1000.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R71", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 71, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-19", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-150.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R72", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 72, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-19", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "250.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R73", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 73, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-19", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /paytm ********@paytm YESB0PTMUPI/Lakshmi Narayana Agency", "amount": "-300.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R74", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 74, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-18", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /deepulakshmi2050@okhdfcbank CNRB0000033/S DEEPALAKSHMI", "amount": "-250.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R75", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 75, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-18", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /sreearavindh1@okaxis KVBL0001636/ARAVINDH S", "amount": "-500.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R76", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 76, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-18", "description": "ONUS BNA DEP BNA SEQ NO6435 ATM ID HIT015271 TRAN DATE (MMDD) 0318 TRAN TIME (HHMMSS) 223416", "amount": "-600.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R77", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 77, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-18", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-220.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R78", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 78, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-18", "description": "BY UPI CREDIT UPI/************/UPI XXXXX08597 /deepulakshmi2050@okhdfcbank CNRB0000033/S DEEPALAKSHMI", "amount": "250.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R79", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 79, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-18", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-200.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R80", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 80, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-18", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "250.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R81", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 81, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-18", "description": "BY TRANSFER NEFT/CITI/CITIN24439434314 /ZOMATO LIMIT/ TRANSFER FROM ***********", "amount": "-241.48", "balance": null, "transaction_type": "DEBIT", "reference_number": "R82", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 82, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-18", "description": "BY TRANSFER NEFT/CITI/CITIN24439077953 /ZOMATO LIMIT/ TRANSFER FROM ***********", "amount": "-15.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R83", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 83, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-18", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /euronetgpay. broadband@icici ICIC0DC0099/EURONETGPAY", "amount": "-478.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R84", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 84, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-18", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "500.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R85", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 85, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-16", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-200.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R86", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 86, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-16", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "200.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R87", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 87, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-16", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /zaranilofar@oksbi FDRL0001394/NILOFAR .", "amount": "-49.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R88", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 88, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-15", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-200.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R89", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 89, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-15", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "200.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R90", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 90, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-15", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-200.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R91", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 91, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-15", "description": "ONUS ATM WDL ATM WDL SEQ NO 5394 ATM ID S1DR0762 SELF MOOLAKULAM OFFSITE PONDICHERRY TRAN DATE (MMDD) 0315 TRAN TIME (HHMMSS) 171426", "amount": "-500.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R92", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 92, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-15", "description": "BY TRANSFER /IMPS/P2A/************/I47434734002/ZOMATO LIMIT TRANSFER FROM ***********", "amount": "-20.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R93", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 93, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-15", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /balaramanmadhavan28@okicici UBIN0904422/MADHAVAN B", "amount": "-500.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R94", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 94, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-14", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /kalassri1984@oksbi SBIN0010482/SRIRAM S", "amount": "-400.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R95", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 95, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-14", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /**********@okbizaxis UTIB0000000/NITHYASRI STORE", "amount": "-20.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R96", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 96, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-14", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /paytmqr4hx2pf7m48@paytm PYTM0123456/SRI Bharath Sweet and Bakery", "amount": "-15.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R97", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 97, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-14", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /gpay ***********@okbizaxis UTIB0000000/Matha Store", "amount": "-22.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R98", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 98, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-14", "description": "THRU UPI DEBIT UPI/************/UHVTBVbv6dcEDeKJzyIS XXXXX56987/zerodhamf@hdfcbank HDFC0000060/ICCL ZERODHA COIN", "amount": "-100.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R99", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 99, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-14", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /gpaybillpay. insur@okpayaxis UTIB0000553/Google India Digital Services", "amount": "-513.3", "balance": null, "transaction_type": "DEBIT", "reference_number": "R100", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 100, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-14", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /balaramanmadhavan28@okicici UBIN0904422/MADHAVAN B", "amount": "-500.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R101", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 101, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-14", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /gpaybillpay. insur@okpayaxis UTIB0000553/Google India Digital Services", "amount": "-2225.02", "balance": null, "transaction_type": "DEBIT", "reference_number": "R102", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 102, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-14", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "2900.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R103", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 103, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-12", "description": "BY UPI CREDIT UPI/************/UPI XXXXX01641 /05lingaeswaran2003 2@okhdfcbank HDFC0008765 /LINGAESWARAN S", "amount": "1000.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R104", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 104, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-12", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /paytmqr2810050501017mkvrmcggwdf@paytm PYTM0123456 /Akshaya Enterprises", "amount": "-100.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R105", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 105, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-12", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "200.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R106", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 106, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-11", "description": "THRU UPI DEBIT UPI/************/Pay to BharatPe Merc XXXXX /bharatpe09916629172@yesbankltd YESB0YESUPI/MS UDHAYA COMPUTERS", "amount": "-400.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R107", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 107, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-11", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "500.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R108", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 108, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-11", "description": "BY TRANSFER NEFT/CITI/CITIN24436596935 /ZOMATO LIMIT/ TRANSFER FROM ***********", "amount": "-30.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R109", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 109, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-11", "description": "BY TRANSFER NEFT/CITI/CITIN24436635575 /ZOMATO LIMIT/ TRANSFER FROM ***********", "amount": "-974.32", "balance": null, "transaction_type": "DEBIT", "reference_number": "R110", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 110, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-11", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /madanrmaddy2121997@okaxis KKBK0008955/MADANKUMAR", "amount": "-1500.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R111", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 111, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-11", "description": "BY UPI CREDIT UPI/************/UPI XXXXX69717 /balaramanmadhavan28@okicici UBIN0904422/MADHAVAN B", "amount": "1499.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R112", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 112, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-10", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /q810178903@ybl YESB0YBLUPI/Mr SILUVAINATHAN S", "amount": "-200.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R113", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 113, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-09", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /madhanmadhan2068@okhdfcbank KKBK0008780/MADHAN K", "amount": "-400.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R114", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 114, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-09", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /pvigneshwaran2102@oksbi SBIN0010507/P <PERSON>n", "amount": "-2900.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R115", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 115, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-09", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "3500.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R116", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 116, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-09", "description": "BY UPI CREDIT UPI/************/UPI XXXXX37318 /rameshsaratha190@oksbi SBIN0006842/RAMESH R", "amount": "25.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R117", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 117, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-08", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /balaramanmadhavan28@okicici UBIN0904422/MADHAVAN B", "amount": "-2000.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R118", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 118, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-08", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "2000.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R119", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 119, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-08", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /mohamedbarithpocom3@oksbi IOBA0000894/HAIRUNNISHA M", "amount": "-20.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R120", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 120, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-08", "description": "BY TRANSFER /IMPS/P2A/************/I47418855692/ZOMATO LIMIT TRANSFER FROM ***********", "amount": "-10.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R121", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 121, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-07", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-570.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R122", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 122, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-07", "description": "BY UPI CREDIT UPI/************/UPI XXXXX23133 /jansiruku91@okaxis IDIB000M054/Ms G RUKMANI", "amount": "570.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R123", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 123, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-07", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /paytmqr281005050101tvhf2i3bnm4a@paytm PYTM0123456/C A K TYRES", "amount": "-3800.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R124", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 124, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-07", "description": "BY UPI CREDIT UPI/************/Payment from PhonePe XXXXX34269/vasanthansbi01@ybl SBIN0070601/VASANTHAN", "amount": "1700.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R125", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 125, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-07", "description": "BY UPI CREDIT UPI/************/UPI XXXXX69717 /balaramanmadhavan28@okicici UBIN0904422/MADHAVAN B", "amount": "2000.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R126", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 126, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-07", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /thanusrii2004@okhdfcbank SBIN0007314/NITHYA R", "amount": "-17.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R127", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 127, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-07", "description": "THRU UPI DEBIT UPI/************/z82Fsvmdh3cm0hjv75HM XXXXX00037/bsestarmfrzp@icici ICIC0DC0099 /IndianClearingCorporation", "amount": "-100.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R128", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 128, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-07", "description": "THRU UPI DEBIT UPI/************/7KD01Uup4j0uXYpyXVfA XXXXX56987/zerodhamf@hdfcbank HDFC0000060/ICCL ZERODHA COIN", "amount": "-100.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R129", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 129, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-07", "description": "BY UPI CREDIT UPI/************/Payment from PhonePe XXXXX34269/vasanthansbi01@ybl SBIN0070601/VASANTHAN", "amount": "300.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R130", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 130, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-07", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /euronetgpay. rch@icici ICIC0DC0099/EURONETGPAY", "amount": "-19.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R131", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 131, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-06", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /paytmqr1vqno434e2@paytm PYTM0123456/SHAMSUDEEN K", "amount": "-90.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R132", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 132, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-06", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /q331977409@ybl YESB0YBLUPI/PADMANATHAN T", "amount": "-10.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R133", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 133, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-06", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /pvigneshwaran2102@oksbi SBIN0010507/P <PERSON>n", "amount": "-900.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R134", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 134, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-06", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /kkamataj 1@oksbi SBIN0009584/Ka<PERSON>j <PERSON>", "amount": "-100.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R135", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 135, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-05", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /deepulakshmi2050@okhdfcbank CNRB0000033/S DEEPALAKSHMI", "amount": "-100.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R136", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 136, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-05", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /paytmqr7c322tf38b@paytm PYTM0123456/VANJULA VALLI", "amount": "-10.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R137", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 137, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-05", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /paytmqr7c322tf38b@paytm PYTM0123456/VANJULA VALLI", "amount": "-20.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R138", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 138, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-05", "description": "BY UPI CREDIT UPI/************/GPlinks Payments XXXXX92995 /poweraccess.cashfree@axisbank utib0001920/Cashfree Payments", "amount": "439.54", "balance": null, "transaction_type": "CREDIT", "reference_number": "R139", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 139, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-05", "description": "THRU UPI DEBIT UPI/************/OzFP2M3CwtKOTlnTgl5u XXXXX00037/bsestarmfrzp@icici ICIC0DC0099 /IndianClearingCorporation", "amount": "-100.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R140", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 140, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-04", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /v.elumalai.abi 1@oksbi IDIB000P152/Mr V ELUMALAI", "amount": "-400.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R141", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 141, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-04", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /v.elumalai.abi 1@oksbi IDIB000P152/Mr V ELUMALAI", "amount": "-3000.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R142", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 142, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-04", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /q731156745@ybl YESB0YBLUPI/Mr <PERSON>", "amount": "-206.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R143", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 143, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-04", "description": "ONUS BNA DEP BNA SEQ NO6959 ATM ID S1C027371 TRAN DATE (MMDD) 0304 TRAN TIME (HHMMSS) 214845", "amount": "-3000.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R144", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 144, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-04", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /<EMAIL> sbin0070601/Mr VASANTHAN", "amount": "-1000.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R145", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 145, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-04", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /<EMAIL> sbin0070601/Mr VASANTHAN", "amount": "-1000.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R146", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 146, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-04", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /<EMAIL> sbin0070601/Mr VASANTHAN", "amount": "-1000.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R147", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 147, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-04", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /v.elumalai.abi 1@oksbi IDIB000P152/Mr V ELUMALAI", "amount": "-5000.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R148", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 148, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-04", "description": "BY TRANSFER /IMPS/P2A/************/ /P2AMOB /LINGAESWARAN TRANSFER FROM ***********", "amount": "-9000.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R149", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 149, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-04", "description": "BY TRANSFER NEFT/CITI/CITIN24433218336 /ZOMATO LIMIT/ TRANSFER FROM ***********", "amount": "-287.82", "balance": null, "transaction_type": "DEBIT", "reference_number": "R150", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 150, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-04", "description": "THRU UPI DEBIT UPI/************/GCMQcIvkCsdRQRZUX7ki XXXXX00037/bsestarmfrzp@icici ICIC0DC0099 /IndianClearingCorporation", "amount": "-10.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R151", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 151, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-04", "description": "THRU UPI DEBIT UPI/************/3renz7yX0DF8su5fsr3n XXXXX00037/bsestarmfrzp@icici ICIC0DC0099 /IndianClearingCorporation", "amount": "-10.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R152", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 152, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-03", "description": "BY TRANSFER /IMPS/P2A/************/I47408240143/ZOMATO LIMIT TRANSFER FROM ***********", "amount": "-277.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R153", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 153, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-03", "description": "BY TRANSFER /IMPS/P2A/************/ /PennyDrop/INDIAN CLEAR TRANSFER FROM ***********", "amount": "-1.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R154", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 154, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-03", "description": "THRU UPI DEBIT UPI/************/GRsTxPJo8205pp86iv7p XXXXX00037/bsestarmfrzp@icici ICIC0DC0099 /IndianClearingCorporation", "amount": "-10.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R155", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 155, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-03", "description": "THRU UPI DEBIT UPI/************/JcuujTxzzYI8kPWrFmA4 XXXXX56987/zerodhamf@hdfcbank HDFC0000060/ICCL ZERODHA COIN", "amount": "-50.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R156", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 156, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-03", "description": "THRU UPI DEBIT UPI/************/DTsIhig9zEF3IG7aaZ2G XXXXX56987/zerodhamf@hdfcbank HDFC0000060/ICCL ZERODHA COIN", "amount": "-10.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R157", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 157, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-02", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /gpaybillpay. rchrg@okpayaxis UTIB0000553/Google India Digital Services", "amount": "-270.9", "balance": null, "transaction_type": "DEBIT", "reference_number": "R158", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 158, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-02", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "200.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R159", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 159, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-02", "description": "THRU UPI DEBIT UPI/************/UPIIntent XXXXX /idfcbankloanrepayment.payu@hdfcbank HDFC0000499/WWW IDFCBANK COM", "amount": "-972.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R160", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 160, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-02", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /deepulakshmi2050@okhdfcbank CNRB0000033/S DEEPALAKSHMI", "amount": "-1000.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R161", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 161, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-02", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /deepulakshmi2050@okhdfcbank CNRB0000033/S DEEPALAKSHMI", "amount": "-1000.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R162", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 162, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-02", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /deepulakshmi2050@okhdfcbank CNRB0000033/S DEEPALAKSHMI", "amount": "-2000.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R163", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 163, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-02", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /deepulakshmi2050@okhdfcbank CNRB0000033/S DEEPALAKSHMI", "amount": "-2000.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R164", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 164, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-02", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /deepulakshmi2050@okhdfcbank CNRB0000033/S DEEPALAKSHMI", "amount": "-2000.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R165", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 165, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-02", "description": "ONUS BNA DEP BNA SEQ NO5076 ATM ID S1C027371 TRAN DATE (MMDD) 0302 TRAN TIME (HHMMSS) 102432", "amount": "-9000.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R166", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 166, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-01", "description": "BY TRANSFER /IMPS/P2A/************/IMPS transac/ZERODHA BROK TRANSFER FROM ***********", "amount": "-1.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R167", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 167, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-01", "description": "THRU UPI DEBIT UPI/************/Zerodha account open XXXXX56987/zerodhabrokingsignup@hdfcbank HDFC0000523 /ZERODHA BROKING LTD", "amount": "-200.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R168", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 168, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-03-01", "description": "BY TRANSFER /IMPS/P2A/************/I47403861852/ZOMATO LIMIT TRANSFER FROM ***********", "amount": "-200.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R169", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 169, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-29", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /euronetgpay. broadband@icici ICIC0DC0099/EURONETGPAY", "amount": "-888.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R170", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 170, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-29", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /pascalraj16@okhdfcbank IOBA0000070/A R PASCAL RAJ", "amount": "-6774.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R171", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 171, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-29", "description": "THRU UPI DEBIT UPI/************/Upi Transaction XXXXX56987 /olaelectric.payu@hdfcbank HDFC0000499/OLAELECTRIC COM", "amount": "-999.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R172", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 172, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-29", "description": "BY UPI CREDIT UPI/************/UPI XXXXX08597 /deepulakshmi2050@okhdfcbank CNRB0000033/S DEEPALAKSHMI", "amount": "8000.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R173", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 173, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-28", "description": "BY TRANSFER /IMPS/P2A/************/I47400158544/ZOMATO LIMIT TRANSFER FROM ***********", "amount": "-20.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R174", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 174, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-28", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /paytmqrg0g6b7x2pv@paytm PYTM0123456/SELVAKUMAR JEEVA", "amount": "-180.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R175", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 175, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-28", "description": "THRU UPI DEBIT UPI/************/UPI Intent XXXXX /meesho. paytm@hdfcbank HDFC0000499/FASHNEAR TECHNOLOGIES PRIVATE", "amount": "-122.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R176", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 176, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-28", "description": "BY UPI CREDIT UPI/************/UPI XXXXX57133 /atk23592@okicici UBIN0830054/PURUSHOTHAMAN A", "amount": "600.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R177", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 177, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-28", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "90.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R178", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 178, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-27", "description": "BY UPI CREDIT UPI/************/hh XXXXX25619 /ahamedfarish3@okhdfcbank IDIB000K049/Mr BARISH AHAMED F", "amount": "98.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R179", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 179, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-27", "description": "BY UPI CREDIT UPI/************/UPI XXXXX18612 /ramyaparlor701@oksbi SBIN0019073/Subha Shree", "amount": "250.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R180", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 180, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-27", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /euronetgpay. rch@icici ICIC0DC0099/EURONETGPAY", "amount": "-10.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R181", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 181, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-27", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V ELUMALAI", "amount": "31.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R182", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 182, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-25", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /balaramanmadhavan28 1@okhdfcbank SBIN0070601/VASANTHAN .", "amount": "-3.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R183", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 183, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-25", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-40.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R184", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 184, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-24", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-320.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R185", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 185, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-24", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /euronetgpay. pay@icici ICIC0DC0099/EURONETGPAY", "amount": "-10.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R186", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 186, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-24", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /v.elumalai. abi@okicici IDIB000P152/Mr V ELUMALAI", "amount": "-200.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R187", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 187, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-24", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /euronetgpay. rch@icici ICIC0DC0099/EURONETGPAY", "amount": "-19.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R188", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 188, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-23", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /shefalijain446@oksbi SBIN0000900/SHEFALI .", "amount": "-59.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R189", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 189, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-22", "description": "BY TRANSFER /IMPS/P2A/************/I47386930136/ZOMATO LIMIT TRANSFER FROM ***********", "amount": "-300.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R190", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 190, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-22", "description": "BY TRANSFER /IMPS/P2A/************/I47386929955/ZOMATO LIMIT TRANSFER FROM ***********", "amount": "-15.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R191", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 191, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-21", "description": "BY UPI CREDIT UPI/************/UPI XXXXX77570/ginoo. gi@oksbi SBIN0002238/GINU P RAJU .", "amount": "300.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R192", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 192, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-21", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /v.elumalai. abi@okicici IDIB000P152/Mr V ELUMALAI", "amount": "-500.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R193", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 193, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-20", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /**********@okbizaxis UTIB0000000/NITHYASRI STORE", "amount": "-85.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R194", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 194, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-20", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /paytmqr6sz2vihcsv@paytm PYTM0123456/SILUVAINATHAN S", "amount": "-130.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R195", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 195, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-19", "description": "BY TRANSFER NEFT/CITI/CITIN24424220102 /ZOMATO LIMIT/ TRANSFER FROM ***********", "amount": "-751.12", "balance": null, "transaction_type": "DEBIT", "reference_number": "R196", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 196, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-19", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /stephenrajraas 1@oksbi SBIN0011054/STEPHENRAJ S", "amount": "-525.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R197", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 197, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-18", "description": "BY TRANSFER /IMPS/P2A/************/I47377730339/ZOMATO LIMIT TRANSFER FROM ***********", "amount": "-30.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R198", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 198, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-18", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /**********@axl SBIN0010507/D Keerthana", "amount": "-26.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R199", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 199, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-18", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /paytmqr2810050501011b82p6jqjtob@paytm PYTM0123456/Shri Balaji Agencies", "amount": "-200.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R200", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 200, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-18", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V ELUMALAI", "amount": "200.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R201", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 201, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-18", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /revanthrr0252@okicici IDIB000P209/revanthrr0252@okicici", "amount": "-100.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R202", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 202, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-17", "description": "BY UPI CREDIT UPI/************/UPI XXXXX31609 /madhanmadhan2068@okhdfcbank KKBK0008780/MADHAN K", "amount": "600.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R203", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 203, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-17", "description": "BY TRANSFER /IMPS/P2A/************/I47375331738/ZOMATO LIMIT TRANSFER FROM ***********", "amount": "-20.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R204", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 204, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-17", "description": "THRU UPI DEBIT UPI/************/Food Ordering XXXXX17671 /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-400.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R205", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 205, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-17", "description": "BY UPI CREDIT UPI/************/UPI XXXXX08597 /deepulakshmi2050@okhdfcbank CNRB0000033/S DEEPALAKSHMI", "amount": "180.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R206", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 206, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-16", "description": "BY UPI CREDIT UPI/************/UPI XXXXX43934 /boscoking8011@okicici IDIB000M070/Master Bosco Raj", "amount": "200.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R207", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 207, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-16", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /mugumugundaram507@okicici IDIB000L007 /mugumugundaram507@okicici", "amount": "-500.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R208", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 208, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-14", "description": "BY UPI CREDIT UPI/************/UPI XXXXX77722 /kalassri1984@oksbi SBIN0010482/SRIRAM S", "amount": "500.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R209", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 209, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-14", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /revanthrr0252@okicici IDIB000P209/Mr M <PERSON>anth", "amount": "-1500.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R210", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 210, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-14", "description": "BY TRANSFER /IMPS/P2A/************/I47368359709/ZOMATO LIMIT TRANSFER FROM ***********", "amount": "-70.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R211", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 211, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-14", "description": "BY UPI CREDIT UPI/************/UPI XXXXX91759 /blackloverz333@okhdfcbank IDIB000P152/Mrs E Abirami", "amount": "100.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R212", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 212, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-13", "description": "ONUS BNA DEP BNA SEQ NO8849 ATM ID HIT015271 TRAN DATE (MMDD) 0213 TRAN TIME (HHMMSS) 222047", "amount": "-500.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R213", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 213, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-13", "description": "ONUS BNA DEP BNA SEQ NO8839 ATM ID HIT015271 TRAN DATE (MMDD) 0213 TRAN TIME (HHMMSS) 220739", "amount": "-700.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R214", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 214, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-13", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /deepulakshmi2050@okhdfcbank CNRB0000033/S DEEPALAKSHMI", "amount": "-50.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R215", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 215, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-13", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /jagansundar0. 0001@okicici SBIN0007545/Jagansundar M", "amount": "-200.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R216", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 216, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-12", "description": "BY TRANSFER NEFT/CITI/CITIN24421347185 /ZOMATO LIMIT/ TRANSFER FROM ***********", "amount": "-248.86", "balance": null, "transaction_type": "DEBIT", "reference_number": "R217", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 217, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-11", "description": "THRU UPI DEBIT UPI/************/UPIIntent XXXXX /idfcfirstbank.payu@mairtel AIRP0000001/IDFC FIRST BANK LIMITED", "amount": "-498.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R218", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 218, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-11", "description": "ONUS BNA DEP BNA SEQ NO7137 ATM ID HIT015271 TRAN DATE (MMDD) 0211 TRAN TIME (HHMMSS) 164202", "amount": "-500.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R219", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 219, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-11", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /paytmqr281005050101gek6lugn31vz@paytm PYTM0123456/SAI VIKA AGENCIES", "amount": "-100.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R220", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 220, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-11", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-450.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R221", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 221, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-11", "description": "BY UPI CREDIT UPI/************/UPI XXXXX10372 /marcellia2612@okhdfcbank IOBA0001516/GABRIEL DUSSOL A", "amount": "500.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R222", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 222, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-11", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-500.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R223", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 223, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-11", "description": "BY UPI CREDIT UPI/************/UPI XXXXX18612 /ramyaparlor701@oksbi SBIN0019073/Subha Shree", "amount": "700.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R224", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 224, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-11", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-200.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R225", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 225, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-11", "description": "BY UPI CREDIT UPI/************/UPI XXXXX50265 /shyaamanburaj@okhdfcbank SBIN0012798/SHYAM A", "amount": "200.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R226", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 226, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-10", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia@icici ICIC0DC0099/RazorpayZomato", "amount": "-350.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R227", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 227, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-10", "description": "BY UPI CREDIT UPI/************/UPI XXXXX08597 /deepulakshmi2050@okicici CNRB0000033/S DEEPALAKSHMI", "amount": "350.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R228", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 228, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-09", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-247.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R229", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 229, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-09", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /thalapathygokul2407@okicici IDIB000V022/Mr <PERSON>", "amount": "-50.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R230", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 230, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-08", "description": "BY UPI CREDIT UPI/************/UPI XXXXX57133 /atk23592@okicici UBIN0830054/PURUSHOTHAMAN A", "amount": "100.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R231", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 231, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-08", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-300.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R232", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 232, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-07", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /paytmqr14k7til2mg@paytm PYTM0123456/NITHYASRI STORE", "amount": "-42.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R233", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 233, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-07", "description": "BY TRANSFER /IMPS/P2A/************/I47353717989/ZOMATO LIMIT TRANSFER FROM ***********", "amount": "-20.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R234", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 234, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-07", "description": "BY TRANSFER NEFT/YESB/YESB40378683780 /BUNDL TECHNO/ TRANSFER FROM ***********", "amount": "-424.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R235", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 235, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-06", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia@icici ICIC0DC0099/RazorpayZomato", "amount": "-500.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R236", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 236, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-06", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /abdullaking303 1@okhdfcbank PYTM0123456/SHEIKABDULLA", "amount": "-50.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R237", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 237, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-06", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /blackloverz333@okhdfcbank IDIB000P152/Mrs E Abirami", "amount": "-50.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R238", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 238, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-06", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /paytmqr18vypuo0sw@paytm PYTM0123456/IRS SYSTEMS", "amount": "-750.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R239", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 239, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-06", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /paytmqr2810050501011xppfh9tglgz@paytm PYTM0123456/Shri Balaji Agencies", "amount": "-100.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R240", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 240, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-05", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /paytmqr2810050501012g58kk703upk@paytm PYTM0123456 /ESSKAY STORE", "amount": "-112.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R241", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 241, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-05", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V ELUMALAI", "amount": "120.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R242", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 242, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-05", "description": "BY TRANSFER NEFT/CITI/CITIN24418445476 /ZOMATO LIMIT/ TRANSFER FROM ***********", "amount": "-30.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R243", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 243, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-05", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V ELUMALAI", "amount": "140.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R244", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 244, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-05", "description": "BY TRANSFER NEFT/CITI/CITIN24418222698 /ZOMATO LIMIT/ TRANSFER FROM ***********", "amount": "-1345.61", "balance": null, "transaction_type": "DEBIT", "reference_number": "R245", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 245, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-05", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /v.elumalai. abi@okicici IDIB000P152/Mr V ELUMALAI", "amount": "-150.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R246", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 246, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-04", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /paytmqr2810050501014tow8wvdw3h6@paytm PYTM0123456/Shri Balaji Agencies", "amount": "-50.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R247", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 247, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-04", "description": "BY UPI CREDIT UPI/************/UPI XXXXX08597 /deepulakshmi2050@okhdfcbank CNRB0000033/S DEEPALAKSHMI", "amount": "50.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R248", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 248, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-04", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /paytmqr2810050501011gljtg40ecop@paytm PYTM0123456/J A AGENCIES", "amount": "-100.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R249", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 249, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-04", "description": "BY TRANSFER /IMPS/P2A/************/I47346475842/ZOMATO LIMIT TRANSFER FROM ***********", "amount": "-30.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R250", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 250, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-04", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia@icici ICIC0DC0099/RazorpayZomato", "amount": "-600.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R251", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 251, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-04", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /paytmqrzwm016a0ey@paytm PYTM0123456/Badmavathy Agencies", "amount": "-100.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R252", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 252, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-03", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /abdullaking303 1@oksbi PYTM0123456/SHEIKABDULLA", "amount": "-50.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R253", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 253, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-03", "description": "BY UPI CREDIT UPI/************/UPI XXXXX91575 /adilsdjd@okaxis CNRB0000033/ADIL SAJID", "amount": "50.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R254", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 254, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-03", "description": "BY UPI CREDIT UPI/************/UPI XXXXX35070 /saravanatnpoorvika@okaxis KKBK0000463/SARAVANAKUMAR MATHIYAS", "amount": "900.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R255", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 255, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-03", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /euronetgpay. rch@icici ICIC0DC0099/EURONETGPAY", "amount": "-240.9", "balance": null, "transaction_type": "DEBIT", "reference_number": "R256", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 256, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-03", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /v.elumalai. abi@okicici IDIB000P152/v.elumalai.abi@okicici", "amount": "-10100.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R257", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 257, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-02", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-300.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R258", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 258, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-02", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /y.anuk22.ay@ibl UBIN0907146/ANURADHA YADAV", "amount": "-38.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R259", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 259, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-02", "description": "BY UPI CREDIT UPI/************/Payment from PhonePe XXXXX34269/vasanthansbi01@ybl SBIN0070601/VASANTHAN", "amount": "796.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R260", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 260, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-02", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /paytmqr2810050501011mwbekd7l0zf@paytm PYTM0123456 /IPOUR GKC And RKC And SONS PET", "amount": "-100.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R261", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 261, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-02", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /paytmqr2810050501011xppfh9tglgz@paytm PYTM0123456/Shri Balaji Agencies", "amount": "-100.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R262", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 262, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-02", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /kkamataj 1@oksbi SBIN0009584/Kamaraj <PERSON>", "amount": "-20.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R263", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 263, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-02", "description": "BY UPI CREDIT UPI/************/UPI XXXXX57133 /atk23592@okicici UBIN0830054/PURUSHOTHAMAN A", "amount": "1000.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R264", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 264, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-01", "description": "BY TRANSFER /IMPS/P2A/************/I47340303164/ZOMATO LIMIT TRANSFER FROM ***********", "amount": "-20.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R265", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 265, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-01", "description": "BY UPI CREDIT UPI/************/UPI XXXXX51513/asuriya674 1@okhdfcbank IDIB000P231/Ms A Suriya", "amount": "1420.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R266", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 266, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-01", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /paytmqrxtbqd938v3@paytm PYTM0123456/ARUNKUMAR R", "amount": "-10.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R267", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 267, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-01", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-100.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R268", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 268, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-01", "description": "THRU UPI DEBIT UPI/************/UPI Intent XXXXX /paytm ********@paytm PYTM0123456/Meesho", "amount": "-162.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R269", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 269, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-01", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /paytmqr2810050501011jq9yc44xw0c@paytm PYTM0123456/KKT Agencies", "amount": "-100.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R270", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 270, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-01", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-600.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R271", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 271, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-01", "description": "BY UPI CREDIT UPI/************/UPI XXXXX08557/adilshaspc 3@okhdfcbank KKBK0008134/ADIL SHAHEED P C", "amount": "90.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R272", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 272, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-01", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /paytmqr2810050501019cyam74jaceq@paytm PYTM0123456/Jalaja Ramamoorthy Agencies Ad", "amount": "-100.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R273", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 273, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-02-01", "description": "BY UPI CREDIT UPI/************/UPI XXXXX51552 /vasanthniquet95140 1@okaxis IOBA0001516/VASANTHAKUMAR NIQUET", "amount": "200.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R274", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 274, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-01-31", "description": "ONUS BNA DEP BNA SEQ NO586 ATM ID HIT015271 TRAN DATE (MMDD) 0131 TRAN TIME (HHMMSS) 092539", "amount": "-500.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R275", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 275, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-01-31", "description": "ONUS BNA DEP BNA SEQ NO582 ATM ID HIT015271 TRAN DATE (MMDD) 0131 TRAN TIME (HHMMSS) 092240", "amount": "-2700.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R276", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 276, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-01-31", "description": "ONUS BNA DEP BNA SEQ NO580 ATM ID HIT015271 TRAN DATE (MMDD) 0131 TRAN TIME (HHMMSS) 092033", "amount": "-3900.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R277", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 277, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-01-30", "description": "BY TRANSFER NEFT/CITI/CITIN24413203319 /ZOMATO LIMIT/ TRANSFER FROM ***********", "amount": "-50.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R278", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 278, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-01-30", "description": "BY TRANSFER NEFT/CITI/CITIN24413204339 /ZOMATO LIMIT/ TRANSFER FROM ***********", "amount": "-1334.35", "balance": null, "transaction_type": "DEBIT", "reference_number": "R279", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 279, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-01-30", "description": "BY TRANSFER /IMPS/P2A/************/ /IMPS/Authblue TRANSFER FROM ***********", "amount": "-1.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R280", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 280, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-01-29", "description": "BY TRANSFER /IMPS/P2A/************/ /FTTransferP2/BUNDL TEC TRANSFER FROM ***********", "amount": "-1.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R281", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 281, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-01-29", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /v.elumalai. abi@okicici IDIB000P152/Mr V ELUMALAI", "amount": "-200.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R282", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 282, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-01-29", "description": "BY UPI CREDIT UPI/************/NA XXXXX74825 /**********@paytm IDIB000P239/Mrs R <PERSON><PERSON><PERSON>", "amount": "200.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R283", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 283, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-01-27", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /gpaybillpay. rchrg@okpayaxis UTIB0000553/Google India Digital Services", "amount": "-58.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R284", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 284, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-01-26", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /euronetgpay. rch@icici ICIC0DC0099/EURONETGPAY", "amount": "-19.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R285", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 285, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-01-25", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-400.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R286", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 286, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-01-25", "description": "BY UPI CREDIT UPI/************/Payment from PhonePe XXXXX34269/vasanthansbi01@ybl SBIN0070601/VASANTHAN", "amount": "100.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R287", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 287, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-01-24", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /<EMAIL> cnrb0019603/", "amount": "-22500.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R288", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 288, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-01-24", "description": "BY UPI CREDIT UPI/************/Payment from PhonePe XXXXX34269/vasanthansbi01@ybl SBIN0070601/VASANTHAN", "amount": "1388.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R289", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 289, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-01-24", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /v.elumalai. abi@okicici IDIB000P152/Mr V ELUMALAI", "amount": "-1.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R290", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 290, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-01-24", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /v.elumalai. abi@oksbi SBIN0070601/V ELUMALAI", "amount": "-100.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R291", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 291, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-01-23", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /gpaybillpay. broad@axisbank utib0000553/Google India Digital Services", "amount": "-888.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R292", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 292, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-01-23", "description": "ONUS BNA DEP BNA SEQ NO3345 ATM ID S1C027371 TRAN DATE (MMDD) 0123 TRAN TIME (HHMMSS) 163748", "amount": "-2200.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R293", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 293, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-01-23", "description": "ONUS BNA DEP BNA SEQ NO3343 ATM ID S1C027371 TRAN DATE (MMDD) 0123 TRAN TIME (HHMMSS) 163549", "amount": "-19800.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R294", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 294, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-01-23", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /paytmqr2810050501011fowx9i92gey@paytm PYTM0123456 /SRINIVASA AUTO SPARES", "amount": "-100.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R295", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 295, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-01-22", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /paytmqrg0g6b7x2pv@paytm PYTM0123456/SELVAKUMAR JEEVA", "amount": "-180.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R296", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 296, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-01-21", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /paytmqr2810050501011lia6xre4sep@paytm PYTM0123456/Shri Balaji Agencies", "amount": "-100.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R297", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 297, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-01-21", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /blackloverz333@okhdfcbank IDIB000P152/Mrs E Abirami", "amount": "-11.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R298", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 298, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-01-20", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /paytmqr281005050101n063jukdv210@paytm PYTM0123456/Mr E M JAHABAR SATHIK", "amount": "-80.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R299", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 299, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-01-19", "description": "BY UPI CREDIT UPI/************/UPI XXXXX57133 /atk23592@okicici UBIN0830054/PURUSHOTHAMAN A", "amount": "275.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R300", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 300, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-01-19", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /kkamataj 1@oksbi SBIN0009584/Kamaraj <PERSON>", "amount": "-100.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R301", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 301, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-01-19", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /gpay ***********@okbizaxis UTIB0000000/Suba Auto Spares", "amount": "-560.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R302", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 302, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-01-17", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /vasanthansbi01@ybl SBIN0070601/VASANTHAN", "amount": "-508.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R303", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 303, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-01-16", "description": "BY UPI CREDIT UPI/************/UPI XXXXX57133 /atk23592@okicici UBIN0830054/PURUSHOTHAMAN A", "amount": "250.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R304", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 304, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-01-16", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /v.elumalai. abi@oksbi SBIN0070601/V ELUMALAI", "amount": "-200.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R305", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 305, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-01-11", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /pvigneshwaran2102@oksbi SBIN0010507/P <PERSON>n", "amount": "-100.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R306", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 306, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-01-11", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /paytmqr2810050501011iz0wgx7uap5@paytm PYTM0123456 /DHARMAA SERVICE STATION", "amount": "-100.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R307", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 307, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-01-10", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /ds2058299@okaxis IDIB000P239/Mr <PERSON>", "amount": "-700.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R308", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 308, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-01-10", "description": "WITHDRAWAL TRANSFER APYSCF_10_2023_500618097438_PENALTY_3 TRANSFER TO ********** IB Collection A/C APY Subsequent Contribution N", "amount": "-87.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R309", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 309, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-01-09", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /v.elumalai. abi@oksbi SBIN0070601/V ELUMALAI", "amount": "-100.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R310", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 310, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-01-09", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@oksbi SBIN0016563/YOGESH H", "amount": "-100.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R311", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 311, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-01-08", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /samuvel5003631 3@okicici UCBA0001318/YOGASHWARAN V", "amount": "-100.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R312", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 312, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-01-07", "description": "THRU UPI DEBIT UPI/************/UPI Intent XXXXX /paytm ********@paytm PYTM0123456/Meesho", "amount": "-73.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R313", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 313, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-01-05", "description": "BY UPI CREDIT UPI/************/UPI XXXXX26155/asuthan102 1@okicici IOBA0001064/ANNA REVATHI M", "amount": "2500.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R314", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 314, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-01-05", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@oksbi IDIB000P152/Mr V ELUMALAI", "amount": "100.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R315", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Ind Jan to March.xlsx", "source_line": 315, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-08-17", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /v.elumalai.abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "-450.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R3", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 3, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-08-17", "description": "THRU UPI DEBIT UPI/************/RoadrunnrFoodOrderin XXXXX95585/roadrunnr387877.rzp@rxairtel AIRP0000011 /ZOMATO MEDIA PRIVATE LIMITED", "amount": "-200.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R4", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 4, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-08-17", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /paytmqrgorz787942@paytm YESB0PTMUPI/VISHNU SHARMA", "amount": "-48.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R5", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 5, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-08-17", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "600.00", "balance": null, "transaction_type": "CREDIT", "reference_number": "R6", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 6, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-08-15", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-254.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R7", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 7, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-08-15", "description": "BY UPI CREDIT UPI/************/UPI XXXXX08597 /deepulakshmi2050@okhdfcbank CNRB0000033/S DEEPALAKSHMI", "amount": "250.00", "balance": null, "transaction_type": "CREDIT", "reference_number": "R8", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 8, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-08-15", "description": "BY UPI CREDIT UPI/************/UPI XXXXX50260 /priyansenthil22@okaxis KVBL0001208/PRIYADHARSHINI SENTH", "amount": "50.00", "balance": null, "transaction_type": "CREDIT", "reference_number": "R9", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 9, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-08-15", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /mailtovara022@oksbi IDIB000K049/Ms Varalakshmi", "amount": "-1500.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R10", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 10, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-08-15", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "121.00", "balance": null, "transaction_type": "CREDIT", "reference_number": "R11", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 11, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-08-15", "description": "BY UPI CREDIT UPI/************/UPI XXXXX91759 /abirami9159@okaxis IDIB000P152/Mrs E Abirami", "amount": "430.00", "balance": null, "transaction_type": "CREDIT", "reference_number": "R12", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 12, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-08-15", "description": "BY UPI CREDIT UPI/************/UPI XXXXX08597 /deepulakshmi2050@okhdfcbank CNRB0000033/S DEEPALAKSHMI", "amount": "1000.00", "balance": null, "transaction_type": "CREDIT", "reference_number": "R13", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 13, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-08-15", "description": "BY UPI CREDIT UPI/************/UPI XXXXX08597 /deepulakshmi2050@okhdfcbank CNRB0000033/S DEEPALAKSHMI", "amount": "1.00", "balance": null, "transaction_type": "CREDIT", "reference_number": "R14", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 14, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-08-13", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /deepulakshmi2050@okhdfcbank CNRB0000033/S DEEPALAKSHMI", "amount": "-260.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R15", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 15, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-08-13", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /deepulakshmi2050@okhdfcbank CNRB0000033/S DEEPALAKSHMI", "amount": "-200.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R16", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 16, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-08-13", "description": "ONUS BNA DEP BNA SEQ NO489 ATM ID S1C015272 TRAN DATE (MMDD) 0813 TRAN TIME (HHMMSS) 183404", "amount": "-300.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R17", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 17, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-08-12", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /q773476770@ybl YESB0YBLUPI/SRI DINESH KRISHNA S", "amount": "-40.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R18", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 18, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-08-12", "description": "BY TRANSFER NEFT/CITI/CITIN24510110529 /ZOMATO LIMIT/ TRANSFER FROM ***********", "amount": "-167.81", "balance": null, "transaction_type": "DEBIT", "reference_number": "R19", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 19, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-08-11", "description": "BY UPI CREDIT UPI/************/I477643654495346 XXXXX37116/zomato.payouts@icici ICIC0DC0099/Zomato", "amount": "30.00", "balance": null, "transaction_type": "CREDIT", "reference_number": "R20", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 20, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-08-10", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /v.elumalai. abi@oksbi SBIN0070601/V ELUMALAI", "amount": "-470.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R21", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 21, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-08-09", "description": "BY UPI CREDIT UPI/************/UPI XXXXX34269 /dominicvasanth777 1@okaxis SBIN0070601/VASANTHAN", "amount": "385.00", "balance": null, "transaction_type": "CREDIT", "reference_number": "R22", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 22, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-08-08", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /v.elumalai. abi@oksbi SBIN0070601/V ELUMALAI", "amount": "-500.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R23", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 23, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-08-05", "description": "THRU UPI DEBIT UPI/************/Request from Amazon XXXXX /amazonpayrecharges@apl UTIB0000100/Amazon Bill Pay", "amount": "-249.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R24", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 24, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-08-05", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "260.00", "balance": null, "transaction_type": "CREDIT", "reference_number": "R25", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 25, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-08-05", "description": "BY TRANSFER NEFT/CITI/CITIN24507402137 /ZOMATO LIMIT/ TRANSFER FROM ***********", "amount": "-439.54", "balance": null, "transaction_type": "DEBIT", "reference_number": "R26", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 26, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-08-04", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "100.00", "balance": null, "transaction_type": "CREDIT", "reference_number": "R27", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 27, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-08-04", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /suriyas332 3@okaxis IOBA0002121/SURESH V", "amount": "-25.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R28", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 28, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-08-04", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "60.00", "balance": null, "transaction_type": "CREDIT", "reference_number": "R29", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 29, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-08-04", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /suriyas332 3@okaxis IOBA0002121/SURESH V", "amount": "-260.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R30", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 30, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-08-03", "description": "BY UPI CREDIT UPI/************/I477471869292914 XXXXX37116/zomato.payouts@icici ICIC0DC0099/Zomato", "amount": "40.00", "balance": null, "transaction_type": "CREDIT", "reference_number": "R31", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 31, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-08-03", "description": "BY UPI CREDIT UPI/************/UPI XXXXX09113 /abdul786<PERSON><PERSON><PERSON>@okaxis IDIB000M266/Mr G ABDUL JAPPAR", "amount": "200.00", "balance": null, "transaction_type": "CREDIT", "reference_number": "R32", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 32, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-08-02", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /sibin230805 1@oksbi SBIN0070013/SIBIN BABU", "amount": "-350.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R33", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 33, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-08-02", "description": "BY UPI CREDIT UPI/************/UPI XXXXX09113 /abdul786<PERSON><PERSON><PERSON>@okaxis IDIB000M266/Mr G ABDUL JAPPAR", "amount": "200.00", "balance": null, "transaction_type": "CREDIT", "reference_number": "R34", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 34, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-08-02", "description": "THRU UPI DEBIT UPI/************/RoadrunnrFoodOrderin XXXXX95585/roadrunnr387877.rzp@rxairtel AIRP0000011 /ZOMATO MEDIA PRIVATE LIMITED", "amount": "-224.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R35", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 35, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-08-02", "description": "BY UPI CREDIT UPI/************/Sent from Paytm XXXXX70798 /**********@ptyes IDIB000P042/Mr <PERSON>", "amount": "250.00", "balance": null, "transaction_type": "CREDIT", "reference_number": "R36", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 36, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-08-01", "description": "THRU UPI DEBIT UPI/************/RoadrunnrFoodOrderin XXXXX95585/roadrunnr387877.rzp@rxairtel AIRP0000011 /ZOMATO MEDIA PRIVATE LIMITED", "amount": "-300.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R37", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 37, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-08-01", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "300.00", "balance": null, "transaction_type": "CREDIT", "reference_number": "R38", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 38, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-08-01", "description": "THRU UPI DEBIT UPI/************/RoadrunnrFoodOrderin XXXXX95585/roadrunnr387877.rzp@rxairtel AIRP0000011 /ZOMATO MEDIA PRIVATE LIMITED", "amount": "-10.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R39", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 39, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-08-01", "description": "BY UPI CREDIT UPI/************/I477429369079410 XXXXX37116/zomato.payouts@icici ICIC0DC0099/Zomato", "amount": "20.00", "balance": null, "transaction_type": "CREDIT", "reference_number": "R40", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 40, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-31", "description": "THRU UPI DEBIT UPI/************/Pay to BharatPe Merc XXXXX /bharatpe.***********@fbpe FDRL0001382/K BAVADHARANI", "amount": "-50.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R41", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 41, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-30", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /gpay tv internet@okpayaxis UTIB0000553/Google India Digital Services", "amount": "-361.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R42", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 42, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-30", "description": "BY UPI CREDIT UPI/************/I477384866942066 XXXXX37116/zomato.payouts@icici ICIC0DC0099/Zomato", "amount": "50.00", "balance": null, "transaction_type": "CREDIT", "reference_number": "R43", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 43, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-30", "description": "BY UPI CREDIT UPI/************/UPI XXXXX51979 /ayaankalpeni@okaxis UBIN0590479/AYAAN MOHAMMED", "amount": "331.00", "balance": null, "transaction_type": "CREDIT", "reference_number": "R44", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 44, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-30", "description": "THRU UPI DEBIT UPI/************/********** XXXXX /cf. swiggy@kotak KKBK0000958/Swiggy", "amount": "-675.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R45", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 45, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-30", "description": "ONUS BNA DEP BNA SEQ NO2402 ATM ID S1C015272 TRAN DATE (MMDD) 0730 TRAN TIME (HHMMSS) 121708", "amount": "-800.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R46", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 46, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-30", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /v.elumalai. abi@oksbi SBIN0070601/V ELUMALAI", "amount": "-550.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R47", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 47, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-30", "description": "BY UPI CREDIT UPI/************/Sent from Paytm XXXXX13209 /**********@paytm SBIN0020109/DASARI ASHRITH", "amount": "391.00", "balance": null, "transaction_type": "CREDIT", "reference_number": "R48", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 48, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-29", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /maaraudhayavarman 1@okaxis IDIB0PBG001/UDHAYAKUMAR R", "amount": "-58.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R49", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 49, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-29", "description": "BY UPI CREDIT UPI/************/TRANSFER XXXXX99999 /meeshov1@yesbank YESB0000022/Meesho", "amount": "228.00", "balance": null, "transaction_type": "CREDIT", "reference_number": "R50", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 50, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-27", "description": "ONUS ATM WDL ATM WDL SEQ NO 6964 ATM ID S1T006031 SELF VILLUPURAM ROAD VILLUPURAM TRAN DATE (MMDD) 0727 TRAN TIME (HHMMSS) 123632", "amount": "-500.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R51", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 51, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-27", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi@oksbi SBIN0070601/V ELUMALAI", "amount": "100.00", "balance": null, "transaction_type": "CREDIT", "reference_number": "R52", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 52, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-27", "description": "BY UPI CREDIT UPI/************/UPI XXXXX34269 /dominicvasanth777 1@oksbi SBIN0070601/VASANTHAN .", "amount": "83.00", "balance": null, "transaction_type": "CREDIT", "reference_number": "R53", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 53, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-27", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /balaramanmadhavan28@okhdfcbank UBIN0904422/MADHAVAN B", "amount": "-10.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R54", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 54, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-26", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /paytmqrgorz787942@paytm YESB0PTMUPI/VISHNU SHARMA", "amount": "-8.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R55", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 55, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-26", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /paytmqrgorz787942@paytm YESB0PTMUPI/VISHNU SHARMA", "amount": "-40.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R56", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 56, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-25", "description": "THRU UPI DEBIT UPI/************/********** XXXXX /cf. swiggy@kotak KKBK0000958/Swiggy", "amount": "-100.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R57", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 57, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-25", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /q030163390@ybl YESB0YBLUPI/RAJKUMAR P", "amount": "-30.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R58", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 58, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-25", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /deepulakshmi2050@okhdfcbank CNRB0000033/S DEEPALAKSHMI", "amount": "-100.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R59", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 59, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-25", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /deepulakshmi2050@okhdfcbank CNRB0000033/S DEEPALAKSHMI", "amount": "-300.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R60", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 60, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-25", "description": "BY UPI CREDIT UPI/************/Payment from PhonePe XXXXX29757/magh<PERSON><PERSON>@ybl CNRB0000000/MAGHOMALA BASU", "amount": "9.00", "balance": null, "transaction_type": "CREDIT", "reference_number": "R61", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 61, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-25", "description": "BY UPI CREDIT UPI/************/Payment from PhonePe XXXXX72535/adki.srikanth@axl HDFC0000218/SRIKANTH KULKARNI", "amount": "228.00", "balance": null, "transaction_type": "CREDIT", "reference_number": "R62", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 62, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-25", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /paytmqr164nhs@paytm YESB0PTMUPI/Mi<PERSON>ra <PERSON>y", "amount": "-80.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R63", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 63, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-25", "description": "BY UPI CREDIT UPI/************/UPI XXXXX14329 /yaazhinipugazhendhi712 1@okhdfcbank HDFC0001305/P YAAZHINI", "amount": "200.00", "balance": null, "transaction_type": "CREDIT", "reference_number": "R64", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 64, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-24", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /q357028120@ybl YESB0YBLUPI/TAJNISHA K", "amount": "-60.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R65", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 65, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-24", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /deepulakshmi2050@okhdfcbank CNRB0000033/S DEEPALAKSHMI", "amount": "-80.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R66", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 66, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-23", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /**********@ybl PUNB0598100/SATHIYA", "amount": "-100.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R67", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 67, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-23", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /ha<PERSON><PERSON><PERSON>an02102k4@oksbi IDIB000B173/Mrs <PERSON>", "amount": "-50.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R68", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 68, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-23", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /euronetgpay. rch@icici ICIC0DC0099/EURONETGPAY", "amount": "-250.90", "balance": null, "transaction_type": "DEBIT", "reference_number": "R69", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 69, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-23", "description": "THRU UPI DEBIT UPI/************/Upi Transaction XXXXX48779 /magma.hdi.payu@icici ICIC0DC0099/Magma HDI General Insurance Co", "amount": "-59.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R70", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 70, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-22", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /q176544589@ybl YESB0YBLUPI/LEELAVATHY R", "amount": "-66.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R71", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 71, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-22", "description": "BY TRANSFER NEFT/CITI/CITIN24499028399 /ZOMATO LIMIT/ TRANSFER FROM ***********", "amount": "-142.27", "balance": null, "transaction_type": "DEBIT", "reference_number": "R72", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 72, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-22", "description": "THRU UPI DEBIT UPI/************/Pay via Razorpay XXXXX20033/olaelectrictech734205.rzp@axisbank UTIB0000100 /Ola Electric Technologies Pvt", "amount": "-1381.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R73", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 73, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-22", "description": "BY UPI CREDIT UPI/************/UPI XXXXX86489 /jagansundar0.0001 1@oksbi SBIN0007545/Jagansundar M", "amount": "1000.00", "balance": null, "transaction_type": "CREDIT", "reference_number": "R74", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 74, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-22", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "525.00", "balance": null, "transaction_type": "CREDIT", "reference_number": "R75", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 75, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-22", "description": "BY UPI CREDIT UPI/************/UPI XXXXX34269 /dominicvasanth777 1@oksbi SBIN0070601/VASANTHAN .", "amount": "120.00", "balance": null, "transaction_type": "CREDIT", "reference_number": "R76", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 76, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-21", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /mug<PERSON>sengeni@okhdfcbank UCBA0001318/S KALPANA", "amount": "-50.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R77", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 77, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-21", "description": "BY UPI CREDIT UPI/************/Publisher Payout VAS XXXXX67702/Payouts.pubmiypw23 1@rbl RATN0000031/Razorpay", "amount": "410.57", "balance": null, "transaction_type": "CREDIT", "reference_number": "R78", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 78, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-19", "description": "BY UPI CREDIT UPI/************/UPI XXXXX57144 /sriramsudarsan501 1@oksbi SBIN0006511/R SRIRAM", "amount": "300.00", "balance": null, "transaction_type": "CREDIT", "reference_number": "R79", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 79, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-19", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /sriramsudarsan501 1@oksbi SBIN0006511/R SRIRAM", "amount": "-300.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R80", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 80, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-18", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /deepulakshmi2050@okhdfcbank CNRB0000033/S DEEPALAKSHMI", "amount": "-50.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R81", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 81, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-18", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /v.elumalai. abi@oksbi SBIN0070601/V ELUMALAI", "amount": "-200.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R82", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 82, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-17", "description": "THRU UPI DEBIT UPI/************/Collect request from XXXXX07799/meesho@ybl YESB0YBLUPI/Meesho", "amount": "-223.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R83", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 83, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-17", "description": "THRU UPI DEBIT UPI/************/Collect request from XXXXX07799/meesho@ybl YESB0YBLUPI/Meesho", "amount": "-245.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R84", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 84, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-17", "description": "ONUS BNA DEP BNA SEQ NO5107 ATM ID S1C027371 TRAN DATE (MMDD) 0717 TRAN TIME (HHMMSS) 183942", "amount": "-800.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R85", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 85, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-15", "description": "BY TRANSFER NEFT/CITI/CITIN24496766730 /ZOMATO LIMIT/ TRANSFER FROM ***********", "amount": "-265.91", "balance": null, "transaction_type": "DEBIT", "reference_number": "R86", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 86, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-12", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /v.elumalai. abi@oksbi SBIN0070601/V ELUMALAI", "amount": "-300.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R87", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 87, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-10", "description": "BY UPI CREDIT UPI/************/I476939912114802 XXXXX37116/zomato.payouts@icici ICIC0DC0099/Zomato", "amount": "30.00", "balance": null, "transaction_type": "CREDIT", "reference_number": "R88", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 88, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-09", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /euronetgpay. rch@icici ICIC0DC0099/EURONETGPAY", "amount": "-250.90", "balance": null, "transaction_type": "DEBIT", "reference_number": "R89", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 89, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-08", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "400.00", "balance": null, "transaction_type": "CREDIT", "reference_number": "R90", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 90, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-08", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /deepulakshmi2050@okhdfcbank CNRB0000033/S DEEPALAKSHMI", "amount": "-500.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R91", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 91, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-06", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-357.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R92", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 92, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-06", "description": "BY UPI CREDIT UPI/************/Payment from PhonePe XXXXX20383/**********@ybl BARB0VJDOML/RAJESH M", "amount": "580.00", "balance": null, "transaction_type": "CREDIT", "reference_number": "R93", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 93, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-06", "description": "BY UPI CREDIT UPI/************/UPI XXXXX79096 /vickysivavickysiva7@oksbi IDIB000T029/Mr <PERSON><PERSON><PERSON>", "amount": "500.00", "balance": null, "transaction_type": "CREDIT", "reference_number": "R94", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 94, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-06", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-240.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R95", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 95, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-06", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "250.00", "balance": null, "transaction_type": "CREDIT", "reference_number": "R96", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 96, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-06", "description": "THRU UPI DEBIT UPI/************/UPI Intent XXXXX /paytm ********@paytm YESB0PTMUPI/Meesho", "amount": "-130.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R97", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 97, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-04", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /q84406087@ybl YESB0YBLUPI/PRABU S", "amount": "-60.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R98", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 98, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-04", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /v.elumalai. abi@oksbi SBIN0070601/V ELUMALAI", "amount": "-800.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R99", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 99, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-04", "description": "ONUS BNA DEP BNA SEQ NO492 ATM ID S1C015272 TRAN DATE (MMDD) 0704 TRAN TIME (HHMMSS) 170859", "amount": "-1000.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R100", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 100, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-04", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /v.elumalai. abi@oksbi SBIN0070601/V ELUMALAI", "amount": "-340.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R101", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 101, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-04", "description": "BY UPI CREDIT UPI/************/UPI XXXXX34269 /dominicvasanth777 1@okaxis SBIN0070601/VASANTHAN", "amount": "93.00", "balance": null, "transaction_type": "CREDIT", "reference_number": "R102", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 102, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-03", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-424.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R103", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 103, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-03", "description": "BY UPI CREDIT UPI/************/UPI XXXXX21154/mahashuma. b 1@okicici ICIC0006233/MAHASHUMA RAHMA A", "amount": "424.00", "balance": null, "transaction_type": "CREDIT", "reference_number": "R104", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 104, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-03", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-372.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R105", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 105, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-03", "description": "BY UPI CREDIT UPI/************/Food XXXXX87586 /**********@upi SBIN0002238/MANGALAG", "amount": "383.00", "balance": null, "transaction_type": "CREDIT", "reference_number": "R106", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 106, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-03", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-720.00", "balance": null, "transaction_type": "DEBIT", "reference_number": "R107", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 107, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-07-03", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai.", "amount": "720.00", "balance": null, "transaction_type": "CREDIT", "reference_number": "R108", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\Jun To Aug.xlsx", "source_line": 108, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-28", "description": "BY UPI CREDIT UPI/************/I476658490874994 XXXXX37116/zomato.payouts@icici ICIC0DC0099/Zomato", "amount": "57.92", "balance": null, "transaction_type": "CREDIT", "reference_number": "R3", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 3, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-27", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-626.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R4", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 4, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-27", "description": "BY UPI CREDIT UPI/************/NA XXXXX26440/kee129@icici ICIC0001504/KRISHNA P", "amount": "663.92", "balance": null, "transaction_type": "CREDIT", "reference_number": "R5", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 5, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-27", "description": "BY UPI CREDIT UPI/************/UPI XXXXX34269 /dominicvasanth777 1@oksbi SBIN0070601/VASANTHAN .", "amount": "380.92", "balance": null, "transaction_type": "CREDIT", "reference_number": "R6", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 6, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-26", "description": "THRU UPI DEBIT UPI/************/Pay to BharatPe Merc XXXXX /bharatpe09916629172@yesbankltd YESB0YESUPI/MS UDHAYA COMPUTERS", "amount": "-170.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R7", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 7, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-26", "description": "THRU UPI DEBIT UPI/************/Pay to BharatPe Merc XXXXX /bharatpe09916629172@yesbankltd YESB0YESUPI/MS UDHAYA COMPUTERS", "amount": "-200.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R8", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 8, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-26", "description": "WITHDRAWAL TRANSFER ACH Debit Rtn Chgs ********: IDIB0000000008 TRANSFER TO ***********", "amount": "-118.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R9", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 9, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-26", "description": "BY UPI CREDIT UPI/************/I476625649548402 XXXXX37116/zomato.payouts@icici ICIC0DC0099/Zomato", "amount": "719.92", "balance": null, "transaction_type": "CREDIT", "reference_number": "R10", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 10, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-26", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "689.92", "balance": null, "transaction_type": "CREDIT", "reference_number": "R11", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 11, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-25", "description": "BY UPI CREDIT UPI/************/UPI XXXXX08597 /deepulakshmi2050@okhdfcbank CNRB0000033/S DEEPALAKSHMI", "amount": "688.92", "balance": null, "transaction_type": "CREDIT", "reference_number": "R12", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 12, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-25", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /paytmqr281005050101bovl50f6xm2a@paytm YESB0PTMUPI /DHARMAA SERVICE STATION", "amount": "-40.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R13", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 13, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-25", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /**********@ibl ICIC0004041/PANDILLAPALLI SRI CHARAN REDDY", "amount": "-126.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R14", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 14, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-25", "description": "BY UPI CREDIT UPI/************/Payment from PhonePe XXXXX73738/**********@ybl UBIN0902977/BOBBA KALPANA", "amount": "354.92", "balance": null, "transaction_type": "CREDIT", "reference_number": "R15", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 15, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-25", "description": "BY UPI CREDIT UPI/************/Payment from PhonePe XXXXX68656/**********@ibl ICIC0004041/PANDILLAPALLI SRI CHARAN REDDY", "amount": "228.92", "balance": null, "transaction_type": "CREDIT", "reference_number": "R16", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 16, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-25", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /paytmqrgorz787942@paytm YESB0PTMUPI/VISHNU SHARMA", "amount": "-50.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R17", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 17, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-25", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /euronetgpay. rch@icici ICIC0DC0099/EURONETGPAY", "amount": "-300.9", "balance": null, "transaction_type": "DEBIT", "reference_number": "R18", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 18, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-25", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /godofwarking61@okicici SBIN0006511/SANTHOSH", "amount": "-100.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R19", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 19, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-25", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /euronetgpay. rch@icici ICIC0DC0099/EURONETGPAY", "amount": "-270.9", "balance": null, "transaction_type": "DEBIT", "reference_number": "R20", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 20, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-24", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /q465400655@ybl YESB0YBLUPI/Mr <PERSON>", "amount": "-50.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R21", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 21, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-24", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /gpay ***********@okbizaxis UTIB0000553/Arun Studio", "amount": "-80.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R22", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 22, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-24", "description": "BY TRANSFER NEFT/CITI/CITIN24485631955 /ZOMATO LIMIT/ TRANSFER FROM ***********", "amount": "-217.71", "balance": null, "transaction_type": "DEBIT", "reference_number": "R23", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 23, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-24", "description": "THRU UPI DEBIT UPI/************/Pay to DSK STORE XXXXX /paynearby.**********@indus INDB0000006/SUMATHI", "amount": "-10.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R24", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 24, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-23", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "747.01", "balance": null, "transaction_type": "CREDIT", "reference_number": "R25", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 25, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-23", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-30.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R26", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 26, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-23", "description": "BY UPI CREDIT UPI/************/UPI XXXXX82890 /muralisha1990@okicici BKID0008101/MURALI I", "amount": "776.01", "balance": null, "transaction_type": "CREDIT", "reference_number": "R27", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 27, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-23", "description": "BY UPI CREDIT UPI/************/UPI XXXXX48321 /praveenkp9092 2@okaxis KKBK0008757/PRAVEEN KUPPUSAMY", "amount": "546.01", "balance": null, "transaction_type": "CREDIT", "reference_number": "R28", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 28, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-23", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /q679527034@ybl YESB0YBLUPI/S AROKIA SUNILSON", "amount": "-360.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R29", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 29, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-23", "description": "BY UPI CREDIT UPI/************/UPI XXXXX34269 /dominicvasanth777 1@oksbi SBIN0070601/VASANTHAN .", "amount": "634.01", "balance": null, "transaction_type": "CREDIT", "reference_number": "R30", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 30, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-23", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /gpay ***********@okbizaxis UTIB0000553/NITHYASRI STORE", "amount": "-100.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R31", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 31, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-23", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "334.01", "balance": null, "transaction_type": "CREDIT", "reference_number": "R32", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 32, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-19", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /<EMAIL> idib000p152/XXXXXXRAMI", "amount": "-300.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R33", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 33, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-18", "description": "BY UPI CREDIT UPI/************/RefundRef OD33151646 XXXXX00000/DEUT2050243000@dbag DEUT0797BGL/Flipkart Internet Private Limi", "amount": "334.01", "balance": null, "transaction_type": "CREDIT", "reference_number": "R34", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 34, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-18", "description": "BY UPI CREDIT UPI/************/RefundRef OD33151646 XXXXX00000/DEUT2050243000@dbag DEUT0797BGL/Flipkart Internet Private Limi", "amount": "235.01", "balance": null, "transaction_type": "CREDIT", "reference_number": "R35", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 35, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-18", "description": "BY UPI CREDIT UPI/************/RefundRef OD33151646 XXXXX00000/DEUT2050243000@dbag DEUT0797BGL/Flipkart Internet Private Limi", "amount": "129.01", "balance": null, "transaction_type": "CREDIT", "reference_number": "R36", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 36, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-18", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /q255303358@ybl YESB0YBLUPI/BEJOHN BEEVI", "amount": "-390.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R37", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 37, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-18", "description": "BY UPI CREDIT UPI/************/UPI XXXXX34269 /dominicvasanth777 1@oksbi SBIN0070601/VASANTHAN .", "amount": "437.01", "balance": null, "transaction_type": "CREDIT", "reference_number": "R38", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 38, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-18", "description": "BY UPI CREDIT UPI/************/UPI XXXXX08597 /deepulakshmi2050@okhdfcbank CNRB0000033/S DEEPALAKSHMI", "amount": "281.01", "balance": null, "transaction_type": "CREDIT", "reference_number": "R39", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 39, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-18", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /ippostore87579315663@icici TMBL0000372/Subramanian cool bar", "amount": "-85.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R40", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 40, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-18", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /q625342875@ybl YESB0YBLUPI/SINGARAVELAN T", "amount": "-55.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R41", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 41, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-18", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /mahendran552@tmb TMBL0000372/MAHENDRAN D", "amount": "-50.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R42", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 42, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-18", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /q373664087@ybl TMBL0000372/VINOTH H", "amount": "-180.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R43", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 43, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-18", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /**********@ybl IPOS0000001/ABDUL QADIR KHAN", "amount": "-60.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R44", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 44, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-18", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "511.01", "balance": null, "transaction_type": "CREDIT", "reference_number": "R45", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 45, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-18", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /vvengat38@oksbi SBIN0008178/Vengadesh. K", "amount": "-306.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R46", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 46, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-17", "description": "ATM AMC CHARGES UNCOLL CHRG DT: 23/05/2024JRNL NO:", "amount": "-4.66", "balance": null, "transaction_type": "DEBIT", "reference_number": "R47", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 47, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-17", "description": "BY TRANSFER NEFT/CITI/CITIN24482940156 /ZOMATO LIMIT/ TRANSFER FROM ***********", "amount": "-321.67", "balance": null, "transaction_type": "DEBIT", "reference_number": "R48", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 48, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-17", "description": "ATM AMC CHARGES UNCOLL CHRG DT: 23/05/2024JRNL NO:", "amount": "-321.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R49", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 49, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-17", "description": "BY TRANSFER NEFT/CITI/CITIN24482930496 /ZOMATO LIMIT/ TRANSFER FROM ***********", "amount": "-20.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R50", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 50, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-16", "description": "BY UPI CREDIT UPI/************/food order XXXXX33527 /pratham.gogoi@okicici ICIC0000056/PRATHAM GOGOI", "amount": "301.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R51", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 51, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-15", "description": "BY UPI CREDIT UPI/************/Payment from PhonePe XXXXX02305/**********@ybl HDFC0004195/LUNAVATH NARESH", "amount": "31.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R52", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 52, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-15", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "1.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R53", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 53, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-12", "description": "ATM AMC CHARGES UNCOLL CHRG DT: 23/05/2024JRNL NO:", "amount": "-17.4", "balance": null, "transaction_type": "DEBIT", "reference_number": "R54", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 54, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-12", "description": "BY TRANSFER /IMPS/P2A/************/I47632445510/ZOMATO LIMIT TRANSFER FROM ***********", "amount": "-15.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R55", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 55, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-10", "description": "THRU UPI DEBIT UPI/************/Pay via Razorpay XXXXX41383/fssai.rzp@axisbank UTIB0001506/FSSAI", "amount": "-109.44", "balance": null, "transaction_type": "DEBIT", "reference_number": "R56", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 56, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-10", "description": "BY UPI CREDIT UPI/************/Payment from PhonePe XXXXX34269/vasanthansbi01@ybl SBIN0070601/VASANTHAN", "amount": "111.84", "balance": null, "transaction_type": "CREDIT", "reference_number": "R57", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 57, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-10", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "101.84", "balance": null, "transaction_type": "CREDIT", "reference_number": "R58", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 58, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-09", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /<EMAIL> sbin0070601/Mr VASANTHAN", "amount": "-2667.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R59", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 59, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-09", "description": "BY UPI CREDIT UPI/************/none XXXXX20702 /snapmintemi@icici ICIC0DC0099/Snapmint", "amount": "2667.84", "balance": null, "transaction_type": "CREDIT", "reference_number": "R60", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 60, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-09", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /v.elumalai.abi 1@oksbi IDIB000P152/Mr V ELUMALAI", "amount": "-1000.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R61", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 61, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-09", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /v.elumalai.abi 1@oksbi IDIB000P152/Mr V ELUMALAI", "amount": "-300.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R62", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 62, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-09", "description": "THRU UPI DEBIT UPI/************/******** XXXXX / /", "amount": "-1350.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R63", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 63, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-09", "description": "BY UPI CREDIT UPI/************/UPI XXXXX34269 /dominicvasanth777 1@okaxis SBIN0070601/VASANTHAN", "amount": "3967.84", "balance": null, "transaction_type": "CREDIT", "reference_number": "R64", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 64, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-08", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /dominicvasanth777 1@okicici SBIN0070601/VASANTHAN", "amount": "-410.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R65", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 65, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-08", "description": "BULK CHARGES SMS_CHGS_MARCH 24_QT *****************", "amount": "-32.7", "balance": null, "transaction_type": "DEBIT", "reference_number": "R66", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 66, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-08", "description": "BY UPI CREDIT UPI/************/CASHFREE XXXXX92995 /poweraccess.cashfree@axisbank utib0001920/Cashfree Payments", "amount": "1010.54", "balance": null, "transaction_type": "CREDIT", "reference_number": "R67", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 67, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-06", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "600.1", "balance": null, "transaction_type": "CREDIT", "reference_number": "R68", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 68, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-05", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /paytmqr2810050501014r27cbgnsopq@paytm YESB0PTMUPI/SRI VENU CORPORATION", "amount": "-50.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R69", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 69, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-05", "description": "BY UPI CREDIT UPI/************/UPI XXXXX07304 /saravanansuresh4590@okaxis UCBA0001318/S SURESH", "amount": "50.1", "balance": null, "transaction_type": "CREDIT", "reference_number": "R70", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 70, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-05", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /dominicvasanth777 1@okicici SBIN0070601/VASANTHAN", "amount": "-4959.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R71", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 71, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-05", "description": "BY UPI CREDIT UPI/************/collect XXXXX01028/paytm ********@paytm YESB0PTMUPI/Snapmint Credit Advisory Pvt L", "amount": "4959.1", "balance": null, "transaction_type": "CREDIT", "reference_number": "R72", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 72, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-04", "description": "BY UPI CREDIT UPI/************/Refund for UPI RRN 4 XXXXX00009/snapmintfinancialserv.payu@mairtel AIRP0000001 /SNAPMINT FINANCIAL SERVICES PR", "amount": "1312.1", "balance": null, "transaction_type": "CREDIT", "reference_number": "R73", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 73, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-03", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /**********@ibl IDIB000P239/Mr <PERSON>", "amount": "-20.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R74", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 74, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-03", "description": "THRU UPI DEBIT UPI/************/UPIIntent XXXXX /snapmintfinancialserv.payu@mairtel AIRP0000001/SNAPMINT FINANCIAL SERVICES PR", "amount": "-1303.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R75", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 75, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-03", "description": "BY UPI CREDIT UPI/************/UPI XXXXX08597 /deepulakshmi2050@okhdfcbank CNRB0000033/S DEEPALAKSHMI", "amount": "1332.1", "balance": null, "transaction_type": "CREDIT", "reference_number": "R76", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 76, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-03", "description": "BY UPI CREDIT UPI/************/Payment from PhonePe XXXXX91759/**********@ybl IDIB000P152/Mrs E Abirami", "amount": "1132.1", "balance": null, "transaction_type": "CREDIT", "reference_number": "R77", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 77, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-03", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "742.1", "balance": null, "transaction_type": "CREDIT", "reference_number": "R78", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 78, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-01", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /deepulakshmi2050@okhdfcbank CNRB0000033/S DEEPALAKSHMI", "amount": "-1000.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R79", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 79, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-06-01", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "1000.1", "balance": null, "transaction_type": "CREDIT", "reference_number": "R80", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 80, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-31", "description": "THRU UPI DEBIT UPI/************/Pay to DSK STORE XXXXX /paynearby.**********@indus INDB0000006/SUMATHI", "amount": "-50.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R81", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 81, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-31", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /paytmqr2810050501011wc7wgseku2q@paytm YESB0PTMUPI /DHARMAA SERVICE STATION", "amount": "-100.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R82", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 82, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-31", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "150.1", "balance": null, "transaction_type": "CREDIT", "reference_number": "R83", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 83, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-30", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /v.elumalai.abi 1@oksbi IDIB000P152/Mr V ELUMALAI", "amount": "-20.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R84", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 84, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-30", "description": "BY UPI CREDIT UPI/************/UPI XXXXX08597 /deepulakshmi2050@okhdfcbank CNRB0000033/S DEEPALAKSHMI", "amount": "20.1", "balance": null, "transaction_type": "CREDIT", "reference_number": "R85", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 85, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-30", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /ganeshkavi59 1@oksbi SBIN0012798/GANESH M", "amount": "-2000.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R86", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 86, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-30", "description": "BY UPI CREDIT UPI/************/UPI XXXXX22530 /v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@oksbi SBIN0016563/H YOGESH", "amount": "2009.1", "balance": null, "transaction_type": "CREDIT", "reference_number": "R87", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 87, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-30", "description": "ONUS BNA DEP BNA SEQ NO1332 ATM ID S1C027371 TRAN DATE (MMDD) 0530 TRAN TIME (HHMMSS) 181435", "amount": "-500.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R88", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 88, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-30", "description": "BY UPI CREDIT UPI/************/UPI XXXXX37899/info. krishnakumar2004@oksbi SBIN0012797/S <PERSON>", "amount": "1489.1", "balance": null, "transaction_type": "CREDIT", "reference_number": "R89", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 89, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-30", "description": "BY UPI CREDIT UPI/************/UPI XXXXX08597 /deepulakshmi2050@okhdfcbank CNRB0000033/S DEEPALAKSHMI", "amount": "1339.1", "balance": null, "transaction_type": "CREDIT", "reference_number": "R90", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 90, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-30", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "339.1", "balance": null, "transaction_type": "CREDIT", "reference_number": "R91", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 91, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-30", "description": "BY UPI CREDIT UPI/************/Payment from PhonePe XXXXX91759/**********@ybl IDIB000P152/Mrs E Abirami", "amount": "246.1", "balance": null, "transaction_type": "CREDIT", "reference_number": "R92", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 92, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-30", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /saravanansuresh4590@okaxis UCBA0001318/S SURESH", "amount": "-200.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R93", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 93, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-29", "description": "THRU UPI DEBIT UPI/************/Upi Transaction XXXXX56987 /olaelectric.payu@hdfcbank HDFC0000499/OLAELECTRIC COM", "amount": "-21952.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R94", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 94, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-29", "description": "BY UPI CREDIT UPI/************/UPI XXXXX02539 /ganeshkavi59@okaxis SBIN0012798/GANESH M", "amount": "22299.1", "balance": null, "transaction_type": "CREDIT", "reference_number": "R95", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 95, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-29", "description": "BY UPI CREDIT UPI/************/Additional XXXXX91759 /**********@ybl IDIB000P152/Mrs E Abirami", "amount": "20347.1", "balance": null, "transaction_type": "CREDIT", "reference_number": "R96", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 96, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-29", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /<EMAIL> idib000p152/XXXXXXRAMI", "amount": "-500.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R97", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 97, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-29", "description": "ONUS BNA DEP BNA SEQ NO8201 ATM ID S1T014151 TRAN DATE (MMDD) 0529 TRAN TIME (HHMMSS) 183110", "amount": "-20000.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R98", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 98, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-29", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /gpayrecharge@icici ICIC0DC0099/Google India Service", "amount": "-156.9", "balance": null, "transaction_type": "DEBIT", "reference_number": "R99", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 99, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-29", "description": "ONUS BNA DEP BNA SEQ NO767 ATM ID S1C027371 TRAN DATE (MMDD) 0529 TRAN TIME (HHMMSS) 163937", "amount": "-500.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R100", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 100, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-29", "description": "WITHDRAWAL TRANSFER RNWL_3227230946_JNS PMSBY 23 24 *********** 963 TRANSFER TO ********** IB PMSBY PREMIUM COLLECTION A/C 1", "amount": "-20.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R101", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 101, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-28", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /sathyaat18@okaxis IOBA0001976/SATHYA K", "amount": "-150.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R102", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 102, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-28", "description": "BY UPI CREDIT UPI/************/UPI XXXXX34269 /dominicvasanth777 1@oksbi SBIN0070601/VASANTHAN .", "amount": "174.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R103", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 103, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-28", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "99.0", "balance": null, "transaction_type": "CREDIT", "reference_number": "R104", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 104, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-23", "description": "BULK CHARGES ATM_AMC_Charges *****************", "amount": "-10.94", "balance": null, "transaction_type": "DEBIT", "reference_number": "R105", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 105, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-21", "description": "THRU UPI DEBIT UPI/************/UPI Intent XXXXX /meesho. payu@hdfcbank HDFC0000499/MEESHO COM", "amount": "-177.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R106", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 106, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-21", "description": "BY UPI CREDIT UPI/************/express XXXXX36065/paytm ********@paytm YESB0PTMUPI/Meesho", "amount": "187.94", "balance": null, "transaction_type": "CREDIT", "reference_number": "R107", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 107, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-20", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /paytmqr16xy22pb85@paytm YESB0PTMUPI/Kaala Fashion", "amount": "-90.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R108", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 108, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-20", "description": "THRU UPI DEBIT UPI/************/Pay to BharatPe Merc XXXXX /bharatpe09916629172@yesbankltd YESB0YESUPI/MS UDHAYA COMPUTERS", "amount": "-20.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R109", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 109, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-20", "description": "THRU UPI DEBIT UPI/************/Pay to BharatPe Merc XXXXX /bharatpe09916629172@yesbankltd YESB0YESUPI/MS UDHAYA COMPUTERS", "amount": "-20.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R110", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 110, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-20", "description": "BY TRANSFER NEFT/CITI/CITIN24469567931 /ZOMATO LIMIT/ TRANSFER FROM ***********", "amount": "-40.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R111", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 111, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-19", "description": "ONUS ATM WDL ATM WDL SEQ NO 3446 ATM ID HIT015271 SELF RS NO 66/4A EAST COAST PONDICHERRY TRAN DATE (MMDD) 0519 TRAN TIME (HHMMSS) 203210", "amount": "-200.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R112", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 112, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-19", "description": "BY UPI CREDIT UPI/************/UPI XXXXX28268 /ramachandran1854@oksbi SBIN0000900/RAMACHANDRAN G", "amount": "339.94", "balance": null, "transaction_type": "CREDIT", "reference_number": "R113", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 113, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-19", "description": "BY UPI CREDIT UPI/************/UPI XXXXX28268 /ramachandran1854@oksbi SBIN0000900/RAMACHANDRAN G", "amount": "335.94", "balance": null, "transaction_type": "CREDIT", "reference_number": "R114", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 114, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-19", "description": "THRU UPI DEBIT UPI/************/Pay to BharatPe Merc XXXXX /bharatpe09916629172@yesbankltd YESB0YESUPI/MS UDHAYA COMPUTERS", "amount": "-60.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R115", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 115, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-17", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /kkamataj 1@oksbi SBIN0009584/Kamaraj <PERSON>", "amount": "-100.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R116", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 116, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-17", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /paytmqrmpqwazb3o4@paytm YESB0PTMUPI/SIVAKUMAR", "amount": "-30.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R117", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 117, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-17", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "250.94", "balance": null, "transaction_type": "CREDIT", "reference_number": "R118", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 118, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-16", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /v.elumalai.abi 1@oksbi IDIB000P152/Mr V ELUMALAI", "amount": "-7.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R119", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 119, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-16", "description": "BY UPI CREDIT UPI/************/Payment from PhonePe XXXXX34269/vasanthansbi01@ybl SBIN0070601/VASANTHAN", "amount": "7.94", "balance": null, "transaction_type": "CREDIT", "reference_number": "R120", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 120, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-16", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /v.elumalai.abi 1@oksbi IDIB000P152/Mr V ELUMALAI", "amount": "-23.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R121", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 121, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-16", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /amazonsellerservices.********@hdfcbank HDFC0000499 /AMAZON SELLER SERVICES PRIVATE", "amount": "-230.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R122", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 122, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-15", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /zomatoorder1. gpay@okpayaxis UTIB0000553/ZOMATO LIMITED", "amount": "-99.85", "balance": null, "transaction_type": "DEBIT", "reference_number": "R123", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 123, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-15", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /q713016524@ybl YESB0YBLUPI/IRUDAYA AROKIA SHILPA S", "amount": "-35.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R124", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 124, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-15", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /q032433576@ybl YESB0YBLUPI/PRABA G", "amount": "-64.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R125", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 125, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-15", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /rajavegetableshop@sbi SBIN0064485/RAJA VEGETABLE SHOP", "amount": "-18.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R126", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 126, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-15", "description": "BY UPI CREDIT UPI/************/UPI XXXXX08597 /deepulakshmi2050@okhdfcbank CNRB0000033/S DEEPALAKSHMI", "amount": "470.79", "balance": null, "transaction_type": "CREDIT", "reference_number": "R127", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 127, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-15", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "390.79", "balance": null, "transaction_type": "CREDIT", "reference_number": "R128", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 128, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-15", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "378.79", "balance": null, "transaction_type": "CREDIT", "reference_number": "R129", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 129, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-15", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /googlerecharge@icici ICIC0DC0099/Google India Digital Services", "amount": "-180.9", "balance": null, "transaction_type": "DEBIT", "reference_number": "R130", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 130, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-15", "description": "THRU UPI DEBIT UPI/************/lic XXXXX /<EMAIL> sbin0070601/Mr VASANTHAN", "amount": "-1158.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R131", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 131, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-15", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "1477.69", "balance": null, "transaction_type": "CREDIT", "reference_number": "R132", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 132, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-15", "description": "THRU UPI DEBIT UPI/************/UPI Intent XXXXX /meesho. payu@hdfcbank HDFC0000499/MEESHO COM", "amount": "-165.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R133", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 133, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-15", "description": "THRU UPI DEBIT UPI/************/UPI Intent XXXXX /paytm ********@paytm YESB0PTMUPI/Meesho", "amount": "-138.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R134", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 134, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-13", "description": "BY TRANSFER NEFT/CITI/CITIN24467243627 /ZOMATO LIMIT/ TRANSFER FROM ***********", "amount": "-493.43", "balance": null, "transaction_type": "DEBIT", "reference_number": "R135", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 135, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-13", "description": "BY TRANSFER NEFT/CITI/CITIN24467262013 /ZOMATO LIMIT/ TRANSFER FROM ***********", "amount": "-80.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R136", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 136, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-13", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /<EMAIL> utib0002694/", "amount": "-300.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R137", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 137, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-13", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /*********@axl IOBA0002693/R SIVAKUMAR", "amount": "-50.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R138", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 138, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-13", "description": "THRU UPI DEBIT UPI/************/this one from my sid XXXXX /deepulakshmi2050@okhdfcbank CNRB0000033/S DEEPALAKSHMI", "amount": "-500.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R139", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 139, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-13", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /deepulakshmi2050@okhdfcbank CNRB0000033/S DEEPALAKSHMI", "amount": "-1000.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R140", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 140, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-13", "description": "ONUS BNA DEP BNA SEQ NO510 ATM ID HIT015271 TRAN DATE (MMDD) 0513 TRAN TIME (HHMMSS) 001705", "amount": "-1100.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R141", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 141, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-12", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /paytmqrtex44v9q0v@paytm YESB0PTMUPI/SRI AMMAN XEROX SHOP", "amount": "-40.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R142", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 142, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-12", "description": "THRU UPI DEBIT UPI/************/********** XXXXX /cf. swiggy@kotak KKBK0000958/Swiggy", "amount": "-40.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R143", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 143, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-12", "description": "THRU UPI DEBIT UPI/************/********** XXXXX /cf. swiggy@kotak KKBK0000958/Swiggy", "amount": "-370.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R144", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 144, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-12", "description": "BY UPI CREDIT UPI/************/Payment from PhonePe XXXXX44859/**********@axl IOBA0001619/VAISHALI G", "amount": "1307.26", "balance": null, "transaction_type": "CREDIT", "reference_number": "R145", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 145, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-12", "description": "THRU UPI DEBIT UPI/************/********** XXXXX /cf. swiggy@kotak KKBK0000958/Swiggy", "amount": "-50.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R146", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 146, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-12", "description": "BY UPI CREDIT UPI/************/Payment from PhonePe XXXXX09009/********** 2@axl HDFC0000407/MALATHI MAYAKRISHNAN", "amount": "1130.26", "balance": null, "transaction_type": "CREDIT", "reference_number": "R147", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 147, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-12", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /q677490301@ybl YESB0YBLUPI/MINAKSHI VEG RESTAUR", "amount": "-42.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R148", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 148, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-12", "description": "ONUS BNA DEP BNA SEQ NO3948 ATM ID S1C015272 TRAN DATE (MMDD) 0512 TRAN TIME (HHMMSS) 151814", "amount": "-700.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R149", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 149, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-12", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "222.26", "balance": null, "transaction_type": "CREDIT", "reference_number": "R150", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 150, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-12", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /deepulakshmi2050@okhdfcbank CNRB0000033/S DEEPALAKSHMI", "amount": "-400.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R151", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 151, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-11", "description": "ONUS BNA DEP BNA SEQ NO3437 ATM ID S1C027371 TRAN DATE (MMDD) 0510 TRAN TIME (HHMMSS) 232044", "amount": "-200.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R152", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 152, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-11", "description": "BY UPI CREDIT UPI/************/UPI XXXXX66023 /srivatsu2@oksbi SBIN0000929/VATHSALA S", "amount": "222.26", "balance": null, "transaction_type": "CREDIT", "reference_number": "R153", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 153, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-08", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /deepulakshmi2050@okhdfcbank CNRB0000033/S DEEPALAKSHMI", "amount": "-600.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R154", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 154, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-08", "description": "BY UPI CREDIT UPI/************/Payment from PhonePe XXXXX34269/vasanthansbi01@ybl SBIN0070601/VASANTHAN", "amount": "606.26", "balance": null, "transaction_type": "CREDIT", "reference_number": "R155", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 155, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-06", "description": "BY TRANSFER NEFT/CITI/CITIN24464613200 /ZOMATO LIMIT/ TRANSFER FROM ***********", "amount": "-6.13", "balance": null, "transaction_type": "DEBIT", "reference_number": "R156", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 156, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-04", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /<EMAIL> sbin0070601/Mr VASANTHAN", "amount": "-98.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R157", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 157, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-04", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /v.elumalai.abi 1@oksbi IDIB000P152/Mr V ELUMALAI", "amount": "-3100.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R158", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 158, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-04", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /jacobpushparaj65 1@okicici IOBA0003220/V JACOB PUSHPARAJ", "amount": "-100.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R159", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 159, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-04", "description": "BY UPI CREDIT UPI/************/UPI XXXXX99843/shreekutty. sc@okicici IDIB000C038/Ms C PADMA SHREE", "amount": "3298.13", "balance": null, "transaction_type": "CREDIT", "reference_number": "R160", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 160, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-04", "description": "BY UPI CREDIT UPI/************/UPI XXXXX08597 /deepulakshmi2050@okhdfcbank CNRB0000033/S DEEPALAKSHMI", "amount": "3186.23", "balance": null, "transaction_type": "CREDIT", "reference_number": "R161", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 161, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-04", "description": "BY UPI CREDIT UPI/************/UPI XXXXX08597 /deepulakshmi2050@okhdfcbank CNRB0000033/S DEEPALAKSHMI", "amount": "1686.23", "balance": null, "transaction_type": "CREDIT", "reference_number": "R162", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 162, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-04", "description": "THRU UPI DEBIT UPI/************/Oid41805223@SNAPMINT XXXXX43726/paytm ********@paytm YESB0PTMUPI/Snapmint Credit Advisory Pvt L", "amount": "186.23", "balance": null, "transaction_type": "CREDIT", "reference_number": "R163", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 163, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-04", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "3833.23", "balance": null, "transaction_type": "CREDIT", "reference_number": "R164", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 164, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-04", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "3133.23", "balance": null, "transaction_type": "CREDIT", "reference_number": "R165", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 165, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-03", "description": "BY UPI CREDIT UPI/************/UPI XXXXX89269 /ppspannerppspanner64899@okhdfcbank FDRL0002349 /PANNERSELVAM", "amount": "94.23", "balance": null, "transaction_type": "CREDIT", "reference_number": "R166", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 166, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-03", "description": "BY TRANSFER /IMPS/P2A/************/I47543162292/ZOMATO LIMIT TRANSFER FROM ***********", "amount": "-15.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R167", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 167, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-03", "description": "BY UPI CREDIT UPI/************/UPI XXXXX69394 /pradeeparokiaraj2004 1@okicici IOBA0000070/X PRADEEP AROKIARAJ", "amount": "59.23", "balance": null, "transaction_type": "CREDIT", "reference_number": "R168", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 168, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-03", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-30.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R169", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 169, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-02", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /v.elumalai.abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "-3900.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R170", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 170, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-02", "description": "BY UPI CREDIT UPI/************/Payment from PhonePe XXXXX34269/vasanthansbi01@ybl SBIN0070601/VASANTHAN", "amount": "3934.23", "balance": null, "transaction_type": "CREDIT", "reference_number": "R171", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 171, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-02", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /<EMAIL> sbin0070601/Mr VASANTHAN", "amount": "-10.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R172", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 172, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-01", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /<EMAIL> sbin0070601/Mr VASANTHAN", "amount": "-3300.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R173", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 173, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-01", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /deepulakshmi2050@okhdfcbank CNRB0000033/S DEEPALAKSHMI", "amount": "-30.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R174", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 174, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-01", "description": "BY TRANSFER /IMPS/P2A/************/I47539270278/ZOMATO LIMIT TRANSFER FROM ***********", "amount": "-45.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R175", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 175, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-01", "description": "ONUS BNA DEP BNA SEQ NO8163 ATM ID S1C027371 TRAN DATE (MMDD) 0501 TRAN TIME (HHMMSS) 145700", "amount": "-600.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R176", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 176, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-05-01", "description": "ONUS BNA DEP BNA SEQ NO8161 ATM ID S1C027371 TRAN DATE (MMDD) 0501 TRAN TIME (HHMMSS) 145536", "amount": "-2600.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R177", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 177, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-30", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "129.23", "balance": null, "transaction_type": "CREDIT", "reference_number": "R178", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 178, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-30", "description": "BY TRANSFER /IMPS/P2A/************/I47537009712/ZOMATO LIMIT TRANSFER FROM ***********", "amount": "-120.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R179", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 179, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-30", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /<EMAIL> sbin0070601/Mr VASANTHAN", "amount": "-100.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R180", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 180, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-30", "description": "BY UPI CREDIT UPI/************/UPI XXXXX57407 /sailiparulekar11@okaxis SBIN0000509/<PERSON><PERSON>", "amount": "108.23", "balance": null, "transaction_type": "CREDIT", "reference_number": "R181", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 181, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-28", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /<EMAIL> sbin0070601/Mr VASANTHAN", "amount": "-2100.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R182", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 182, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-28", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-40.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R183", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 183, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-28", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /yetiyalamanda@okaxis ESFB0001001/Yeti <PERSON>anda", "amount": "-30.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R184", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 184, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-28", "description": "BY TRANSFER /IMPS/P2A/************/I47532617079/ZOMATO LIMIT TRANSFER FROM ***********", "amount": "-30.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R185", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 185, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-28", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-650.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R186", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 186, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-28", "description": "BY UPI CREDIT UPI/************/UPI XXXXX42354 /mohana<PERSON><PERSON><PERSON><PERSON>.<PERSON><PERSON><PERSON>@okicici SBIN0006842/MOHANA KRISHNAN S", "amount": "2818.23", "balance": null, "transaction_type": "CREDIT", "reference_number": "R187", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 187, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-28", "description": "BY UPI CREDIT UPI/************/UPI XXXXX43575 /aanandrajmaya1@okicici ICIC0000056/ANANDRAJ SOUPRAMANIEN T", "amount": "1106.23", "balance": null, "transaction_type": "CREDIT", "reference_number": "R188", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 188, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-28", "description": "BY UPI CREDIT UPI/************/UPI XXXXX86628 /indiravenkatachalam85@okhdfcbank IDIB000M054/Mrs S Indira", "amount": "906.23", "balance": null, "transaction_type": "CREDIT", "reference_number": "R189", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 189, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-28", "description": "BY UPI CREDIT UPI/************/CASHFREE XXXXX92995 /poweraccess.cashfree@axisbank utib0001920/Cashfree Payments", "amount": "448.23", "balance": null, "transaction_type": "CREDIT", "reference_number": "R190", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 190, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-26", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /gpayrecharge@okpayaxis UTIB0000553/Google India Digital Services", "amount": "-270.9", "balance": null, "transaction_type": "DEBIT", "reference_number": "R191", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 191, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-26", "description": "BY UPI CREDIT UPI/************/UPI XXXXX08597 /deepulakshmi2050@okhdfcbank CNRB0000033/S DEEPALAKSHMI", "amount": "272.23", "balance": null, "transaction_type": "CREDIT", "reference_number": "R192", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 192, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-26", "description": "BY UPI CREDIT UPI/************/Payment from PhonePe XXXXX34269/vasanthansbi01@ybl SBIN0070601/VASANTHAN", "amount": "268.23", "balance": null, "transaction_type": "CREDIT", "reference_number": "R193", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 193, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-25", "description": "ONUS ATM WDL ATM WDL SEQ NO 5022 ATM ID S1T006031 SELF VILLUPURAM ROAD VILLUPURAM TRAN DATE (MMDD) 0425 TRAN TIME (HHMMSS) 162201", "amount": "-1500.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R194", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 194, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-25", "description": "BY UPI CREDIT UPI/************/Payment from PhonePe XXXXX34269/vasanthansbi01@ybl SBIN0070601/VASANTHAN", "amount": "1528.23", "balance": null, "transaction_type": "CREDIT", "reference_number": "R195", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 195, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-23", "description": "THRU UPI DEBIT UPI/************/UwOQpjDsjkxOZ2PnHNmU XXXXX00037/bsestarmfrzp@icici ICIC0DC0099 /IndianClearingCorporation", "amount": "-100.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R196", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 196, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-23", "description": "BY UPI CREDIT UPI/************/Stock XXXXX34269 /vasanthansbi01@ybl SBIN0070601/VASANTHAN", "amount": "128.23", "balance": null, "transaction_type": "CREDIT", "reference_number": "R197", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 197, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-23", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /sadhikbatcha268@okicici CIUB0000275/Mr SATHICK BATCHA M", "amount": "-600.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R198", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 198, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-22", "description": "BY TRANSFER NEFT/CITI/CITIN24456076164 /ZOMATO LIMIT/ TRANSFER FROM ***********", "amount": "-617.8", "balance": null, "transaction_type": "DEBIT", "reference_number": "R199", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 199, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-22", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /<EMAIL> sbin0070601/Mr VASANTHAN", "amount": "-2400.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R200", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 200, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-22", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /gpay ***********@okbizaxis UTIB0000000/NATRAJ GRAPHICS", "amount": "-30.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R201", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 201, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-22", "description": "BY TRANSFER /IMPS/P2A/************/I47517493682/ZOMATO LIM TRANSFER FROM ***********", "amount": "-40.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R202", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 202, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-20", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@oksbi SBIN0016563/YOGESH H", "amount": "-100.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R203", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 203, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-20", "description": "ONUS BNA DEP BNA SEQ NO2561 ATM ID S1C027371 TRAN DATE (MMDD) 0420 TRAN TIME (HHMMSS) 130944", "amount": "-100.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R204", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 204, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-20", "description": "ONUS BNA DEP BNA SEQ NO2559 ATM ID S1C027371 TRAN DATE (MMDD) 0420 TRAN TIME (HHMMSS) 130810", "amount": "-2400.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R205", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 205, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-20", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /dharba75 1@oksbi HDFC0000407/R DHARBARANESWARAN", "amount": "-300.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R206", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 206, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-20", "description": "BY UPI CREDIT UPI/************/Payment from PhonePe XXXXX34269/vasanthansbi01@ybl SBIN0070601/VASANTHAN", "amount": "300.43", "balance": null, "transaction_type": "CREDIT", "reference_number": "R207", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 207, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-20", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "200.43", "balance": null, "transaction_type": "CREDIT", "reference_number": "R208", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 208, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-19", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /v.elumalai.abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "-49.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R209", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 209, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-19", "description": "BY TRANSFER /IMPS/P2A/************/I47512513190/ZOMATO LIMIT TRANSFER FROM ***********", "amount": "-20.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R210", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 210, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-19", "description": "THRU UPI DEBIT UPI/************/079UAQRNtugjmYkMZqgo XXXXX56987/zerodhamf@hdfcbank HDFC0000060/ICCL ZERODHA COIN", "amount": "-100.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R211", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 211, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-19", "description": "THRU UPI DEBIT UPI/************/2oDmEXyxFgNUhuRxGRKp XXXXX56987/zerodhamf@hdfcbank HDFC0000060/ICCL ZERODHA COIN", "amount": "-100.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R212", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 212, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-19", "description": "THRU UPI DEBIT UPI/************/b10Yikr07skXhPBNlS9u XXXXX00037/bsestarmfrzp@icici ICIC0DC0099 /IndianClearingCorporation", "amount": "-100.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R213", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 213, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-19", "description": "BY UPI CREDIT UPI/************/Payment from PhonePe XXXXX34269/vasanthansbi01@ybl SBIN0070601/VASANTHAN", "amount": "329.43", "balance": null, "transaction_type": "CREDIT", "reference_number": "R214", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 214, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-17", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-5.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R215", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 215, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-17", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-168.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R216", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 216, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-17", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-209.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R217", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 217, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-17", "description": "BY TRANSFER /IMPS/P2A/************/I47506505059/ZOMATO LIM TRANSFER FROM ***********", "amount": "-15.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R218", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 218, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-17", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-50.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R219", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 219, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-16", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /paytmqr281005050101o10a4w4yd7vl@paytm YESB0PTMUPI/N S AGENCIES", "amount": "-150.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R220", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 220, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-16", "description": "BY UPI CREDIT UPI/************/UPI XXXXX45086 /saro8675445086@oksbi SBIN0007850/PERUMAL SARAVANAN", "amount": "596.43", "balance": null, "transaction_type": "CREDIT", "reference_number": "R221", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 221, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-16", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /<EMAIL> sbin0070601/Mr VASANTHAN", "amount": "-1050.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R222", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 222, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-16", "description": "BY UPI CREDIT UPI/************/UPI XXXXX99329 /tomailcaleb@oksbi SBIN0006842/CALEB T", "amount": "1496.43", "balance": null, "transaction_type": "CREDIT", "reference_number": "R223", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 223, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-15", "description": "BY TRANSFER NEFT/CITI/CITIN24453729214 /ZOMATO LIMIT/ TRANSFER FROM ***********", "amount": "-330.94", "balance": null, "transaction_type": "DEBIT", "reference_number": "R224", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 224, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-14", "description": "BY TRANSFER /IMPS/P2A/************/I47501948882/ZOMATO LIMIT TRANSFER FROM ***********", "amount": "-65.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R225", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 225, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-14", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-250.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R226", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 226, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-14", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "300.49", "balance": null, "transaction_type": "CREDIT", "reference_number": "R227", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 227, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-13", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /v.elumalai.abi 1@oksbi IDIB000P152/Mr V ELUMALAI", "amount": "-400.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R228", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 228, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-13", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /v.elumalai.abi 1@oksbi IDIB000P152/Mr V ELUMALAI", "amount": "-700.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R229", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 229, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-13", "description": "ONUS BNA DEP BNA SEQ NO895 ATM ID S1C015272 TRAN DATE (MMDD) 0413 TRAN TIME (HHMMSS) 180823", "amount": "-1300.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R230", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 230, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-13", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /v.elumalai.abi 1@oksbi IDIB000P152/Mr V ELUMALAI", "amount": "-400.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R231", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 231, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-12", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /mahalakshmmaha21@okaxis UJVN0001197/MAHALAKSHMI", "amount": "-200.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R232", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 232, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-12", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "600.49", "balance": null, "transaction_type": "CREDIT", "reference_number": "R233", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 233, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-12", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /deepulakshmi2050@okhdfcbank CNRB0000033/S DEEPALAKSHMI", "amount": "-100.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R234", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 234, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-12", "description": "BY UPI CREDIT UPI/************/UPI XXXXX22530 /v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@oksbi SBIN0016563/YOGESH H", "amount": "100.49", "balance": null, "transaction_type": "CREDIT", "reference_number": "R235", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 235, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-12", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /balaramanmadhavan28@okicici UBIN0904422/MADHAVAN B", "amount": "-200.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R236", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 236, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-12", "description": "BY UPI CREDIT UPI/************/UPI XXXXX22530 /v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@oksbi SBIN0016563/YOGESH H", "amount": "200.49", "balance": null, "transaction_type": "CREDIT", "reference_number": "R237", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 237, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-12", "description": "BY UPI CREDIT UPI/************/UPI XXXXX22530 /v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@oksbi SBIN0016563/YOGESH H", "amount": "190.49", "balance": null, "transaction_type": "CREDIT", "reference_number": "R238", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 238, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-11", "description": "BY UPI CREDIT UPI/************/Payment from PhonePe XXXXX34269/vasanthansbi01@ybl SBIN0070601/VASANTHAN", "amount": "90.49", "balance": null, "transaction_type": "CREDIT", "reference_number": "R239", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 239, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-11", "description": "THRU UPI DEBIT UPI/************/UPI Intent XXXXX /paytm ********@paytm YESB0PTMUPI/Meesho", "amount": "-267.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R240", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 240, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-11", "description": "BY UPI CREDIT UPI/************/Payment from PhonePe XXXXX34269/vasanthansbi01@ybl SBIN0070601/VASANTHAN", "amount": "268.49", "balance": null, "transaction_type": "CREDIT", "reference_number": "R241", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 241, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-11", "description": "THRU UPI DEBIT UPI/************/UPI Intent XXXXX /meesho@axl UTIB0AXLUPI/Meesho", "amount": "-193.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R242", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 242, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-10", "description": "BULK CHARGES SMS_CHGS_DEC 23_QTR *****************", "amount": "-14.4", "balance": null, "transaction_type": "DEBIT", "reference_number": "R243", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 243, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-10", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /lakshmidevi33567@okaxis DBSS0IN0811/B <PERSON>", "amount": "-30.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R244", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 244, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-10", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /shadowfax@icici ICIC0DC0099/Shadowfax", "amount": "-238.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R245", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 245, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-09", "description": "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd", "amount": "-210.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R246", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 246, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-09", "description": "ONUS BNA DEP BNA SEQ NO6822 ATM ID HIT015271 TRAN DATE (MMDD) 0409 TRAN TIME (HHMMSS) 212835", "amount": "-900.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R247", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 247, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-08", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /paytmqr1jiklsidkq@paytm YESB0PTMUPI/ANITHA STORES", "amount": "-80.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R248", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 248, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-08", "description": "BY UPI CREDIT UPI/************/UPI XXXXX16269/v.elumalai. abi 1@okaxis IDIB000P152/Mr V <PERSON><PERSON><PERSON>", "amount": "128.89", "balance": null, "transaction_type": "CREDIT", "reference_number": "R249", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 249, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-08", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /deepulakshmi2050@okhdfcbank CNRB0000033/S DEEPALAKSHMI", "amount": "-100.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R250", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 250, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-08", "description": "THRU UPI DEBIT UPI/************/first vaaganaa money XXXXX /deepulakshmi2050@okhdfcbank CNRB0000033/S DEEPALAKSHMI", "amount": "-400.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R251", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 251, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-08", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /q28049208@ybl YESB0YBLUPI/S Amaresh", "amount": "-35.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R252", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 252, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-08", "description": "BY TRANSFER NEFT/CITI/CITIN24451114667 /ZOMATO LIMIT/ TRANSFER FROM ***********", "amount": "-533.61", "balance": null, "transaction_type": "DEBIT", "reference_number": "R253", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 253, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-08", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /v.elumalai.abi 1@oksbi IDIB000P152/Mr V ELUMALAI", "amount": "-10.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R254", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 254, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-08", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /v.elumalai.abi 1@oksbi IDIB000P152/Mr V ELUMALAI", "amount": "-40.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R255", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 255, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-07", "description": "THRU UPI DEBIT UPI/************/********** XXXXX /cf. swiggy@kotak KKBK0000958/Swiggy", "amount": "-30.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R256", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 256, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-07", "description": "THRU UPI DEBIT UPI/************/********** XXXXX /cf. swiggy@kotak KKBK0000958/Swiggy", "amount": "-177.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R257", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 257, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-07", "description": "THRU UPI DEBIT UPI/************/balance amount XXXXX /balaramanmadhavan28@okicici UBIN0904422/MADHAVAN B", "amount": "-500.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R258", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 258, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-07", "description": "THRU UPI DEBIT UPI/************/emi XXXXX /balaramanmadhavan28@okicici UBIN0904422/MADHAVAN B", "amount": "-500.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R259", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 259, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-07", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /wattsmadhavan2@oksbi SBIN0012798/Watts M Jesudoss", "amount": "-323.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R260", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 260, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-07", "description": "ONUS BNA DEP BNA SEQ NO7690 ATM ID S1C015272 TRAN DATE (MMDD) 0407 TRAN TIME (HHMMSS) 204931", "amount": "-500.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R261", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 261, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-07", "description": "ONUS BNA DEP BNA SEQ NO7687 ATM ID S1C015272 TRAN DATE (MMDD) 0407 TRAN TIME (HHMMSS) 204740", "amount": "-1100.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R262", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 262, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-07", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /v.elumalai.abi 1@oksbi IDIB000P152/Mr V ELUMALAI", "amount": "-24.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R263", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 263, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-07", "description": "BY UPI CREDIT UPI/************/Payment from PhonePe XXXXX34269/vasanthansbi01@ybl SBIN0070601/VASANTHAN", "amount": "24.28", "balance": null, "transaction_type": "CREDIT", "reference_number": "R264", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 264, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-06", "description": "WITHDRAWAL TRANSFER APYSCF_11_2023_500618097438_PENALTY_5 TRANSFER TO ********** IB Collection A/C APY Subsequent Contribution N", "amount": "-89.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R265", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 265, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-05", "description": "BY TRANSFER /IMPS/P2A/************/I47479997765/ZOMATO LIM TRANSFER FROM ***********", "amount": "-40.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R266", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 266, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}, {"date": "2024-04-05", "description": "THRU UPI DEBIT UPI/************/UPI XXXXX /<EMAIL> sbin0070601/Mr VASANTHAN", "amount": "-1200.0", "balance": null, "transaction_type": "DEBIT", "reference_number": "R267", "cheque_number": null, "branch_code": null, "source_file": "C:\\Users\\<USER>\\Documents\\GitHub\\bank_statement_analyzer_standalone\\statements\\Indian bank\\Format Changed\\mar to jun.xlsx", "source_line": 267, "bank_name": "Unknown", "account_number": null, "is_processed": false, "processing_errors": []}]