{"description": "Optimized patterns for better AI categorization effectiveness", "version": "2.0", "optimizations": ["Reduced manual pattern sensitivity", "Increased AI suitability thresholds", "Added more generic patterns", "Refined merchant detection"], "manual_patterns": ["\\bMr\\.\\s+[A-Z][a-z]+\\s+[A-Z][a-z]+\\b", "\\bMs\\.\\s+[A-Z][a-z]+\\s+[A-Z][a-z]+\\b", "\\bDr\\.\\s+[A-Z][a-z]+\\s+[A-Z][a-z]+\\b", "\\bREF\\s*#\\s*[A-Z0-9]{8,}\\b", "\\bTXN\\s*#\\s*[A-Z0-9]{8,}\\b", "\\bINV\\s*#\\s*[A-Z0-9]{8,}\\b", "\\b[A-Z0-9]{15,}\\b"], "generic_patterns": {"banking": ["atm\\s+withdrawal", "cash\\s+withdrawal", "bank\\s+transfer", "wire\\s+transfer", "online\\s+transfer", "deposit", "direct\\s+deposit"], "shopping": ["grocery\\s+store", "supermarket", "department\\s+store", "shopping\\s+mall", "retail\\s+store", "online\\s+purchase", "e-commerce", "marketplace"], "food_dining": ["restaurant", "cafe", "coffee\\s+shop", "fast\\s+food", "food\\s+court", "dining", "takeout", "delivery", "pizza", "burger"], "transportation": ["gas\\s+station", "fuel\\s+station", "petrol\\s+pump", "taxi\\s+fare", "cab\\s+ride", "ride\\s+share", "uber", "lyft", "bus\\s+fare", "train\\s+ticket", "metro\\s+card"], "utilities": ["electric\\s+bill", "electricity\\s+payment", "power\\s+bill", "phone\\s+bill", "mobile\\s+bill", "internet\\s+bill", "broadband\\s+payment", "utility\\s+bill", "water\\s+bill", "gas\\s+bill"], "healthcare": ["pharmacy", "drug\\s+store", "medical\\s+store", "hospital", "clinic", "doctor", "dentist", "medical\\s+center"], "entertainment": ["subscription", "streaming\\s+service", "netflix", "spotify", "amazon\\s+prime", "movie\\s+theater", "cinema", "gym\\s+membership", "fitness\\s+center"]}, "excluded_patterns": ["test\\s+transaction", "pending\\s+transaction", "error\\s+transaction", "failed\\s+transaction", "cancelled\\s+transaction", "reversal\\s+transaction"], "thresholds": {"manual_score_threshold": 0.5, "generic_score_threshold": 0.4, "merchant_score_threshold": 0.6, "complexity_threshold": 0.4, "amount_threshold": 0.2}}