# Bank Statement Analyzer Requirements
# Additional requirements for the bank statement analyzer application

# PDF processing
PyPDF2>=3.0.0
pdfplumber>=0.9.0

# Excel file processing
openpyxl>=3.1.0
xlrd>=2.0.0

# Data processing (should already be available from main app)
pandas>=1.5.0

# GUI framework (should already be available from main app)
PySide6>=6.5.0

# Optional: For better text extraction from PDFs
# pymupdf>=1.23.0  # Uncomment if needed for better PDF parsing

# Machine Learning for transaction categorization
scikit-learn>=1.3.0
nltk>=3.8.0
joblib>=1.3.0
numpy>=1.24.0
