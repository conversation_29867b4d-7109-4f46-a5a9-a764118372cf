#!/usr/bin/env python3
"""
Check if the 231 transactions are duplicates and combine any additional data
"""

import sys
import pandas as pd
from pathlib import Path
from datetime import datetime

# Add the bank_analyzer module to the path
sys.path.insert(0, str(Path(__file__).parent))


def compare_data_sources():
    """Compare labeling history vs training data to see if they're the same"""
    print("🔍 Comparing Data Sources for Duplicates")
    print("=" * 60)
    
    try:
        # Load both files
        history_path = Path("bank_analyzer_config/ml_data/labeling_history.csv")
        training_path = Path("bank_analyzer_config/ml_data/unique_transactions.csv")
        
        history_df = pd.read_csv(history_path)
        training_df = pd.read_csv(training_path)
        
        print(f"📊 Labeling history: {len(history_df)} entries, {history_df['hash_id'].nunique()} unique")
        print(f"📊 Training data: {len(training_df)} entries, {training_df['hash_id'].nunique()} unique")
        
        # Get unique hash IDs from each
        history_hashes = set(history_df['hash_id'].unique())
        training_hashes = set(training_df['hash_id'].unique())
        
        # Check overlap
        overlap = history_hashes.intersection(training_hashes)
        only_in_history = history_hashes - training_hashes
        only_in_training = training_hashes - history_hashes
        
        print(f"\n📊 Comparison Results:")
        print(f"   Overlap (same transactions): {len(overlap)}")
        print(f"   Only in history: {len(only_in_history)}")
        print(f"   Only in training: {len(only_in_training)}")
        
        if len(overlap) == len(history_hashes) == len(training_hashes):
            print(f"\n✅ CONFIRMED: The 231 transactions are exactly the same!")
            print(f"   Both sources contain identical transaction sets")
            return True, history_df, training_df
        else:
            print(f"\n⚠️ DIFFERENT: The sources contain different transactions")
            if len(only_in_history) > 0:
                print(f"   {len(only_in_history)} additional transactions in history")
            if len(only_in_training) > 0:
                print(f"   {len(only_in_training)} additional transactions in training")
            return False, history_df, training_df
            
    except Exception as e:
        print(f"❌ Error comparing data: {str(e)}")
        return False, None, None


def search_for_additional_data():
    """Search for any additional labeled data we might have missed"""
    print(f"\n🔍 Searching for Additional Labeled Data")
    print("=" * 60)
    
    additional_sources = []
    
    # Check if there are any other CSV files with transaction data
    search_dirs = [
        Path("bank_analyzer_config/ml_data/"),
        Path("bank_analyzer_config/"),
        Path("./"),
        Path("data/") if Path("data/").exists() else None,
        Path("backup/") if Path("backup/").exists() else None
    ]
    
    for search_dir in search_dirs:
        if search_dir and search_dir.exists():
            print(f"📁 Searching in: {search_dir}")
            
            # Look for CSV files
            for csv_file in search_dir.glob("*.csv"):
                if csv_file.name not in ["labeling_history.csv", "unique_transactions.csv"]:
                    try:
                        df = pd.read_csv(csv_file)
                        
                        # Check if it has transaction-like data
                        has_hash_id = 'hash_id' in df.columns
                        has_category = 'category' in df.columns
                        has_description = any(col in df.columns for col in ['description', 'transaction', 'desc'])
                        
                        if has_hash_id and has_category:
                            labeled_count = len(df[df['category'].notna() & (df['category'] != '') & (df['category'] != 'Uncategorized')])
                            if labeled_count > 0:
                                print(f"   ✅ Found: {csv_file.name} - {labeled_count} labeled transactions")
                                additional_sources.append((csv_file, df, labeled_count))
                        
                    except Exception as e:
                        print(f"   ❌ Error reading {csv_file.name}: {str(e)}")
    
    return additional_sources


def check_categorization_service_for_more_data():
    """Check if categorization service has more data than what's in training"""
    print(f"\n🔍 Checking Categorization Service for Additional Data")
    print("=" * 60)
    
    try:
        from bank_analyzer.ml.transaction_categorization_service import TransactionCategorizationService
        
        service = TransactionCategorizationService()
        existing_transactions = service._load_existing_transactions()
        
        print(f"📊 Total transactions in service: {len(existing_transactions)}")
        
        # Count categorized transactions
        categorized_count = 0
        categorized_hashes = set()
        
        for hash_id, txn_data in existing_transactions.items():
            if txn_data.get('category') and txn_data.get('category') != 'Uncategorized':
                categorized_count += 1
                categorized_hashes.add(hash_id)
        
        print(f"📊 Categorized transactions in service: {categorized_count}")
        
        # Compare with current training data
        training_path = Path("bank_analyzer_config/ml_data/unique_transactions.csv")
        if training_path.exists():
            training_df = pd.read_csv(training_path)
            training_hashes = set(training_df['hash_id'].unique())
            
            additional_in_service = categorized_hashes - training_hashes
            print(f"📊 Additional categorized in service: {len(additional_in_service)}")
            
            if len(additional_in_service) > 0:
                print(f"✅ Found {len(additional_in_service)} additional categorized transactions in service!")
                return len(additional_in_service), existing_transactions
        
        return 0, existing_transactions
        
    except Exception as e:
        print(f"❌ Error checking service: {str(e)}")
        return 0, {}


def combine_all_available_data():
    """Combine all available labeled data into one comprehensive dataset"""
    print(f"\n🔧 Combining All Available Labeled Data")
    print("=" * 60)
    
    try:
        all_labeled_data = {}  # hash_id -> transaction data
        
        # Start with current training data
        training_path = Path("bank_analyzer_config/ml_data/unique_transactions.csv")
        if training_path.exists():
            training_df = pd.read_csv(training_path)
            labeled_training = training_df[training_df['is_manually_labeled'] == True]
            
            print(f"📊 Starting with training data: {len(labeled_training)} labeled transactions")
            
            for _, row in labeled_training.iterrows():
                all_labeled_data[row['hash_id']] = {
                    'hash_id': row['hash_id'],
                    'description': row.get('description', ''),
                    'category': row['category'],
                    'sub_category': row['sub_category'],
                    'confidence': row.get('confidence', 1.0),
                    'source': 'training_data'
                }
        
        # Add from categorization service if it has additional data
        additional_count, service_data = check_categorization_service_for_more_data()
        
        if additional_count > 0:
            print(f"📊 Adding from service: {additional_count} additional transactions")
            
            for hash_id, txn_data in service_data.items():
                if (hash_id not in all_labeled_data and 
                    txn_data.get('category') and 
                    txn_data.get('category') != 'Uncategorized'):
                    
                    all_labeled_data[hash_id] = {
                        'hash_id': hash_id,
                        'description': txn_data.get('description', ''),
                        'category': txn_data['category'],
                        'sub_category': txn_data.get('sub_category', ''),
                        'confidence': txn_data.get('confidence', 1.0),
                        'source': 'categorization_service'
                    }
        
        # Check additional sources
        additional_sources = search_for_additional_data()
        
        for file_path, df, labeled_count in additional_sources:
            print(f"📊 Adding from {file_path.name}: {labeled_count} transactions")
            
            for _, row in df.iterrows():
                if (hasattr(row, 'hash_id') and row['hash_id'] not in all_labeled_data and
                    hasattr(row, 'category') and row['category'] and row['category'] != 'Uncategorized'):
                    
                    all_labeled_data[row['hash_id']] = {
                        'hash_id': row['hash_id'],
                        'description': row.get('description', ''),
                        'category': row['category'],
                        'sub_category': row.get('sub_category', ''),
                        'confidence': row.get('confidence', 1.0),
                        'source': f'file_{file_path.name}'
                    }
        
        print(f"\n📊 Total combined labeled data: {len(all_labeled_data)} unique transactions")
        
        # Show breakdown by source
        source_counts = {}
        for data in all_labeled_data.values():
            source = data['source']
            source_counts[source] = source_counts.get(source, 0) + 1
        
        print(f"📋 Breakdown by source:")
        for source, count in source_counts.items():
            print(f"   {source}: {count} transactions")
        
        return all_labeled_data
        
    except Exception as e:
        print(f"❌ Error combining data: {str(e)}")
        import traceback
        traceback.print_exc()
        return {}


def create_enhanced_training_data(combined_data):
    """Create enhanced training data with all available labeled transactions"""
    print(f"\n🚀 Creating Enhanced Training Data")
    print("=" * 60)
    
    try:
        if not combined_data:
            print("❌ No combined data to work with")
            return False
        
        # Create training records
        training_records = []
        
        for hash_id, data in combined_data.items():
            record = {
                'hash_id': hash_id,
                'description': data.get('description', f"Transaction_{hash_id}"),
                'normalized_description': data.get('description', f"transaction_{hash_id}").lower(),
                'frequency': 1,
                'first_seen': datetime.now().date().isoformat(),
                'last_seen': datetime.now().date().isoformat(),
                'min_amount': 0.0,
                'max_amount': 0.0,
                'sample_amounts': '0.0',
                'transaction_types': 'debit',
                'debit_frequency': 1,
                'credit_frequency': 0,
                'source_files': '',
                'bank_names': '',
                'category': data['category'],
                'sub_category': data['sub_category'],
                'confidence': data.get('confidence', 1.0),
                'is_manually_labeled': True,
                'labeled_by': data.get('source', 'combined'),
                'labeled_at': datetime.now().isoformat()
            }
            training_records.append(record)
        
        # Create DataFrame and save
        enhanced_df = pd.DataFrame(training_records)
        
        # Save enhanced training data
        enhanced_path = Path("bank_analyzer_config/ml_data/unique_transactions_enhanced.csv")
        enhanced_df.to_csv(enhanced_path, index=False)
        
        # Also update the main training file
        main_path = Path("bank_analyzer_config/ml_data/unique_transactions.csv")
        enhanced_df.to_csv(main_path, index=False)
        
        print(f"✅ Created enhanced training data: {len(enhanced_df)} labeled transactions")
        print(f"✅ Saved to: {enhanced_path}")
        print(f"✅ Updated main training file: {main_path}")
        
        # Show category breakdown
        category_counts = enhanced_df['category'].value_counts()
        print(f"\n📋 Category breakdown:")
        for category, count in category_counts.head(10).items():
            print(f"   {category}: {count} transactions")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating enhanced data: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main function to check and combine data"""
    print("🔍 Checking and Combining All Labeled Data")
    print("=" * 70)
    print("Checking if your 231 transactions are duplicates and finding additional data")
    print("")
    
    # Compare current data sources
    are_same, history_df, training_df = compare_data_sources()
    
    # Combine all available data
    combined_data = combine_all_available_data()
    
    if len(combined_data) > 231:
        print(f"\n🎉 SUCCESS! Found additional labeled data!")
        print(f"✅ Total unique labeled transactions: {len(combined_data)}")
        
        # Create enhanced training data
        success = create_enhanced_training_data(combined_data)
        
        if success:
            print(f"\n🎯 READY FOR TRAINING!")
            print(f"✅ Enhanced training data created with {len(combined_data)} labeled transactions")
            print(f"✅ This should provide much better ML training results")
            print(f"\n🔧 Next steps:")
            print(f"1. Try training again: ML Model Management → Train Model")
            print(f"2. With {len(combined_data)} labeled transactions, accuracy should improve")
            print(f"3. Run: python check_model_training_status.py to verify")
        
        return True
    else:
        print(f"\n⚠️ CONFIRMED: You only have 231 unique labeled transactions")
        print(f"📊 This appears to be all your recent labeling work")
        print(f"❌ No additional old labeled data was found")
        print(f"\n💡 Options:")
        print(f"1. Continue with 231 labels (may need better category distribution)")
        print(f"2. Label more diverse transactions to improve training")
        print(f"3. Focus on categories with fewer examples")
        
        return False


if __name__ == "__main__":
    try:
        found_more = main()
        sys.exit(0 if found_more else 1)
    except Exception as e:
        print(f"❌ Script failed: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
