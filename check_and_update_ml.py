#!/usr/bin/env python3
"""
Check current ML model status and update it with latest labeled data
"""

import sys
import pandas as pd
from pathlib import Path
from datetime import datetime
import os

# Add the bank_analyzer module to the path
sys.path.insert(0, str(Path(__file__).parent))

from bank_analyzer.ml.training_data_manager import TrainingDataManager
from bank_analyzer.ml.model_trainer import ModelTrainer
from bank_analyzer.ml.integrated_categorizer import IntegratedCategorizer


def check_current_labeled_data():
    """Check how much labeled data we currently have"""
    print("📊 Current Labeled Data Status")
    print("=" * 50)
    
    try:
        # Check training data manager
        training_manager = TrainingDataManager()
        stats = training_manager.get_labeling_stats()
        
        print(f"✅ Training Data Manager:")
        print(f"   Total transactions: {stats.total_unique_transactions}")
        print(f"   Labeled transactions: {stats.labeled_transactions}")
        print(f"   Unlabeled transactions: {stats.unlabeled_transactions}")
        print(f"   Labeling progress: {stats.labeling_progress:.1f}%")
        
        # Check labeling history file
        labeling_history_path = Path("bank_analyzer_config/ml_data/labeling_history.csv")
        if labeling_history_path.exists():
            labeling_df = pd.read_csv(labeling_history_path)
            unique_labeled = labeling_df['hash_id'].nunique()
            print(f"\n✅ Labeling History:")
            print(f"   Total labeling entries: {len(labeling_df)}")
            print(f"   Unique labeled transactions: {unique_labeled}")
            
            # Show recent labels
            if len(labeling_df) > 0:
                recent_labels = labeling_df.tail(5)
                print(f"\n📋 Recent labels:")
                for _, row in recent_labels.iterrows():
                    print(f"   {row['category']}/{row['sub_category']} (Hash: {row['hash_id'][:8]}...)")
        
        return stats.labeled_transactions, unique_labeled if 'unique_labeled' in locals() else 0
        
    except Exception as e:
        print(f"❌ Error checking labeled data: {str(e)}")
        return 0, 0


def check_model_status():
    """Check if ML model exists and when it was last trained"""
    print(f"\n🤖 ML Model Status")
    print("=" * 50)
    
    model_dir = Path("bank_analyzer_config/ml_models")
    
    if not model_dir.exists():
        print("❌ No ML models directory found")
        return False, None
    
    # Check for model files
    model_files = [
        "category_model.pkl",
        "category_encoder.pkl", 
        "subcategory_models.pkl",
        "subcategory_encoders.pkl",
        "model_metadata.json"
    ]
    
    files_found = 0
    latest_timestamp = None
    
    for file_name in model_files:
        file_path = model_dir / file_name
        if file_path.exists():
            files_found += 1
            file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
            print(f"✅ {file_name}: {file_time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            if latest_timestamp is None or file_time > latest_timestamp:
                latest_timestamp = file_time
        else:
            print(f"❌ {file_name}: Not found")
    
    model_exists = files_found >= 3  # Need at least 3 core files
    
    if latest_timestamp:
        time_since_update = datetime.now() - latest_timestamp
        print(f"\n🕒 Latest model update: {latest_timestamp.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"⏰ Time since update: {time_since_update}")
        
        # Check if model is recent (within last day)
        is_recent = time_since_update.total_seconds() < 86400  # 24 hours
        print(f"📅 Model is recent: {'✅ Yes' if is_recent else '❌ No (older than 24 hours)'}")
    
    return model_exists, latest_timestamp


def test_model_predictions():
    """Test if the current model can make predictions"""
    print(f"\n🧪 Testing Model Predictions")
    print("=" * 50)
    
    try:
        # Test with integrated categorizer
        categorizer = IntegratedCategorizer()
        
        # Test transactions
        test_transactions = [
            "CREDIT INTEREST",
            "UPI DEBIT PAYMENT",
            "ATM WITHDRAWAL",
            "SALARY CREDIT"
        ]
        
        print("Testing predictions:")
        working_predictions = 0
        
        for test_desc in test_transactions:
            try:
                result = categorizer.categorize_transaction(test_desc)
                category = result.get('category', 'Unknown')
                subcategory = result.get('sub_category', 'Unknown')
                confidence = result.get('confidence', 0)
                method = result.get('method', 'Unknown')
                
                print(f"   {test_desc[:30]:30} -> {category}/{subcategory} ({confidence:.2f}, {method})")
                
                if category != 'Unknown' and category != 'Uncategorized':
                    working_predictions += 1
                    
            except Exception as e:
                print(f"   {test_desc[:30]:30} -> ERROR: {str(e)}")
        
        print(f"\n📊 Working predictions: {working_predictions}/{len(test_transactions)}")
        return working_predictions > 0
        
    except Exception as e:
        print(f"❌ Error testing predictions: {str(e)}")
        return False


def update_model_with_current_data():
    """Update the ML model with current labeled data"""
    print(f"\n🚀 Updating ML Model with Current Data")
    print("=" * 50)
    
    try:
        # Check if we have enough labeled data
        training_manager = TrainingDataManager()
        stats = training_manager.get_labeling_stats()
        
        if stats.labeled_transactions < 5:
            print(f"❌ Insufficient labeled data: {stats.labeled_transactions} transactions")
            print("💡 Need at least 5 labeled transactions for training")
            return False
        
        print(f"✅ Found {stats.labeled_transactions} labeled transactions for training")
        
        # Initialize integrated categorizer and trigger training
        categorizer = IntegratedCategorizer()
        
        print("🔄 Starting model training...")
        job_id = categorizer.trigger_model_training()
        
        if job_id:
            print(f"✅ Training started successfully!")
            print(f"📋 Training job ID: {job_id}")
            print("⏳ Training in progress...")
            
            # Wait a moment for training to start
            import time
            time.sleep(3)
            
            print("✅ Model training has been initiated!")
            print("📝 Check the application for training progress")
            return True
        else:
            print("❌ Failed to start model training")
            return False
            
    except Exception as e:
        print(f"❌ Error updating model: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def verify_model_update():
    """Verify that the model was updated successfully"""
    print(f"\n✅ Verifying Model Update")
    print("=" * 50)
    
    try:
        # Wait a bit for training to complete
        import time
        print("⏳ Waiting for training to complete...")
        time.sleep(5)
        
        # Check if model files were updated
        model_exists, latest_timestamp = check_model_status()
        
        if model_exists and latest_timestamp:
            time_since_update = datetime.now() - latest_timestamp
            if time_since_update.total_seconds() < 300:  # Updated within last 5 minutes
                print("✅ Model files have been updated recently!")
                
                # Test predictions again
                predictions_working = test_model_predictions()
                
                if predictions_working:
                    print("🎉 SUCCESS! Model has been updated and is working!")
                    return True
                else:
                    print("⚠️ Model updated but predictions may need improvement")
                    return True
            else:
                print("❌ Model files were not updated recently")
                return False
        else:
            print("❌ Model files not found or not updated")
            return False
            
    except Exception as e:
        print(f"❌ Error verifying update: {str(e)}")
        return False


def main():
    """Main function to check and update ML model"""
    print("🔍 ML Model Status Check and Update")
    print("=" * 60)
    print("Checking current model status and updating with latest labeled data")
    print("")
    
    # Step 1: Check current labeled data
    training_labeled, history_labeled = check_current_labeled_data()
    
    # Step 2: Check model status
    model_exists, model_timestamp = check_model_status()
    
    # Step 3: Test current predictions
    predictions_working = test_model_predictions()
    
    # Step 4: Determine if update is needed
    print(f"\n" + "=" * 60)
    print("🎯 ASSESSMENT")
    print("=" * 60)
    
    print(f"📊 Labeled data available: {max(training_labeled, history_labeled)} transactions")
    print(f"🤖 Model exists: {'✅ Yes' if model_exists else '❌ No'}")
    print(f"🧪 Predictions working: {'✅ Yes' if predictions_working else '❌ No'}")
    
    # Decide if update is needed
    needs_update = False
    
    if not model_exists:
        print("\n🔄 Model needs to be created (no model found)")
        needs_update = True
    elif not predictions_working:
        print("\n🔄 Model needs update (predictions not working)")
        needs_update = True
    elif model_timestamp:
        time_since_update = datetime.now() - model_timestamp
        if time_since_update.total_seconds() > 86400:  # Older than 24 hours
            print("\n🔄 Model needs update (older than 24 hours)")
            needs_update = True
    
    if needs_update:
        print(f"\n🚀 UPDATING MODEL...")
        update_success = update_model_with_current_data()
        
        if update_success:
            verify_success = verify_model_update()
            
            if verify_success:
                print(f"\n🎉 SUCCESS! ML model has been updated!")
                print(f"✅ Your model now includes your latest {max(training_labeled, history_labeled)} labeled transactions")
                print(f"✅ Model is ready for use")
            else:
                print(f"\n⚠️ Model update initiated but verification incomplete")
                print(f"💡 Check the application for training completion")
        else:
            print(f"\n❌ Failed to update model")
            print(f"💡 Try using the UI: ML Model Management → Train Model")
    else:
        print(f"\n✅ Model is up to date and working!")
        print(f"🎯 No update needed")
    
    return needs_update


if __name__ == "__main__":
    try:
        updated = main()
        sys.exit(0)
    except Exception as e:
        print(f"❌ Script failed: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
