#!/usr/bin/env python3
"""
Check ML Model Training Status
Verify if the model training was successful and the model is updated
"""

import sys
from pathlib import Path
from datetime import datetime
import os

# Add the bank_analyzer module to the path
sys.path.insert(0, str(Path(__file__).parent))

from bank_analyzer.ml.training_data_manager import TrainingDataManager
from bank_analyzer.ml.model_trainer import ModelTrainer
from bank_analyzer.ml.ml_categorizer import MLCategorizer
from bank_analyzer.ml.hybrid_ml_categorizer import HybridMLCategorizer


def check_training_data_stats():
    """Check the current training data statistics"""
    print("📊 Training Data Statistics")
    print("=" * 40)
    
    try:
        training_manager = TrainingDataManager()
        stats = training_manager.get_labeling_stats()
        
        print(f"✅ Total unique transactions: {stats.total_unique_transactions}")
        print(f"✅ Labeled transactions: {stats.labeled_transactions}")
        print(f"✅ Unlabeled transactions: {stats.unlabeled_transactions}")
        print(f"✅ Labeling progress: {stats.labeling_progress:.1f}%")
        
        return stats.labeled_transactions
        
    except Exception as e:
        print(f"❌ Error checking training data: {str(e)}")
        return 0


def check_model_files():
    """Check if model files exist and their timestamps"""
    print("\n📁 Model Files Status")
    print("=" * 40)
    
    model_dir = Path("bank_analyzer_config/ml_models")
    
    if not model_dir.exists():
        print("❌ Model directory does not exist")
        return False
    
    print(f"✅ Model directory exists: {model_dir}")
    
    # Check for model files
    model_files = [
        "category_model.pkl",
        "category_encoder.pkl", 
        "subcategory_models.pkl",
        "subcategory_encoders.pkl",
        "model_metadata.json"
    ]
    
    files_found = 0
    latest_timestamp = None
    
    for file_name in model_files:
        file_path = model_dir / file_name
        if file_path.exists():
            files_found += 1
            file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
            print(f"✅ {file_name}: {file_time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            if latest_timestamp is None or file_time > latest_timestamp:
                latest_timestamp = file_time
        else:
            print(f"❌ {file_name}: Not found")
    
    print(f"\n📊 Model files found: {files_found}/{len(model_files)}")
    
    if latest_timestamp:
        time_since_update = datetime.now() - latest_timestamp
        print(f"🕒 Latest model update: {latest_timestamp.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"⏰ Time since update: {time_since_update}")
        
        # Check if updated recently (within last hour)
        if time_since_update.total_seconds() < 3600:  # 1 hour
            print("🎉 Model was updated recently!")
            return True
        else:
            print("⚠️ Model files are older - may not reflect recent training")
            return False
    else:
        print("❌ No model files found")
        return False


def check_training_jobs():
    """Check recent training jobs"""
    print("\n📋 Training Jobs Status")
    print("=" * 40)
    
    try:
        model_trainer = ModelTrainer()
        
        # Check if there's a current job
        if hasattr(model_trainer, 'current_job') and model_trainer.current_job:
            job = model_trainer.current_job
            print(f"🔄 Current job: {job.job_id}")
            print(f"📊 Status: {job.status}")
            print(f"🎯 Training samples: {job.training_samples}")
            if hasattr(job, 'accuracy'):
                print(f"📈 Accuracy: {job.accuracy:.3f}")
        
        # Check training history
        training_history_file = Path("bank_analyzer_config/ml_data/training_history.json")
        if training_history_file.exists():
            import json
            with open(training_history_file, 'r') as f:
                history = json.load(f)
            
            if history:
                latest_job = max(history.values(), key=lambda x: x.get('completed_at', ''))
                print(f"\n📈 Latest completed training:")
                print(f"   Job ID: {latest_job.get('job_id', 'Unknown')}")
                print(f"   Status: {latest_job.get('status', 'Unknown')}")
                print(f"   Samples: {latest_job.get('training_samples', 'Unknown')}")
                print(f"   Accuracy: {latest_job.get('accuracy', 'Unknown')}")
                print(f"   Completed: {latest_job.get('completed_at', 'Unknown')}")
                
                return latest_job.get('status') == 'completed'
        else:
            print("ℹ️ No training history file found")
            
    except Exception as e:
        print(f"❌ Error checking training jobs: {str(e)}")
        return False
    
    return False


def test_model_functionality():
    """Test if the model can make predictions"""
    print("\n🧪 Model Functionality Test")
    print("=" * 40)
    
    try:
        # Try to initialize the ML categorizer
        ml_categorizer = MLCategorizer()
        
        # Check if models are loaded
        if hasattr(ml_categorizer, 'is_trained') and ml_categorizer.is_trained:
            print("✅ ML Categorizer is trained and ready")
            
            # Test with a sample transaction
            test_description = "CREDIT INTEREST"
            
            try:
                result = ml_categorizer.categorize_transaction(test_description)
                print(f"✅ Model prediction test successful:")
                print(f"   Input: {test_description}")
                print(f"   Predicted category: {result.get('category', 'Unknown')}")
                print(f"   Predicted subcategory: {result.get('sub_category', 'Unknown')}")
                print(f"   Confidence: {result.get('confidence', 0):.3f}")
                return True
                
            except Exception as e:
                print(f"❌ Model prediction failed: {str(e)}")
                return False
        else:
            print("❌ ML Categorizer is not trained")
            return False
            
    except Exception as e:
        print(f"❌ Error testing model functionality: {str(e)}")
        return False


def check_hybrid_categorizer():
    """Check the hybrid categorizer status"""
    print("\n🔗 Hybrid Categorizer Status")
    print("=" * 40)
    
    try:
        hybrid_categorizer = HybridMLCategorizer()
        
        # Check if it's initialized
        if hasattr(hybrid_categorizer, 'ml_categorizer'):
            print("✅ Hybrid categorizer initialized")
            
            # Test categorization
            test_description = "UPI DEBIT PAYMENT"
            result = hybrid_categorizer.categorize_transaction(test_description)
            
            print(f"✅ Hybrid categorization test:")
            print(f"   Input: {test_description}")
            print(f"   Category: {result.get('category', 'Unknown')}")
            print(f"   Subcategory: {result.get('sub_category', 'Unknown')}")
            print(f"   Method: {result.get('method', 'Unknown')}")
            print(f"   Confidence: {result.get('confidence', 0):.3f}")
            
            return True
        else:
            print("❌ Hybrid categorizer not properly initialized")
            return False
            
    except Exception as e:
        print(f"❌ Error checking hybrid categorizer: {str(e)}")
        return False


def main():
    """Main verification function"""
    print("🔍 ML Model Training Verification")
    print("=" * 60)
    print("Checking if your model training was successful...")
    print("")
    
    # Check training data
    labeled_count = check_training_data_stats()
    
    # Check model files
    model_files_updated = check_model_files()
    
    # Check training jobs
    training_completed = check_training_jobs()
    
    # Test model functionality
    model_working = test_model_functionality()
    
    # Check hybrid categorizer
    hybrid_working = check_hybrid_categorizer()
    
    # Overall assessment
    print("\n" + "=" * 60)
    print("🎯 TRAINING VERIFICATION SUMMARY")
    print("=" * 60)
    
    print(f"📊 Training data: {labeled_count} labeled transactions")
    print(f"📁 Model files: {'✅ Updated' if model_files_updated else '❌ Not updated'}")
    print(f"📋 Training job: {'✅ Completed' if training_completed else '❌ Not completed'}")
    print(f"🧪 Model functionality: {'✅ Working' if model_working else '❌ Not working'}")
    print(f"🔗 Hybrid categorizer: {'✅ Working' if hybrid_working else '❌ Not working'}")
    
    # Final verdict
    if model_files_updated and model_working:
        print("\n🎉 SUCCESS! Your model training was successful!")
        print("✅ The model has been updated with your new labeled data")
        print("✅ The model is ready to use for categorizing transactions")
        print("\n🎯 Next steps:")
        print("   • Test the model on some unlabeled transactions")
        print("   • Check if categorization accuracy has improved")
        print("   • Continue labeling more transactions if needed")
        return True
    elif model_files_updated:
        print("\n⚠️ PARTIAL SUCCESS: Model files updated but functionality issues")
        print("💡 The training may have completed but there might be loading issues")
        return False
    else:
        print("\n❌ TRAINING NOT SUCCESSFUL")
        print("💡 The model training may not have completed properly")
        print("🔧 Try training again or check the application logs for errors")
        return False


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ Verification failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
