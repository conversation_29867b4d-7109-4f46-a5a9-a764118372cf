#!/usr/bin/env python3
"""
Scrip<PERSON> to diagnose ML model issues and understand why it's categorizing everything as 'Other'
"""

import sys
import os
import logging
from pathlib import Path

# Add the bank_analyzer module to the path
sys.path.insert(0, str(Path(__file__).parent))

from bank_analyzer.ml.ml_categorizer import MLTransactionCategorizer
from bank_analyzer.ml.data_preparation import TransactionDataPreparator
from bank_analyzer.ml.training_data_manager import TrainingDataManager
from bank_analyzer.core.logger import get_logger

def main():
    """Main function to diagnose ML model"""
    
    # Set up logging with debug level
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    logger = get_logger(__name__)
    
    logger.info("Starting ML model diagnosis...")
    
    try:
        # Initialize components
        logger.info("Initializing ML components...")

        # MLTransactionCategorizer creates its own data_preparator
        ml_model_dir = "bank_analyzer_config/ml_models"
        ml_categorizer = MLTransactionCategorizer(ml_model_dir)

        # TrainingDataManager for training operations
        ml_data_dir = "bank_analyzer_config/ml_data"
        training_manager = TrainingDataManager(ml_data_dir)
        training_manager.data_preparator = ml_categorizer.data_preparator
        
        # Check if model is trained
        logger.info(f"Model trained status: {ml_categorizer.is_trained}")
        logger.info(f"Model info: {ml_categorizer.get_model_info()}")
        
        if not ml_categorizer.is_trained:
            logger.warning("Model is not trained! This is likely the main issue.")
            logger.info("Let's check the training data...")
            
            # Check training data
            training_data, has_sufficient_data = ml_categorizer.prepare_training_data()
            logger.info(f"Has sufficient training data: {has_sufficient_data}")
            
            if has_sufficient_data:
                logger.info("Training data is available. Let's train the model...")
                result = ml_categorizer.train_models(force_retrain=True)
                logger.info(f"Training result: {result}")
                
                if result.get("success", False):
                    logger.info("Model trained successfully!")
                else:
                    logger.error(f"Training failed: {result.get('errors', [])}")
                    return False
            else:
                logger.error("Insufficient training data available")
                return False
        
        # Test predictions with sample transactions
        logger.info("Testing model predictions...")
        test_descriptions = [
            "THRU UPI DEBIT UPI/************/FoodOrdering XXXXX /zomatoindia.rzp@axisbank UTIB0001507/zomato private ltd",
            "THRU UPI DEBIT UPI/************/UPI XXXXX /euronetgpay. rch@icici ICIC0DC0099/EURONETGPAY", 
            "BY UPI CREDIT UPI/************/Payment from PhonePe XXXXX34269/vasanthansbi01@ybl SBIN0070601/VASANTHAN",
            "THRU UPI DEBIT UPI/************/UPI Intent XXXXX /paytm ********@paytm YESB0PTMUPI/Meesho",
            "THRU UPI DEBIT UPI/************/UPI XXXXX /paytm ********@paytm YESB0PTMUPI/Lakshmi Narayana Agency"
        ]
        
        logger.info("=" * 80)
        logger.info("PREDICTION RESULTS:")
        logger.info("=" * 80)
        
        for i, desc in enumerate(test_descriptions, 1):
            logger.info(f"\nTest {i}: {desc[:60]}...")
            prediction = ml_categorizer.predict_category(desc)
            if prediction:
                logger.info(f"  -> Category: {prediction.category}")
                logger.info(f"  -> Sub-category: {prediction.sub_category}")
                logger.info(f"  -> Confidence: {prediction.confidence:.3f}")
            else:
                logger.warning(f"  -> No prediction returned!")
        
        # Check what categories the model knows about
        if hasattr(ml_categorizer, 'category_encoder') and ml_categorizer.category_encoder:
            if hasattr(ml_categorizer.category_encoder, 'classes_'):
                logger.info(f"\nModel knows about these categories: {list(ml_categorizer.category_encoder.classes_)}")
            else:
                logger.warning("Category encoder has no classes_")
        else:
            logger.warning("No category encoder found")
        
        # Check model performance
        logger.info("\nEvaluating model performance...")
        evaluation = ml_categorizer.evaluate_model()
        logger.info(f"Model evaluation: {evaluation}")
        
        return True
            
    except Exception as e:
        logger.error(f"Error during diagnosis: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ Diagnosis completed!")
        print("Check the logs above to understand the model behavior.")
    else:
        print("\n❌ Diagnosis failed!")
        print("Check the logs above for error details.")
    
    sys.exit(0 if success else 1)
