#!/usr/bin/env python3
"""
<PERSON>ript to fix ML categorization issues by retraining the model with improved category mapping
"""

import sys
import os
import logging
from pathlib import Path

# Add the bank_analyzer module to the path
sys.path.insert(0, str(Path(__file__).parent))

from bank_analyzer.ml.ml_categorizer import MLTransactionCategorizer
from bank_analyzer.ml.data_preparation import TransactionDataPreparator
from bank_analyzer.ml.training_data_manager import TrainingDataManager
from bank_analyzer.ml.model_trainer import ModelTrainer
from bank_analyzer.core.logger import get_logger

def main():
    """Main function to fix ML categorization"""
    
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    logger = get_logger(__name__)
    
    logger.info("Starting ML categorization fix...")
    
    try:
        # Initialize components
        logger.info("Initializing ML components...")
        data_preparator = TransactionDataPreparator()
        training_manager = TrainingDataManager(data_preparator)
        ml_categorizer = MLTransactionCategorizer(data_preparator, training_manager)
        model_trainer = ModelTrainer(ml_categorizer, training_manager)
        
        # Check current training data
        logger.info("Analyzing current training data...")
        training_data, has_sufficient_data = ml_categorizer.prepare_training_data()
        
        if not has_sufficient_data:
            logger.error("Insufficient training data even after category mapping!")
            return False
        
        logger.info(f"Training data prepared: {len(training_data)} samples")
        
        # Display category distribution
        category_counts = training_data['category'].value_counts()
        logger.info("Category distribution after mapping:")
        for category, count in category_counts.items():
            logger.info(f"  {category}: {count} samples")
        
        # Create a training job
        logger.info("Creating training job...")
        job_id = model_trainer.create_training_job(
            name="ML Categorization Fix",
            description="Retrain model with improved category mapping to fix 'Other' category issue"
        )
        
        if not job_id:
            logger.error("Failed to create training job")
            return False
        
        logger.info(f"Created training job: {job_id}")
        
        # Start training
        logger.info("Starting model training...")
        success = model_trainer.start_training(job_id, async_training=False)
        
        if not success:
            logger.error("Failed to start training")
            return False
        
        # Get training results
        job = model_trainer._load_training_job(job_id)
        if job and job.status == "completed":
            logger.info(f"Training completed successfully!")
            logger.info(f"Model accuracy: {job.accuracy:.3f}")
            logger.info(f"Model version: {job.model_version}")
            
            # Test the model with a few predictions
            logger.info("Testing model predictions...")
            test_descriptions = [
                "THRU UPI DEBIT UPI/FoodOrdering/zomatoindia.rzp@axisbank/zomato private ltd",
                "THRU UPI DEBIT UPI/UPI/paytm ********@paytm/Lakshmi Narayana Agency", 
                "BY UPI CREDIT UPI/Payment from PhonePe/vasanthansbi01@ybl/VASANTHAN",
                "THRU UPI DEBIT UPI/UPI Intent/meesho.payu@hdfcbank/MEESHO COM",
                "THRU UPI DEBIT UPI/UPI/euronetgpay.rch@icici/EURONETGPAY"
            ]
            
            for desc in test_descriptions:
                prediction = ml_categorizer.predict_category(desc)
                if prediction:
                    logger.info(f"'{desc[:50]}...' -> {prediction.category} ({prediction.confidence:.3f})")
                else:
                    logger.warning(f"No prediction for: {desc[:50]}...")
            
            return True
        else:
            logger.error(f"Training failed. Job status: {job.status if job else 'Unknown'}")
            return False
            
    except Exception as e:
        logger.error(f"Error during ML categorization fix: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ ML categorization fix completed successfully!")
        print("The model should now categorize transactions more accurately.")
        print("Try processing your bank statements again to see the improvement.")
    else:
        print("\n❌ ML categorization fix failed!")
        print("Check the logs above for error details.")
    
    sys.exit(0 if success else 1)
