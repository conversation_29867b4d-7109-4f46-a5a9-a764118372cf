#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix the is_trained flag in the ML model
"""

import joblib
from pathlib import Path
import sys

def main():
    """Fix the is_trained flag"""
    
    try:
        # Check if model files exist
        model_dir = Path("bank_analyzer_config/ml_models")
        encoders_file = model_dir / "label_encoders.joblib"
        category_model_file = model_dir / "category_model.joblib"
        
        print(f"Model directory: {model_dir}")
        print(f"Encoders file exists: {encoders_file.exists()}")
        print(f"Category model file exists: {category_model_file.exists()}")
        
        if not encoders_file.exists():
            print("❌ Encoders file not found!")
            return False
        
        if not category_model_file.exists():
            print("❌ Category model file not found!")
            return False
        
        # Load current encoders
        print("\nLoading current encoders...")
        encoders = joblib.load(encoders_file)
        
        print("Current encoders content:")
        for key, value in encoders.items():
            if key == 'is_trained':
                print(f"  {key}: {value}")
            elif key in ['category_encoder', 'subcategory_encoders']:
                print(f"  {key}: {type(value)}")
            else:
                print(f"  {key}: {value}")
        
        # Check if we have the required components
        has_category_encoder = 'category_encoder' in encoders and encoders['category_encoder'] is not None
        has_subcategory_encoders = 'subcategory_encoders' in encoders
        
        print(f"\nModel components:")
        print(f"  Has category encoder: {has_category_encoder}")
        print(f"  Has subcategory encoders: {has_subcategory_encoders}")
        
        if has_category_encoder:
            # Set is_trained to True since we have all required components
            encoders['is_trained'] = True
            encoders['model_version'] = encoders.get('model_version', '1.0')
            
            # Save the updated encoders
            print("\nUpdating is_trained flag to True...")
            joblib.dump(encoders, encoders_file)
            
            print("✅ Successfully updated the is_trained flag!")
            
            # Test the model
            print("\nTesting the model...")
            sys.path.insert(0, str(Path(__file__).parent))
            from bank_analyzer.ml.ml_categorizer import MLTransactionCategorizer
            
            ml_cat = MLTransactionCategorizer()
            print(f"Model trained status: {ml_cat.is_trained}")
            
            if ml_cat.is_trained:
                # Test with a sample transaction
                test_desc = "THRU UPI DEBIT UPI/FoodOrdering/zomatoindia.rzp@axisbank/zomato private ltd"
                prediction = ml_cat.predict_category(test_desc)
                
                if prediction:
                    print(f"✅ Test prediction successful!")
                    print(f"   Category: {prediction.category}")
                    print(f"   Confidence: {prediction.confidence:.3f}")
                else:
                    print("⚠️ Test prediction returned None")
            
            return True
        else:
            print("❌ Missing required model components!")
            return False
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 Model fix completed successfully!")
        print("Your ML model should now work properly for categorizing transactions.")
    else:
        print("\n💥 Model fix failed!")
        print("Please check the error messages above.")
    
    sys.exit(0 if success else 1)
