#!/usr/bin/env python3
"""
Fix Training Data - Combine all labeled data properly
"""

import sys
import pandas as pd
from pathlib import Path
from datetime import datetime

# Add the bank_analyzer module to the path
sys.path.insert(0, str(Path(__file__).parent))

from bank_analyzer.ml.training_data_manager import TrainingDataManager
from bank_analyzer.ml.session_training_manager import SessionTrainingManager


def check_all_labeled_data_sources():
    """Check all sources of labeled data"""
    print("🔍 Checking All Labeled Data Sources")
    print("=" * 50)
    
    # Check labeling history file
    labeling_history_path = Path("bank_analyzer_config/ml_data/labeling_history.csv")
    if labeling_history_path.exists():
        labeling_df = pd.read_csv(labeling_history_path)
        unique_labeled = labeling_df['hash_id'].nunique()
        print(f"✅ Labeling history: {len(labeling_df)} entries, {unique_labeled} unique transactions")
    else:
        print("❌ No labeling history file found")
        unique_labeled = 0
    
    # Check unique transactions file
    unique_transactions_path = Path("bank_analyzer_config/ml_data/unique_transactions.csv")
    if unique_transactions_path.exists():
        unique_df = pd.read_csv(unique_transactions_path)
        manually_labeled = unique_df[unique_df.get('is_manually_labeled', False) == True]
        print(f"✅ Unique transactions: {len(unique_df)} total, {len(manually_labeled)} manually labeled")
    else:
        print("❌ No unique transactions file found")
    
    # Check session training data
    try:
        session_manager = SessionTrainingManager("bank_analyzer_config/ml_data")
        combined_data = session_manager.get_combined_training_data()
        print(f"✅ Session training data: {len(combined_data)} labeled transactions")
    except Exception as e:
        print(f"❌ Error checking session data: {str(e)}")
        combined_data = pd.DataFrame()
    
    return unique_labeled, len(combined_data)


def rebuild_training_data():
    """Rebuild training data from all sources"""
    print("\n🔧 Rebuilding Training Data")
    print("=" * 50)
    
    try:
        # Load labeling history
        labeling_history_path = Path("bank_analyzer_config/ml_data/labeling_history.csv")
        if not labeling_history_path.exists():
            print("❌ No labeling history found")
            return False
        
        labeling_df = pd.read_csv(labeling_history_path)
        print(f"📊 Found {len(labeling_df)} labeling entries")
        
        # Group by hash_id to get unique labeled transactions
        labeled_transactions = {}
        
        for _, row in labeling_df.iterrows():
            hash_id = row['hash_id']
            if hash_id not in labeled_transactions:
                labeled_transactions[hash_id] = {
                    'hash_id': hash_id,
                    'category': row['category'],
                    'sub_category': row['sub_category'],
                    'confidence': row.get('confidence', 1.0),
                    'session_id': row.get('session_id', 'manual'),
                    'labeled_at': row.get('timestamp', datetime.now().isoformat())
                }
        
        print(f"📊 Unique labeled transactions: {len(labeled_transactions)}")
        
        # Convert to UniqueTransaction objects
        from bank_analyzer.ml.data_preparation import UniqueTransaction
        
        unique_transactions = {}
        
        for hash_id, label_data in labeled_transactions.items():
            txn = UniqueTransaction(
                hash_id=hash_id,
                description=f"Transaction {hash_id}",  # Placeholder description
                normalized_description="",
                frequency=1,
                first_seen=datetime.now().date(),
                last_seen=datetime.now().date(),
                amount_range=(0.0, 0.0),
                sample_amounts=[0.0],
                transaction_types={'debit'},
                debit_frequency=1,
                credit_frequency=0,
                category=label_data['category'],
                sub_category=label_data['sub_category'],
                confidence=label_data['confidence'],
                is_manually_labeled=True,
                labeled_by=label_data['session_id'],
                labeled_at=label_data['labeled_at'],
                source_files=set(),
                bank_names=set()
            )
            unique_transactions[hash_id] = txn
        
        # Save to training data
        training_manager = TrainingDataManager()
        success = training_manager.data_preparator.save_unique_transactions(unique_transactions)
        
        if success:
            print(f"✅ Successfully rebuilt training data with {len(unique_transactions)} labeled transactions")
            return True
        else:
            print("❌ Failed to save rebuilt training data")
            return False
            
    except Exception as e:
        print(f"❌ Error rebuilding training data: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def verify_rebuilt_data():
    """Verify the rebuilt training data"""
    print("\n✅ Verifying Rebuilt Data")
    print("=" * 50)
    
    try:
        training_manager = TrainingDataManager()
        stats = training_manager.get_labeling_stats()
        
        print(f"📊 Training data statistics:")
        print(f"   Total transactions: {stats.total_unique_transactions}")
        print(f"   Labeled transactions: {stats.labeled_transactions}")
        print(f"   Unlabeled transactions: {stats.unlabeled_transactions}")
        print(f"   Labeling progress: {stats.labeling_progress:.1f}%")
        
        return stats.labeled_transactions > 100  # Should have much more than 30
        
    except Exception as e:
        print(f"❌ Error verifying data: {str(e)}")
        return False


def main():
    """Main function to fix training data"""
    print("🔧 Fixing Training Data Issues")
    print("=" * 60)
    print("This will combine all your labeled data properly for training")
    print("")
    
    # Check current state
    history_count, session_count = check_all_labeled_data_sources()
    
    if history_count < 100:
        print("\n⚠️ Warning: Only found limited labeled data in history")
        print("This might mean your previous 489 labeled transactions aren't in the labeling history")
    
    # Rebuild training data
    rebuild_success = rebuild_training_data()
    
    if rebuild_success:
        # Verify the rebuilt data
        verify_success = verify_rebuilt_data()
        
        if verify_success:
            print("\n🎉 SUCCESS! Training data has been fixed!")
            print("✅ All your labeled data has been properly combined")
            print("\n🎯 Next steps:")
            print("1. Now try training the model again")
            print("2. Use: ML Model Management → Train Model")
            print("3. The training should now work with all your labeled data")
            return True
        else:
            print("\n⚠️ Data rebuilt but verification failed")
            return False
    else:
        print("\n❌ Failed to rebuild training data")
        print("💡 You may need to manually check your labeling history file")
        return False


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ Script failed: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
