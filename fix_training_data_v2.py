#!/usr/bin/env python3
"""
Fix Training Data V2 - Properly combine labeled data with real descriptions
"""

import sys
import pandas as pd
from pathlib import Path
from datetime import datetime

# Add the bank_analyzer module to the path
sys.path.insert(0, str(Path(__file__).parent))

from bank_analyzer.ml.training_data_manager import TrainingDataManager


def get_real_transaction_descriptions():
    """Get real transaction descriptions from the categorization service data"""
    print("🔍 Finding Real Transaction Descriptions")
    print("=" * 50)
    
    try:
        from bank_analyzer.ml.transaction_categorization_service import TransactionCategorizationService
        
        service = TransactionCategorizationService()
        existing_transactions = service._load_existing_transactions()
        
        print(f"✅ Found {len(existing_transactions)} existing transactions with descriptions")
        
        # Create mapping of hash_id to real description
        hash_to_description = {}
        for hash_id, txn_data in existing_transactions.items():
            if txn_data.get('description') and not txn_data['description'].startswith('['):
                hash_to_description[hash_id] = {
                    'description': txn_data['description'],
                    'normalized_description': txn_data.get('normalized_description', ''),
                    'source': txn_data.get('source', 'unknown')
                }
        
        print(f"✅ Found {len(hash_to_description)} transactions with real descriptions")
        return hash_to_description
        
    except Exception as e:
        print(f"❌ Error getting transaction descriptions: {str(e)}")
        return {}


def rebuild_training_data_properly():
    """Rebuild training data with real descriptions and labels"""
    print("\n🔧 Rebuilding Training Data with Real Descriptions")
    print("=" * 50)
    
    try:
        # Get real transaction descriptions
        hash_to_description = get_real_transaction_descriptions()
        
        if not hash_to_description:
            print("❌ No real transaction descriptions found")
            return False
        
        # Load labeling history
        labeling_history_path = Path("bank_analyzer_config/ml_data/labeling_history.csv")
        if not labeling_history_path.exists():
            print("❌ No labeling history found")
            return False
        
        labeling_df = pd.read_csv(labeling_history_path)
        print(f"📊 Found {len(labeling_df)} labeling entries")
        
        # Create training data by combining descriptions with labels
        training_data = []
        
        for _, row in labeling_df.iterrows():
            hash_id = row['hash_id']
            
            # Get real description for this hash_id
            if hash_id in hash_to_description:
                desc_data = hash_to_description[hash_id]
                
                training_record = {
                    'hash_id': hash_id,
                    'description': desc_data['description'],
                    'normalized_description': desc_data['normalized_description'],
                    'category': row['category'],
                    'sub_category': row['sub_category'],
                    'confidence': row.get('confidence', 1.0),
                    'is_manually_labeled': True,
                    'labeled_by': row.get('session_id', 'manual'),
                    'labeled_at': row.get('timestamp', datetime.now().isoformat()),
                    'frequency': 1,
                    'first_seen': datetime.now().date().isoformat(),
                    'last_seen': datetime.now().date().isoformat(),
                    'min_amount': 0.0,
                    'max_amount': 0.0,
                    'sample_amounts': '0.0',
                    'transaction_types': 'debit',
                    'debit_frequency': 1,
                    'credit_frequency': 0,
                    'source_files': '',
                    'bank_names': ''
                }
                
                training_data.append(training_record)
        
        print(f"📊 Created {len(training_data)} training records with real descriptions")
        
        if not training_data:
            print("❌ No training data could be created")
            return False
        
        # Save directly to unique_transactions.csv
        training_df = pd.DataFrame(training_data)
        
        # Remove duplicates by hash_id (keep latest label)
        training_df = training_df.drop_duplicates(subset=['hash_id'], keep='last')
        
        print(f"📊 Final training data: {len(training_df)} unique transactions")
        
        # Save to file
        unique_transactions_path = Path("bank_analyzer_config/ml_data/unique_transactions.csv")
        training_df.to_csv(unique_transactions_path, index=False)
        
        print(f"✅ Saved training data to {unique_transactions_path}")
        return True
        
    except Exception as e:
        print(f"❌ Error rebuilding training data: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def verify_fixed_data():
    """Verify the fixed training data"""
    print("\n✅ Verifying Fixed Training Data")
    print("=" * 50)
    
    try:
        training_manager = TrainingDataManager()
        stats = training_manager.get_labeling_stats()
        
        print(f"📊 Training data statistics:")
        print(f"   Total transactions: {stats.total_unique_transactions}")
        print(f"   Labeled transactions: {stats.labeled_transactions}")
        print(f"   Unlabeled transactions: {stats.unlabeled_transactions}")
        print(f"   Labeling progress: {stats.labeling_progress:.1f}%")
        
        # Check if we have a reasonable number of labeled transactions
        if stats.labeled_transactions > 100:
            print("✅ Good! Found substantial labeled training data")
            return True
        else:
            print("⚠️ Warning: Limited labeled training data found")
            return False
        
    except Exception as e:
        print(f"❌ Error verifying data: {str(e)}")
        return False


def show_sample_training_data():
    """Show sample of the training data"""
    print("\n📋 Sample Training Data")
    print("=" * 50)
    
    try:
        unique_transactions_path = Path("bank_analyzer_config/ml_data/unique_transactions.csv")
        if unique_transactions_path.exists():
            df = pd.read_csv(unique_transactions_path)
            labeled_df = df[df['is_manually_labeled'] == True]
            
            print(f"📊 Total transactions: {len(df)}")
            print(f"📊 Labeled transactions: {len(labeled_df)}")
            
            if len(labeled_df) > 0:
                print(f"\n📋 Sample labeled transactions:")
                for i, (_, row) in enumerate(labeled_df.head(5).iterrows()):
                    desc = row['description'][:50] + "..." if len(row['description']) > 50 else row['description']
                    print(f"   {i+1}. {desc} -> {row['category']}/{row['sub_category']}")
            
        else:
            print("❌ No unique_transactions.csv file found")
            
    except Exception as e:
        print(f"❌ Error showing sample data: {str(e)}")


def main():
    """Main function to fix training data properly"""
    print("🔧 Fixing Training Data - Version 2")
    print("=" * 60)
    print("This will properly combine your labeled data with real transaction descriptions")
    print("")
    
    # Rebuild training data properly
    rebuild_success = rebuild_training_data_properly()
    
    if rebuild_success:
        # Verify the fixed data
        verify_success = verify_fixed_data()
        
        # Show sample data
        show_sample_training_data()
        
        if verify_success:
            print("\n🎉 SUCCESS! Training data has been properly fixed!")
            print("✅ Your labeled data now has real transaction descriptions")
            print("✅ All your labels have been preserved and combined")
            print("\n🎯 Next steps:")
            print("1. Now try training the model again")
            print("2. Use: ML Model Management → Train Model")
            print("3. The training should now work with 200+ labeled transactions")
            print("4. Run: python check_model_training_status.py to verify")
            return True
        else:
            print("\n⚠️ Data rebuilt but verification shows limited data")
            print("💡 Try training anyway - it might still work")
            return False
    else:
        print("\n❌ Failed to rebuild training data properly")
        print("💡 Check that your transaction categorization system has the descriptions")
        return False


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ Script failed: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
