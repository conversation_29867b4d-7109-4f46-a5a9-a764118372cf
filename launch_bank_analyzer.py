#!/usr/bin/env python3
"""
Simple Launcher for Bank Statement Analyzer
One-click setup and launch with automatic dependency checking
"""

import sys
import subprocess
import os
from pathlib import Path

def print_header():
    """Print application header"""
    print("=" * 60)
    print("🏦 STANDALONE BANK STATEMENT ANALYZER")
    print("=" * 60)
    print("📊 Analyze • 🎯 Categorize • 📤 Export")
    print("Comprehensive standalone bank statement analysis and categorization")
    print("=" * 60)

def check_python():
    """Check if Python is available and version"""
    try:
        version = sys.version_info
        if version.major < 3 or (version.major == 3 and version.minor < 8):
            print("❌ Python 3.8+ required. Current version:", f"{version.major}.{version.minor}")
            return False
        
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} detected")
        return True
    except Exception as e:
        print(f"❌ Python check failed: {e}")
        return False

def check_dependencies():
    """Check and install required dependencies"""
    print("\n🔍 Checking dependencies...")
    
    required_packages = [
        ("pandas", "Data processing"),
        ("PySide6", "GUI framework"),
        ("PyPDF2", "PDF parsing"),
        ("pdfplumber", "Advanced PDF parsing"),
        ("openpyxl", "Excel file support"),
    ]
    
    missing_packages = []
    
    for package, description in required_packages:
        try:
            __import__(package)
            print(f"   ✅ {package} - {description}")
        except ImportError:
            print(f"   ❌ {package} - {description} (MISSING)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n📦 Installing missing packages: {', '.join(missing_packages)}")
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", 
                *missing_packages
            ])
            print("✅ All dependencies installed successfully!")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install dependencies: {e}")
            print("💡 Try running manually: pip install pandas PySide6 PyPDF2 pdfplumber openpyxl")
            return False
    
    print("✅ All dependencies are available!")
    return True

def check_data_structure():
    """Check if standalone data structure exists"""
    print("\n📁 Checking standalone data structure...")

    # Check for required directories
    required_dirs = ["data", "logs", "bank_analyzer_config"]

    for dir_name in required_dirs:
        dir_path = Path(dir_name)
        if not dir_path.exists():
            print(f"   ⚠️  {dir_name} directory not found")
            try:
                dir_path.mkdir(exist_ok=True)
                print(f"   ✅ Created {dir_name} directory")
            except Exception as e:
                print(f"   ❌ Could not create {dir_name} directory: {e}")
                return False
        else:
            print(f"   ✅ Found {dir_name} directory")

    print("   ✅ Standalone data structure is ready")
    return True

def check_statements_folder():
    """Check if statements folder exists"""
    print("\n📄 Checking statements folder...")
    
    statements_dir = Path("statements")
    if not statements_dir.exists():
        print("   ⚠️  Statements folder not found")
        print("   💡 Creating statements folder...")
        try:
            statements_dir.mkdir(exist_ok=True)
            print("   ✅ Created statements folder")
        except Exception as e:
            print(f"   ❌ Could not create statements folder: {e}")
            return False
    
    # Check for statement files
    statement_files = []
    for ext in ['*.pdf', '*.csv', '*.xlsx', '*.xls']:
        statement_files.extend(statements_dir.glob(ext))
    
    if statement_files:
        print(f"   ✅ Found {len(statement_files)} statement files:")
        for file in statement_files[:5]:  # Show first 5
            print(f"      📄 {file.name}")
        if len(statement_files) > 5:
            print(f"      ... and {len(statement_files) - 5} more files")
    else:
        print("   ℹ️  No statement files found")
        print("   💡 Add your bank statement files (PDF, CSV, Excel) to the statements folder")
    
    return True

def run_tests():
    """Run basic tests to ensure everything works"""
    print("\n🧪 Running system tests...")
    
    try:
        # Test basic imports
        from bank_analyzer.core.categorizer import TransactionCategorizer
        from bank_analyzer.core.data_importer import DataImporter
        from bank_analyzer.parsers.parser_factory import parser_factory
        print("   ✅ Core modules import successfully")
        
        # Test UI imports
        from bank_analyzer.ui.main_window import BankAnalyzerMainWindow
        print("   ✅ UI modules import successfully")
        
        # Test basic functionality
        categorizer = TransactionCategorizer()
        importer = DataImporter()
        print("   ✅ Core components initialize successfully")
        
        return True
        
    except Exception as e:
        print(f"   ❌ System test failed: {e}")
        return False

def setup_confidence_improvements():
    """Setup confidence improvements for standalone analyzer"""
    print("\n🎯 Setting up confidence improvements...")

    try:
        # Check if ML models exist
        ml_models_dir = Path("bank_analyzer_config/ml_models")
        if ml_models_dir.exists() and any(ml_models_dir.glob("*.joblib")):
            print("   ✅ Found existing ML models - confidence improvements ready")
        else:
            print("   ℹ️  No ML models found - will use rule-based categorization")
            print("   💡 Train models using the ML menu in the application")

        # Check if configuration files exist
        config_files = [
            "bank_analyzer_config/enhanced_categorizer_config.json",
            "bank_analyzer_config/merchant_mappings.json"
        ]

        for config_file in config_files:
            if Path(config_file).exists():
                print(f"   ✅ Found {Path(config_file).name}")
            else:
                print(f"   ℹ️  {Path(config_file).name} will be created on first run")

        return True

    except Exception as e:
        print(f"   ⚠️  Could not setup confidence improvements: {e}")
        print("   💡 Application will create default configurations")
        return True  # Non-critical, continue anyway

def show_quick_start_guide():
    """Show quick start guide"""
    print("\n📚 QUICK START GUIDE")
    print("=" * 20)
    print("1️⃣  Add bank statements to the 'statements' folder")
    print("   📄 Supported: PDF, CSV, Excel (.xlsx, .xls)")
    print()
    print("2️⃣  Launch the application (starting now...)")
    print("   🖱️  Click 'Select Files' to choose your statements")
    print()
    print("3️⃣  Review and edit transactions")
    print("   🎯 Green = High confidence (auto-categorized)")
    print("   🟡 Yellow = Medium confidence (review recommended)")
    print("   🔴 Red = Low confidence (manual review needed)")
    print()
    print("4️⃣  Export processed transactions")
    print("   💾 Save categorized transactions to CSV")
    print("   ✅ Ready for import into any finance application")
    print()

def launch_application():
    """Launch the Bank Statement Analyzer application"""
    print("\n🚀 LAUNCHING BANK STATEMENT ANALYZER")
    print("=" * 40)
    
    try:
        # Launch the application
        subprocess.run([sys.executable, "bank_statement_analyzer.py"])
        print("\n👋 Application closed. Thank you for using Bank Statement Analyzer!")
        
    except KeyboardInterrupt:
        print("\n⏹️  Application interrupted by user")
    except Exception as e:
        print(f"\n❌ Error launching application: {e}")
        print("💡 Try running manually: python bank_statement_analyzer.py")

def main():
    """Main launcher function"""
    print_header()
    
    # System checks
    if not check_python():
        input("\nPress Enter to exit...")
        return False
    
    if not check_dependencies():
        input("\nPress Enter to exit...")
        return False
    
    if not check_data_structure():
        print("\n⚠️  Data structure issues detected")
        choice = input("Continue anyway? (y/n): ").lower().strip()
        if choice != 'y':
            return False
    
    check_statements_folder()
    
    if not run_tests():
        print("\n⚠️  System tests failed")
        choice = input("Continue anyway? (y/n): ").lower().strip()
        if choice != 'y':
            return False
    
    # Setup improvements
    setup_confidence_improvements()
    
    # Show guide and launch
    show_quick_start_guide()
    
    print("\n" + "=" * 60)
    input("📱 Press Enter to launch Bank Statement Analyzer...")
    
    launch_application()
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n👋 Launcher interrupted. Goodbye!")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Launcher error: {e}")
        input("Press Enter to exit...")
        sys.exit(1)
