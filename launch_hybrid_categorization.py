#!/usr/bin/env python3
"""
Launch the Hybrid Categorization System
"""

import sys
import tkinter as tk
from pathlib import Path

# Add the bank_analyzer module to the path
sys.path.insert(0, str(Path(__file__).parent))

from bank_analyzer.ui.hybrid_categorization import HybridCategorizationUI
from bank_analyzer.core.logger import get_logger

def main():
    """Main function to launch hybrid categorization"""
    
    logger = get_logger(__name__)
    logger.info("Starting Hybrid Categorization System...")
    
    try:
        # Create main window
        root = tk.Tk()
        root.withdraw()  # Hide the root window
        
        # Create hybrid categorization UI
        app = HybridCategorizationUI()
        
        # Start the application
        app.root.mainloop()
        
    except Exception as e:
        logger.error(f"Error starting application: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("❌ Failed to start Hybrid Categorization System")
        sys.exit(1)
    else:
        print("✅ Hybrid Categorization System closed successfully")
        sys.exit(0)
