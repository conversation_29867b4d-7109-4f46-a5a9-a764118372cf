2025-06-30 00:02:39 - bank_analyzer.core.logger - INFO - setup_logging:72 - Logging system initialized
2025-06-30 00:02:39 - bank_analyzer.core.logger - INFO - setup_logging:73 - Log file: logs\bank_analyzer_20250630.log
2025-06-30 00:02:39 - bank_analyzer.core.logger - INFO - setup_logging:74 - Log level: INFO
2025-06-30 00:02:39 - __main__ - INFO - main:47 - Starting Bank Statement Analyzer application
2025-06-30 00:02:39 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 00:02:39 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 00:02:39 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:02:39 - bank_analyzer.ml.sambanova_client - INFO - _check_daily_reset:127 - Daily cost tracking reset
2025-06-30 00:02:39 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:02:39 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 00:02:39 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 00:02:39 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:02:39 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 00:02:39 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:02:39 - bank_analyzer.ml.ai_categorization_coordinator - INFO - _load_config:117 - Loaded AI coordinator config from bank_analyzer_config\ai_coordinator_config.json
2025-06-30 00:02:39 - bank_analyzer.ml.enhanced_merchant_mapper - INFO - _load_patterns:497 - Loaded 24 merchant patterns
2025-06-30 00:02:39 - bank_analyzer.ml.enhanced_merchant_mapper - INFO - _create_automatic_backup:689 - Created automatic backup: bank_analyzer_config\merchant_cache\backups\patterns_backup_20250630_000239.json
2025-06-30 00:02:39 - bank_analyzer.ml.enhanced_merchant_mapper - INFO - __init__:158 - Enhanced merchant mapper initialized with 24 patterns
2025-06-30 00:02:39 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 00:02:39 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 00:02:39 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 00:02:39 - bank_analyzer.ml.transaction_context_analyzer - INFO - __init__:131 - Transaction Context Analyzer initialized
2025-06-30 00:02:39 - bank_analyzer.ml.smart_transaction_router - INFO - _load_config:109 - Loaded router config from bank_analyzer_config\router_config.json
2025-06-30 00:02:39 - bank_analyzer.ml.smart_transaction_router - INFO - __init__:80 - Router initialized: cache_threshold=0.6, max_ai_batch=100
2025-06-30 00:02:39 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:02:39 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:02:39 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 00:02:39 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:02:39 - bank_analyzer.ml.ai_categorization_coordinator - INFO - __init__:91 - AI Coordinator initialized with budget: $2.0
2025-06-30 00:02:39 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 00:02:39 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:02:39 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:02:39 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 00:02:39 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 00:02:39 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:02:39 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 00:02:39 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:02:39 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 00:02:39 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:02:39 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 00:02:39 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:02:39 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:02:39 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:02:39 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 00:02:39 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 00:02:39 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 00:02:39 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 00:02:39 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:02:39 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:02:39 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 00:02:39 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 00:02:39 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 00:02:39 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 00:02:39 - bank_analyzer.ml.sambanova_cost_tracker - INFO - __init__:83 - Cost tracker initialized (total: $0.0000)
2025-06-30 00:02:39 - bank_analyzer.ml.batch_processor - INFO - __init__:87 - Batch processor initialized (batch size: 20-30)
2025-06-30 00:02:39 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:241 - Loaded 60 categories from main application
2025-06-30 00:02:39 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:274 - Loaded 106 ML-specific categories
2025-06-30 00:02:39 - bank_analyzer.ml.dynamic_category_manager - INFO - _load_existing_categories:137 - Loaded 32 main categories
2025-06-30 00:02:39 - bank_analyzer.ml.dynamic_category_manager - INFO - __init__:118 - Dynamic category manager initialized
2025-06-30 00:02:39 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 00:02:39 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 00:02:39 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 00:02:39 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 00:02:39 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 00:02:39 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 00:02:39 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 00:02:39 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 00:02:39 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 00:02:39 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 00:02:39 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 00:02:39 - bank_analyzer.ml.budget_guardian - INFO - __init__:107 - Budget guardian initialized ($5.0 total, $1.0 daily)
2025-06-30 00:02:39 - bank_analyzer.ml.enhanced_sambanova_categorizer - INFO - __init__:109 - Enhanced SambaNova categorizer initialized
2025-06-30 00:02:39 - bank_analyzer.ml.hybrid_ml_categorizer - INFO - __init__:115 - Hybrid ML categorizer initialized in hybrid mode
2025-06-30 00:02:39 - bank_analyzer.ml.integrated_categorizer - INFO - __init__:46 - Enhanced integrated categorizer with AI coordinator initialized
2025-06-30 00:02:39 - bank_analyzer.core.transaction_data_manager - INFO - __init__:100 - Transaction Data Manager initialized
2025-06-30 00:02:39 - bank_analyzer.ui.main_window - INFO - load_initial_data:741 - Initial data loaded successfully
2025-06-30 00:02:39 - bank_analyzer.ui.main_window - INFO - __init__:237 - Bank Analyzer main window initialized
2025-06-30 00:02:40 - __main__ - INFO - main:76 - Bank Statement Analyzer application started successfully
2025-06-30 00:02:41 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:521 - Loaded 83 unique transactions from storage in 0.189 seconds
2025-06-30 00:02:41 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:521 - Loaded 83 unique transactions from storage in 0.129 seconds
2025-06-30 00:22:31 - bank_analyzer.core.transaction_data_manager - INFO - __init__:100 - Transaction Data Manager initialized
2025-06-30 00:22:31 - bank_analyzer.ui.ml_labeling_window - INFO - __init__:1002 - ML Labeling Window initialized with performance optimizations
2025-06-30 00:22:32 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:241 - Loaded 60 categories from main application
2025-06-30 00:22:32 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:274 - Loaded 106 ML-specific categories
2025-06-30 00:22:32 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 00:22:32 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:22:32 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:22:32 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 00:22:32 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 00:22:32 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:22:32 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 00:22:32 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:22:32 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 00:22:32 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:22:32 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 00:22:32 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:22:32 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:22:32 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:22:32 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 00:22:32 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 00:22:32 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 00:22:32 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 00:22:32 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:22:32 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:22:32 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 00:22:32 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 00:22:32 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 00:22:32 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 00:22:32 - bank_analyzer.ml.batch_processor - INFO - __init__:87 - Batch processor initialized (batch size: 20-30)
2025-06-30 00:22:32 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:241 - Loaded 60 categories from main application
2025-06-30 00:22:32 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:274 - Loaded 106 ML-specific categories
2025-06-30 00:22:32 - bank_analyzer.ml.dynamic_category_manager - INFO - _load_existing_categories:137 - Loaded 32 main categories
2025-06-30 00:22:32 - bank_analyzer.ml.dynamic_category_manager - INFO - __init__:118 - Dynamic category manager initialized
2025-06-30 00:22:32 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 00:22:32 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 00:22:32 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 00:22:32 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 00:22:32 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 00:22:32 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 00:22:32 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 00:22:32 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 00:22:32 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 00:22:32 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 00:22:32 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 00:22:32 - bank_analyzer.ml.budget_guardian - INFO - __init__:107 - Budget guardian initialized ($5.0 total, $1.0 daily)
2025-06-30 00:22:32 - bank_analyzer.ml.enhanced_sambanova_categorizer - INFO - __init__:109 - Enhanced SambaNova categorizer initialized
2025-06-30 00:22:32 - bank_analyzer.ml.hybrid_ml_categorizer - INFO - __init__:115 - Hybrid ML categorizer initialized in hybrid mode
2025-06-30 00:22:32 - bank_analyzer.core.transaction_data_manager - INFO - start_clean_session:584 - Started clean session
2025-06-30 00:22:32 - bank_analyzer.ui.ml_labeling_window - INFO - on_selection_changed:2194 - Selection changed: 0 transactions selected
2025-06-30 00:22:32 - bank_analyzer.ui.ml_labeling_window - INFO - on_selection_changed:2195 - Selected hash_ids: []...
2025-06-30 00:22:32 - bank_analyzer.ui.enhanced_transaction_table - INFO - populate_table:221 - No valid transactions to display
2025-06-30 00:22:32 - bank_analyzer.ui.ml_labeling_window - INFO - _show_clean_start_message:1051 - Clean start message displayed
2025-06-30 00:22:32 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:22:32 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:521 - Loaded 83 unique transactions from storage in 0.111 seconds
2025-06-30 00:22:32 - bank_analyzer.ui.background_workers - INFO - run:137 - Filtering completed in 0.112 seconds, found 50 transactions
2025-06-30 00:22:32 - bank_analyzer.ui.ml_labeling_window - INFO - on_filter_completed:2151 - Skipping filter results display - no active session or data
2025-06-30 00:22:32 - bank_analyzer.ui.ml_labeling_window - INFO - on_selection_changed:2194 - Selection changed: 0 transactions selected
2025-06-30 00:22:32 - bank_analyzer.ui.ml_labeling_window - INFO - on_selection_changed:2195 - Selected hash_ids: []...
2025-06-30 00:22:32 - bank_analyzer.ui.enhanced_transaction_table - INFO - populate_table:221 - No valid transactions to display
2025-06-30 00:22:36 - bank_analyzer.ui.session_management_dialog - INFO - load_sessions:281 - Loaded 6 sessions
2025-06-30 00:22:42 - bank_analyzer.core.transaction_data_manager - INFO - delete_session:536 - Deleted session: txn_session_20250629_232813_d41d8cd9
2025-06-30 00:22:42 - bank_analyzer.ui.session_management_dialog - INFO - load_sessions:281 - Loaded 5 sessions
2025-06-30 00:22:48 - bank_analyzer.core.transaction_data_manager - INFO - delete_session:536 - Deleted session: txn_session_20250629_231337_b5697f60
2025-06-30 00:22:49 - bank_analyzer.ui.session_management_dialog - INFO - load_sessions:281 - Loaded 4 sessions
2025-06-30 00:22:53 - bank_analyzer.core.transaction_data_manager - INFO - delete_session:536 - Deleted session: txn_session_20250629_231629_b5697f60
2025-06-30 00:22:54 - bank_analyzer.ui.session_management_dialog - INFO - load_sessions:281 - Loaded 3 sessions
2025-06-30 00:22:59 - bank_analyzer.core.transaction_data_manager - INFO - delete_session:536 - Deleted session: txn_session_20250629_144122_b5697f60
2025-06-30 00:22:59 - bank_analyzer.ui.session_management_dialog - INFO - load_sessions:281 - Loaded 2 sessions
2025-06-30 00:23:16 - bank_analyzer.ui.session_management_dialog - INFO - load_sessions:281 - Loaded 2 sessions
2025-06-30 00:23:19 - bank_analyzer.core.transaction_data_manager - INFO - delete_session:536 - Deleted session: txn_session_20250629_235355_50d3a86c
2025-06-30 00:23:20 - bank_analyzer.ui.session_management_dialog - INFO - load_sessions:281 - Loaded 1 sessions
2025-06-30 00:23:39 - __main__ - INFO - main:80 - Bank Statement Analyzer application exited with code: 0
2025-06-30 00:46:36 - bank_analyzer.core.logger - INFO - setup_logging:72 - Logging system initialized
2025-06-30 00:46:36 - bank_analyzer.core.logger - INFO - setup_logging:73 - Log file: logs\bank_analyzer_20250630.log
2025-06-30 00:46:36 - bank_analyzer.core.logger - INFO - setup_logging:74 - Log level: INFO
2025-06-30 00:46:36 - __main__ - INFO - main:47 - Starting Bank Statement Analyzer application
2025-06-30 00:46:36 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 00:46:36 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 00:46:36 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:46:36 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:46:36 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 00:46:37 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 00:46:37 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:46:37 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 00:46:37 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:46:37 - bank_analyzer.ml.ai_categorization_coordinator - INFO - _load_config:117 - Loaded AI coordinator config from bank_analyzer_config\ai_coordinator_config.json
2025-06-30 00:46:37 - bank_analyzer.ml.enhanced_merchant_mapper - INFO - _load_patterns:497 - Loaded 24 merchant patterns
2025-06-30 00:46:37 - bank_analyzer.ml.enhanced_merchant_mapper - INFO - _create_automatic_backup:689 - Created automatic backup: bank_analyzer_config\merchant_cache\backups\patterns_backup_20250630_004637.json
2025-06-30 00:46:37 - bank_analyzer.ml.enhanced_merchant_mapper - INFO - __init__:158 - Enhanced merchant mapper initialized with 24 patterns
2025-06-30 00:46:37 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 00:46:37 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 00:46:37 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 00:46:37 - bank_analyzer.ml.transaction_context_analyzer - INFO - __init__:131 - Transaction Context Analyzer initialized
2025-06-30 00:46:37 - bank_analyzer.ml.smart_transaction_router - INFO - _load_config:109 - Loaded router config from bank_analyzer_config\router_config.json
2025-06-30 00:46:37 - bank_analyzer.ml.smart_transaction_router - INFO - __init__:80 - Router initialized: cache_threshold=0.6, max_ai_batch=100
2025-06-30 00:46:37 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:46:37 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:46:37 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 00:46:37 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:46:37 - bank_analyzer.ml.ai_categorization_coordinator - INFO - __init__:91 - AI Coordinator initialized with budget: $2.0
2025-06-30 00:46:37 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 00:46:37 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:46:37 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:46:37 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 00:46:37 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 00:46:37 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:46:37 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 00:46:37 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:46:37 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 00:46:37 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:46:37 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 00:46:37 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:46:37 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:46:37 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:46:37 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 00:46:37 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 00:46:37 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 00:46:37 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 00:46:37 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:46:37 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:46:37 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 00:46:37 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 00:46:37 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 00:46:37 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 00:46:37 - bank_analyzer.ml.sambanova_cost_tracker - INFO - __init__:83 - Cost tracker initialized (total: $0.0000)
2025-06-30 00:46:37 - bank_analyzer.ml.batch_processor - INFO - __init__:87 - Batch processor initialized (batch size: 20-30)
2025-06-30 00:46:37 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:241 - Loaded 60 categories from main application
2025-06-30 00:46:37 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:274 - Loaded 106 ML-specific categories
2025-06-30 00:46:37 - bank_analyzer.ml.dynamic_category_manager - INFO - _load_existing_categories:137 - Loaded 32 main categories
2025-06-30 00:46:37 - bank_analyzer.ml.dynamic_category_manager - INFO - __init__:118 - Dynamic category manager initialized
2025-06-30 00:46:37 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 00:46:37 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 00:46:37 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 00:46:37 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 00:46:37 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 00:46:37 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 00:46:37 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 00:46:37 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 00:46:37 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 00:46:37 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 00:46:37 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 00:46:37 - bank_analyzer.ml.budget_guardian - INFO - __init__:107 - Budget guardian initialized ($5.0 total, $1.0 daily)
2025-06-30 00:46:37 - bank_analyzer.ml.enhanced_sambanova_categorizer - INFO - __init__:109 - Enhanced SambaNova categorizer initialized
2025-06-30 00:46:37 - bank_analyzer.ml.hybrid_ml_categorizer - INFO - __init__:115 - Hybrid ML categorizer initialized in hybrid mode
2025-06-30 00:46:37 - bank_analyzer.ml.integrated_categorizer - INFO - __init__:46 - Enhanced integrated categorizer with AI coordinator initialized
2025-06-30 00:46:37 - bank_analyzer.core.transaction_data_manager - INFO - __init__:100 - Transaction Data Manager initialized
2025-06-30 00:46:38 - bank_analyzer.ui.main_window - INFO - load_initial_data:741 - Initial data loaded successfully
2025-06-30 00:46:38 - bank_analyzer.ui.main_window - INFO - __init__:237 - Bank Analyzer main window initialized
2025-06-30 00:46:38 - __main__ - INFO - main:76 - Bank Statement Analyzer application started successfully
2025-06-30 00:46:39 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.213 seconds
2025-06-30 00:46:39 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.129 seconds
2025-06-30 00:46:39 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.270 seconds
2025-06-30 00:46:39 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.142 seconds
2025-06-30 00:46:40 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.222 seconds
2025-06-30 00:46:40 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.109 seconds
2025-06-30 00:46:40 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.124 seconds
2025-06-30 00:46:40 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.119 seconds
2025-06-30 00:46:48 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.157 seconds
2025-06-30 00:46:48 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.161 seconds
2025-06-30 00:46:55 - bank_analyzer.ui.main_window - INFO - on_bank_selection_changed:774 - Bank selection changed to: Indian Bank
2025-06-30 00:46:57 - bank_analyzer.parsers.parser_factory - INFO - get_parser:64 - Selected Indian Bank specific parser for C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\Jan to march.pdf
2025-06-30 00:46:57 - bank_analyzer.parsers.parser_factory - INFO - parse_file:154 - Successfully parsed 0 transactions from C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\Jan to march.pdf
2025-06-30 00:46:57 - bank_analyzer.ui.main_window - INFO - run:66 - Parsed 0 transactions from C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\Jan to march.pdf
2025-06-30 00:46:57 - bank_analyzer.parsers.parser_factory - INFO - get_parser:64 - Selected Indian Bank specific parser for C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\june to august.pdf
2025-06-30 00:46:57 - bank_analyzer.parsers.parser_factory - INFO - parse_file:154 - Successfully parsed 0 transactions from C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\june to august.pdf
2025-06-30 00:46:57 - bank_analyzer.ui.main_window - INFO - run:66 - Parsed 0 transactions from C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\june to august.pdf
2025-06-30 00:46:57 - bank_analyzer.parsers.parser_factory - INFO - get_parser:64 - Selected Indian Bank specific parser for C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\March to june.pdf
2025-06-30 00:46:57 - bank_analyzer.parsers.parser_factory - INFO - parse_file:154 - Successfully parsed 0 transactions from C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\March to june.pdf
2025-06-30 00:46:57 - bank_analyzer.ui.main_window - INFO - run:66 - Parsed 0 transactions from C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\March to june.pdf
2025-06-30 00:46:57 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 00:46:57 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.189 seconds
2025-06-30 00:46:57 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.141 seconds
2025-06-30 00:46:58 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.103 seconds
2025-06-30 00:46:58 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.108 seconds
2025-06-30 00:46:58 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.104 seconds
2025-06-30 00:46:58 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.107 seconds
2025-06-30 00:46:58 - bank_analyzer.core.transaction_data_manager - INFO - create_session:166 - Created transaction session: txn_session_20250630_004658_d41d8cd9 (0 transactions)
2025-06-30 00:46:58 - bank_analyzer.ui.main_window - INFO - on_processing_completed:1061 - Created session: txn_session_20250630_004658_d41d8cd9
2025-06-30 00:46:58 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.122 seconds
2025-06-30 00:46:58 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.110 seconds
2025-06-30 00:47:08 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.356 seconds
2025-06-30 00:47:08 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.136 seconds
2025-06-30 00:47:18 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.173 seconds
2025-06-30 00:47:18 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.115 seconds
2025-06-30 00:47:28 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.459 seconds
2025-06-30 00:47:28 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.120 seconds
2025-06-30 00:47:38 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.194 seconds
2025-06-30 00:47:38 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.119 seconds
2025-06-30 00:47:48 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.248 seconds
2025-06-30 00:47:48 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.180 seconds
2025-06-30 00:47:58 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.238 seconds
2025-06-30 00:47:58 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.150 seconds
2025-06-30 00:48:08 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.125 seconds
2025-06-30 00:48:08 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.116 seconds
2025-06-30 00:48:18 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.153 seconds
2025-06-30 00:48:18 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.147 seconds
2025-06-30 00:56:25 - bank_analyzer.core.logger - INFO - setup_logging:72 - Logging system initialized
2025-06-30 00:56:25 - bank_analyzer.core.logger - INFO - setup_logging:73 - Log file: logs\bank_analyzer_20250630.log
2025-06-30 00:56:25 - bank_analyzer.core.logger - INFO - setup_logging:74 - Log level: INFO
2025-06-30 00:56:25 - __main__ - INFO - main:47 - Starting Bank Statement Analyzer application
2025-06-30 00:56:25 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 00:56:25 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 00:56:25 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:56:25 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:56:25 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 00:56:25 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 00:56:25 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:56:25 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 00:56:25 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:56:25 - bank_analyzer.ml.ai_categorization_coordinator - INFO - _load_config:117 - Loaded AI coordinator config from bank_analyzer_config\ai_coordinator_config.json
2025-06-30 00:56:25 - bank_analyzer.ml.enhanced_merchant_mapper - INFO - _load_patterns:497 - Loaded 24 merchant patterns
2025-06-30 00:56:25 - bank_analyzer.ml.enhanced_merchant_mapper - INFO - _create_automatic_backup:689 - Created automatic backup: bank_analyzer_config\merchant_cache\backups\patterns_backup_20250630_005625.json
2025-06-30 00:56:25 - bank_analyzer.ml.enhanced_merchant_mapper - INFO - __init__:158 - Enhanced merchant mapper initialized with 24 patterns
2025-06-30 00:56:25 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 00:56:25 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 00:56:25 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 00:56:25 - bank_analyzer.ml.transaction_context_analyzer - INFO - __init__:131 - Transaction Context Analyzer initialized
2025-06-30 00:56:25 - bank_analyzer.ml.smart_transaction_router - INFO - _load_config:109 - Loaded router config from bank_analyzer_config\router_config.json
2025-06-30 00:56:25 - bank_analyzer.ml.smart_transaction_router - INFO - __init__:80 - Router initialized: cache_threshold=0.6, max_ai_batch=100
2025-06-30 00:56:25 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:56:25 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:56:25 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 00:56:25 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:56:25 - bank_analyzer.ml.ai_categorization_coordinator - INFO - __init__:91 - AI Coordinator initialized with budget: $2.0
2025-06-30 00:56:25 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 00:56:25 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:56:25 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:56:25 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 00:56:25 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 00:56:25 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:56:26 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 00:56:26 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:56:26 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 00:56:26 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:56:26 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 00:56:26 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:56:26 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:56:26 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:56:26 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 00:56:26 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 00:56:26 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 00:56:26 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 00:56:26 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:56:26 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:56:26 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 00:56:26 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 00:56:26 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 00:56:26 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 00:56:26 - bank_analyzer.ml.sambanova_cost_tracker - INFO - __init__:83 - Cost tracker initialized (total: $0.0000)
2025-06-30 00:56:26 - bank_analyzer.ml.batch_processor - INFO - __init__:87 - Batch processor initialized (batch size: 20-30)
2025-06-30 00:56:26 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:241 - Loaded 60 categories from main application
2025-06-30 00:56:26 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:274 - Loaded 106 ML-specific categories
2025-06-30 00:56:26 - bank_analyzer.ml.dynamic_category_manager - INFO - _load_existing_categories:137 - Loaded 32 main categories
2025-06-30 00:56:26 - bank_analyzer.ml.dynamic_category_manager - INFO - __init__:118 - Dynamic category manager initialized
2025-06-30 00:56:26 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 00:56:26 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 00:56:26 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 00:56:26 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 00:56:26 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 00:56:26 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 00:56:26 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 00:56:26 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 00:56:26 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 00:56:26 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 00:56:26 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 00:56:26 - bank_analyzer.ml.budget_guardian - INFO - __init__:107 - Budget guardian initialized ($5.0 total, $1.0 daily)
2025-06-30 00:56:26 - bank_analyzer.ml.enhanced_sambanova_categorizer - INFO - __init__:109 - Enhanced SambaNova categorizer initialized
2025-06-30 00:56:26 - bank_analyzer.ml.hybrid_ml_categorizer - INFO - __init__:115 - Hybrid ML categorizer initialized in hybrid mode
2025-06-30 00:56:26 - bank_analyzer.ml.integrated_categorizer - INFO - __init__:46 - Enhanced integrated categorizer with AI coordinator initialized
2025-06-30 00:56:26 - bank_analyzer.core.transaction_data_manager - INFO - __init__:100 - Transaction Data Manager initialized
2025-06-30 00:56:26 - bank_analyzer.ui.main_window - INFO - load_initial_data:741 - Initial data loaded successfully
2025-06-30 00:56:26 - bank_analyzer.ui.main_window - INFO - __init__:237 - Bank Analyzer main window initialized
2025-06-30 00:56:27 - __main__ - INFO - main:76 - Bank Statement Analyzer application started successfully
2025-06-30 00:56:27 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.126 seconds
2025-06-30 00:56:27 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.138 seconds
2025-06-30 00:56:28 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.125 seconds
2025-06-30 00:56:28 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.129 seconds
2025-06-30 00:56:28 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.218 seconds
2025-06-30 00:56:29 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.446 seconds
2025-06-30 00:56:29 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.186 seconds
2025-06-30 00:56:29 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.148 seconds
2025-06-30 00:56:30 - __main__ - INFO - main:80 - Bank Statement Analyzer application exited with code: 0
2025-06-30 01:08:58 - bank_analyzer.core.logger - INFO - setup_logging:72 - Logging system initialized
2025-06-30 01:08:58 - bank_analyzer.core.logger - INFO - setup_logging:73 - Log file: logs\bank_analyzer_20250630.log
2025-06-30 01:08:58 - bank_analyzer.core.logger - INFO - setup_logging:74 - Log level: INFO
2025-06-30 01:08:58 - __main__ - INFO - main:47 - Starting Bank Statement Analyzer application
2025-06-30 01:08:58 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 01:08:58 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 01:08:58 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:08:58 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:08:58 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:08:59 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:08:59 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:08:59 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:08:59 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:08:59 - bank_analyzer.ml.ai_categorization_coordinator - INFO - _load_config:117 - Loaded AI coordinator config from bank_analyzer_config\ai_coordinator_config.json
2025-06-30 01:08:59 - bank_analyzer.ml.enhanced_merchant_mapper - INFO - _load_patterns:497 - Loaded 24 merchant patterns
2025-06-30 01:08:59 - bank_analyzer.ml.enhanced_merchant_mapper - INFO - _create_automatic_backup:689 - Created automatic backup: bank_analyzer_config\merchant_cache\backups\patterns_backup_20250630_010859.json
2025-06-30 01:08:59 - bank_analyzer.ml.enhanced_merchant_mapper - INFO - __init__:158 - Enhanced merchant mapper initialized with 24 patterns
2025-06-30 01:08:59 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:08:59 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:08:59 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:08:59 - bank_analyzer.ml.transaction_context_analyzer - INFO - __init__:131 - Transaction Context Analyzer initialized
2025-06-30 01:08:59 - bank_analyzer.ml.smart_transaction_router - INFO - _load_config:109 - Loaded router config from bank_analyzer_config\router_config.json
2025-06-30 01:08:59 - bank_analyzer.ml.smart_transaction_router - INFO - __init__:80 - Router initialized: cache_threshold=0.6, max_ai_batch=100
2025-06-30 01:08:59 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:08:59 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:08:59 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:08:59 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:08:59 - bank_analyzer.ml.ai_categorization_coordinator - INFO - __init__:91 - AI Coordinator initialized with budget: $2.0
2025-06-30 01:08:59 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 01:08:59 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:08:59 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:08:59 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:08:59 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:08:59 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:08:59 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:08:59 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:08:59 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:08:59 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:08:59 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:08:59 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:08:59 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:08:59 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:08:59 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:08:59 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:08:59 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:08:59 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:08:59 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:08:59 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:08:59 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:08:59 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:08:59 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:08:59 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:08:59 - bank_analyzer.ml.sambanova_cost_tracker - INFO - __init__:83 - Cost tracker initialized (total: $0.0000)
2025-06-30 01:08:59 - bank_analyzer.ml.batch_processor - INFO - __init__:87 - Batch processor initialized (batch size: 20-30)
2025-06-30 01:08:59 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:241 - Loaded 60 categories from main application
2025-06-30 01:08:59 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:274 - Loaded 106 ML-specific categories
2025-06-30 01:08:59 - bank_analyzer.ml.dynamic_category_manager - INFO - _load_existing_categories:137 - Loaded 32 main categories
2025-06-30 01:08:59 - bank_analyzer.ml.dynamic_category_manager - INFO - __init__:118 - Dynamic category manager initialized
2025-06-30 01:08:59 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:08:59 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:08:59 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:08:59 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 01:08:59 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:08:59 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:08:59 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:08:59 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:08:59 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:08:59 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:08:59 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 01:08:59 - bank_analyzer.ml.budget_guardian - INFO - __init__:107 - Budget guardian initialized ($5.0 total, $1.0 daily)
2025-06-30 01:08:59 - bank_analyzer.ml.enhanced_sambanova_categorizer - INFO - __init__:109 - Enhanced SambaNova categorizer initialized
2025-06-30 01:08:59 - bank_analyzer.ml.hybrid_ml_categorizer - INFO - __init__:115 - Hybrid ML categorizer initialized in hybrid mode
2025-06-30 01:08:59 - bank_analyzer.ml.integrated_categorizer - INFO - __init__:46 - Enhanced integrated categorizer with AI coordinator initialized
2025-06-30 01:08:59 - bank_analyzer.core.transaction_data_manager - INFO - __init__:100 - Transaction Data Manager initialized
2025-06-30 01:09:00 - bank_analyzer.ui.main_window - INFO - load_initial_data:741 - Initial data loaded successfully
2025-06-30 01:09:00 - bank_analyzer.ui.main_window - INFO - __init__:237 - Bank Analyzer main window initialized
2025-06-30 01:09:00 - __main__ - INFO - main:76 - Bank Statement Analyzer application started successfully
2025-06-30 01:09:01 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.148 seconds
2025-06-30 01:09:01 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.161 seconds
2025-06-30 01:09:01 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.160 seconds
2025-06-30 01:09:01 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.169 seconds
2025-06-30 01:09:02 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.197 seconds
2025-06-30 01:09:02 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.134 seconds
2025-06-30 01:09:02 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.128 seconds
2025-06-30 01:09:02 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.127 seconds
2025-06-30 01:09:10 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.167 seconds
2025-06-30 01:09:10 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.120 seconds
2025-06-30 01:09:20 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.145 seconds
2025-06-30 01:09:20 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.115 seconds
2025-06-30 01:09:30 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.128 seconds
2025-06-30 01:09:30 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.112 seconds
2025-06-30 01:09:40 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.174 seconds
2025-06-30 01:09:40 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.112 seconds
2025-06-30 01:09:50 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.171 seconds
2025-06-30 01:09:50 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.122 seconds
2025-06-30 01:10:00 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.163 seconds
2025-06-30 01:10:00 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.111 seconds
2025-06-30 01:10:10 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.184 seconds
2025-06-30 01:10:10 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.110 seconds
2025-06-30 01:10:20 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.189 seconds
2025-06-30 01:10:20 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.141 seconds
2025-06-30 01:10:24 - bank_analyzer.parsers.parser_factory - INFO - get_parser:89 - Selected pdf parser for C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\Jan to march.pdf
2025-06-30 01:10:24 - bank_analyzer.parsers.pdf_parser - INFO - parse:53 - Parsing PDF with rewritten parser: C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\Jan to march.pdf
2025-06-30 01:10:28 - bank_analyzer.parsers.pdf_parser - INFO - parse:68 - Rewritten parser extracted 316 transactions
2025-06-30 01:10:28 - bank_analyzer.parsers.parser_factory - INFO - parse_file:158 - Successfully parsed 316 transactions from C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\Jan to march.pdf
2025-06-30 01:10:28 - bank_analyzer.ui.main_window - INFO - run:66 - Parsed 316 transactions from C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\Jan to march.pdf
2025-06-30 01:10:28 - bank_analyzer.parsers.parser_factory - INFO - get_parser:89 - Selected pdf parser for C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\june to august.pdf
2025-06-30 01:10:28 - bank_analyzer.parsers.pdf_parser - INFO - parse:53 - Parsing PDF with rewritten parser: C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\june to august.pdf
2025-06-30 01:10:30 - bank_analyzer.parsers.pdf_parser - INFO - parse:68 - Rewritten parser extracted 109 transactions
2025-06-30 01:10:30 - bank_analyzer.parsers.parser_factory - INFO - parse_file:158 - Successfully parsed 109 transactions from C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\june to august.pdf
2025-06-30 01:10:30 - bank_analyzer.ui.main_window - INFO - run:66 - Parsed 109 transactions from C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\june to august.pdf
2025-06-30 01:10:30 - bank_analyzer.parsers.parser_factory - INFO - get_parser:89 - Selected pdf parser for C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\March to june.pdf
2025-06-30 01:10:30 - bank_analyzer.parsers.pdf_parser - INFO - parse:53 - Parsing PDF with rewritten parser: C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\March to june.pdf
2025-06-30 01:10:30 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.452 seconds
2025-06-30 01:10:31 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.298 seconds
2025-06-30 01:10:34 - bank_analyzer.parsers.pdf_parser - INFO - parse:68 - Rewritten parser extracted 281 transactions
2025-06-30 01:10:34 - bank_analyzer.parsers.parser_factory - INFO - parse_file:158 - Successfully parsed 281 transactions from C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\March to june.pdf
2025-06-30 01:10:34 - bank_analyzer.ui.main_window - INFO - run:66 - Parsed 281 transactions from C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\March to june.pdf
2025-06-30 01:10:34 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 01:10:34 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.117 seconds
2025-06-30 01:10:34 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.114 seconds
2025-06-30 01:10:34 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.109 seconds
2025-06-30 01:10:35 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.121 seconds
2025-06-30 01:10:35 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.112 seconds
2025-06-30 01:10:35 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.111 seconds
2025-06-30 01:10:35 - bank_analyzer.core.transaction_data_manager - INFO - create_session:166 - Created transaction session: txn_session_20250630_011035_8d4a20e2 (706 transactions)
2025-06-30 01:10:35 - bank_analyzer.ui.main_window - INFO - on_processing_completed:1061 - Created session: txn_session_20250630_011035_8d4a20e2
2025-06-30 01:10:39 - bank_analyzer.core.transaction_data_manager - INFO - __init__:100 - Transaction Data Manager initialized
2025-06-30 01:10:39 - bank_analyzer.ui.ml_labeling_window - INFO - __init__:1002 - ML Labeling Window initialized with performance optimizations
2025-06-30 01:10:39 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:241 - Loaded 60 categories from main application
2025-06-30 01:10:39 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:274 - Loaded 106 ML-specific categories
2025-06-30 01:10:39 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 01:10:39 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:10:39 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:10:39 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:10:40 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:10:40 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:10:40 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:10:40 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:10:40 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:10:40 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:10:40 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:10:40 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:10:40 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:10:40 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:10:40 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:10:40 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:10:40 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:10:40 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:10:40 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:10:40 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:10:40 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:10:40 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:10:40 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:10:40 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:10:40 - bank_analyzer.ml.batch_processor - INFO - __init__:87 - Batch processor initialized (batch size: 20-30)
2025-06-30 01:10:40 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:241 - Loaded 60 categories from main application
2025-06-30 01:10:40 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:274 - Loaded 106 ML-specific categories
2025-06-30 01:10:40 - bank_analyzer.ml.dynamic_category_manager - INFO - _load_existing_categories:137 - Loaded 32 main categories
2025-06-30 01:10:40 - bank_analyzer.ml.dynamic_category_manager - INFO - __init__:118 - Dynamic category manager initialized
2025-06-30 01:10:40 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:10:40 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:10:40 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:10:40 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 01:10:40 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:10:40 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:10:40 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:10:40 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:10:40 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:10:40 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:10:40 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 01:10:40 - bank_analyzer.ml.budget_guardian - INFO - __init__:107 - Budget guardian initialized ($5.0 total, $1.0 daily)
2025-06-30 01:10:40 - bank_analyzer.ml.enhanced_sambanova_categorizer - INFO - __init__:109 - Enhanced SambaNova categorizer initialized
2025-06-30 01:10:40 - bank_analyzer.ml.hybrid_ml_categorizer - INFO - __init__:115 - Hybrid ML categorizer initialized in hybrid mode
2025-06-30 01:10:40 - bank_analyzer.core.transaction_data_manager - INFO - start_clean_session:584 - Started clean session
2025-06-30 01:10:40 - bank_analyzer.ui.ml_labeling_window - INFO - on_selection_changed:2194 - Selection changed: 0 transactions selected
2025-06-30 01:10:40 - bank_analyzer.ui.ml_labeling_window - INFO - on_selection_changed:2195 - Selected hash_ids: []...
2025-06-30 01:10:40 - bank_analyzer.ui.enhanced_transaction_table - INFO - populate_table:221 - No valid transactions to display
2025-06-30 01:10:40 - bank_analyzer.ui.ml_labeling_window - INFO - _show_clean_start_message:1051 - Clean start message displayed
2025-06-30 01:10:40 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.120 seconds
2025-06-30 01:10:40 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.119 seconds
2025-06-30 01:10:40 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:10:40 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.124 seconds
2025-06-30 01:10:40 - bank_analyzer.ui.background_workers - INFO - run:137 - Filtering completed in 0.125 seconds, found 50 transactions
2025-06-30 01:10:40 - bank_analyzer.ui.ml_labeling_window - INFO - on_filter_completed:2151 - Skipping filter results display - no active session or data
2025-06-30 01:10:40 - bank_analyzer.ui.ml_labeling_window - INFO - on_selection_changed:2194 - Selection changed: 0 transactions selected
2025-06-30 01:10:40 - bank_analyzer.ui.ml_labeling_window - INFO - on_selection_changed:2195 - Selected hash_ids: []...
2025-06-30 01:10:40 - bank_analyzer.ui.enhanced_transaction_table - INFO - populate_table:221 - No valid transactions to display
2025-06-30 01:10:46 - bank_analyzer.ui.ml_labeling_window - INFO - load_latest_session_directly:3630 - No reasonable sessions found, using most recent session
2025-06-30 01:10:46 - bank_analyzer.ml.data_preparation - INFO - extract_unique_transactions:363 - Extracted 405 unique transaction descriptions from 706 transactions
2025-06-30 01:10:46 - bank_analyzer.ui.ml_labeling_window - INFO - _force_clear_all_caches:4224 - Cache clearing no longer needed - caching system removed for reliability
2025-06-30 01:10:46 - bank_analyzer.ui.ml_labeling_window - INFO - _force_clear_all_caches:4238 - All caches cleared successfully
2025-06-30 01:10:48 - bank_analyzer.ui.ml_labeling_window - INFO - load_latest_session_directly:3685 - Loaded 405 transactions directly from latest session: txn_session_20250630_011035_8d4a20e2
2025-06-30 01:10:50 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.144 seconds
2025-06-30 01:10:50 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.134 seconds
2025-06-30 01:11:00 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.123 seconds
2025-06-30 01:11:00 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.113 seconds
2025-06-30 01:11:10 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.123 seconds
2025-06-30 01:11:10 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.109 seconds
2025-06-30 01:11:14 - __main__ - INFO - main:80 - Bank Statement Analyzer application exited with code: 0
2025-06-30 01:11:49 - bank_analyzer.core.logger - INFO - setup_logging:72 - Logging system initialized
2025-06-30 01:11:49 - bank_analyzer.core.logger - INFO - setup_logging:73 - Log file: logs\bank_analyzer_20250630.log
2025-06-30 01:11:49 - bank_analyzer.core.logger - INFO - setup_logging:74 - Log level: INFO
2025-06-30 01:11:49 - __main__ - INFO - main:47 - Starting Bank Statement Analyzer application
2025-06-30 01:11:49 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 01:11:49 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 01:11:49 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:49 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:49 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:11:49 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:11:49 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:49 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:11:49 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:49 - bank_analyzer.ml.ai_categorization_coordinator - INFO - _load_config:117 - Loaded AI coordinator config from bank_analyzer_config\ai_coordinator_config.json
2025-06-30 01:11:49 - bank_analyzer.ml.enhanced_merchant_mapper - INFO - _load_patterns:497 - Loaded 24 merchant patterns
2025-06-30 01:11:49 - bank_analyzer.ml.enhanced_merchant_mapper - INFO - _create_automatic_backup:689 - Created automatic backup: bank_analyzer_config\merchant_cache\backups\patterns_backup_20250630_011149.json
2025-06-30 01:11:49 - bank_analyzer.ml.enhanced_merchant_mapper - INFO - __init__:158 - Enhanced merchant mapper initialized with 24 patterns
2025-06-30 01:11:49 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:11:49 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:11:49 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:11:49 - bank_analyzer.ml.transaction_context_analyzer - INFO - __init__:131 - Transaction Context Analyzer initialized
2025-06-30 01:11:49 - bank_analyzer.ml.smart_transaction_router - INFO - _load_config:109 - Loaded router config from bank_analyzer_config\router_config.json
2025-06-30 01:11:49 - bank_analyzer.ml.smart_transaction_router - INFO - __init__:80 - Router initialized: cache_threshold=0.6, max_ai_batch=100
2025-06-30 01:11:49 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:49 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:49 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:11:49 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:49 - bank_analyzer.ml.ai_categorization_coordinator - INFO - __init__:91 - AI Coordinator initialized with budget: $2.0
2025-06-30 01:11:49 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 01:11:49 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:49 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:49 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:11:49 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:11:49 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:49 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:11:49 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:49 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:11:49 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:49 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:11:49 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:49 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:49 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:49 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:11:49 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:11:49 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:11:49 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:11:49 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:49 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:49 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:11:49 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:11:49 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:11:49 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:11:49 - bank_analyzer.ml.sambanova_cost_tracker - INFO - __init__:83 - Cost tracker initialized (total: $0.0000)
2025-06-30 01:11:49 - bank_analyzer.ml.batch_processor - INFO - __init__:87 - Batch processor initialized (batch size: 20-30)
2025-06-30 01:11:50 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:241 - Loaded 60 categories from main application
2025-06-30 01:11:50 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:274 - Loaded 106 ML-specific categories
2025-06-30 01:11:50 - bank_analyzer.ml.dynamic_category_manager - INFO - _load_existing_categories:137 - Loaded 32 main categories
2025-06-30 01:11:50 - bank_analyzer.ml.dynamic_category_manager - INFO - __init__:118 - Dynamic category manager initialized
2025-06-30 01:11:50 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:11:50 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:11:50 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:11:50 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 01:11:50 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:11:50 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:11:50 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:11:50 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:11:50 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:11:50 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:11:50 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 01:11:50 - bank_analyzer.ml.budget_guardian - INFO - __init__:107 - Budget guardian initialized ($5.0 total, $1.0 daily)
2025-06-30 01:11:50 - bank_analyzer.ml.enhanced_sambanova_categorizer - INFO - __init__:109 - Enhanced SambaNova categorizer initialized
2025-06-30 01:11:50 - bank_analyzer.ml.hybrid_ml_categorizer - INFO - __init__:115 - Hybrid ML categorizer initialized in hybrid mode
2025-06-30 01:11:50 - bank_analyzer.ml.integrated_categorizer - INFO - __init__:46 - Enhanced integrated categorizer with AI coordinator initialized
2025-06-30 01:11:50 - bank_analyzer.core.transaction_data_manager - INFO - __init__:100 - Transaction Data Manager initialized
2025-06-30 01:11:50 - bank_analyzer.ui.main_window - INFO - load_initial_data:741 - Initial data loaded successfully
2025-06-30 01:11:50 - bank_analyzer.ui.main_window - INFO - __init__:237 - Bank Analyzer main window initialized
2025-06-30 01:11:50 - __main__ - INFO - main:76 - Bank Statement Analyzer application started successfully
2025-06-30 01:11:51 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.236 seconds
2025-06-30 01:11:51 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.158 seconds
2025-06-30 01:11:51 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.112 seconds
2025-06-30 01:11:51 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.111 seconds
2025-06-30 01:11:52 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.369 seconds
2025-06-30 01:11:52 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.263 seconds
2025-06-30 01:11:52 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.194 seconds
2025-06-30 01:11:53 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.115 seconds
2025-06-30 01:11:55 - bank_analyzer.core.transaction_data_manager - INFO - __init__:100 - Transaction Data Manager initialized
2025-06-30 01:11:55 - bank_analyzer.ui.ml_labeling_window - INFO - __init__:1002 - ML Labeling Window initialized with performance optimizations
2025-06-30 01:11:55 - bank_analyzer.core.transaction_data_manager - INFO - start_clean_session:584 - Started clean session
2025-06-30 01:11:55 - bank_analyzer.ui.ml_labeling_window - ERROR - _show_empty_stats_without_loading:1736 - Error showing empty stats without loading: 'MLLabelingWindow' object has no attribute 'progress_bar'
2025-06-30 01:11:55 - bank_analyzer.ui.ml_labeling_window - INFO - _show_clean_start_message:1051 - Clean start message displayed
2025-06-30 01:11:55 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:241 - Loaded 60 categories from main application
2025-06-30 01:11:55 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:274 - Loaded 106 ML-specific categories
2025-06-30 01:11:55 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 01:11:55 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:55 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:55 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:11:55 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:11:55 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:55 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:11:55 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:55 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:11:55 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:55 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:11:55 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:55 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:55 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:55 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:11:55 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:11:55 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:11:55 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:11:55 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:55 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:55 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:11:55 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:11:55 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:11:55 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:11:55 - bank_analyzer.ml.batch_processor - INFO - __init__:87 - Batch processor initialized (batch size: 20-30)
2025-06-30 01:11:55 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:241 - Loaded 60 categories from main application
2025-06-30 01:11:55 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:274 - Loaded 106 ML-specific categories
2025-06-30 01:11:55 - bank_analyzer.ml.dynamic_category_manager - INFO - _load_existing_categories:137 - Loaded 32 main categories
2025-06-30 01:11:55 - bank_analyzer.ml.dynamic_category_manager - INFO - __init__:118 - Dynamic category manager initialized
2025-06-30 01:11:55 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:11:55 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:11:55 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:11:55 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 01:11:55 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:11:55 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:11:55 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:11:55 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:11:55 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:11:55 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:11:55 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 01:11:55 - bank_analyzer.ml.budget_guardian - INFO - __init__:107 - Budget guardian initialized ($5.0 total, $1.0 daily)
2025-06-30 01:11:55 - bank_analyzer.ml.enhanced_sambanova_categorizer - INFO - __init__:109 - Enhanced SambaNova categorizer initialized
2025-06-30 01:11:55 - bank_analyzer.ml.hybrid_ml_categorizer - INFO - __init__:115 - Hybrid ML categorizer initialized in hybrid mode
2025-06-30 01:11:56 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.108 seconds
2025-06-30 01:11:56 - bank_analyzer.ui.background_workers - INFO - run:137 - Filtering completed in 0.108 seconds, found 50 transactions
2025-06-30 01:11:56 - bank_analyzer.ui.ml_labeling_window - INFO - on_filter_completed:2151 - Skipping filter results display - no active session or data
2025-06-30 01:11:56 - bank_analyzer.ui.ml_labeling_window - INFO - on_selection_changed:2194 - Selection changed: 0 transactions selected
2025-06-30 01:11:56 - bank_analyzer.ui.ml_labeling_window - INFO - on_selection_changed:2195 - Selected hash_ids: []...
2025-06-30 01:11:56 - bank_analyzer.ui.enhanced_transaction_table - INFO - populate_table:221 - No valid transactions to display
2025-06-30 01:12:00 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.264 seconds
2025-06-30 01:12:00 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.151 seconds
2025-06-30 01:12:10 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.152 seconds
2025-06-30 01:12:10 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.107 seconds
2025-06-30 01:12:20 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.249 seconds
2025-06-30 01:12:20 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.132 seconds
2025-06-30 01:12:30 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.313 seconds
2025-06-30 01:12:30 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.125 seconds
2025-06-30 01:12:40 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.285 seconds
2025-06-30 01:12:40 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.106 seconds
2025-06-30 01:12:50 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.606 seconds
2025-06-30 01:12:51 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.313 seconds
2025-06-30 01:13:00 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.503 seconds
2025-06-30 01:13:00 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.130 seconds
2025-06-30 01:13:10 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.256 seconds
2025-06-30 01:13:10 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.177 seconds
2025-06-30 01:13:20 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.226 seconds
2025-06-30 01:13:20 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.128 seconds
2025-06-30 01:13:22 - __main__ - INFO - main:80 - Bank Statement Analyzer application exited with code: 0
2025-06-30 01:34:44 - bank_analyzer.core.logger - INFO - setup_logging:72 - Logging system initialized
2025-06-30 01:34:44 - bank_analyzer.core.logger - INFO - setup_logging:73 - Log file: logs\bank_analyzer_20250630.log
2025-06-30 01:34:44 - bank_analyzer.core.logger - INFO - setup_logging:74 - Log level: INFO
2025-06-30 01:34:44 - __main__ - INFO - main:47 - Starting Bank Statement Analyzer application
2025-06-30 01:34:44 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 01:34:44 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 01:34:44 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:34:44 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:34:44 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:34:45 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:34:45 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:34:45 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:34:45 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:34:45 - bank_analyzer.ml.ai_categorization_coordinator - INFO - _load_config:117 - Loaded AI coordinator config from bank_analyzer_config\ai_coordinator_config.json
2025-06-30 01:34:45 - bank_analyzer.ml.enhanced_merchant_mapper - INFO - _load_patterns:497 - Loaded 24 merchant patterns
2025-06-30 01:34:45 - bank_analyzer.ml.enhanced_merchant_mapper - INFO - _create_automatic_backup:689 - Created automatic backup: bank_analyzer_config\merchant_cache\backups\patterns_backup_20250630_013445.json
2025-06-30 01:34:45 - bank_analyzer.ml.enhanced_merchant_mapper - INFO - __init__:158 - Enhanced merchant mapper initialized with 24 patterns
2025-06-30 01:34:45 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:34:45 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:34:45 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:34:45 - bank_analyzer.ml.transaction_context_analyzer - INFO - __init__:131 - Transaction Context Analyzer initialized
2025-06-30 01:34:45 - bank_analyzer.ml.smart_transaction_router - INFO - _load_config:109 - Loaded router config from bank_analyzer_config\router_config.json
2025-06-30 01:34:45 - bank_analyzer.ml.smart_transaction_router - INFO - __init__:80 - Router initialized: cache_threshold=0.6, max_ai_batch=100
2025-06-30 01:34:45 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:34:45 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:34:45 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:34:45 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:34:45 - bank_analyzer.ml.ai_categorization_coordinator - INFO - __init__:91 - AI Coordinator initialized with budget: $2.0
2025-06-30 01:34:45 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 01:34:45 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:34:45 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:34:45 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:34:45 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:34:45 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:34:45 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:34:45 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:34:45 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:34:45 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:34:45 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:34:45 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:34:45 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:34:45 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:34:45 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:34:45 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:34:45 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:34:45 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:34:45 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:34:45 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:34:45 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:34:45 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:34:45 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:34:45 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:34:45 - bank_analyzer.ml.sambanova_cost_tracker - INFO - __init__:83 - Cost tracker initialized (total: $0.0000)
2025-06-30 01:34:45 - bank_analyzer.ml.batch_processor - INFO - __init__:87 - Batch processor initialized (batch size: 20-30)
2025-06-30 01:34:45 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:241 - Loaded 60 categories from main application
2025-06-30 01:34:45 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:274 - Loaded 106 ML-specific categories
2025-06-30 01:34:45 - bank_analyzer.ml.dynamic_category_manager - INFO - _load_existing_categories:137 - Loaded 32 main categories
2025-06-30 01:34:45 - bank_analyzer.ml.dynamic_category_manager - INFO - __init__:118 - Dynamic category manager initialized
2025-06-30 01:34:45 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:34:45 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:34:45 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:34:45 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 01:34:45 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:34:45 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:34:45 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:34:45 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:34:45 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:34:45 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:34:45 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 01:34:45 - bank_analyzer.ml.budget_guardian - INFO - __init__:107 - Budget guardian initialized ($5.0 total, $1.0 daily)
2025-06-30 01:34:45 - bank_analyzer.ml.enhanced_sambanova_categorizer - INFO - __init__:109 - Enhanced SambaNova categorizer initialized
2025-06-30 01:34:45 - bank_analyzer.ml.hybrid_ml_categorizer - INFO - __init__:115 - Hybrid ML categorizer initialized in hybrid mode
2025-06-30 01:34:45 - bank_analyzer.ml.integrated_categorizer - INFO - __init__:46 - Enhanced integrated categorizer with AI coordinator initialized
2025-06-30 01:34:45 - bank_analyzer.core.transaction_data_manager - INFO - __init__:121 - Transaction Data Manager initialized
2025-06-30 01:34:45 - bank_analyzer.ui.main_window - INFO - load_initial_data:741 - Initial data loaded successfully
2025-06-30 01:34:45 - bank_analyzer.ui.main_window - INFO - __init__:237 - Bank Analyzer main window initialized
2025-06-30 01:34:45 - __main__ - INFO - main:76 - Bank Statement Analyzer application started successfully
2025-06-30 01:34:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.129 seconds
2025-06-30 01:34:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.124 seconds
2025-06-30 01:34:47 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.121 seconds
2025-06-30 01:34:47 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.115 seconds
2025-06-30 01:34:47 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.187 seconds
2025-06-30 01:34:47 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.124 seconds
2025-06-30 01:34:48 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.133 seconds
2025-06-30 01:34:48 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.117 seconds
2025-06-30 01:34:55 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.160 seconds
2025-06-30 01:34:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.142 seconds
2025-06-30 01:35:05 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.164 seconds
2025-06-30 01:35:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.164 seconds
2025-06-30 01:35:09 - bank_analyzer.parsers.parser_factory - INFO - get_parser:89 - Selected pdf parser for C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\Jan to march.pdf
2025-06-30 01:35:09 - bank_analyzer.parsers.pdf_parser - INFO - parse:53 - Parsing PDF with rewritten parser: C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\Jan to march.pdf
2025-06-30 01:35:14 - bank_analyzer.parsers.pdf_parser - INFO - parse:68 - Rewritten parser extracted 316 transactions
2025-06-30 01:35:14 - bank_analyzer.parsers.parser_factory - INFO - parse_file:158 - Successfully parsed 316 transactions from C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\Jan to march.pdf
2025-06-30 01:35:14 - bank_analyzer.ui.main_window - INFO - run:66 - Parsed 316 transactions from C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\Jan to march.pdf
2025-06-30 01:35:14 - bank_analyzer.parsers.parser_factory - INFO - get_parser:89 - Selected pdf parser for C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\june to august.pdf
2025-06-30 01:35:14 - bank_analyzer.parsers.pdf_parser - INFO - parse:53 - Parsing PDF with rewritten parser: C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\june to august.pdf
2025-06-30 01:35:15 - bank_analyzer.parsers.pdf_parser - INFO - parse:68 - Rewritten parser extracted 109 transactions
2025-06-30 01:35:15 - bank_analyzer.parsers.parser_factory - INFO - parse_file:158 - Successfully parsed 109 transactions from C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\june to august.pdf
2025-06-30 01:35:15 - bank_analyzer.ui.main_window - INFO - run:66 - Parsed 109 transactions from C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\june to august.pdf
2025-06-30 01:35:15 - bank_analyzer.parsers.parser_factory - INFO - get_parser:89 - Selected pdf parser for C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\March to june.pdf
2025-06-30 01:35:15 - bank_analyzer.parsers.pdf_parser - INFO - parse:53 - Parsing PDF with rewritten parser: C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\March to june.pdf
2025-06-30 01:35:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.454 seconds
2025-06-30 01:35:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.517 seconds
2025-06-30 01:35:19 - bank_analyzer.parsers.pdf_parser - INFO - parse:68 - Rewritten parser extracted 281 transactions
2025-06-30 01:35:19 - bank_analyzer.parsers.parser_factory - INFO - parse_file:158 - Successfully parsed 281 transactions from C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\March to june.pdf
2025-06-30 01:35:19 - bank_analyzer.ui.main_window - INFO - run:66 - Parsed 281 transactions from C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\March to june.pdf
2025-06-30 01:35:19 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 01:35:19 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.111 seconds
2025-06-30 01:35:19 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.108 seconds
2025-06-30 01:35:20 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.106 seconds
2025-06-30 01:35:20 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.108 seconds
2025-06-30 01:35:20 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.109 seconds
2025-06-30 01:35:20 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.107 seconds
2025-06-30 01:35:20 - bank_analyzer.core.transaction_data_manager - INFO - create_session:187 - Created transaction session: txn_session_20250630_013520_8d4a20e2 (706 transactions)
2025-06-30 01:35:20 - bank_analyzer.ui.main_window - INFO - on_processing_completed:1061 - Created session: txn_session_20250630_013520_8d4a20e2
2025-06-30 01:35:25 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.155 seconds
2025-06-30 01:35:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.113 seconds
2025-06-30 01:35:34 - bank_analyzer.core.transaction_data_manager - INFO - __init__:121 - Transaction Data Manager initialized
2025-06-30 01:35:34 - bank_analyzer.ui.ml_labeling_window - INFO - __init__:1002 - ML Labeling Window initialized with performance optimizations
2025-06-30 01:35:34 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:241 - Loaded 60 categories from main application
2025-06-30 01:35:34 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:274 - Loaded 106 ML-specific categories
2025-06-30 01:35:34 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 01:35:34 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:35:34 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:35:34 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:35:34 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:35:34 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:35:34 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:35:34 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:35:34 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:35:34 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:35:34 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:35:34 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:35:34 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:35:34 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:35:34 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:35:34 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:35:34 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:35:34 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:35:34 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:35:34 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:35:34 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:35:34 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:35:34 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:35:34 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:35:34 - bank_analyzer.ml.batch_processor - INFO - __init__:87 - Batch processor initialized (batch size: 20-30)
2025-06-30 01:35:34 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:241 - Loaded 60 categories from main application
2025-06-30 01:35:34 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:274 - Loaded 106 ML-specific categories
2025-06-30 01:35:34 - bank_analyzer.ml.dynamic_category_manager - INFO - _load_existing_categories:137 - Loaded 32 main categories
2025-06-30 01:35:34 - bank_analyzer.ml.dynamic_category_manager - INFO - __init__:118 - Dynamic category manager initialized
2025-06-30 01:35:34 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:35:34 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:35:34 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:35:34 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 01:35:34 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:35:34 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:35:34 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:35:34 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:35:34 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:35:34 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:35:34 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 01:35:34 - bank_analyzer.ml.budget_guardian - INFO - __init__:107 - Budget guardian initialized ($5.0 total, $1.0 daily)
2025-06-30 01:35:34 - bank_analyzer.ml.enhanced_sambanova_categorizer - INFO - __init__:109 - Enhanced SambaNova categorizer initialized
2025-06-30 01:35:34 - bank_analyzer.ml.hybrid_ml_categorizer - INFO - __init__:115 - Hybrid ML categorizer initialized in hybrid mode
2025-06-30 01:35:34 - bank_analyzer.core.transaction_data_manager - INFO - start_clean_session:700 - Started clean session
2025-06-30 01:35:34 - bank_analyzer.ui.ml_labeling_window - INFO - on_selection_changed:2194 - Selection changed: 0 transactions selected
2025-06-30 01:35:34 - bank_analyzer.ui.ml_labeling_window - INFO - on_selection_changed:2195 - Selected hash_ids: []...
2025-06-30 01:35:34 - bank_analyzer.ui.enhanced_transaction_table - INFO - populate_table:221 - No valid transactions to display
2025-06-30 01:35:34 - bank_analyzer.ui.ml_labeling_window - INFO - _show_clean_start_message:1051 - Clean start message displayed
2025-06-30 01:35:34 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:35:35 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.119 seconds
2025-06-30 01:35:35 - bank_analyzer.ui.background_workers - INFO - run:137 - Filtering completed in 0.120 seconds, found 50 transactions
2025-06-30 01:35:35 - bank_analyzer.ui.ml_labeling_window - INFO - on_filter_completed:2151 - Skipping filter results display - no active session or data
2025-06-30 01:35:35 - bank_analyzer.ui.ml_labeling_window - INFO - on_selection_changed:2194 - Selection changed: 0 transactions selected
2025-06-30 01:35:35 - bank_analyzer.ui.ml_labeling_window - INFO - on_selection_changed:2195 - Selected hash_ids: []...
2025-06-30 01:35:35 - bank_analyzer.ui.enhanced_transaction_table - INFO - populate_table:221 - No valid transactions to display
2025-06-30 01:35:35 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.142 seconds
2025-06-30 01:35:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.138 seconds
2025-06-30 01:35:45 - bank_analyzer.ui.ml_labeling_window - INFO - load_latest_session_directly:3630 - No reasonable sessions found, using most recent session
2025-06-30 01:35:45 - bank_analyzer.ml.data_preparation - INFO - extract_unique_transactions:363 - Extracted 405 unique transaction descriptions from 706 transactions
2025-06-30 01:35:45 - bank_analyzer.ui.ml_labeling_window - INFO - _force_clear_all_caches:4224 - Cache clearing no longer needed - caching system removed for reliability
2025-06-30 01:35:45 - bank_analyzer.ui.ml_labeling_window - INFO - _force_clear_all_caches:4238 - All caches cleared successfully
2025-06-30 01:35:45 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.115 seconds
2025-06-30 01:35:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.113 seconds
2025-06-30 01:35:47 - bank_analyzer.ui.ml_labeling_window - INFO - load_latest_session_directly:3685 - Loaded 405 transactions directly from latest session: txn_session_20250630_013520_8d4a20e2
2025-06-30 01:35:55 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.105 seconds
2025-06-30 01:35:55 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.115 seconds
2025-06-30 01:36:05 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.108 seconds
2025-06-30 01:36:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.116 seconds
2025-06-30 01:36:15 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.144 seconds
2025-06-30 01:36:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.112 seconds
2025-06-30 01:36:25 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.157 seconds
2025-06-30 01:36:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.110 seconds
2025-06-30 01:36:27 - bank_analyzer.ml.session_training_manager - INFO - start_session_training:90 - Started training session: training_20250630_013627 for source: txn_session_20250630_013520_8d4a20e2
2025-06-30 01:36:27 - bank_analyzer.ui.ml_labeling_window - INFO - start_session_based_training:1842 - Started session-based training: training_20250630_013627
2025-06-30 01:36:27 - bank_analyzer.ui.ml_labeling_window - INFO - _load_session_data_directly:3417 - _load_session_data_directly called with 706 raw transactions from session txn_session_20250630_013520_8d4a20e2
2025-06-30 01:36:27 - bank_analyzer.ui.ml_labeling_window - INFO - _load_session_data_directly:3420 - Sample transaction: CREDIT INTEREST... Date: 2024-03-31
2025-06-30 01:36:27 - bank_analyzer.ml.data_preparation - INFO - extract_unique_transactions:363 - Extracted 405 unique transaction descriptions from 706 transactions
2025-06-30 01:36:27 - bank_analyzer.ui.ml_labeling_window - INFO - _load_session_data_directly:3430 - Extracted 405 unique transactions
2025-06-30 01:36:27 - bank_analyzer.ui.ml_labeling_window - INFO - _load_session_data_directly:3433 - Sample unique transaction: CREDIT INTEREST... Frequency: 2
2025-06-30 01:36:27 - bank_analyzer.ui.ml_labeling_window - INFO - _force_clear_all_caches:4224 - Cache clearing no longer needed - caching system removed for reliability
2025-06-30 01:36:27 - bank_analyzer.ui.ml_labeling_window - INFO - _force_clear_all_caches:4238 - All caches cleared successfully
2025-06-30 01:36:35 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.170 seconds
2025-06-30 01:36:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.108 seconds
2025-06-30 01:36:37 - bank_analyzer.ui.ml_labeling_window - INFO - _load_session_data_directly:3473 - Loaded 405 transactions directly from session: txn_session_20250630_013520_8d4a20e2
2025-06-30 01:36:45 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.113 seconds
2025-06-30 01:36:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.107 seconds
2025-06-30 01:36:55 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.152 seconds
2025-06-30 01:36:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.108 seconds
2025-06-30 01:37:05 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.177 seconds
2025-06-30 01:37:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.114 seconds
2025-06-30 01:37:15 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.107 seconds
2025-06-30 01:37:15 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.109 seconds
2025-06-30 01:37:25 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.128 seconds
2025-06-30 01:37:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.135 seconds
2025-06-30 01:37:35 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.137 seconds
2025-06-30 01:37:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.120 seconds
2025-06-30 01:37:45 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.178 seconds
2025-06-30 01:37:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.121 seconds
2025-06-30 01:37:55 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.156 seconds
2025-06-30 01:37:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.131 seconds
2025-06-30 01:38:05 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.117 seconds
2025-06-30 01:38:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.114 seconds
2025-06-30 01:38:15 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.134 seconds
2025-06-30 01:38:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.118 seconds
2025-06-30 01:38:25 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.183 seconds
2025-06-30 01:38:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.168 seconds
2025-06-30 01:38:35 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.145 seconds
2025-06-30 01:38:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.147 seconds
2025-06-30 01:38:45 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.141 seconds
2025-06-30 01:38:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.129 seconds
2025-06-30 01:38:55 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.148 seconds
2025-06-30 01:38:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.117 seconds
2025-06-30 01:39:05 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.143 seconds
2025-06-30 01:39:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.132 seconds
2025-06-30 01:39:15 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.144 seconds
2025-06-30 01:39:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.150 seconds
2025-06-30 01:39:25 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.184 seconds
2025-06-30 01:39:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.121 seconds
2025-06-30 01:39:35 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.139 seconds
2025-06-30 01:39:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.119 seconds
2025-06-30 01:39:45 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.171 seconds
2025-06-30 01:39:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.137 seconds
2025-06-30 01:39:55 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.136 seconds
2025-06-30 01:39:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.125 seconds
2025-06-30 01:40:05 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.219 seconds
2025-06-30 01:40:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.208 seconds
2025-06-30 01:40:15 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.168 seconds
2025-06-30 01:40:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.185 seconds
2025-06-30 01:40:25 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.209 seconds
2025-06-30 01:40:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.176 seconds
2025-06-30 01:40:27 - bank_analyzer.core.logger - INFO - setup_logging:72 - Logging system initialized
2025-06-30 01:40:27 - bank_analyzer.core.logger - INFO - setup_logging:73 - Log file: logs\bank_analyzer_20250630.log
2025-06-30 01:40:27 - bank_analyzer.core.logger - INFO - setup_logging:74 - Log level: INFO
2025-06-30 01:40:27 - __main__ - INFO - main:47 - Starting Bank Statement Analyzer application
2025-06-30 01:40:27 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 01:40:27 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 01:40:27 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:40:27 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:40:27 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:40:28 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:40:28 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:40:28 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:40:28 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:40:28 - bank_analyzer.ml.ai_categorization_coordinator - INFO - _load_config:117 - Loaded AI coordinator config from bank_analyzer_config\ai_coordinator_config.json
2025-06-30 01:40:28 - bank_analyzer.ml.enhanced_merchant_mapper - INFO - _load_patterns:497 - Loaded 24 merchant patterns
2025-06-30 01:40:28 - bank_analyzer.ml.enhanced_merchant_mapper - INFO - _create_automatic_backup:689 - Created automatic backup: bank_analyzer_config\merchant_cache\backups\patterns_backup_20250630_014028.json
2025-06-30 01:40:28 - bank_analyzer.ml.enhanced_merchant_mapper - INFO - __init__:158 - Enhanced merchant mapper initialized with 24 patterns
2025-06-30 01:40:28 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:40:28 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:40:28 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:40:28 - bank_analyzer.ml.transaction_context_analyzer - INFO - __init__:131 - Transaction Context Analyzer initialized
2025-06-30 01:40:28 - bank_analyzer.ml.smart_transaction_router - INFO - _load_config:109 - Loaded router config from bank_analyzer_config\router_config.json
2025-06-30 01:40:28 - bank_analyzer.ml.smart_transaction_router - INFO - __init__:80 - Router initialized: cache_threshold=0.6, max_ai_batch=100
2025-06-30 01:40:28 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:40:28 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:40:28 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:40:28 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:40:28 - bank_analyzer.ml.ai_categorization_coordinator - INFO - __init__:91 - AI Coordinator initialized with budget: $2.0
2025-06-30 01:40:28 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 01:40:28 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:40:28 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:40:28 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:40:28 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:40:28 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:40:28 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:40:28 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:40:28 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:40:28 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:40:28 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:40:28 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:40:28 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:40:28 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:40:28 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:40:28 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:40:28 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:40:28 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:40:28 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:40:28 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:40:28 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:40:28 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:40:28 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:40:28 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:40:28 - bank_analyzer.ml.sambanova_cost_tracker - INFO - __init__:83 - Cost tracker initialized (total: $0.0000)
2025-06-30 01:40:28 - bank_analyzer.ml.batch_processor - INFO - __init__:87 - Batch processor initialized (batch size: 20-30)
2025-06-30 01:40:28 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:241 - Loaded 60 categories from main application
2025-06-30 01:40:28 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:274 - Loaded 106 ML-specific categories
2025-06-30 01:40:28 - bank_analyzer.ml.dynamic_category_manager - INFO - _load_existing_categories:137 - Loaded 32 main categories
2025-06-30 01:40:28 - bank_analyzer.ml.dynamic_category_manager - INFO - __init__:118 - Dynamic category manager initialized
2025-06-30 01:40:28 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:40:28 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:40:28 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:40:28 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 01:40:28 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:40:28 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:40:28 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:40:28 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:40:28 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:40:28 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:40:28 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 01:40:28 - bank_analyzer.ml.budget_guardian - INFO - __init__:107 - Budget guardian initialized ($5.0 total, $1.0 daily)
2025-06-30 01:40:28 - bank_analyzer.ml.enhanced_sambanova_categorizer - INFO - __init__:109 - Enhanced SambaNova categorizer initialized
2025-06-30 01:40:28 - bank_analyzer.ml.hybrid_ml_categorizer - INFO - __init__:115 - Hybrid ML categorizer initialized in hybrid mode
2025-06-30 01:40:28 - bank_analyzer.ml.integrated_categorizer - INFO - __init__:46 - Enhanced integrated categorizer with AI coordinator initialized
2025-06-30 01:40:28 - bank_analyzer.core.transaction_data_manager - INFO - __init__:121 - Transaction Data Manager initialized
2025-06-30 01:40:28 - bank_analyzer.ui.main_window - INFO - load_initial_data:741 - Initial data loaded successfully
2025-06-30 01:40:28 - bank_analyzer.ui.main_window - INFO - __init__:237 - Bank Analyzer main window initialized
2025-06-30 01:40:28 - __main__ - INFO - main:76 - Bank Statement Analyzer application started successfully
2025-06-30 01:40:29 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.190 seconds
2025-06-30 01:40:29 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.117 seconds
2025-06-30 01:40:29 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.118 seconds
2025-06-30 01:40:29 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.133 seconds
2025-06-30 01:40:30 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.182 seconds
2025-06-30 01:40:30 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.114 seconds
2025-06-30 01:40:30 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.109 seconds
2025-06-30 01:40:30 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.120 seconds
2025-06-30 01:40:35 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.157 seconds
2025-06-30 01:40:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.140 seconds
2025-06-30 01:40:38 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.147 seconds
2025-06-30 01:40:38 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.129 seconds
2025-06-30 01:40:45 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.185 seconds
2025-06-30 01:40:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.128 seconds
2025-06-30 01:40:48 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.122 seconds
2025-06-30 01:40:48 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.112 seconds
2025-06-30 01:40:55 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.157 seconds
2025-06-30 01:40:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.131 seconds
2025-06-30 01:40:56 - bank_analyzer.ui.session_management_dialog - INFO - load_sessions:314 - Loaded 4 sessions
2025-06-30 01:40:58 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.150 seconds
2025-06-30 01:40:58 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.151 seconds
2025-06-30 01:41:04 - bank_analyzer.core.transaction_data_manager - INFO - load_session:334 - Loaded session: txn_session_20250630_013520_8d4a20e2
2025-06-30 01:41:04 - bank_analyzer.ml.session_training_manager - INFO - get_all_trained_hash_ids:231 - Collected 201 trained transaction hash IDs from 2 sessions
2025-06-30 01:41:04 - bank_analyzer.core.transaction_data_manager - INFO - load_session_with_duplicate_detection:444 - Loaded session txn_session_20250630_013520_8d4a20e2 with duplicate detection: 706 new, 0 duplicates
2025-06-30 01:41:04 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.117 seconds
2025-06-30 01:41:04 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.116 seconds
2025-06-30 01:41:04 - bank_analyzer.ui.main_window - INFO - on_session_loaded_from_dialog:2024 - Loaded session from dialog: txn_session_20250630_013520_8d4a20e2 (706 transactions)
2025-06-30 01:41:05 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.124 seconds
2025-06-30 01:41:05 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.118 seconds
2025-06-30 01:41:05 - bank_analyzer.ui.main_window - INFO - on_session_loaded_from_dialog:2024 - Loaded session from dialog: txn_session_20250630_013520_8d4a20e2 (706 transactions)
2025-06-30 01:41:05 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.139 seconds
2025-06-30 01:41:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.142 seconds
2025-06-30 01:41:08 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.126 seconds
2025-06-30 01:41:08 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.139 seconds
2025-06-30 01:41:15 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.144 seconds
2025-06-30 01:41:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.145 seconds
2025-06-30 01:41:18 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.172 seconds
2025-06-30 01:41:18 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.114 seconds
2025-06-30 01:41:25 - bank_analyzer.core.transaction_data_manager - INFO - __init__:121 - Transaction Data Manager initialized
2025-06-30 01:41:25 - bank_analyzer.ui.ml_labeling_window - INFO - __init__:1002 - ML Labeling Window initialized with performance optimizations
2025-06-30 01:41:25 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:241 - Loaded 60 categories from main application
2025-06-30 01:41:25 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:274 - Loaded 106 ML-specific categories
2025-06-30 01:41:25 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 01:41:25 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:41:25 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:41:25 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:41:25 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:41:25 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:41:25 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:41:25 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:41:25 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.204 seconds
2025-06-30 01:41:25 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:41:25 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:41:26 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:41:26 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:41:26 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:41:26 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:41:26 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:41:26 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:41:26 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:41:26 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:41:26 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:41:26 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:41:26 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:41:26 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:41:26 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:41:26 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:41:26 - bank_analyzer.ml.batch_processor - INFO - __init__:87 - Batch processor initialized (batch size: 20-30)
2025-06-30 01:41:26 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:241 - Loaded 60 categories from main application
2025-06-30 01:41:26 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:274 - Loaded 106 ML-specific categories
2025-06-30 01:41:26 - bank_analyzer.ml.dynamic_category_manager - INFO - _load_existing_categories:137 - Loaded 32 main categories
2025-06-30 01:41:26 - bank_analyzer.ml.dynamic_category_manager - INFO - __init__:118 - Dynamic category manager initialized
2025-06-30 01:41:26 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:41:26 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:41:26 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:41:26 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 01:41:26 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:41:26 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:41:26 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:41:26 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:41:26 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:41:26 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:41:26 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 01:41:26 - bank_analyzer.ml.budget_guardian - INFO - __init__:107 - Budget guardian initialized ($5.0 total, $1.0 daily)
2025-06-30 01:41:26 - bank_analyzer.ml.enhanced_sambanova_categorizer - INFO - __init__:109 - Enhanced SambaNova categorizer initialized
2025-06-30 01:41:26 - bank_analyzer.ml.hybrid_ml_categorizer - INFO - __init__:115 - Hybrid ML categorizer initialized in hybrid mode
2025-06-30 01:41:26 - bank_analyzer.core.transaction_data_manager - INFO - start_clean_session:700 - Started clean session
2025-06-30 01:41:26 - bank_analyzer.ui.ml_labeling_window - INFO - on_selection_changed:2194 - Selection changed: 0 transactions selected
2025-06-30 01:41:26 - bank_analyzer.ui.ml_labeling_window - INFO - on_selection_changed:2195 - Selected hash_ids: []...
2025-06-30 01:41:26 - bank_analyzer.ui.enhanced_transaction_table - INFO - populate_table:221 - No valid transactions to display
2025-06-30 01:41:26 - bank_analyzer.ui.ml_labeling_window - INFO - _show_clean_start_message:1051 - Clean start message displayed
2025-06-30 01:41:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.196 seconds
2025-06-30 01:41:26 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:41:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.153 seconds
2025-06-30 01:41:26 - bank_analyzer.ui.background_workers - INFO - run:137 - Filtering completed in 0.153 seconds, found 50 transactions
2025-06-30 01:41:26 - bank_analyzer.ui.ml_labeling_window - INFO - on_filter_completed:2151 - Skipping filter results display - no active session or data
2025-06-30 01:41:26 - bank_analyzer.ui.ml_labeling_window - INFO - on_selection_changed:2194 - Selection changed: 0 transactions selected
2025-06-30 01:41:26 - bank_analyzer.ui.ml_labeling_window - INFO - on_selection_changed:2195 - Selected hash_ids: []...
2025-06-30 01:41:26 - bank_analyzer.ui.enhanced_transaction_table - INFO - populate_table:221 - No valid transactions to display
2025-06-30 01:41:28 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.110 seconds
2025-06-30 01:41:28 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.119 seconds
2025-06-30 01:41:31 - bank_analyzer.ui.session_management_dialog - INFO - load_sessions:314 - Loaded 4 sessions
2025-06-30 01:41:35 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.152 seconds
2025-06-30 01:41:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.121 seconds
2025-06-30 01:41:38 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.129 seconds
2025-06-30 01:41:38 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.116 seconds
2025-06-30 01:41:41 - bank_analyzer.ui.ml_labeling_window - INFO - load_latest_session_directly:3630 - No reasonable sessions found, using most recent session
2025-06-30 01:41:41 - bank_analyzer.ml.data_preparation - INFO - extract_unique_transactions:363 - Extracted 405 unique transaction descriptions from 706 transactions
2025-06-30 01:41:41 - bank_analyzer.ui.ml_labeling_window - INFO - _force_clear_all_caches:4224 - Cache clearing no longer needed - caching system removed for reliability
2025-06-30 01:41:41 - bank_analyzer.ui.ml_labeling_window - INFO - _force_clear_all_caches:4238 - All caches cleared successfully
2025-06-30 01:41:43 - bank_analyzer.ui.ml_labeling_window - INFO - load_latest_session_directly:3685 - Loaded 405 transactions directly from latest session: txn_session_20250630_013520_8d4a20e2
2025-06-30 01:41:45 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.147 seconds
2025-06-30 01:41:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.123 seconds
2025-06-30 01:41:48 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.114 seconds
2025-06-30 01:41:48 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.114 seconds
2025-06-30 01:41:51 - __main__ - INFO - main:80 - Bank Statement Analyzer application exited with code: 0
2025-06-30 01:41:55 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.205 seconds
2025-06-30 01:41:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.127 seconds
2025-06-30 01:42:05 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.175 seconds
2025-06-30 01:42:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.134 seconds
2025-06-30 01:42:15 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.187 seconds
2025-06-30 01:42:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.134 seconds
2025-06-30 01:42:25 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.180 seconds
2025-06-30 01:42:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.162 seconds
2025-06-30 01:42:35 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.190 seconds
2025-06-30 01:42:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.123 seconds
2025-06-30 01:42:45 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.115 seconds
2025-06-30 01:42:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.128 seconds
2025-06-30 01:42:55 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.163 seconds
2025-06-30 01:42:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.128 seconds
2025-06-30 01:43:05 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.178 seconds
2025-06-30 01:43:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.116 seconds
2025-06-30 01:43:15 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.124 seconds
2025-06-30 01:43:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.116 seconds
2025-06-30 01:43:22 - bank_analyzer.ui.session_management_dialog - INFO - load_sessions:314 - Loaded 4 sessions
2025-06-30 01:43:25 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.183 seconds
2025-06-30 01:43:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.114 seconds
2025-06-30 01:43:35 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.127 seconds
2025-06-30 01:43:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.131 seconds
2025-06-30 01:43:45 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.161 seconds
2025-06-30 01:43:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.114 seconds
2025-06-30 01:43:55 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.171 seconds
2025-06-30 01:43:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.161 seconds
2025-06-30 01:44:05 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.177 seconds
2025-06-30 01:44:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.111 seconds
2025-06-30 01:44:15 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.189 seconds
2025-06-30 01:44:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.108 seconds
2025-06-30 01:44:20 - __main__ - INFO - main:80 - Bank Statement Analyzer application exited with code: 0
2025-06-30 02:15:34 - bank_analyzer.core.logger - INFO - setup_logging:72 - Logging system initialized
2025-06-30 02:15:34 - bank_analyzer.core.logger - INFO - setup_logging:73 - Log file: logs\bank_analyzer_20250630.log
2025-06-30 02:15:34 - bank_analyzer.core.logger - INFO - setup_logging:74 - Log level: INFO
2025-06-30 02:15:34 - __main__ - INFO - main:47 - Starting Bank Statement Analyzer application
2025-06-30 02:15:34 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 02:15:34 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 02:15:34 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 02:15:34 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 02:15:34 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 02:15:35 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 02:15:35 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 02:15:35 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 02:15:35 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 02:15:35 - bank_analyzer.ml.ai_categorization_coordinator - INFO - _load_config:117 - Loaded AI coordinator config from bank_analyzer_config\ai_coordinator_config.json
2025-06-30 02:15:35 - bank_analyzer.ml.enhanced_merchant_mapper - INFO - _load_patterns:497 - Loaded 24 merchant patterns
2025-06-30 02:15:35 - bank_analyzer.ml.enhanced_merchant_mapper - INFO - _create_automatic_backup:689 - Created automatic backup: bank_analyzer_config\merchant_cache\backups\patterns_backup_20250630_021535.json
2025-06-30 02:15:35 - bank_analyzer.ml.enhanced_merchant_mapper - INFO - __init__:158 - Enhanced merchant mapper initialized with 24 patterns
2025-06-30 02:15:35 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 02:15:35 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 02:15:35 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 02:15:35 - bank_analyzer.ml.transaction_context_analyzer - INFO - __init__:131 - Transaction Context Analyzer initialized
2025-06-30 02:15:35 - bank_analyzer.ml.smart_transaction_router - INFO - _load_config:109 - Loaded router config from bank_analyzer_config\router_config.json
2025-06-30 02:15:35 - bank_analyzer.ml.smart_transaction_router - INFO - __init__:80 - Router initialized: cache_threshold=0.6, max_ai_batch=100
2025-06-30 02:15:35 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 02:15:35 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 02:15:35 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 02:15:35 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 02:15:35 - bank_analyzer.ml.ai_categorization_coordinator - INFO - __init__:91 - AI Coordinator initialized with budget: $2.0
2025-06-30 02:15:35 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 02:15:35 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 02:15:35 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 02:15:35 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 02:15:35 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 02:15:35 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 02:15:35 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 02:15:35 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 02:15:35 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 02:15:35 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 02:15:35 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 02:15:35 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 02:15:35 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 02:15:35 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 02:15:35 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 02:15:35 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 02:15:35 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 02:15:35 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 02:15:35 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 02:15:35 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 02:15:35 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 02:15:35 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 02:15:35 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 02:15:35 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 02:15:35 - bank_analyzer.ml.sambanova_cost_tracker - INFO - __init__:83 - Cost tracker initialized (total: $0.0000)
2025-06-30 02:15:35 - bank_analyzer.ml.batch_processor - INFO - __init__:87 - Batch processor initialized (batch size: 20-30)
2025-06-30 02:15:35 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:241 - Loaded 60 categories from main application
2025-06-30 02:15:35 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:274 - Loaded 106 ML-specific categories
2025-06-30 02:15:35 - bank_analyzer.ml.dynamic_category_manager - INFO - _load_existing_categories:137 - Loaded 32 main categories
2025-06-30 02:15:35 - bank_analyzer.ml.dynamic_category_manager - INFO - __init__:118 - Dynamic category manager initialized
2025-06-30 02:15:35 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 02:15:35 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 02:15:35 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 02:15:35 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 02:15:35 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 02:15:35 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 02:15:35 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 02:15:35 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 02:15:35 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 02:15:35 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 02:15:35 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 02:15:35 - bank_analyzer.ml.budget_guardian - INFO - __init__:107 - Budget guardian initialized ($5.0 total, $1.0 daily)
2025-06-30 02:15:35 - bank_analyzer.ml.enhanced_sambanova_categorizer - INFO - __init__:109 - Enhanced SambaNova categorizer initialized
2025-06-30 02:15:35 - bank_analyzer.ml.hybrid_ml_categorizer - INFO - __init__:115 - Hybrid ML categorizer initialized in hybrid mode
2025-06-30 02:15:35 - bank_analyzer.ml.integrated_categorizer - INFO - __init__:46 - Enhanced integrated categorizer with AI coordinator initialized
2025-06-30 02:15:35 - bank_analyzer.core.transaction_data_manager - INFO - __init__:105 - Transaction Data Manager initialized
2025-06-30 02:15:35 - bank_analyzer.ui.main_window - INFO - load_initial_data:741 - Initial data loaded successfully
2025-06-30 02:15:35 - bank_analyzer.ui.main_window - INFO - __init__:237 - Bank Analyzer main window initialized
2025-06-30 02:15:36 - __main__ - INFO - main:76 - Bank Statement Analyzer application started successfully
2025-06-30 02:15:37 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.246 seconds
2025-06-30 02:15:37 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.139 seconds
2025-06-30 02:15:37 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.106 seconds
2025-06-30 02:15:37 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.120 seconds
2025-06-30 02:15:38 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.110 seconds
2025-06-30 02:15:38 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.104 seconds
2025-06-30 02:15:38 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.101 seconds
2025-06-30 02:15:38 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.102 seconds
2025-06-30 02:15:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.447 seconds
2025-06-30 02:15:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.109 seconds
2025-06-30 02:15:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.503 seconds
2025-06-30 02:15:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.112 seconds
2025-06-30 02:16:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.280 seconds
2025-06-30 02:16:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.160 seconds
2025-06-30 02:16:10 - bank_analyzer.ui.session_management_dialog - INFO - load_sessions:281 - Loaded 4 sessions
2025-06-30 02:16:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.478 seconds
2025-06-30 02:16:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.395 seconds
2025-06-30 02:16:18 - bank_analyzer.core.transaction_data_manager - INFO - load_session:318 - Loaded session: txn_session_20250630_013520_8d4a20e2
2025-06-30 02:16:19 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.102 seconds
2025-06-30 02:16:19 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.103 seconds
2025-06-30 02:16:19 - bank_analyzer.ui.main_window - INFO - on_session_loaded_from_dialog:2024 - Loaded session from dialog: txn_session_20250630_013520_8d4a20e2 (706 transactions)
2025-06-30 02:16:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.606 seconds
2025-06-30 02:16:27 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.593 seconds
2025-06-30 02:16:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.517 seconds
2025-06-30 02:16:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.165 seconds
2025-06-30 02:16:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.609 seconds
2025-06-30 02:16:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.154 seconds
2025-06-30 02:16:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.603 seconds
2025-06-30 02:16:57 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.599 seconds
2025-06-30 02:17:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.485 seconds
2025-06-30 02:17:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.386 seconds
2025-06-30 02:17:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.604 seconds
2025-06-30 02:17:17 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.463 seconds
2025-06-30 02:17:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.604 seconds
2025-06-30 02:17:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.223 seconds
2025-06-30 02:17:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.299 seconds
2025-06-30 02:17:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.114 seconds
2025-06-30 02:17:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.801 seconds
2025-06-30 02:17:47 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.585 seconds
2025-06-30 02:17:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.799 seconds
2025-06-30 02:17:57 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.839 seconds
2025-06-30 02:18:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.111 seconds
2025-06-30 02:18:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.106 seconds
2025-06-30 02:18:08 - bank_analyzer.core.transaction_data_manager - INFO - __init__:105 - Transaction Data Manager initialized
2025-06-30 02:18:08 - bank_analyzer.ui.ml_labeling_window - INFO - __init__:1005 - ML Labeling Window initialized with performance optimizations
2025-06-30 02:18:08 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:241 - Loaded 60 categories from main application
2025-06-30 02:18:08 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:274 - Loaded 106 ML-specific categories
2025-06-30 02:18:08 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 02:18:08 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 02:18:08 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 02:18:08 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 02:18:08 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 02:18:08 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 02:18:08 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 02:18:08 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 02:18:08 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 02:18:09 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 02:18:09 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 02:18:09 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 02:18:09 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 02:18:09 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 02:18:09 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 02:18:09 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 02:18:09 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 02:18:09 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 02:18:09 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 02:18:09 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 02:18:09 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 02:18:09 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 02:18:09 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 02:18:09 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 02:18:09 - bank_analyzer.ml.batch_processor - INFO - __init__:87 - Batch processor initialized (batch size: 20-30)
2025-06-30 02:18:09 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:241 - Loaded 60 categories from main application
2025-06-30 02:18:09 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:274 - Loaded 106 ML-specific categories
2025-06-30 02:18:09 - bank_analyzer.ml.dynamic_category_manager - INFO - _load_existing_categories:137 - Loaded 32 main categories
2025-06-30 02:18:09 - bank_analyzer.ml.dynamic_category_manager - INFO - __init__:118 - Dynamic category manager initialized
2025-06-30 02:18:09 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 02:18:09 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 02:18:09 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 02:18:09 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 02:18:09 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 02:18:09 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 02:18:09 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 02:18:09 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 02:18:09 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 02:18:09 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 02:18:09 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 02:18:09 - bank_analyzer.ml.budget_guardian - INFO - __init__:107 - Budget guardian initialized ($5.0 total, $1.0 daily)
2025-06-30 02:18:09 - bank_analyzer.ml.enhanced_sambanova_categorizer - INFO - __init__:109 - Enhanced SambaNova categorizer initialized
2025-06-30 02:18:09 - bank_analyzer.ml.hybrid_ml_categorizer - INFO - __init__:115 - Hybrid ML categorizer initialized in hybrid mode
2025-06-30 02:18:09 - bank_analyzer.core.transaction_data_manager - INFO - start_clean_session:589 - Started clean session
2025-06-30 02:18:09 - bank_analyzer.ui.ml_labeling_window - INFO - on_selection_changed:2197 - Selection changed: 0 transactions selected
2025-06-30 02:18:09 - bank_analyzer.ui.ml_labeling_window - INFO - on_selection_changed:2198 - Selected hash_ids: []...
2025-06-30 02:18:09 - bank_analyzer.ui.enhanced_transaction_table - INFO - populate_table:221 - No valid transactions to display
2025-06-30 02:18:09 - bank_analyzer.ui.ml_labeling_window - INFO - _show_clean_start_message:1054 - Clean start message displayed
2025-06-30 02:18:09 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 02:18:09 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.104 seconds
2025-06-30 02:18:09 - bank_analyzer.ui.background_workers - INFO - run:137 - Filtering completed in 0.104 seconds, found 50 transactions
2025-06-30 02:18:09 - bank_analyzer.ui.ml_labeling_window - INFO - on_filter_completed:2154 - Skipping filter results display - no active session or data
2025-06-30 02:18:09 - bank_analyzer.ui.ml_labeling_window - INFO - on_selection_changed:2197 - Selection changed: 0 transactions selected
2025-06-30 02:18:09 - bank_analyzer.ui.ml_labeling_window - INFO - on_selection_changed:2198 - Selected hash_ids: []...
2025-06-30 02:18:09 - bank_analyzer.ui.enhanced_transaction_table - INFO - populate_table:221 - No valid transactions to display
2025-06-30 02:18:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.510 seconds
2025-06-30 02:18:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.386 seconds
2025-06-30 02:18:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.561 seconds
2025-06-30 02:18:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.111 seconds
2025-06-30 02:18:31 - bank_analyzer.ui.session_management_dialog - INFO - load_sessions:281 - Loaded 4 sessions
2025-06-30 02:18:34 - bank_analyzer.core.transaction_data_manager - INFO - load_session:318 - Loaded session: txn_session_20250630_013520_8d4a20e2
2025-06-30 02:18:34 - bank_analyzer.ui.ml_labeling_window - INFO - load_session_data:3253 - Loading session data with three-way categorization: txn_session_20250630_013520_8d4a20e2
2025-06-30 02:18:34 - bank_analyzer.ml.transaction_categorization_service - INFO - _load_existing_transactions:193 - Loaded 83 existing transactions
2025-06-30 02:18:34 - bank_analyzer.ml.transaction_categorization_service - INFO - _load_labeled_hash_ids:222 - Loaded 202 labeled transactions from history
2025-06-30 02:18:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.117 seconds
2025-06-30 02:18:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.128 seconds
2025-06-30 02:18:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.606 seconds
2025-06-30 02:18:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.210 seconds
2025-06-30 02:18:54 - bank_analyzer.ui.ml_labeling_window - INFO - _load_categorized_transactions:3308 - Added 706 new transactions for training
2025-06-30 02:18:54 - bank_analyzer.ui.ml_labeling_window - INFO - enable_auto_reload:4666 - Auto-reload re-enabled
2025-06-30 02:18:55 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.297 seconds
2025-06-30 02:18:55 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.169 seconds
2025-06-30 02:18:55 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.098 seconds
2025-06-30 02:18:55 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.099 seconds
2025-06-30 02:18:55 - bank_analyzer.ui.ml_labeling_window - INFO - _load_categorized_transactions:3341 - Loaded categorized session: txn_session_20250630_013520_8d4a20e2 (706 trainable, 0 reference transactions)
2025-06-30 02:18:55 - bank_analyzer.ui.ml_labeling_window - INFO - load_session_data:3272 - User accepted categorization results
2025-06-30 02:18:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.206 seconds
2025-06-30 02:18:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.139 seconds
2025-06-30 02:19:04 - bank_analyzer.ui.session_management_dialog - INFO - load_sessions:281 - Loaded 4 sessions
2025-06-30 02:19:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.395 seconds
2025-06-30 02:19:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.418 seconds
2025-06-30 02:19:08 - bank_analyzer.core.transaction_data_manager - INFO - load_session:318 - Loaded session: txn_session_20250630_013520_8d4a20e2
2025-06-30 02:19:08 - bank_analyzer.ui.ml_labeling_window - INFO - load_session_data:3253 - Loading session data with three-way categorization: txn_session_20250630_013520_8d4a20e2
2025-06-30 02:19:08 - bank_analyzer.ml.transaction_categorization_service - INFO - _load_existing_transactions:193 - Loaded 83 existing transactions
2025-06-30 02:19:08 - bank_analyzer.ml.transaction_categorization_service - INFO - _load_labeled_hash_ids:222 - Loaded 202 labeled transactions from history
2025-06-30 02:19:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.218 seconds
2025-06-30 02:19:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.128 seconds
2025-06-30 02:19:20 - bank_analyzer.ui.ml_labeling_window - INFO - load_session_data:3274 - User cancelled categorization
2025-06-30 02:19:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.120 seconds
2025-06-30 02:19:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.117 seconds
2025-06-30 02:19:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.213 seconds
2025-06-30 02:19:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.172 seconds
2025-06-30 02:19:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.538 seconds
2025-06-30 02:19:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.212 seconds
2025-06-30 02:19:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.769 seconds
2025-06-30 02:19:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.202 seconds
2025-06-30 02:20:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.507 seconds
2025-06-30 02:20:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.349 seconds
2025-06-30 02:20:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.455 seconds
2025-06-30 02:20:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.352 seconds
2025-06-30 02:20:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.326 seconds
2025-06-30 02:20:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.302 seconds
2025-06-30 02:20:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.187 seconds
2025-06-30 02:20:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.120 seconds
2025-06-30 02:20:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.844 seconds
2025-06-30 02:20:47 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.293 seconds
2025-06-30 02:20:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.876 seconds
2025-06-30 02:20:57 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.266 seconds
2025-06-30 02:21:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.816 seconds
2025-06-30 02:21:07 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.485 seconds
2025-06-30 02:21:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.854 seconds
2025-06-30 02:21:17 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.827 seconds
2025-06-30 02:21:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.828 seconds
2025-06-30 02:21:27 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.448 seconds
2025-06-30 02:21:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.393 seconds
2025-06-30 02:21:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.349 seconds
2025-06-30 02:21:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.666 seconds
2025-06-30 02:21:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.328 seconds
2025-06-30 02:21:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.640 seconds
2025-06-30 02:21:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.395 seconds
2025-06-30 02:22:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.400 seconds
2025-06-30 02:22:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.332 seconds
2025-06-30 02:22:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.697 seconds
2025-06-30 02:22:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.109 seconds
2025-06-30 02:22:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.819 seconds
2025-06-30 02:22:27 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.257 seconds
2025-06-30 02:22:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.248 seconds
2025-06-30 02:22:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.110 seconds
2025-06-30 02:22:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.668 seconds
2025-06-30 02:22:47 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.586 seconds
2025-06-30 02:22:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.312 seconds
2025-06-30 02:22:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.115 seconds
2025-06-30 02:23:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.148 seconds
2025-06-30 02:23:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.127 seconds
2025-06-30 02:23:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.503 seconds
2025-06-30 02:23:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.173 seconds
2025-06-30 02:23:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.287 seconds
2025-06-30 02:23:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.140 seconds
2025-06-30 02:23:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.261 seconds
2025-06-30 02:23:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.115 seconds
2025-06-30 02:23:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.308 seconds
2025-06-30 02:23:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.139 seconds
2025-06-30 02:23:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.511 seconds
2025-06-30 02:23:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.268 seconds
2025-06-30 02:24:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.141 seconds
2025-06-30 02:24:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.148 seconds
2025-06-30 02:24:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.136 seconds
2025-06-30 02:24:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.138 seconds
2025-06-30 02:24:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.276 seconds
2025-06-30 02:24:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.111 seconds
2025-06-30 02:24:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.282 seconds
2025-06-30 02:24:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.133 seconds
2025-06-30 02:24:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.660 seconds
2025-06-30 02:24:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.118 seconds
2025-06-30 02:24:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.857 seconds
2025-06-30 02:24:57 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.257 seconds
2025-06-30 02:25:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.783 seconds
2025-06-30 02:25:07 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.486 seconds
2025-06-30 02:25:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.835 seconds
2025-06-30 02:25:17 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.823 seconds
2025-06-30 02:25:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.429 seconds
2025-06-30 02:25:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.120 seconds
2025-06-30 02:25:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.136 seconds
2025-06-30 02:25:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.116 seconds
2025-06-30 02:25:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.302 seconds
2025-06-30 02:25:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.203 seconds
2025-06-30 02:25:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.676 seconds
2025-06-30 02:25:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.105 seconds
2025-06-30 02:26:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.348 seconds
2025-06-30 02:26:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.139 seconds
2025-06-30 02:26:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.466 seconds
2025-06-30 02:26:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.182 seconds
2025-06-30 02:26:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.470 seconds
2025-06-30 02:26:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.105 seconds
2025-06-30 02:26:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.362 seconds
2025-06-30 02:26:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.146 seconds
2025-06-30 02:26:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.519 seconds
2025-06-30 02:26:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.354 seconds
2025-06-30 02:26:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.225 seconds
2025-06-30 02:26:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.102 seconds
2025-06-30 02:27:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.591 seconds
2025-06-30 02:27:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.133 seconds
2025-06-30 02:27:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.304 seconds
2025-06-30 02:27:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.116 seconds
2025-06-30 02:27:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.615 seconds
2025-06-30 02:27:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.106 seconds
2025-06-30 02:27:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.777 seconds
2025-06-30 02:27:37 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.714 seconds
2025-06-30 02:27:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.740 seconds
2025-06-30 02:27:47 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.507 seconds
2025-06-30 02:27:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.396 seconds
2025-06-30 02:27:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.112 seconds
2025-06-30 02:28:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.665 seconds
2025-06-30 02:28:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.323 seconds
2025-06-30 02:28:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.211 seconds
2025-06-30 02:28:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.141 seconds
2025-06-30 02:28:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.519 seconds
2025-06-30 02:28:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.346 seconds
2025-06-30 02:28:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.446 seconds
2025-06-30 02:28:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.411 seconds
2025-06-30 02:28:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.439 seconds
2025-06-30 02:28:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.324 seconds
2025-06-30 02:28:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.467 seconds
2025-06-30 02:28:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.346 seconds
2025-06-30 02:29:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.648 seconds
2025-06-30 02:29:07 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.498 seconds
2025-06-30 02:29:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.370 seconds
2025-06-30 02:29:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.219 seconds
2025-06-30 02:29:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.634 seconds
2025-06-30 02:29:27 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.488 seconds
2025-06-30 02:29:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.502 seconds
2025-06-30 02:29:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.208 seconds
2025-06-30 02:29:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.631 seconds
2025-06-30 02:29:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.216 seconds
2025-06-30 02:29:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.885 seconds
2025-06-30 02:29:57 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.871 seconds
2025-06-30 02:30:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.589 seconds
2025-06-30 02:30:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.148 seconds
2025-06-30 02:30:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.191 seconds
2025-06-30 02:30:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.146 seconds
2025-06-30 02:30:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.536 seconds
2025-06-30 02:30:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.382 seconds
2025-06-30 02:30:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.703 seconds
2025-06-30 02:30:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.214 seconds
2025-06-30 02:30:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.709 seconds
2025-06-30 02:30:47 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.385 seconds
2025-06-30 02:30:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.728 seconds
2025-06-30 02:30:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.277 seconds
2025-06-30 02:31:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.284 seconds
2025-06-30 02:31:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.291 seconds
2025-06-30 02:31:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.767 seconds
2025-06-30 02:31:17 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.570 seconds
2025-06-30 02:31:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.435 seconds
2025-06-30 02:31:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.132 seconds
2025-06-30 02:31:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.874 seconds
2025-06-30 02:31:37 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.384 seconds
2025-06-30 02:31:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.872 seconds
2025-06-30 02:31:47 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.716 seconds
2025-06-30 02:31:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.392 seconds
2025-06-30 02:31:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.229 seconds
2025-06-30 02:32:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.783 seconds
2025-06-30 02:32:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.117 seconds
2025-06-30 02:32:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.841 seconds
2025-06-30 02:32:17 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.252 seconds
2025-06-30 02:32:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.621 seconds
2025-06-30 02:32:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.346 seconds
2025-06-30 02:32:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.432 seconds
2025-06-30 02:32:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.232 seconds
2025-06-30 02:32:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.236 seconds
2025-06-30 02:32:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.167 seconds
2025-06-30 02:32:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.427 seconds
2025-06-30 02:32:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.342 seconds
2025-06-30 02:33:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.153 seconds
2025-06-30 02:33:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.121 seconds
2025-06-30 02:33:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.605 seconds
2025-06-30 02:33:17 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.571 seconds
2025-06-30 02:33:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.160 seconds
2025-06-30 02:33:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.133 seconds
2025-06-30 02:33:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 1.019 seconds
2025-06-30 02:33:38 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 1.329 seconds
2025-06-30 02:33:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.250 seconds
2025-06-30 02:33:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.121 seconds
2025-06-30 02:33:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.391 seconds
2025-06-30 02:33:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.113 seconds
2025-06-30 02:34:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.165 seconds
2025-06-30 02:34:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.118 seconds
2025-06-30 02:34:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.255 seconds
2025-06-30 02:34:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.265 seconds
2025-06-30 02:34:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.618 seconds
2025-06-30 02:34:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.352 seconds
2025-06-30 02:34:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.266 seconds
2025-06-30 02:34:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.112 seconds
2025-06-30 02:34:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.805 seconds
2025-06-30 02:34:47 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.796 seconds
2025-06-30 02:34:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.766 seconds
2025-06-30 02:34:57 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.798 seconds
2025-06-30 02:35:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.749 seconds
2025-06-30 02:35:07 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.733 seconds
2025-06-30 02:35:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.764 seconds
2025-06-30 02:35:17 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.744 seconds
2025-06-30 02:35:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.748 seconds
2025-06-30 02:35:27 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.480 seconds
2025-06-30 02:35:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.782 seconds
2025-06-30 02:35:37 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.780 seconds
2025-06-30 02:35:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.504 seconds
2025-06-30 02:35:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.345 seconds
2025-06-30 02:35:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.764 seconds
2025-06-30 02:35:57 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.754 seconds
2025-06-30 02:36:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.404 seconds
2025-06-30 02:36:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.106 seconds
2025-06-30 02:36:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.349 seconds
2025-06-30 02:36:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.107 seconds
2025-06-30 02:36:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.778 seconds
2025-06-30 02:36:27 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.767 seconds
2025-06-30 02:36:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.296 seconds
2025-06-30 02:36:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.140 seconds
2025-06-30 02:36:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.195 seconds
2025-06-30 02:36:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.117 seconds
2025-06-30 02:36:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.298 seconds
2025-06-30 02:36:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.104 seconds
2025-06-30 02:37:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.752 seconds
2025-06-30 02:37:07 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.746 seconds
2025-06-30 02:37:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.765 seconds
2025-06-30 02:37:17 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.778 seconds
2025-06-30 02:37:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.616 seconds
2025-06-30 02:37:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.117 seconds
2025-06-30 02:37:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.793 seconds
2025-06-30 02:37:37 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.774 seconds
2025-06-30 02:37:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.686 seconds
2025-06-30 02:37:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.105 seconds
2025-06-30 02:37:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.492 seconds
2025-06-30 02:37:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.129 seconds
2025-06-30 02:38:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.769 seconds
2025-06-30 02:38:07 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.761 seconds
2025-06-30 02:38:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.252 seconds
2025-06-30 02:38:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.112 seconds
2025-06-30 02:38:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.797 seconds
2025-06-30 02:38:27 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.478 seconds
2025-06-30 02:38:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.639 seconds
2025-06-30 02:38:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.102 seconds
2025-06-30 02:38:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.791 seconds
2025-06-30 02:38:47 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.797 seconds
2025-06-30 02:38:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.796 seconds
2025-06-30 02:38:57 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.763 seconds
2025-06-30 02:39:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.502 seconds
2025-06-30 02:39:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.124 seconds
2025-06-30 02:39:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.766 seconds
2025-06-30 02:39:17 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.750 seconds
2025-06-30 02:39:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.777 seconds
2025-06-30 02:39:27 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.765 seconds
2025-06-30 02:39:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.784 seconds
2025-06-30 02:39:37 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.780 seconds
2025-06-30 02:39:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.480 seconds
2025-06-30 02:39:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.136 seconds
2025-06-30 02:39:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.372 seconds
2025-06-30 02:39:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.143 seconds
2025-06-30 02:40:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.772 seconds
2025-06-30 02:40:07 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.749 seconds
2025-06-30 02:40:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.765 seconds
2025-06-30 02:40:17 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.450 seconds
2025-06-30 02:40:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.780 seconds
2025-06-30 02:40:27 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.776 seconds
2025-06-30 02:40:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.784 seconds
2025-06-30 02:40:37 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.628 seconds
2025-06-30 02:40:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.718 seconds
2025-06-30 02:40:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.293 seconds
2025-06-30 02:40:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.785 seconds
2025-06-30 02:40:57 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.401 seconds
2025-06-30 02:41:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.781 seconds
2025-06-30 02:41:07 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.766 seconds
2025-06-30 02:41:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.608 seconds
2025-06-30 02:41:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.314 seconds
2025-06-30 02:41:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.779 seconds
2025-06-30 02:41:27 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.774 seconds
2025-06-30 02:41:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.604 seconds
2025-06-30 02:41:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.369 seconds
2025-06-30 02:41:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.704 seconds
2025-06-30 02:41:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.183 seconds
2025-06-30 02:41:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.787 seconds
2025-06-30 02:41:57 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.760 seconds
2025-06-30 02:42:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.606 seconds
2025-06-30 02:42:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.117 seconds
2025-06-30 02:42:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.755 seconds
2025-06-30 02:42:17 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.748 seconds
2025-06-30 02:42:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.785 seconds
2025-06-30 02:42:27 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.786 seconds
2025-06-30 02:42:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.790 seconds
2025-06-30 02:42:37 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.782 seconds
2025-06-30 02:42:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.786 seconds
2025-06-30 02:42:47 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.753 seconds
2025-06-30 02:42:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.825 seconds
2025-06-30 02:42:57 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.762 seconds
2025-06-30 02:43:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.761 seconds
2025-06-30 02:43:07 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.755 seconds
2025-06-30 02:43:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.492 seconds
2025-06-30 02:43:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.128 seconds
2025-06-30 02:43:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.775 seconds
2025-06-30 02:43:27 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.761 seconds
2025-06-30 02:43:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.777 seconds
2025-06-30 02:43:37 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.798 seconds
2025-06-30 02:43:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.785 seconds
2025-06-30 02:43:47 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.786 seconds
2025-06-30 02:43:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.761 seconds
2025-06-30 02:43:57 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.754 seconds
2025-06-30 02:44:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.758 seconds
2025-06-30 02:44:07 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.783 seconds
2025-06-30 02:44:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.770 seconds
2025-06-30 02:44:17 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.602 seconds
2025-06-30 02:44:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.357 seconds
2025-06-30 02:44:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.131 seconds
2025-06-30 02:44:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.777 seconds
2025-06-30 02:44:37 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.785 seconds
2025-06-30 02:44:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.760 seconds
2025-06-30 02:44:47 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.760 seconds
2025-06-30 02:44:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.307 seconds
2025-06-30 02:44:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.149 seconds
2025-06-30 02:45:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.758 seconds
2025-06-30 02:45:07 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.753 seconds
2025-06-30 02:45:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.772 seconds
2025-06-30 02:45:17 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.766 seconds
2025-06-30 02:45:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.788 seconds
2025-06-30 02:45:27 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.658 seconds
2025-06-30 02:45:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.645 seconds
2025-06-30 02:45:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.379 seconds
2025-06-30 02:45:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.739 seconds
2025-06-30 02:45:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.223 seconds
2025-06-30 02:45:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.556 seconds
2025-06-30 02:45:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.240 seconds
2025-06-30 02:46:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.719 seconds
2025-06-30 02:46:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.156 seconds
2025-06-30 02:46:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.687 seconds
2025-06-30 02:46:17 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.590 seconds
2025-06-30 02:46:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.213 seconds
2025-06-30 02:46:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.120 seconds
2025-06-30 02:46:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.522 seconds
2025-06-30 02:46:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.178 seconds
2025-06-30 02:46:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.593 seconds
2025-06-30 02:46:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.416 seconds
2025-06-30 02:46:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.578 seconds
2025-06-30 02:46:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.171 seconds
2025-06-30 02:47:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.375 seconds
2025-06-30 02:47:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.230 seconds
2025-06-30 02:47:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.401 seconds
2025-06-30 02:47:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.118 seconds
2025-06-30 02:47:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.119 seconds
2025-06-30 02:47:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.115 seconds
2025-06-30 02:47:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.116 seconds
2025-06-30 02:47:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.119 seconds
2025-06-30 02:47:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.364 seconds
2025-06-30 02:47:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.224 seconds
2025-06-30 02:47:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.543 seconds
2025-06-30 02:47:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.182 seconds
2025-06-30 02:48:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.659 seconds
2025-06-30 02:48:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.219 seconds
2025-06-30 02:48:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.448 seconds
2025-06-30 02:48:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.312 seconds
2025-06-30 02:48:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.274 seconds
2025-06-30 02:48:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.178 seconds
2025-06-30 02:48:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.794 seconds
2025-06-30 02:48:38 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 1.673 seconds
2025-06-30 02:48:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.258 seconds
2025-06-30 02:48:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.106 seconds
2025-06-30 02:48:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.459 seconds
2025-06-30 02:48:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.388 seconds
2025-06-30 02:49:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.683 seconds
2025-06-30 02:49:07 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.550 seconds
2025-06-30 02:49:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.370 seconds
2025-06-30 02:49:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.253 seconds
2025-06-30 02:49:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.680 seconds
2025-06-30 02:49:27 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.588 seconds
2025-06-30 02:49:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.143 seconds
2025-06-30 02:49:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.142 seconds
2025-06-30 02:49:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.868 seconds
2025-06-30 02:49:47 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.567 seconds
2025-06-30 02:49:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.131 seconds
2025-06-30 02:49:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.140 seconds
2025-06-30 02:50:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.729 seconds
2025-06-30 02:50:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.115 seconds
2025-06-30 02:50:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.688 seconds
2025-06-30 02:50:17 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.445 seconds
2025-06-30 02:50:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.360 seconds
2025-06-30 02:50:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.165 seconds
2025-06-30 02:50:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.681 seconds
2025-06-30 02:50:37 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.589 seconds
2025-06-30 02:50:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.667 seconds
2025-06-30 02:50:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.134 seconds
2025-06-30 02:50:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.163 seconds
2025-06-30 02:50:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.129 seconds
2025-06-30 02:51:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.605 seconds
2025-06-30 02:51:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.296 seconds
2025-06-30 02:51:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.666 seconds
2025-06-30 02:51:17 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.578 seconds
2025-06-30 02:51:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.380 seconds
2025-06-30 02:51:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.131 seconds
2025-06-30 02:51:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.499 seconds
2025-06-30 02:51:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.356 seconds
2025-06-30 02:51:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.720 seconds
2025-06-30 02:51:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.258 seconds
2025-06-30 02:51:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.111 seconds
2025-06-30 02:51:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.112 seconds
2025-06-30 02:52:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.649 seconds
2025-06-30 02:52:07 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.439 seconds
2025-06-30 02:52:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.473 seconds
2025-06-30 02:52:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.359 seconds
2025-06-30 02:52:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.411 seconds
2025-06-30 02:52:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.119 seconds
2025-06-30 02:52:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.744 seconds
2025-06-30 02:52:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.124 seconds
2025-06-30 02:52:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.361 seconds
2025-06-30 02:52:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.238 seconds
2025-06-30 02:52:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.720 seconds
2025-06-30 02:52:57 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.551 seconds
2025-06-30 02:53:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.605 seconds
2025-06-30 02:53:07 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.561 seconds
2025-06-30 02:53:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.199 seconds
2025-06-30 02:53:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.172 seconds
2025-06-30 02:53:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.122 seconds
2025-06-30 02:53:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.127 seconds
2025-06-30 02:53:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.379 seconds
2025-06-30 02:53:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.120 seconds
2025-06-30 02:53:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.263 seconds
2025-06-30 02:53:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.150 seconds
2025-06-30 02:53:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.504 seconds
2025-06-30 02:53:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.108 seconds
2025-06-30 02:54:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.517 seconds
2025-06-30 02:54:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.110 seconds
2025-06-30 02:54:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.389 seconds
2025-06-30 02:54:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.128 seconds
2025-06-30 02:54:25 - __main__ - INFO - main:80 - Bank Statement Analyzer application exited with code: 0
