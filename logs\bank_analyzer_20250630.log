2025-06-30 00:02:39 - bank_analyzer.core.logger - INFO - setup_logging:72 - Logging system initialized
2025-06-30 00:02:39 - bank_analyzer.core.logger - INFO - setup_logging:73 - Log file: logs\bank_analyzer_20250630.log
2025-06-30 00:02:39 - bank_analyzer.core.logger - INFO - setup_logging:74 - Log level: INFO
2025-06-30 00:02:39 - __main__ - INFO - main:47 - Starting Bank Statement Analyzer application
2025-06-30 00:02:39 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 00:02:39 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 00:02:39 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:02:39 - bank_analyzer.ml.sambanova_client - INFO - _check_daily_reset:127 - Daily cost tracking reset
2025-06-30 00:02:39 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:02:39 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 00:02:39 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 00:02:39 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:02:39 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 00:02:39 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:02:39 - bank_analyzer.ml.ai_categorization_coordinator - INFO - _load_config:117 - Loaded AI coordinator config from bank_analyzer_config\ai_coordinator_config.json
2025-06-30 00:02:39 - bank_analyzer.ml.enhanced_merchant_mapper - INFO - _load_patterns:497 - Loaded 24 merchant patterns
2025-06-30 00:02:39 - bank_analyzer.ml.enhanced_merchant_mapper - INFO - _create_automatic_backup:689 - Created automatic backup: bank_analyzer_config\merchant_cache\backups\patterns_backup_20250630_000239.json
2025-06-30 00:02:39 - bank_analyzer.ml.enhanced_merchant_mapper - INFO - __init__:158 - Enhanced merchant mapper initialized with 24 patterns
2025-06-30 00:02:39 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 00:02:39 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 00:02:39 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 00:02:39 - bank_analyzer.ml.transaction_context_analyzer - INFO - __init__:131 - Transaction Context Analyzer initialized
2025-06-30 00:02:39 - bank_analyzer.ml.smart_transaction_router - INFO - _load_config:109 - Loaded router config from bank_analyzer_config\router_config.json
2025-06-30 00:02:39 - bank_analyzer.ml.smart_transaction_router - INFO - __init__:80 - Router initialized: cache_threshold=0.6, max_ai_batch=100
2025-06-30 00:02:39 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:02:39 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:02:39 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 00:02:39 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:02:39 - bank_analyzer.ml.ai_categorization_coordinator - INFO - __init__:91 - AI Coordinator initialized with budget: $2.0
2025-06-30 00:02:39 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 00:02:39 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:02:39 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:02:39 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 00:02:39 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 00:02:39 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:02:39 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 00:02:39 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:02:39 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 00:02:39 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:02:39 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 00:02:39 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:02:39 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:02:39 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:02:39 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 00:02:39 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 00:02:39 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 00:02:39 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 00:02:39 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:02:39 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:02:39 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 00:02:39 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 00:02:39 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 00:02:39 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 00:02:39 - bank_analyzer.ml.sambanova_cost_tracker - INFO - __init__:83 - Cost tracker initialized (total: $0.0000)
2025-06-30 00:02:39 - bank_analyzer.ml.batch_processor - INFO - __init__:87 - Batch processor initialized (batch size: 20-30)
2025-06-30 00:02:39 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:241 - Loaded 60 categories from main application
2025-06-30 00:02:39 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:274 - Loaded 106 ML-specific categories
2025-06-30 00:02:39 - bank_analyzer.ml.dynamic_category_manager - INFO - _load_existing_categories:137 - Loaded 32 main categories
2025-06-30 00:02:39 - bank_analyzer.ml.dynamic_category_manager - INFO - __init__:118 - Dynamic category manager initialized
2025-06-30 00:02:39 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 00:02:39 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 00:02:39 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 00:02:39 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 00:02:39 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 00:02:39 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 00:02:39 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 00:02:39 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 00:02:39 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 00:02:39 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 00:02:39 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 00:02:39 - bank_analyzer.ml.budget_guardian - INFO - __init__:107 - Budget guardian initialized ($5.0 total, $1.0 daily)
2025-06-30 00:02:39 - bank_analyzer.ml.enhanced_sambanova_categorizer - INFO - __init__:109 - Enhanced SambaNova categorizer initialized
2025-06-30 00:02:39 - bank_analyzer.ml.hybrid_ml_categorizer - INFO - __init__:115 - Hybrid ML categorizer initialized in hybrid mode
2025-06-30 00:02:39 - bank_analyzer.ml.integrated_categorizer - INFO - __init__:46 - Enhanced integrated categorizer with AI coordinator initialized
2025-06-30 00:02:39 - bank_analyzer.core.transaction_data_manager - INFO - __init__:100 - Transaction Data Manager initialized
2025-06-30 00:02:39 - bank_analyzer.ui.main_window - INFO - load_initial_data:741 - Initial data loaded successfully
2025-06-30 00:02:39 - bank_analyzer.ui.main_window - INFO - __init__:237 - Bank Analyzer main window initialized
2025-06-30 00:02:40 - __main__ - INFO - main:76 - Bank Statement Analyzer application started successfully
2025-06-30 00:02:41 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:521 - Loaded 83 unique transactions from storage in 0.189 seconds
2025-06-30 00:02:41 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:521 - Loaded 83 unique transactions from storage in 0.129 seconds
2025-06-30 00:22:31 - bank_analyzer.core.transaction_data_manager - INFO - __init__:100 - Transaction Data Manager initialized
2025-06-30 00:22:31 - bank_analyzer.ui.ml_labeling_window - INFO - __init__:1002 - ML Labeling Window initialized with performance optimizations
2025-06-30 00:22:32 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:241 - Loaded 60 categories from main application
2025-06-30 00:22:32 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:274 - Loaded 106 ML-specific categories
2025-06-30 00:22:32 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 00:22:32 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:22:32 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:22:32 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 00:22:32 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 00:22:32 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:22:32 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 00:22:32 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:22:32 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 00:22:32 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:22:32 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 00:22:32 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:22:32 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:22:32 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:22:32 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 00:22:32 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 00:22:32 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 00:22:32 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 00:22:32 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:22:32 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:22:32 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 00:22:32 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 00:22:32 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 00:22:32 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 00:22:32 - bank_analyzer.ml.batch_processor - INFO - __init__:87 - Batch processor initialized (batch size: 20-30)
2025-06-30 00:22:32 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:241 - Loaded 60 categories from main application
2025-06-30 00:22:32 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:274 - Loaded 106 ML-specific categories
2025-06-30 00:22:32 - bank_analyzer.ml.dynamic_category_manager - INFO - _load_existing_categories:137 - Loaded 32 main categories
2025-06-30 00:22:32 - bank_analyzer.ml.dynamic_category_manager - INFO - __init__:118 - Dynamic category manager initialized
2025-06-30 00:22:32 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 00:22:32 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 00:22:32 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 00:22:32 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 00:22:32 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 00:22:32 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 00:22:32 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 00:22:32 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 00:22:32 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 00:22:32 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 00:22:32 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 00:22:32 - bank_analyzer.ml.budget_guardian - INFO - __init__:107 - Budget guardian initialized ($5.0 total, $1.0 daily)
2025-06-30 00:22:32 - bank_analyzer.ml.enhanced_sambanova_categorizer - INFO - __init__:109 - Enhanced SambaNova categorizer initialized
2025-06-30 00:22:32 - bank_analyzer.ml.hybrid_ml_categorizer - INFO - __init__:115 - Hybrid ML categorizer initialized in hybrid mode
2025-06-30 00:22:32 - bank_analyzer.core.transaction_data_manager - INFO - start_clean_session:584 - Started clean session
2025-06-30 00:22:32 - bank_analyzer.ui.ml_labeling_window - INFO - on_selection_changed:2194 - Selection changed: 0 transactions selected
2025-06-30 00:22:32 - bank_analyzer.ui.ml_labeling_window - INFO - on_selection_changed:2195 - Selected hash_ids: []...
2025-06-30 00:22:32 - bank_analyzer.ui.enhanced_transaction_table - INFO - populate_table:221 - No valid transactions to display
2025-06-30 00:22:32 - bank_analyzer.ui.ml_labeling_window - INFO - _show_clean_start_message:1051 - Clean start message displayed
2025-06-30 00:22:32 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:22:32 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:521 - Loaded 83 unique transactions from storage in 0.111 seconds
2025-06-30 00:22:32 - bank_analyzer.ui.background_workers - INFO - run:137 - Filtering completed in 0.112 seconds, found 50 transactions
2025-06-30 00:22:32 - bank_analyzer.ui.ml_labeling_window - INFO - on_filter_completed:2151 - Skipping filter results display - no active session or data
2025-06-30 00:22:32 - bank_analyzer.ui.ml_labeling_window - INFO - on_selection_changed:2194 - Selection changed: 0 transactions selected
2025-06-30 00:22:32 - bank_analyzer.ui.ml_labeling_window - INFO - on_selection_changed:2195 - Selected hash_ids: []...
2025-06-30 00:22:32 - bank_analyzer.ui.enhanced_transaction_table - INFO - populate_table:221 - No valid transactions to display
2025-06-30 00:22:36 - bank_analyzer.ui.session_management_dialog - INFO - load_sessions:281 - Loaded 6 sessions
2025-06-30 00:22:42 - bank_analyzer.core.transaction_data_manager - INFO - delete_session:536 - Deleted session: txn_session_20250629_232813_d41d8cd9
2025-06-30 00:22:42 - bank_analyzer.ui.session_management_dialog - INFO - load_sessions:281 - Loaded 5 sessions
2025-06-30 00:22:48 - bank_analyzer.core.transaction_data_manager - INFO - delete_session:536 - Deleted session: txn_session_20250629_231337_b5697f60
2025-06-30 00:22:49 - bank_analyzer.ui.session_management_dialog - INFO - load_sessions:281 - Loaded 4 sessions
2025-06-30 00:22:53 - bank_analyzer.core.transaction_data_manager - INFO - delete_session:536 - Deleted session: txn_session_20250629_231629_b5697f60
2025-06-30 00:22:54 - bank_analyzer.ui.session_management_dialog - INFO - load_sessions:281 - Loaded 3 sessions
2025-06-30 00:22:59 - bank_analyzer.core.transaction_data_manager - INFO - delete_session:536 - Deleted session: txn_session_20250629_144122_b5697f60
2025-06-30 00:22:59 - bank_analyzer.ui.session_management_dialog - INFO - load_sessions:281 - Loaded 2 sessions
2025-06-30 00:23:16 - bank_analyzer.ui.session_management_dialog - INFO - load_sessions:281 - Loaded 2 sessions
2025-06-30 00:23:19 - bank_analyzer.core.transaction_data_manager - INFO - delete_session:536 - Deleted session: txn_session_20250629_235355_50d3a86c
2025-06-30 00:23:20 - bank_analyzer.ui.session_management_dialog - INFO - load_sessions:281 - Loaded 1 sessions
2025-06-30 00:23:39 - __main__ - INFO - main:80 - Bank Statement Analyzer application exited with code: 0
2025-06-30 00:46:36 - bank_analyzer.core.logger - INFO - setup_logging:72 - Logging system initialized
2025-06-30 00:46:36 - bank_analyzer.core.logger - INFO - setup_logging:73 - Log file: logs\bank_analyzer_20250630.log
2025-06-30 00:46:36 - bank_analyzer.core.logger - INFO - setup_logging:74 - Log level: INFO
2025-06-30 00:46:36 - __main__ - INFO - main:47 - Starting Bank Statement Analyzer application
2025-06-30 00:46:36 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 00:46:36 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 00:46:36 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:46:36 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:46:36 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 00:46:37 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 00:46:37 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:46:37 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 00:46:37 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:46:37 - bank_analyzer.ml.ai_categorization_coordinator - INFO - _load_config:117 - Loaded AI coordinator config from bank_analyzer_config\ai_coordinator_config.json
2025-06-30 00:46:37 - bank_analyzer.ml.enhanced_merchant_mapper - INFO - _load_patterns:497 - Loaded 24 merchant patterns
2025-06-30 00:46:37 - bank_analyzer.ml.enhanced_merchant_mapper - INFO - _create_automatic_backup:689 - Created automatic backup: bank_analyzer_config\merchant_cache\backups\patterns_backup_20250630_004637.json
2025-06-30 00:46:37 - bank_analyzer.ml.enhanced_merchant_mapper - INFO - __init__:158 - Enhanced merchant mapper initialized with 24 patterns
2025-06-30 00:46:37 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 00:46:37 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 00:46:37 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 00:46:37 - bank_analyzer.ml.transaction_context_analyzer - INFO - __init__:131 - Transaction Context Analyzer initialized
2025-06-30 00:46:37 - bank_analyzer.ml.smart_transaction_router - INFO - _load_config:109 - Loaded router config from bank_analyzer_config\router_config.json
2025-06-30 00:46:37 - bank_analyzer.ml.smart_transaction_router - INFO - __init__:80 - Router initialized: cache_threshold=0.6, max_ai_batch=100
2025-06-30 00:46:37 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:46:37 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:46:37 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 00:46:37 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:46:37 - bank_analyzer.ml.ai_categorization_coordinator - INFO - __init__:91 - AI Coordinator initialized with budget: $2.0
2025-06-30 00:46:37 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 00:46:37 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:46:37 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:46:37 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 00:46:37 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 00:46:37 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:46:37 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 00:46:37 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:46:37 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 00:46:37 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:46:37 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 00:46:37 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:46:37 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:46:37 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:46:37 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 00:46:37 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 00:46:37 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 00:46:37 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 00:46:37 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:46:37 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:46:37 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 00:46:37 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 00:46:37 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 00:46:37 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 00:46:37 - bank_analyzer.ml.sambanova_cost_tracker - INFO - __init__:83 - Cost tracker initialized (total: $0.0000)
2025-06-30 00:46:37 - bank_analyzer.ml.batch_processor - INFO - __init__:87 - Batch processor initialized (batch size: 20-30)
2025-06-30 00:46:37 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:241 - Loaded 60 categories from main application
2025-06-30 00:46:37 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:274 - Loaded 106 ML-specific categories
2025-06-30 00:46:37 - bank_analyzer.ml.dynamic_category_manager - INFO - _load_existing_categories:137 - Loaded 32 main categories
2025-06-30 00:46:37 - bank_analyzer.ml.dynamic_category_manager - INFO - __init__:118 - Dynamic category manager initialized
2025-06-30 00:46:37 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 00:46:37 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 00:46:37 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 00:46:37 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 00:46:37 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 00:46:37 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 00:46:37 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 00:46:37 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 00:46:37 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 00:46:37 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 00:46:37 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 00:46:37 - bank_analyzer.ml.budget_guardian - INFO - __init__:107 - Budget guardian initialized ($5.0 total, $1.0 daily)
2025-06-30 00:46:37 - bank_analyzer.ml.enhanced_sambanova_categorizer - INFO - __init__:109 - Enhanced SambaNova categorizer initialized
2025-06-30 00:46:37 - bank_analyzer.ml.hybrid_ml_categorizer - INFO - __init__:115 - Hybrid ML categorizer initialized in hybrid mode
2025-06-30 00:46:37 - bank_analyzer.ml.integrated_categorizer - INFO - __init__:46 - Enhanced integrated categorizer with AI coordinator initialized
2025-06-30 00:46:37 - bank_analyzer.core.transaction_data_manager - INFO - __init__:100 - Transaction Data Manager initialized
2025-06-30 00:46:38 - bank_analyzer.ui.main_window - INFO - load_initial_data:741 - Initial data loaded successfully
2025-06-30 00:46:38 - bank_analyzer.ui.main_window - INFO - __init__:237 - Bank Analyzer main window initialized
2025-06-30 00:46:38 - __main__ - INFO - main:76 - Bank Statement Analyzer application started successfully
2025-06-30 00:46:39 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.213 seconds
2025-06-30 00:46:39 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.129 seconds
2025-06-30 00:46:39 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.270 seconds
2025-06-30 00:46:39 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.142 seconds
2025-06-30 00:46:40 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.222 seconds
2025-06-30 00:46:40 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.109 seconds
2025-06-30 00:46:40 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.124 seconds
2025-06-30 00:46:40 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.119 seconds
2025-06-30 00:46:48 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.157 seconds
2025-06-30 00:46:48 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.161 seconds
2025-06-30 00:46:55 - bank_analyzer.ui.main_window - INFO - on_bank_selection_changed:774 - Bank selection changed to: Indian Bank
2025-06-30 00:46:57 - bank_analyzer.parsers.parser_factory - INFO - get_parser:64 - Selected Indian Bank specific parser for C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\Jan to march.pdf
2025-06-30 00:46:57 - bank_analyzer.parsers.parser_factory - INFO - parse_file:154 - Successfully parsed 0 transactions from C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\Jan to march.pdf
2025-06-30 00:46:57 - bank_analyzer.ui.main_window - INFO - run:66 - Parsed 0 transactions from C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\Jan to march.pdf
2025-06-30 00:46:57 - bank_analyzer.parsers.parser_factory - INFO - get_parser:64 - Selected Indian Bank specific parser for C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\june to august.pdf
2025-06-30 00:46:57 - bank_analyzer.parsers.parser_factory - INFO - parse_file:154 - Successfully parsed 0 transactions from C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\june to august.pdf
2025-06-30 00:46:57 - bank_analyzer.ui.main_window - INFO - run:66 - Parsed 0 transactions from C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\june to august.pdf
2025-06-30 00:46:57 - bank_analyzer.parsers.parser_factory - INFO - get_parser:64 - Selected Indian Bank specific parser for C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\March to june.pdf
2025-06-30 00:46:57 - bank_analyzer.parsers.parser_factory - INFO - parse_file:154 - Successfully parsed 0 transactions from C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\March to june.pdf
2025-06-30 00:46:57 - bank_analyzer.ui.main_window - INFO - run:66 - Parsed 0 transactions from C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\March to june.pdf
2025-06-30 00:46:57 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 00:46:57 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.189 seconds
2025-06-30 00:46:57 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.141 seconds
2025-06-30 00:46:58 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.103 seconds
2025-06-30 00:46:58 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.108 seconds
2025-06-30 00:46:58 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.104 seconds
2025-06-30 00:46:58 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.107 seconds
2025-06-30 00:46:58 - bank_analyzer.core.transaction_data_manager - INFO - create_session:166 - Created transaction session: txn_session_20250630_004658_d41d8cd9 (0 transactions)
2025-06-30 00:46:58 - bank_analyzer.ui.main_window - INFO - on_processing_completed:1061 - Created session: txn_session_20250630_004658_d41d8cd9
2025-06-30 00:46:58 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.122 seconds
2025-06-30 00:46:58 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.110 seconds
2025-06-30 00:47:08 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.356 seconds
2025-06-30 00:47:08 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.136 seconds
2025-06-30 00:47:18 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.173 seconds
2025-06-30 00:47:18 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.115 seconds
2025-06-30 00:47:28 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.459 seconds
2025-06-30 00:47:28 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.120 seconds
2025-06-30 00:47:38 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.194 seconds
2025-06-30 00:47:38 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.119 seconds
2025-06-30 00:47:48 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.248 seconds
2025-06-30 00:47:48 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.180 seconds
2025-06-30 00:47:58 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.238 seconds
2025-06-30 00:47:58 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.150 seconds
2025-06-30 00:48:08 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.125 seconds
2025-06-30 00:48:08 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.116 seconds
2025-06-30 00:48:18 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.153 seconds
2025-06-30 00:48:18 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.147 seconds
2025-06-30 00:56:25 - bank_analyzer.core.logger - INFO - setup_logging:72 - Logging system initialized
2025-06-30 00:56:25 - bank_analyzer.core.logger - INFO - setup_logging:73 - Log file: logs\bank_analyzer_20250630.log
2025-06-30 00:56:25 - bank_analyzer.core.logger - INFO - setup_logging:74 - Log level: INFO
2025-06-30 00:56:25 - __main__ - INFO - main:47 - Starting Bank Statement Analyzer application
2025-06-30 00:56:25 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 00:56:25 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 00:56:25 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:56:25 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:56:25 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 00:56:25 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 00:56:25 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:56:25 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 00:56:25 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:56:25 - bank_analyzer.ml.ai_categorization_coordinator - INFO - _load_config:117 - Loaded AI coordinator config from bank_analyzer_config\ai_coordinator_config.json
2025-06-30 00:56:25 - bank_analyzer.ml.enhanced_merchant_mapper - INFO - _load_patterns:497 - Loaded 24 merchant patterns
2025-06-30 00:56:25 - bank_analyzer.ml.enhanced_merchant_mapper - INFO - _create_automatic_backup:689 - Created automatic backup: bank_analyzer_config\merchant_cache\backups\patterns_backup_20250630_005625.json
2025-06-30 00:56:25 - bank_analyzer.ml.enhanced_merchant_mapper - INFO - __init__:158 - Enhanced merchant mapper initialized with 24 patterns
2025-06-30 00:56:25 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 00:56:25 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 00:56:25 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 00:56:25 - bank_analyzer.ml.transaction_context_analyzer - INFO - __init__:131 - Transaction Context Analyzer initialized
2025-06-30 00:56:25 - bank_analyzer.ml.smart_transaction_router - INFO - _load_config:109 - Loaded router config from bank_analyzer_config\router_config.json
2025-06-30 00:56:25 - bank_analyzer.ml.smart_transaction_router - INFO - __init__:80 - Router initialized: cache_threshold=0.6, max_ai_batch=100
2025-06-30 00:56:25 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:56:25 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:56:25 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 00:56:25 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:56:25 - bank_analyzer.ml.ai_categorization_coordinator - INFO - __init__:91 - AI Coordinator initialized with budget: $2.0
2025-06-30 00:56:25 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 00:56:25 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:56:25 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:56:25 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 00:56:25 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 00:56:25 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:56:26 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 00:56:26 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:56:26 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 00:56:26 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:56:26 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 00:56:26 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:56:26 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:56:26 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:56:26 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 00:56:26 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 00:56:26 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 00:56:26 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 00:56:26 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:56:26 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 00:56:26 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 00:56:26 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 00:56:26 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 00:56:26 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 00:56:26 - bank_analyzer.ml.sambanova_cost_tracker - INFO - __init__:83 - Cost tracker initialized (total: $0.0000)
2025-06-30 00:56:26 - bank_analyzer.ml.batch_processor - INFO - __init__:87 - Batch processor initialized (batch size: 20-30)
2025-06-30 00:56:26 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:241 - Loaded 60 categories from main application
2025-06-30 00:56:26 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:274 - Loaded 106 ML-specific categories
2025-06-30 00:56:26 - bank_analyzer.ml.dynamic_category_manager - INFO - _load_existing_categories:137 - Loaded 32 main categories
2025-06-30 00:56:26 - bank_analyzer.ml.dynamic_category_manager - INFO - __init__:118 - Dynamic category manager initialized
2025-06-30 00:56:26 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 00:56:26 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 00:56:26 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 00:56:26 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 00:56:26 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 00:56:26 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 00:56:26 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 00:56:26 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 00:56:26 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 00:56:26 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 00:56:26 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 00:56:26 - bank_analyzer.ml.budget_guardian - INFO - __init__:107 - Budget guardian initialized ($5.0 total, $1.0 daily)
2025-06-30 00:56:26 - bank_analyzer.ml.enhanced_sambanova_categorizer - INFO - __init__:109 - Enhanced SambaNova categorizer initialized
2025-06-30 00:56:26 - bank_analyzer.ml.hybrid_ml_categorizer - INFO - __init__:115 - Hybrid ML categorizer initialized in hybrid mode
2025-06-30 00:56:26 - bank_analyzer.ml.integrated_categorizer - INFO - __init__:46 - Enhanced integrated categorizer with AI coordinator initialized
2025-06-30 00:56:26 - bank_analyzer.core.transaction_data_manager - INFO - __init__:100 - Transaction Data Manager initialized
2025-06-30 00:56:26 - bank_analyzer.ui.main_window - INFO - load_initial_data:741 - Initial data loaded successfully
2025-06-30 00:56:26 - bank_analyzer.ui.main_window - INFO - __init__:237 - Bank Analyzer main window initialized
2025-06-30 00:56:27 - __main__ - INFO - main:76 - Bank Statement Analyzer application started successfully
2025-06-30 00:56:27 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.126 seconds
2025-06-30 00:56:27 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.138 seconds
2025-06-30 00:56:28 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.125 seconds
2025-06-30 00:56:28 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.129 seconds
2025-06-30 00:56:28 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.218 seconds
2025-06-30 00:56:29 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.446 seconds
2025-06-30 00:56:29 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.186 seconds
2025-06-30 00:56:29 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.148 seconds
2025-06-30 00:56:30 - __main__ - INFO - main:80 - Bank Statement Analyzer application exited with code: 0
2025-06-30 01:08:58 - bank_analyzer.core.logger - INFO - setup_logging:72 - Logging system initialized
2025-06-30 01:08:58 - bank_analyzer.core.logger - INFO - setup_logging:73 - Log file: logs\bank_analyzer_20250630.log
2025-06-30 01:08:58 - bank_analyzer.core.logger - INFO - setup_logging:74 - Log level: INFO
2025-06-30 01:08:58 - __main__ - INFO - main:47 - Starting Bank Statement Analyzer application
2025-06-30 01:08:58 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 01:08:58 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 01:08:58 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:08:58 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:08:58 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:08:59 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:08:59 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:08:59 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:08:59 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:08:59 - bank_analyzer.ml.ai_categorization_coordinator - INFO - _load_config:117 - Loaded AI coordinator config from bank_analyzer_config\ai_coordinator_config.json
2025-06-30 01:08:59 - bank_analyzer.ml.enhanced_merchant_mapper - INFO - _load_patterns:497 - Loaded 24 merchant patterns
2025-06-30 01:08:59 - bank_analyzer.ml.enhanced_merchant_mapper - INFO - _create_automatic_backup:689 - Created automatic backup: bank_analyzer_config\merchant_cache\backups\patterns_backup_20250630_010859.json
2025-06-30 01:08:59 - bank_analyzer.ml.enhanced_merchant_mapper - INFO - __init__:158 - Enhanced merchant mapper initialized with 24 patterns
2025-06-30 01:08:59 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:08:59 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:08:59 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:08:59 - bank_analyzer.ml.transaction_context_analyzer - INFO - __init__:131 - Transaction Context Analyzer initialized
2025-06-30 01:08:59 - bank_analyzer.ml.smart_transaction_router - INFO - _load_config:109 - Loaded router config from bank_analyzer_config\router_config.json
2025-06-30 01:08:59 - bank_analyzer.ml.smart_transaction_router - INFO - __init__:80 - Router initialized: cache_threshold=0.6, max_ai_batch=100
2025-06-30 01:08:59 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:08:59 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:08:59 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:08:59 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:08:59 - bank_analyzer.ml.ai_categorization_coordinator - INFO - __init__:91 - AI Coordinator initialized with budget: $2.0
2025-06-30 01:08:59 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 01:08:59 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:08:59 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:08:59 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:08:59 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:08:59 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:08:59 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:08:59 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:08:59 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:08:59 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:08:59 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:08:59 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:08:59 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:08:59 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:08:59 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:08:59 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:08:59 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:08:59 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:08:59 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:08:59 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:08:59 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:08:59 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:08:59 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:08:59 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:08:59 - bank_analyzer.ml.sambanova_cost_tracker - INFO - __init__:83 - Cost tracker initialized (total: $0.0000)
2025-06-30 01:08:59 - bank_analyzer.ml.batch_processor - INFO - __init__:87 - Batch processor initialized (batch size: 20-30)
2025-06-30 01:08:59 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:241 - Loaded 60 categories from main application
2025-06-30 01:08:59 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:274 - Loaded 106 ML-specific categories
2025-06-30 01:08:59 - bank_analyzer.ml.dynamic_category_manager - INFO - _load_existing_categories:137 - Loaded 32 main categories
2025-06-30 01:08:59 - bank_analyzer.ml.dynamic_category_manager - INFO - __init__:118 - Dynamic category manager initialized
2025-06-30 01:08:59 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:08:59 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:08:59 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:08:59 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 01:08:59 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:08:59 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:08:59 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:08:59 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:08:59 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:08:59 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:08:59 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 01:08:59 - bank_analyzer.ml.budget_guardian - INFO - __init__:107 - Budget guardian initialized ($5.0 total, $1.0 daily)
2025-06-30 01:08:59 - bank_analyzer.ml.enhanced_sambanova_categorizer - INFO - __init__:109 - Enhanced SambaNova categorizer initialized
2025-06-30 01:08:59 - bank_analyzer.ml.hybrid_ml_categorizer - INFO - __init__:115 - Hybrid ML categorizer initialized in hybrid mode
2025-06-30 01:08:59 - bank_analyzer.ml.integrated_categorizer - INFO - __init__:46 - Enhanced integrated categorizer with AI coordinator initialized
2025-06-30 01:08:59 - bank_analyzer.core.transaction_data_manager - INFO - __init__:100 - Transaction Data Manager initialized
2025-06-30 01:09:00 - bank_analyzer.ui.main_window - INFO - load_initial_data:741 - Initial data loaded successfully
2025-06-30 01:09:00 - bank_analyzer.ui.main_window - INFO - __init__:237 - Bank Analyzer main window initialized
2025-06-30 01:09:00 - __main__ - INFO - main:76 - Bank Statement Analyzer application started successfully
2025-06-30 01:09:01 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.148 seconds
2025-06-30 01:09:01 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.161 seconds
2025-06-30 01:09:01 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.160 seconds
2025-06-30 01:09:01 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.169 seconds
2025-06-30 01:09:02 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.197 seconds
2025-06-30 01:09:02 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.134 seconds
2025-06-30 01:09:02 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.128 seconds
2025-06-30 01:09:02 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.127 seconds
2025-06-30 01:09:10 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.167 seconds
2025-06-30 01:09:10 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.120 seconds
2025-06-30 01:09:20 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.145 seconds
2025-06-30 01:09:20 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.115 seconds
2025-06-30 01:09:30 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.128 seconds
2025-06-30 01:09:30 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.112 seconds
2025-06-30 01:09:40 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.174 seconds
2025-06-30 01:09:40 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.112 seconds
2025-06-30 01:09:50 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.171 seconds
2025-06-30 01:09:50 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.122 seconds
2025-06-30 01:10:00 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.163 seconds
2025-06-30 01:10:00 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.111 seconds
2025-06-30 01:10:10 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.184 seconds
2025-06-30 01:10:10 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.110 seconds
2025-06-30 01:10:20 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.189 seconds
2025-06-30 01:10:20 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.141 seconds
2025-06-30 01:10:24 - bank_analyzer.parsers.parser_factory - INFO - get_parser:89 - Selected pdf parser for C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\Jan to march.pdf
2025-06-30 01:10:24 - bank_analyzer.parsers.pdf_parser - INFO - parse:53 - Parsing PDF with rewritten parser: C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\Jan to march.pdf
2025-06-30 01:10:28 - bank_analyzer.parsers.pdf_parser - INFO - parse:68 - Rewritten parser extracted 316 transactions
2025-06-30 01:10:28 - bank_analyzer.parsers.parser_factory - INFO - parse_file:158 - Successfully parsed 316 transactions from C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\Jan to march.pdf
2025-06-30 01:10:28 - bank_analyzer.ui.main_window - INFO - run:66 - Parsed 316 transactions from C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\Jan to march.pdf
2025-06-30 01:10:28 - bank_analyzer.parsers.parser_factory - INFO - get_parser:89 - Selected pdf parser for C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\june to august.pdf
2025-06-30 01:10:28 - bank_analyzer.parsers.pdf_parser - INFO - parse:53 - Parsing PDF with rewritten parser: C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\june to august.pdf
2025-06-30 01:10:30 - bank_analyzer.parsers.pdf_parser - INFO - parse:68 - Rewritten parser extracted 109 transactions
2025-06-30 01:10:30 - bank_analyzer.parsers.parser_factory - INFO - parse_file:158 - Successfully parsed 109 transactions from C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\june to august.pdf
2025-06-30 01:10:30 - bank_analyzer.ui.main_window - INFO - run:66 - Parsed 109 transactions from C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\june to august.pdf
2025-06-30 01:10:30 - bank_analyzer.parsers.parser_factory - INFO - get_parser:89 - Selected pdf parser for C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\March to june.pdf
2025-06-30 01:10:30 - bank_analyzer.parsers.pdf_parser - INFO - parse:53 - Parsing PDF with rewritten parser: C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\March to june.pdf
2025-06-30 01:10:30 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.452 seconds
2025-06-30 01:10:31 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.298 seconds
2025-06-30 01:10:34 - bank_analyzer.parsers.pdf_parser - INFO - parse:68 - Rewritten parser extracted 281 transactions
2025-06-30 01:10:34 - bank_analyzer.parsers.parser_factory - INFO - parse_file:158 - Successfully parsed 281 transactions from C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\March to june.pdf
2025-06-30 01:10:34 - bank_analyzer.ui.main_window - INFO - run:66 - Parsed 281 transactions from C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\March to june.pdf
2025-06-30 01:10:34 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 01:10:34 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.117 seconds
2025-06-30 01:10:34 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.114 seconds
2025-06-30 01:10:34 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.109 seconds
2025-06-30 01:10:35 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.121 seconds
2025-06-30 01:10:35 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.112 seconds
2025-06-30 01:10:35 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.111 seconds
2025-06-30 01:10:35 - bank_analyzer.core.transaction_data_manager - INFO - create_session:166 - Created transaction session: txn_session_20250630_011035_8d4a20e2 (706 transactions)
2025-06-30 01:10:35 - bank_analyzer.ui.main_window - INFO - on_processing_completed:1061 - Created session: txn_session_20250630_011035_8d4a20e2
2025-06-30 01:10:39 - bank_analyzer.core.transaction_data_manager - INFO - __init__:100 - Transaction Data Manager initialized
2025-06-30 01:10:39 - bank_analyzer.ui.ml_labeling_window - INFO - __init__:1002 - ML Labeling Window initialized with performance optimizations
2025-06-30 01:10:39 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:241 - Loaded 60 categories from main application
2025-06-30 01:10:39 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:274 - Loaded 106 ML-specific categories
2025-06-30 01:10:39 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 01:10:39 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:10:39 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:10:39 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:10:40 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:10:40 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:10:40 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:10:40 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:10:40 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:10:40 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:10:40 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:10:40 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:10:40 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:10:40 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:10:40 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:10:40 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:10:40 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:10:40 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:10:40 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:10:40 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:10:40 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:10:40 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:10:40 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:10:40 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:10:40 - bank_analyzer.ml.batch_processor - INFO - __init__:87 - Batch processor initialized (batch size: 20-30)
2025-06-30 01:10:40 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:241 - Loaded 60 categories from main application
2025-06-30 01:10:40 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:274 - Loaded 106 ML-specific categories
2025-06-30 01:10:40 - bank_analyzer.ml.dynamic_category_manager - INFO - _load_existing_categories:137 - Loaded 32 main categories
2025-06-30 01:10:40 - bank_analyzer.ml.dynamic_category_manager - INFO - __init__:118 - Dynamic category manager initialized
2025-06-30 01:10:40 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:10:40 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:10:40 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:10:40 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 01:10:40 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:10:40 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:10:40 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:10:40 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:10:40 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:10:40 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:10:40 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 01:10:40 - bank_analyzer.ml.budget_guardian - INFO - __init__:107 - Budget guardian initialized ($5.0 total, $1.0 daily)
2025-06-30 01:10:40 - bank_analyzer.ml.enhanced_sambanova_categorizer - INFO - __init__:109 - Enhanced SambaNova categorizer initialized
2025-06-30 01:10:40 - bank_analyzer.ml.hybrid_ml_categorizer - INFO - __init__:115 - Hybrid ML categorizer initialized in hybrid mode
2025-06-30 01:10:40 - bank_analyzer.core.transaction_data_manager - INFO - start_clean_session:584 - Started clean session
2025-06-30 01:10:40 - bank_analyzer.ui.ml_labeling_window - INFO - on_selection_changed:2194 - Selection changed: 0 transactions selected
2025-06-30 01:10:40 - bank_analyzer.ui.ml_labeling_window - INFO - on_selection_changed:2195 - Selected hash_ids: []...
2025-06-30 01:10:40 - bank_analyzer.ui.enhanced_transaction_table - INFO - populate_table:221 - No valid transactions to display
2025-06-30 01:10:40 - bank_analyzer.ui.ml_labeling_window - INFO - _show_clean_start_message:1051 - Clean start message displayed
2025-06-30 01:10:40 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.120 seconds
2025-06-30 01:10:40 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.119 seconds
2025-06-30 01:10:40 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:10:40 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.124 seconds
2025-06-30 01:10:40 - bank_analyzer.ui.background_workers - INFO - run:137 - Filtering completed in 0.125 seconds, found 50 transactions
2025-06-30 01:10:40 - bank_analyzer.ui.ml_labeling_window - INFO - on_filter_completed:2151 - Skipping filter results display - no active session or data
2025-06-30 01:10:40 - bank_analyzer.ui.ml_labeling_window - INFO - on_selection_changed:2194 - Selection changed: 0 transactions selected
2025-06-30 01:10:40 - bank_analyzer.ui.ml_labeling_window - INFO - on_selection_changed:2195 - Selected hash_ids: []...
2025-06-30 01:10:40 - bank_analyzer.ui.enhanced_transaction_table - INFO - populate_table:221 - No valid transactions to display
2025-06-30 01:10:46 - bank_analyzer.ui.ml_labeling_window - INFO - load_latest_session_directly:3630 - No reasonable sessions found, using most recent session
2025-06-30 01:10:46 - bank_analyzer.ml.data_preparation - INFO - extract_unique_transactions:363 - Extracted 405 unique transaction descriptions from 706 transactions
2025-06-30 01:10:46 - bank_analyzer.ui.ml_labeling_window - INFO - _force_clear_all_caches:4224 - Cache clearing no longer needed - caching system removed for reliability
2025-06-30 01:10:46 - bank_analyzer.ui.ml_labeling_window - INFO - _force_clear_all_caches:4238 - All caches cleared successfully
2025-06-30 01:10:48 - bank_analyzer.ui.ml_labeling_window - INFO - load_latest_session_directly:3685 - Loaded 405 transactions directly from latest session: txn_session_20250630_011035_8d4a20e2
2025-06-30 01:10:50 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.144 seconds
2025-06-30 01:10:50 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.134 seconds
2025-06-30 01:11:00 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.123 seconds
2025-06-30 01:11:00 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.113 seconds
2025-06-30 01:11:10 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.123 seconds
2025-06-30 01:11:10 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.109 seconds
2025-06-30 01:11:14 - __main__ - INFO - main:80 - Bank Statement Analyzer application exited with code: 0
2025-06-30 01:11:49 - bank_analyzer.core.logger - INFO - setup_logging:72 - Logging system initialized
2025-06-30 01:11:49 - bank_analyzer.core.logger - INFO - setup_logging:73 - Log file: logs\bank_analyzer_20250630.log
2025-06-30 01:11:49 - bank_analyzer.core.logger - INFO - setup_logging:74 - Log level: INFO
2025-06-30 01:11:49 - __main__ - INFO - main:47 - Starting Bank Statement Analyzer application
2025-06-30 01:11:49 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 01:11:49 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 01:11:49 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:49 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:49 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:11:49 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:11:49 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:49 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:11:49 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:49 - bank_analyzer.ml.ai_categorization_coordinator - INFO - _load_config:117 - Loaded AI coordinator config from bank_analyzer_config\ai_coordinator_config.json
2025-06-30 01:11:49 - bank_analyzer.ml.enhanced_merchant_mapper - INFO - _load_patterns:497 - Loaded 24 merchant patterns
2025-06-30 01:11:49 - bank_analyzer.ml.enhanced_merchant_mapper - INFO - _create_automatic_backup:689 - Created automatic backup: bank_analyzer_config\merchant_cache\backups\patterns_backup_20250630_011149.json
2025-06-30 01:11:49 - bank_analyzer.ml.enhanced_merchant_mapper - INFO - __init__:158 - Enhanced merchant mapper initialized with 24 patterns
2025-06-30 01:11:49 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:11:49 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:11:49 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:11:49 - bank_analyzer.ml.transaction_context_analyzer - INFO - __init__:131 - Transaction Context Analyzer initialized
2025-06-30 01:11:49 - bank_analyzer.ml.smart_transaction_router - INFO - _load_config:109 - Loaded router config from bank_analyzer_config\router_config.json
2025-06-30 01:11:49 - bank_analyzer.ml.smart_transaction_router - INFO - __init__:80 - Router initialized: cache_threshold=0.6, max_ai_batch=100
2025-06-30 01:11:49 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:49 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:49 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:11:49 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:49 - bank_analyzer.ml.ai_categorization_coordinator - INFO - __init__:91 - AI Coordinator initialized with budget: $2.0
2025-06-30 01:11:49 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 01:11:49 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:49 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:49 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:11:49 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:11:49 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:49 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:11:49 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:49 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:11:49 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:49 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:11:49 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:49 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:49 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:49 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:11:49 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:11:49 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:11:49 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:11:49 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:49 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:49 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:11:49 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:11:49 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:11:49 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:11:49 - bank_analyzer.ml.sambanova_cost_tracker - INFO - __init__:83 - Cost tracker initialized (total: $0.0000)
2025-06-30 01:11:49 - bank_analyzer.ml.batch_processor - INFO - __init__:87 - Batch processor initialized (batch size: 20-30)
2025-06-30 01:11:50 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:241 - Loaded 60 categories from main application
2025-06-30 01:11:50 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:274 - Loaded 106 ML-specific categories
2025-06-30 01:11:50 - bank_analyzer.ml.dynamic_category_manager - INFO - _load_existing_categories:137 - Loaded 32 main categories
2025-06-30 01:11:50 - bank_analyzer.ml.dynamic_category_manager - INFO - __init__:118 - Dynamic category manager initialized
2025-06-30 01:11:50 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:11:50 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:11:50 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:11:50 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 01:11:50 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:11:50 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:11:50 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:11:50 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:11:50 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:11:50 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:11:50 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 01:11:50 - bank_analyzer.ml.budget_guardian - INFO - __init__:107 - Budget guardian initialized ($5.0 total, $1.0 daily)
2025-06-30 01:11:50 - bank_analyzer.ml.enhanced_sambanova_categorizer - INFO - __init__:109 - Enhanced SambaNova categorizer initialized
2025-06-30 01:11:50 - bank_analyzer.ml.hybrid_ml_categorizer - INFO - __init__:115 - Hybrid ML categorizer initialized in hybrid mode
2025-06-30 01:11:50 - bank_analyzer.ml.integrated_categorizer - INFO - __init__:46 - Enhanced integrated categorizer with AI coordinator initialized
2025-06-30 01:11:50 - bank_analyzer.core.transaction_data_manager - INFO - __init__:100 - Transaction Data Manager initialized
2025-06-30 01:11:50 - bank_analyzer.ui.main_window - INFO - load_initial_data:741 - Initial data loaded successfully
2025-06-30 01:11:50 - bank_analyzer.ui.main_window - INFO - __init__:237 - Bank Analyzer main window initialized
2025-06-30 01:11:50 - __main__ - INFO - main:76 - Bank Statement Analyzer application started successfully
2025-06-30 01:11:51 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.236 seconds
2025-06-30 01:11:51 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.158 seconds
2025-06-30 01:11:51 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.112 seconds
2025-06-30 01:11:51 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.111 seconds
2025-06-30 01:11:52 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.369 seconds
2025-06-30 01:11:52 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.263 seconds
2025-06-30 01:11:52 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.194 seconds
2025-06-30 01:11:53 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.115 seconds
2025-06-30 01:11:55 - bank_analyzer.core.transaction_data_manager - INFO - __init__:100 - Transaction Data Manager initialized
2025-06-30 01:11:55 - bank_analyzer.ui.ml_labeling_window - INFO - __init__:1002 - ML Labeling Window initialized with performance optimizations
2025-06-30 01:11:55 - bank_analyzer.core.transaction_data_manager - INFO - start_clean_session:584 - Started clean session
2025-06-30 01:11:55 - bank_analyzer.ui.ml_labeling_window - ERROR - _show_empty_stats_without_loading:1736 - Error showing empty stats without loading: 'MLLabelingWindow' object has no attribute 'progress_bar'
2025-06-30 01:11:55 - bank_analyzer.ui.ml_labeling_window - INFO - _show_clean_start_message:1051 - Clean start message displayed
2025-06-30 01:11:55 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:241 - Loaded 60 categories from main application
2025-06-30 01:11:55 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:274 - Loaded 106 ML-specific categories
2025-06-30 01:11:55 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 01:11:55 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:55 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:55 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:11:55 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:11:55 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:55 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:11:55 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:55 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:11:55 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:55 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:11:55 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:55 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:55 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:55 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:11:55 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:11:55 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:11:55 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:11:55 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:55 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:55 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:11:55 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:11:55 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:11:55 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:11:55 - bank_analyzer.ml.batch_processor - INFO - __init__:87 - Batch processor initialized (batch size: 20-30)
2025-06-30 01:11:55 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:241 - Loaded 60 categories from main application
2025-06-30 01:11:55 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:274 - Loaded 106 ML-specific categories
2025-06-30 01:11:55 - bank_analyzer.ml.dynamic_category_manager - INFO - _load_existing_categories:137 - Loaded 32 main categories
2025-06-30 01:11:55 - bank_analyzer.ml.dynamic_category_manager - INFO - __init__:118 - Dynamic category manager initialized
2025-06-30 01:11:55 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:11:55 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:11:55 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:11:55 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 01:11:55 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:11:55 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:11:55 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:11:55 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:11:55 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:11:55 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:11:55 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 01:11:55 - bank_analyzer.ml.budget_guardian - INFO - __init__:107 - Budget guardian initialized ($5.0 total, $1.0 daily)
2025-06-30 01:11:55 - bank_analyzer.ml.enhanced_sambanova_categorizer - INFO - __init__:109 - Enhanced SambaNova categorizer initialized
2025-06-30 01:11:55 - bank_analyzer.ml.hybrid_ml_categorizer - INFO - __init__:115 - Hybrid ML categorizer initialized in hybrid mode
2025-06-30 01:11:56 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:11:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.108 seconds
2025-06-30 01:11:56 - bank_analyzer.ui.background_workers - INFO - run:137 - Filtering completed in 0.108 seconds, found 50 transactions
2025-06-30 01:11:56 - bank_analyzer.ui.ml_labeling_window - INFO - on_filter_completed:2151 - Skipping filter results display - no active session or data
2025-06-30 01:11:56 - bank_analyzer.ui.ml_labeling_window - INFO - on_selection_changed:2194 - Selection changed: 0 transactions selected
2025-06-30 01:11:56 - bank_analyzer.ui.ml_labeling_window - INFO - on_selection_changed:2195 - Selected hash_ids: []...
2025-06-30 01:11:56 - bank_analyzer.ui.enhanced_transaction_table - INFO - populate_table:221 - No valid transactions to display
2025-06-30 01:12:00 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.264 seconds
2025-06-30 01:12:00 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.151 seconds
2025-06-30 01:12:10 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.152 seconds
2025-06-30 01:12:10 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.107 seconds
2025-06-30 01:12:20 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.249 seconds
2025-06-30 01:12:20 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.132 seconds
2025-06-30 01:12:30 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.313 seconds
2025-06-30 01:12:30 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.125 seconds
2025-06-30 01:12:40 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.285 seconds
2025-06-30 01:12:40 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.106 seconds
2025-06-30 01:12:50 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.606 seconds
2025-06-30 01:12:51 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.313 seconds
2025-06-30 01:13:00 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.503 seconds
2025-06-30 01:13:00 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.130 seconds
2025-06-30 01:13:10 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.256 seconds
2025-06-30 01:13:10 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.177 seconds
2025-06-30 01:13:20 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.226 seconds
2025-06-30 01:13:20 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.128 seconds
2025-06-30 01:13:22 - __main__ - INFO - main:80 - Bank Statement Analyzer application exited with code: 0
2025-06-30 01:34:44 - bank_analyzer.core.logger - INFO - setup_logging:72 - Logging system initialized
2025-06-30 01:34:44 - bank_analyzer.core.logger - INFO - setup_logging:73 - Log file: logs\bank_analyzer_20250630.log
2025-06-30 01:34:44 - bank_analyzer.core.logger - INFO - setup_logging:74 - Log level: INFO
2025-06-30 01:34:44 - __main__ - INFO - main:47 - Starting Bank Statement Analyzer application
2025-06-30 01:34:44 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 01:34:44 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 01:34:44 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:34:44 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:34:44 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:34:45 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:34:45 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:34:45 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:34:45 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:34:45 - bank_analyzer.ml.ai_categorization_coordinator - INFO - _load_config:117 - Loaded AI coordinator config from bank_analyzer_config\ai_coordinator_config.json
2025-06-30 01:34:45 - bank_analyzer.ml.enhanced_merchant_mapper - INFO - _load_patterns:497 - Loaded 24 merchant patterns
2025-06-30 01:34:45 - bank_analyzer.ml.enhanced_merchant_mapper - INFO - _create_automatic_backup:689 - Created automatic backup: bank_analyzer_config\merchant_cache\backups\patterns_backup_20250630_013445.json
2025-06-30 01:34:45 - bank_analyzer.ml.enhanced_merchant_mapper - INFO - __init__:158 - Enhanced merchant mapper initialized with 24 patterns
2025-06-30 01:34:45 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:34:45 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:34:45 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:34:45 - bank_analyzer.ml.transaction_context_analyzer - INFO - __init__:131 - Transaction Context Analyzer initialized
2025-06-30 01:34:45 - bank_analyzer.ml.smart_transaction_router - INFO - _load_config:109 - Loaded router config from bank_analyzer_config\router_config.json
2025-06-30 01:34:45 - bank_analyzer.ml.smart_transaction_router - INFO - __init__:80 - Router initialized: cache_threshold=0.6, max_ai_batch=100
2025-06-30 01:34:45 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:34:45 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:34:45 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:34:45 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:34:45 - bank_analyzer.ml.ai_categorization_coordinator - INFO - __init__:91 - AI Coordinator initialized with budget: $2.0
2025-06-30 01:34:45 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 01:34:45 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:34:45 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:34:45 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:34:45 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:34:45 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:34:45 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:34:45 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:34:45 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:34:45 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:34:45 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:34:45 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:34:45 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:34:45 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:34:45 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:34:45 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:34:45 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:34:45 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:34:45 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:34:45 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:34:45 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:34:45 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:34:45 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:34:45 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:34:45 - bank_analyzer.ml.sambanova_cost_tracker - INFO - __init__:83 - Cost tracker initialized (total: $0.0000)
2025-06-30 01:34:45 - bank_analyzer.ml.batch_processor - INFO - __init__:87 - Batch processor initialized (batch size: 20-30)
2025-06-30 01:34:45 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:241 - Loaded 60 categories from main application
2025-06-30 01:34:45 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:274 - Loaded 106 ML-specific categories
2025-06-30 01:34:45 - bank_analyzer.ml.dynamic_category_manager - INFO - _load_existing_categories:137 - Loaded 32 main categories
2025-06-30 01:34:45 - bank_analyzer.ml.dynamic_category_manager - INFO - __init__:118 - Dynamic category manager initialized
2025-06-30 01:34:45 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:34:45 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:34:45 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:34:45 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 01:34:45 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:34:45 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:34:45 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:34:45 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:34:45 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:34:45 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:34:45 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 01:34:45 - bank_analyzer.ml.budget_guardian - INFO - __init__:107 - Budget guardian initialized ($5.0 total, $1.0 daily)
2025-06-30 01:34:45 - bank_analyzer.ml.enhanced_sambanova_categorizer - INFO - __init__:109 - Enhanced SambaNova categorizer initialized
2025-06-30 01:34:45 - bank_analyzer.ml.hybrid_ml_categorizer - INFO - __init__:115 - Hybrid ML categorizer initialized in hybrid mode
2025-06-30 01:34:45 - bank_analyzer.ml.integrated_categorizer - INFO - __init__:46 - Enhanced integrated categorizer with AI coordinator initialized
2025-06-30 01:34:45 - bank_analyzer.core.transaction_data_manager - INFO - __init__:121 - Transaction Data Manager initialized
2025-06-30 01:34:45 - bank_analyzer.ui.main_window - INFO - load_initial_data:741 - Initial data loaded successfully
2025-06-30 01:34:45 - bank_analyzer.ui.main_window - INFO - __init__:237 - Bank Analyzer main window initialized
2025-06-30 01:34:45 - __main__ - INFO - main:76 - Bank Statement Analyzer application started successfully
2025-06-30 01:34:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.129 seconds
2025-06-30 01:34:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.124 seconds
2025-06-30 01:34:47 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.121 seconds
2025-06-30 01:34:47 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.115 seconds
2025-06-30 01:34:47 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.187 seconds
2025-06-30 01:34:47 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.124 seconds
2025-06-30 01:34:48 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.133 seconds
2025-06-30 01:34:48 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.117 seconds
2025-06-30 01:34:55 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.160 seconds
2025-06-30 01:34:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.142 seconds
2025-06-30 01:35:05 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.164 seconds
2025-06-30 01:35:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.164 seconds
2025-06-30 01:35:09 - bank_analyzer.parsers.parser_factory - INFO - get_parser:89 - Selected pdf parser for C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\Jan to march.pdf
2025-06-30 01:35:09 - bank_analyzer.parsers.pdf_parser - INFO - parse:53 - Parsing PDF with rewritten parser: C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\Jan to march.pdf
2025-06-30 01:35:14 - bank_analyzer.parsers.pdf_parser - INFO - parse:68 - Rewritten parser extracted 316 transactions
2025-06-30 01:35:14 - bank_analyzer.parsers.parser_factory - INFO - parse_file:158 - Successfully parsed 316 transactions from C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\Jan to march.pdf
2025-06-30 01:35:14 - bank_analyzer.ui.main_window - INFO - run:66 - Parsed 316 transactions from C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\Jan to march.pdf
2025-06-30 01:35:14 - bank_analyzer.parsers.parser_factory - INFO - get_parser:89 - Selected pdf parser for C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\june to august.pdf
2025-06-30 01:35:14 - bank_analyzer.parsers.pdf_parser - INFO - parse:53 - Parsing PDF with rewritten parser: C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\june to august.pdf
2025-06-30 01:35:15 - bank_analyzer.parsers.pdf_parser - INFO - parse:68 - Rewritten parser extracted 109 transactions
2025-06-30 01:35:15 - bank_analyzer.parsers.parser_factory - INFO - parse_file:158 - Successfully parsed 109 transactions from C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\june to august.pdf
2025-06-30 01:35:15 - bank_analyzer.ui.main_window - INFO - run:66 - Parsed 109 transactions from C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\june to august.pdf
2025-06-30 01:35:15 - bank_analyzer.parsers.parser_factory - INFO - get_parser:89 - Selected pdf parser for C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\March to june.pdf
2025-06-30 01:35:15 - bank_analyzer.parsers.pdf_parser - INFO - parse:53 - Parsing PDF with rewritten parser: C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\March to june.pdf
2025-06-30 01:35:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.454 seconds
2025-06-30 01:35:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.517 seconds
2025-06-30 01:35:19 - bank_analyzer.parsers.pdf_parser - INFO - parse:68 - Rewritten parser extracted 281 transactions
2025-06-30 01:35:19 - bank_analyzer.parsers.parser_factory - INFO - parse_file:158 - Successfully parsed 281 transactions from C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\March to june.pdf
2025-06-30 01:35:19 - bank_analyzer.ui.main_window - INFO - run:66 - Parsed 281 transactions from C:\Users\<USER>\Documents\GitHub\bank_statement_analyzer_standalone\statements\Indian bank\March to june.pdf
2025-06-30 01:35:19 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 01:35:19 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.111 seconds
2025-06-30 01:35:19 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.108 seconds
2025-06-30 01:35:20 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.106 seconds
2025-06-30 01:35:20 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.108 seconds
2025-06-30 01:35:20 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.109 seconds
2025-06-30 01:35:20 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.107 seconds
2025-06-30 01:35:20 - bank_analyzer.core.transaction_data_manager - INFO - create_session:187 - Created transaction session: txn_session_20250630_013520_8d4a20e2 (706 transactions)
2025-06-30 01:35:20 - bank_analyzer.ui.main_window - INFO - on_processing_completed:1061 - Created session: txn_session_20250630_013520_8d4a20e2
2025-06-30 01:35:25 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.155 seconds
2025-06-30 01:35:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.113 seconds
2025-06-30 01:35:34 - bank_analyzer.core.transaction_data_manager - INFO - __init__:121 - Transaction Data Manager initialized
2025-06-30 01:35:34 - bank_analyzer.ui.ml_labeling_window - INFO - __init__:1002 - ML Labeling Window initialized with performance optimizations
2025-06-30 01:35:34 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:241 - Loaded 60 categories from main application
2025-06-30 01:35:34 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:274 - Loaded 106 ML-specific categories
2025-06-30 01:35:34 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 01:35:34 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:35:34 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:35:34 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:35:34 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:35:34 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:35:34 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:35:34 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:35:34 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:35:34 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:35:34 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:35:34 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:35:34 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:35:34 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:35:34 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:35:34 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:35:34 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:35:34 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:35:34 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:35:34 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:35:34 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:35:34 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:35:34 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:35:34 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:35:34 - bank_analyzer.ml.batch_processor - INFO - __init__:87 - Batch processor initialized (batch size: 20-30)
2025-06-30 01:35:34 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:241 - Loaded 60 categories from main application
2025-06-30 01:35:34 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:274 - Loaded 106 ML-specific categories
2025-06-30 01:35:34 - bank_analyzer.ml.dynamic_category_manager - INFO - _load_existing_categories:137 - Loaded 32 main categories
2025-06-30 01:35:34 - bank_analyzer.ml.dynamic_category_manager - INFO - __init__:118 - Dynamic category manager initialized
2025-06-30 01:35:34 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:35:34 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:35:34 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:35:34 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 01:35:34 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:35:34 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:35:34 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:35:34 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:35:34 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:35:34 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:35:34 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 01:35:34 - bank_analyzer.ml.budget_guardian - INFO - __init__:107 - Budget guardian initialized ($5.0 total, $1.0 daily)
2025-06-30 01:35:34 - bank_analyzer.ml.enhanced_sambanova_categorizer - INFO - __init__:109 - Enhanced SambaNova categorizer initialized
2025-06-30 01:35:34 - bank_analyzer.ml.hybrid_ml_categorizer - INFO - __init__:115 - Hybrid ML categorizer initialized in hybrid mode
2025-06-30 01:35:34 - bank_analyzer.core.transaction_data_manager - INFO - start_clean_session:700 - Started clean session
2025-06-30 01:35:34 - bank_analyzer.ui.ml_labeling_window - INFO - on_selection_changed:2194 - Selection changed: 0 transactions selected
2025-06-30 01:35:34 - bank_analyzer.ui.ml_labeling_window - INFO - on_selection_changed:2195 - Selected hash_ids: []...
2025-06-30 01:35:34 - bank_analyzer.ui.enhanced_transaction_table - INFO - populate_table:221 - No valid transactions to display
2025-06-30 01:35:34 - bank_analyzer.ui.ml_labeling_window - INFO - _show_clean_start_message:1051 - Clean start message displayed
2025-06-30 01:35:34 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:35:35 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.119 seconds
2025-06-30 01:35:35 - bank_analyzer.ui.background_workers - INFO - run:137 - Filtering completed in 0.120 seconds, found 50 transactions
2025-06-30 01:35:35 - bank_analyzer.ui.ml_labeling_window - INFO - on_filter_completed:2151 - Skipping filter results display - no active session or data
2025-06-30 01:35:35 - bank_analyzer.ui.ml_labeling_window - INFO - on_selection_changed:2194 - Selection changed: 0 transactions selected
2025-06-30 01:35:35 - bank_analyzer.ui.ml_labeling_window - INFO - on_selection_changed:2195 - Selected hash_ids: []...
2025-06-30 01:35:35 - bank_analyzer.ui.enhanced_transaction_table - INFO - populate_table:221 - No valid transactions to display
2025-06-30 01:35:35 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.142 seconds
2025-06-30 01:35:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.138 seconds
2025-06-30 01:35:45 - bank_analyzer.ui.ml_labeling_window - INFO - load_latest_session_directly:3630 - No reasonable sessions found, using most recent session
2025-06-30 01:35:45 - bank_analyzer.ml.data_preparation - INFO - extract_unique_transactions:363 - Extracted 405 unique transaction descriptions from 706 transactions
2025-06-30 01:35:45 - bank_analyzer.ui.ml_labeling_window - INFO - _force_clear_all_caches:4224 - Cache clearing no longer needed - caching system removed for reliability
2025-06-30 01:35:45 - bank_analyzer.ui.ml_labeling_window - INFO - _force_clear_all_caches:4238 - All caches cleared successfully
2025-06-30 01:35:45 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.115 seconds
2025-06-30 01:35:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.113 seconds
2025-06-30 01:35:47 - bank_analyzer.ui.ml_labeling_window - INFO - load_latest_session_directly:3685 - Loaded 405 transactions directly from latest session: txn_session_20250630_013520_8d4a20e2
2025-06-30 01:35:55 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.105 seconds
2025-06-30 01:35:55 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.115 seconds
2025-06-30 01:36:05 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.108 seconds
2025-06-30 01:36:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.116 seconds
2025-06-30 01:36:15 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.144 seconds
2025-06-30 01:36:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.112 seconds
2025-06-30 01:36:25 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.157 seconds
2025-06-30 01:36:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.110 seconds
2025-06-30 01:36:27 - bank_analyzer.ml.session_training_manager - INFO - start_session_training:90 - Started training session: training_20250630_013627 for source: txn_session_20250630_013520_8d4a20e2
2025-06-30 01:36:27 - bank_analyzer.ui.ml_labeling_window - INFO - start_session_based_training:1842 - Started session-based training: training_20250630_013627
2025-06-30 01:36:27 - bank_analyzer.ui.ml_labeling_window - INFO - _load_session_data_directly:3417 - _load_session_data_directly called with 706 raw transactions from session txn_session_20250630_013520_8d4a20e2
2025-06-30 01:36:27 - bank_analyzer.ui.ml_labeling_window - INFO - _load_session_data_directly:3420 - Sample transaction: CREDIT INTEREST... Date: 2024-03-31
2025-06-30 01:36:27 - bank_analyzer.ml.data_preparation - INFO - extract_unique_transactions:363 - Extracted 405 unique transaction descriptions from 706 transactions
2025-06-30 01:36:27 - bank_analyzer.ui.ml_labeling_window - INFO - _load_session_data_directly:3430 - Extracted 405 unique transactions
2025-06-30 01:36:27 - bank_analyzer.ui.ml_labeling_window - INFO - _load_session_data_directly:3433 - Sample unique transaction: CREDIT INTEREST... Frequency: 2
2025-06-30 01:36:27 - bank_analyzer.ui.ml_labeling_window - INFO - _force_clear_all_caches:4224 - Cache clearing no longer needed - caching system removed for reliability
2025-06-30 01:36:27 - bank_analyzer.ui.ml_labeling_window - INFO - _force_clear_all_caches:4238 - All caches cleared successfully
2025-06-30 01:36:35 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.170 seconds
2025-06-30 01:36:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.108 seconds
2025-06-30 01:36:37 - bank_analyzer.ui.ml_labeling_window - INFO - _load_session_data_directly:3473 - Loaded 405 transactions directly from session: txn_session_20250630_013520_8d4a20e2
2025-06-30 01:36:45 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.113 seconds
2025-06-30 01:36:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.107 seconds
2025-06-30 01:36:55 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.152 seconds
2025-06-30 01:36:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.108 seconds
2025-06-30 01:37:05 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.177 seconds
2025-06-30 01:37:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.114 seconds
2025-06-30 01:37:15 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.107 seconds
2025-06-30 01:37:15 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.109 seconds
2025-06-30 01:37:25 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.128 seconds
2025-06-30 01:37:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.135 seconds
2025-06-30 01:37:35 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.137 seconds
2025-06-30 01:37:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.120 seconds
2025-06-30 01:37:45 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.178 seconds
2025-06-30 01:37:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.121 seconds
2025-06-30 01:37:55 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.156 seconds
2025-06-30 01:37:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.131 seconds
2025-06-30 01:38:05 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.117 seconds
2025-06-30 01:38:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.114 seconds
2025-06-30 01:38:15 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.134 seconds
2025-06-30 01:38:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.118 seconds
2025-06-30 01:38:25 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.183 seconds
2025-06-30 01:38:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.168 seconds
2025-06-30 01:38:35 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.145 seconds
2025-06-30 01:38:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.147 seconds
2025-06-30 01:38:45 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.141 seconds
2025-06-30 01:38:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.129 seconds
2025-06-30 01:38:55 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.148 seconds
2025-06-30 01:38:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.117 seconds
2025-06-30 01:39:05 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.143 seconds
2025-06-30 01:39:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.132 seconds
2025-06-30 01:39:15 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.144 seconds
2025-06-30 01:39:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.150 seconds
2025-06-30 01:39:25 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.184 seconds
2025-06-30 01:39:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.121 seconds
2025-06-30 01:39:35 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.139 seconds
2025-06-30 01:39:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.119 seconds
2025-06-30 01:39:45 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.171 seconds
2025-06-30 01:39:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.137 seconds
2025-06-30 01:39:55 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.136 seconds
2025-06-30 01:39:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.125 seconds
2025-06-30 01:40:05 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.219 seconds
2025-06-30 01:40:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.208 seconds
2025-06-30 01:40:15 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.168 seconds
2025-06-30 01:40:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.185 seconds
2025-06-30 01:40:25 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.209 seconds
2025-06-30 01:40:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.176 seconds
2025-06-30 01:40:27 - bank_analyzer.core.logger - INFO - setup_logging:72 - Logging system initialized
2025-06-30 01:40:27 - bank_analyzer.core.logger - INFO - setup_logging:73 - Log file: logs\bank_analyzer_20250630.log
2025-06-30 01:40:27 - bank_analyzer.core.logger - INFO - setup_logging:74 - Log level: INFO
2025-06-30 01:40:27 - __main__ - INFO - main:47 - Starting Bank Statement Analyzer application
2025-06-30 01:40:27 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 01:40:27 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 01:40:27 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:40:27 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:40:27 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:40:28 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:40:28 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:40:28 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:40:28 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:40:28 - bank_analyzer.ml.ai_categorization_coordinator - INFO - _load_config:117 - Loaded AI coordinator config from bank_analyzer_config\ai_coordinator_config.json
2025-06-30 01:40:28 - bank_analyzer.ml.enhanced_merchant_mapper - INFO - _load_patterns:497 - Loaded 24 merchant patterns
2025-06-30 01:40:28 - bank_analyzer.ml.enhanced_merchant_mapper - INFO - _create_automatic_backup:689 - Created automatic backup: bank_analyzer_config\merchant_cache\backups\patterns_backup_20250630_014028.json
2025-06-30 01:40:28 - bank_analyzer.ml.enhanced_merchant_mapper - INFO - __init__:158 - Enhanced merchant mapper initialized with 24 patterns
2025-06-30 01:40:28 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:40:28 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:40:28 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:40:28 - bank_analyzer.ml.transaction_context_analyzer - INFO - __init__:131 - Transaction Context Analyzer initialized
2025-06-30 01:40:28 - bank_analyzer.ml.smart_transaction_router - INFO - _load_config:109 - Loaded router config from bank_analyzer_config\router_config.json
2025-06-30 01:40:28 - bank_analyzer.ml.smart_transaction_router - INFO - __init__:80 - Router initialized: cache_threshold=0.6, max_ai_batch=100
2025-06-30 01:40:28 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:40:28 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:40:28 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:40:28 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:40:28 - bank_analyzer.ml.ai_categorization_coordinator - INFO - __init__:91 - AI Coordinator initialized with budget: $2.0
2025-06-30 01:40:28 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 01:40:28 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:40:28 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:40:28 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:40:28 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:40:28 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:40:28 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:40:28 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:40:28 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:40:28 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:40:28 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:40:28 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:40:28 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:40:28 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:40:28 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:40:28 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:40:28 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:40:28 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:40:28 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:40:28 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:40:28 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:40:28 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:40:28 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:40:28 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:40:28 - bank_analyzer.ml.sambanova_cost_tracker - INFO - __init__:83 - Cost tracker initialized (total: $0.0000)
2025-06-30 01:40:28 - bank_analyzer.ml.batch_processor - INFO - __init__:87 - Batch processor initialized (batch size: 20-30)
2025-06-30 01:40:28 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:241 - Loaded 60 categories from main application
2025-06-30 01:40:28 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:274 - Loaded 106 ML-specific categories
2025-06-30 01:40:28 - bank_analyzer.ml.dynamic_category_manager - INFO - _load_existing_categories:137 - Loaded 32 main categories
2025-06-30 01:40:28 - bank_analyzer.ml.dynamic_category_manager - INFO - __init__:118 - Dynamic category manager initialized
2025-06-30 01:40:28 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:40:28 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:40:28 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:40:28 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 01:40:28 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:40:28 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:40:28 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:40:28 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:40:28 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:40:28 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:40:28 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 01:40:28 - bank_analyzer.ml.budget_guardian - INFO - __init__:107 - Budget guardian initialized ($5.0 total, $1.0 daily)
2025-06-30 01:40:28 - bank_analyzer.ml.enhanced_sambanova_categorizer - INFO - __init__:109 - Enhanced SambaNova categorizer initialized
2025-06-30 01:40:28 - bank_analyzer.ml.hybrid_ml_categorizer - INFO - __init__:115 - Hybrid ML categorizer initialized in hybrid mode
2025-06-30 01:40:28 - bank_analyzer.ml.integrated_categorizer - INFO - __init__:46 - Enhanced integrated categorizer with AI coordinator initialized
2025-06-30 01:40:28 - bank_analyzer.core.transaction_data_manager - INFO - __init__:121 - Transaction Data Manager initialized
2025-06-30 01:40:28 - bank_analyzer.ui.main_window - INFO - load_initial_data:741 - Initial data loaded successfully
2025-06-30 01:40:28 - bank_analyzer.ui.main_window - INFO - __init__:237 - Bank Analyzer main window initialized
2025-06-30 01:40:28 - __main__ - INFO - main:76 - Bank Statement Analyzer application started successfully
2025-06-30 01:40:29 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.190 seconds
2025-06-30 01:40:29 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.117 seconds
2025-06-30 01:40:29 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.118 seconds
2025-06-30 01:40:29 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.133 seconds
2025-06-30 01:40:30 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.182 seconds
2025-06-30 01:40:30 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.114 seconds
2025-06-30 01:40:30 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.109 seconds
2025-06-30 01:40:30 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.120 seconds
2025-06-30 01:40:35 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.157 seconds
2025-06-30 01:40:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.140 seconds
2025-06-30 01:40:38 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.147 seconds
2025-06-30 01:40:38 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.129 seconds
2025-06-30 01:40:45 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.185 seconds
2025-06-30 01:40:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.128 seconds
2025-06-30 01:40:48 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.122 seconds
2025-06-30 01:40:48 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.112 seconds
2025-06-30 01:40:55 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.157 seconds
2025-06-30 01:40:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.131 seconds
2025-06-30 01:40:56 - bank_analyzer.ui.session_management_dialog - INFO - load_sessions:314 - Loaded 4 sessions
2025-06-30 01:40:58 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.150 seconds
2025-06-30 01:40:58 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.151 seconds
2025-06-30 01:41:04 - bank_analyzer.core.transaction_data_manager - INFO - load_session:334 - Loaded session: txn_session_20250630_013520_8d4a20e2
2025-06-30 01:41:04 - bank_analyzer.ml.session_training_manager - INFO - get_all_trained_hash_ids:231 - Collected 201 trained transaction hash IDs from 2 sessions
2025-06-30 01:41:04 - bank_analyzer.core.transaction_data_manager - INFO - load_session_with_duplicate_detection:444 - Loaded session txn_session_20250630_013520_8d4a20e2 with duplicate detection: 706 new, 0 duplicates
2025-06-30 01:41:04 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.117 seconds
2025-06-30 01:41:04 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.116 seconds
2025-06-30 01:41:04 - bank_analyzer.ui.main_window - INFO - on_session_loaded_from_dialog:2024 - Loaded session from dialog: txn_session_20250630_013520_8d4a20e2 (706 transactions)
2025-06-30 01:41:05 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.124 seconds
2025-06-30 01:41:05 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.118 seconds
2025-06-30 01:41:05 - bank_analyzer.ui.main_window - INFO - on_session_loaded_from_dialog:2024 - Loaded session from dialog: txn_session_20250630_013520_8d4a20e2 (706 transactions)
2025-06-30 01:41:05 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.139 seconds
2025-06-30 01:41:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.142 seconds
2025-06-30 01:41:08 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.126 seconds
2025-06-30 01:41:08 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.139 seconds
2025-06-30 01:41:15 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.144 seconds
2025-06-30 01:41:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.145 seconds
2025-06-30 01:41:18 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.172 seconds
2025-06-30 01:41:18 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.114 seconds
2025-06-30 01:41:25 - bank_analyzer.core.transaction_data_manager - INFO - __init__:121 - Transaction Data Manager initialized
2025-06-30 01:41:25 - bank_analyzer.ui.ml_labeling_window - INFO - __init__:1002 - ML Labeling Window initialized with performance optimizations
2025-06-30 01:41:25 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:241 - Loaded 60 categories from main application
2025-06-30 01:41:25 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:274 - Loaded 106 ML-specific categories
2025-06-30 01:41:25 - bank_analyzer.core.categorizer - INFO - _load_existing_categories:70 - Loaded 12 categories from main application
2025-06-30 01:41:25 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:41:25 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:41:25 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:41:25 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:41:25 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:41:25 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:41:25 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:41:25 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.204 seconds
2025-06-30 01:41:25 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:41:25 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:41:26 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:508 - ML models loaded successfully (version: 1.0)
2025-06-30 01:41:26 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:41:26 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:41:26 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:41:26 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:41:26 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:41:26 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:41:26 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:41:26 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:41:26 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:41:26 - bank_analyzer.ml.sambanova_categorizer - INFO - __init__:81 - SambaNova categorizer initialized (available: True)
2025-06-30 01:41:26 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:41:26 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:41:26 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:41:26 - bank_analyzer.ml.batch_processor - INFO - __init__:87 - Batch processor initialized (batch size: 20-30)
2025-06-30 01:41:26 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:241 - Loaded 60 categories from main application
2025-06-30 01:41:26 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:274 - Loaded 106 ML-specific categories
2025-06-30 01:41:26 - bank_analyzer.ml.dynamic_category_manager - INFO - _load_existing_categories:137 - Loaded 32 main categories
2025-06-30 01:41:26 - bank_analyzer.ml.dynamic_category_manager - INFO - __init__:118 - Dynamic category manager initialized
2025-06-30 01:41:26 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:41:26 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:41:26 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:41:26 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 01:41:26 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:41:26 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:41:26 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:41:26 - bank_analyzer.ml.transaction_filter - INFO - _load_custom_patterns:219 - Loaded custom filtering patterns
2025-06-30 01:41:26 - bank_analyzer.ml.transaction_filter - INFO - _load_thresholds:235 - Loaded custom thresholds from config
2025-06-30 01:41:26 - bank_analyzer.ml.transaction_filter - INFO - __init__:192 - Smart transaction filter initialized with thresholds: manual=0.5, generic=0.4
2025-06-30 01:41:26 - bank_analyzer.ml.transaction_prioritizer - INFO - __init__:81 - Transaction prioritizer initialized
2025-06-30 01:41:26 - bank_analyzer.ml.budget_guardian - INFO - __init__:107 - Budget guardian initialized ($5.0 total, $1.0 daily)
2025-06-30 01:41:26 - bank_analyzer.ml.enhanced_sambanova_categorizer - INFO - __init__:109 - Enhanced SambaNova categorizer initialized
2025-06-30 01:41:26 - bank_analyzer.ml.hybrid_ml_categorizer - INFO - __init__:115 - Hybrid ML categorizer initialized in hybrid mode
2025-06-30 01:41:26 - bank_analyzer.core.transaction_data_manager - INFO - start_clean_session:700 - Started clean session
2025-06-30 01:41:26 - bank_analyzer.ui.ml_labeling_window - INFO - on_selection_changed:2194 - Selection changed: 0 transactions selected
2025-06-30 01:41:26 - bank_analyzer.ui.ml_labeling_window - INFO - on_selection_changed:2195 - Selected hash_ids: []...
2025-06-30 01:41:26 - bank_analyzer.ui.enhanced_transaction_table - INFO - populate_table:221 - No valid transactions to display
2025-06-30 01:41:26 - bank_analyzer.ui.ml_labeling_window - INFO - _show_clean_start_message:1051 - Clean start message displayed
2025-06-30 01:41:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.196 seconds
2025-06-30 01:41:26 - bank_analyzer.ml.sambanova_client - INFO - __init__:98 - SambaNova client initialized (cache: 3 entries)
2025-06-30 01:41:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.153 seconds
2025-06-30 01:41:26 - bank_analyzer.ui.background_workers - INFO - run:137 - Filtering completed in 0.153 seconds, found 50 transactions
2025-06-30 01:41:26 - bank_analyzer.ui.ml_labeling_window - INFO - on_filter_completed:2151 - Skipping filter results display - no active session or data
2025-06-30 01:41:26 - bank_analyzer.ui.ml_labeling_window - INFO - on_selection_changed:2194 - Selection changed: 0 transactions selected
2025-06-30 01:41:26 - bank_analyzer.ui.ml_labeling_window - INFO - on_selection_changed:2195 - Selected hash_ids: []...
2025-06-30 01:41:26 - bank_analyzer.ui.enhanced_transaction_table - INFO - populate_table:221 - No valid transactions to display
2025-06-30 01:41:28 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.110 seconds
2025-06-30 01:41:28 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.119 seconds
2025-06-30 01:41:31 - bank_analyzer.ui.session_management_dialog - INFO - load_sessions:314 - Loaded 4 sessions
2025-06-30 01:41:35 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.152 seconds
2025-06-30 01:41:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.121 seconds
2025-06-30 01:41:38 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.129 seconds
2025-06-30 01:41:38 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.116 seconds
2025-06-30 01:41:41 - bank_analyzer.ui.ml_labeling_window - INFO - load_latest_session_directly:3630 - No reasonable sessions found, using most recent session
2025-06-30 01:41:41 - bank_analyzer.ml.data_preparation - INFO - extract_unique_transactions:363 - Extracted 405 unique transaction descriptions from 706 transactions
2025-06-30 01:41:41 - bank_analyzer.ui.ml_labeling_window - INFO - _force_clear_all_caches:4224 - Cache clearing no longer needed - caching system removed for reliability
2025-06-30 01:41:41 - bank_analyzer.ui.ml_labeling_window - INFO - _force_clear_all_caches:4238 - All caches cleared successfully
2025-06-30 01:41:43 - bank_analyzer.ui.ml_labeling_window - INFO - load_latest_session_directly:3685 - Loaded 405 transactions directly from latest session: txn_session_20250630_013520_8d4a20e2
2025-06-30 01:41:45 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.147 seconds
2025-06-30 01:41:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.123 seconds
2025-06-30 01:41:48 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.114 seconds
2025-06-30 01:41:48 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.114 seconds
2025-06-30 01:41:51 - __main__ - INFO - main:80 - Bank Statement Analyzer application exited with code: 0
2025-06-30 01:41:55 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.205 seconds
2025-06-30 01:41:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.127 seconds
2025-06-30 01:42:05 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.175 seconds
2025-06-30 01:42:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.134 seconds
2025-06-30 01:42:15 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.187 seconds
2025-06-30 01:42:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.134 seconds
2025-06-30 01:42:25 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.180 seconds
2025-06-30 01:42:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.162 seconds
2025-06-30 01:42:35 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.190 seconds
2025-06-30 01:42:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.123 seconds
2025-06-30 01:42:45 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.115 seconds
2025-06-30 01:42:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.128 seconds
2025-06-30 01:42:55 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.163 seconds
2025-06-30 01:42:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.128 seconds
2025-06-30 01:43:05 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.178 seconds
2025-06-30 01:43:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.116 seconds
2025-06-30 01:43:15 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.124 seconds
2025-06-30 01:43:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.116 seconds
2025-06-30 01:43:22 - bank_analyzer.ui.session_management_dialog - INFO - load_sessions:314 - Loaded 4 sessions
2025-06-30 01:43:25 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.183 seconds
2025-06-30 01:43:26 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.114 seconds
2025-06-30 01:43:35 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.127 seconds
2025-06-30 01:43:36 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.131 seconds
2025-06-30 01:43:45 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.161 seconds
2025-06-30 01:43:46 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.114 seconds
2025-06-30 01:43:55 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.171 seconds
2025-06-30 01:43:56 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.161 seconds
2025-06-30 01:44:05 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.177 seconds
2025-06-30 01:44:06 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.111 seconds
2025-06-30 01:44:15 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.189 seconds
2025-06-30 01:44:16 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:513 - Loaded 83 unique transactions from storage in 0.108 seconds
2025-06-30 01:44:20 - __main__ - INFO - main:80 - Bank Statement Analyzer application exited with code: 0
