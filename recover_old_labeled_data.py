#!/usr/bin/env python3
"""
Recover old labeled data that might be stored elsewhere
"""

import sys
import pandas as pd
from pathlib import Path
from datetime import datetime
import os

# Add the bank_analyzer module to the path
sys.path.insert(0, str(Path(__file__).parent))


def search_for_old_labeled_data():
    """Search for old labeled data in various locations"""
    print("🔍 Searching for Old Labeled Data")
    print("=" * 50)
    
    search_locations = [
        "bank_analyzer_config/ml_data/",
        "bank_analyzer_config/",
        "backup/",
        "./",
        "data/",
        "ml_data/"
    ]
    
    potential_files = [
        "labeling_history.csv",
        "unique_transactions.csv", 
        "training_data.csv",
        "labeled_transactions.csv",
        "transaction_categories.csv",
        "categorized_transactions.csv"
    ]
    
    found_files = []
    
    for location in search_locations:
        location_path = Path(location)
        if location_path.exists():
            print(f"📁 Checking: {location}")
            
            for file_pattern in potential_files:
                # Check exact match
                file_path = location_path / file_pattern
                if file_path.exists():
                    file_size = file_path.stat().st_size
                    file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                    print(f"   ✅ Found: {file_path} ({file_size} bytes, {file_time.strftime('%Y-%m-%d %H:%M')})")
                    found_files.append(file_path)
                
                # Check for backup files
                backup_patterns = [f"{file_pattern}.bak", f"{file_pattern}.backup", f"backup_{file_pattern}"]
                for backup_pattern in backup_patterns:
                    backup_path = location_path / backup_pattern
                    if backup_path.exists():
                        file_size = backup_path.stat().st_size
                        file_time = datetime.fromtimestamp(backup_path.stat().st_mtime)
                        print(f"   ✅ Found backup: {backup_path} ({file_size} bytes, {file_time.strftime('%Y-%m-%d %H:%M')})")
                        found_files.append(backup_path)
    
    return found_files


def check_session_data():
    """Check session-based training data"""
    print(f"\n🔍 Checking Session Training Data")
    print("=" * 50)
    
    try:
        from bank_analyzer.ml.session_training_manager import SessionTrainingManager
        
        session_manager = SessionTrainingManager("bank_analyzer_config/ml_data")
        
        # Get all sessions
        sessions = session_manager.get_all_sessions()
        print(f"📊 Found {len(sessions)} training sessions")
        
        total_session_data = 0
        for session in sessions:
            session_data = session_manager.get_session_labeled_data(session.session_id)
            print(f"   Session {session.session_id}: {len(session_data)} labeled transactions")
            total_session_data += len(session_data)
        
        # Get combined data
        combined_data = session_manager.get_combined_training_data()
        print(f"📊 Combined session data: {len(combined_data)} transactions")
        
        return len(combined_data)
        
    except Exception as e:
        print(f"❌ Error checking session data: {str(e)}")
        return 0


def check_categorization_service_data():
    """Check if old data is in the categorization service"""
    print(f"\n🔍 Checking Categorization Service Data")
    print("=" * 50)
    
    try:
        from bank_analyzer.ml.transaction_categorization_service import TransactionCategorizationService
        
        service = TransactionCategorizationService()
        existing_transactions = service._load_existing_transactions()
        
        print(f"📊 Transactions in categorization service: {len(existing_transactions)}")
        
        # Check for manually categorized transactions
        manually_categorized = 0
        for hash_id, txn_data in existing_transactions.items():
            if txn_data.get('category') and txn_data.get('category') != 'Uncategorized':
                manually_categorized += 1
        
        print(f"📊 Manually categorized transactions: {manually_categorized}")
        
        if manually_categorized > 0:
            print(f"✅ Found {manually_categorized} categorized transactions in service!")
            
            # Show sample categories
            sample_categories = {}
            count = 0
            for hash_id, txn_data in existing_transactions.items():
                if txn_data.get('category') and txn_data.get('category') != 'Uncategorized':
                    category = txn_data['category']
                    sample_categories[category] = sample_categories.get(category, 0) + 1
                    count += 1
                    if count >= 10:  # Limit sample
                        break
            
            print(f"📋 Sample categories found:")
            for category, count in sample_categories.items():
                print(f"   {category}: {count} transactions")
        
        return manually_categorized
        
    except Exception as e:
        print(f"❌ Error checking categorization service: {str(e)}")
        return 0


def analyze_file_contents(file_path):
    """Analyze contents of a potential data file"""
    print(f"\n📋 Analyzing: {file_path}")
    print("-" * 40)
    
    try:
        # Try to read as CSV
        df = pd.read_csv(file_path)
        print(f"✅ Successfully read CSV: {len(df)} rows, {len(df.columns)} columns")
        
        # Show columns
        print(f"📊 Columns: {list(df.columns)}")
        
        # Check for labeled data indicators
        labeled_indicators = ['category', 'sub_category', 'is_manually_labeled', 'labeled_by']
        found_indicators = [col for col in labeled_indicators if col in df.columns]
        
        if found_indicators:
            print(f"✅ Found labeling indicators: {found_indicators}")
            
            # Count labeled data
            if 'category' in df.columns:
                labeled_count = len(df[df['category'].notna() & (df['category'] != '') & (df['category'] != 'Uncategorized')])
                print(f"📊 Transactions with categories: {labeled_count}")
                
                if labeled_count > 0:
                    # Show category breakdown
                    category_counts = df['category'].value_counts().head(10)
                    print(f"📋 Top categories:")
                    for category, count in category_counts.items():
                        if pd.notna(category) and category != 'Uncategorized':
                            print(f"   {category}: {count}")
                
                return labeled_count
        
        return 0
        
    except Exception as e:
        print(f"❌ Error reading file: {str(e)}")
        return 0


def main():
    """Main function to recover old labeled data"""
    print("🔍 Recovering Your Old Labeled Data")
    print("=" * 60)
    print("Searching for your missing 200+ labeled transactions...")
    print("")
    
    # Search for files
    found_files = search_for_old_labeled_data()
    
    # Check session data
    session_data_count = check_session_data()
    
    # Check categorization service
    service_data_count = check_categorization_service_data()
    
    # Analyze found files
    total_old_labeled = 0
    best_file = None
    best_count = 0
    
    if found_files:
        print(f"\n📋 Analyzing Found Files")
        print("=" * 50)
        
        for file_path in found_files:
            labeled_count = analyze_file_contents(file_path)
            total_old_labeled += labeled_count
            
            if labeled_count > best_count:
                best_count = labeled_count
                best_file = file_path
    
    # Summary
    print(f"\n" + "=" * 60)
    print("🎯 RECOVERY SUMMARY")
    print("=" * 60)
    
    print(f"📊 Current labeled data: 231 transactions (recent only)")
    print(f"📊 Session data found: {session_data_count} transactions")
    print(f"📊 Service data found: {service_data_count} transactions")
    print(f"📊 File data found: {total_old_labeled} transactions")
    
    if best_file and best_count > 200:
        print(f"\n🎉 FOUND YOUR OLD DATA!")
        print(f"✅ Best source: {best_file}")
        print(f"✅ Contains: {best_count} labeled transactions")
        print(f"\n🔧 Next steps:")
        print(f"1. This file likely contains your old 200+ labeled transactions")
        print(f"2. We can merge this with your current 231 labels")
        print(f"3. This would give you 400+ total labeled transactions")
        print(f"4. Much better for ML training!")
        
    elif service_data_count > 200:
        print(f"\n🎉 FOUND YOUR OLD DATA!")
        print(f"✅ Source: Categorization Service")
        print(f"✅ Contains: {service_data_count} categorized transactions")
        print(f"\n🔧 Next steps:")
        print(f"1. Extract categorized data from the service")
        print(f"2. Merge with your current 231 labels")
        print(f"3. This would give you 400+ total labeled transactions")
        
    elif session_data_count > 200:
        print(f"\n🎉 FOUND YOUR OLD DATA!")
        print(f"✅ Source: Session Training Data")
        print(f"✅ Contains: {session_data_count} labeled transactions")
        print(f"\n🔧 Next steps:")
        print(f"1. Combine session data properly")
        print(f"2. Merge with current labels")
        
    else:
        print(f"\n❌ OLD DATA NOT FOUND")
        print(f"💡 Your old 200+ labeled transactions may have been:")
        print(f"   • Overwritten during recent labeling")
        print(f"   • Stored in a different location")
        print(f"   • Lost during system updates")
        print(f"\n🔧 Options:")
        print(f"1. Continue with current 231 labels (may need more diverse data)")
        print(f"2. Re-label more transactions to improve training")
        print(f"3. Check if you have any backup files")
    
    return best_count > 200 or service_data_count > 200 or session_data_count > 200


if __name__ == "__main__":
    try:
        found_old_data = main()
        sys.exit(0 if found_old_data else 1)
    except Exception as e:
        print(f"❌ Recovery failed: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
