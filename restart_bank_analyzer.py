#!/usr/bin/env python3
import sys
import os
import time

print("Restarting Bank Statement Analyzer with fresh cache...")
print("Please wait while the application loads with all fixes applied...")

# Clear any remaining Python cache
import importlib
if hasattr(importlib, 'invalidate_caches'):
    importlib.invalidate_caches()

# Import and run the application
try:
    import bank_statement_analyzer
    print("Bank Statement Analyzer started successfully!")
except Exception as e:
    print(f"Error starting application: {e}")
    print("Please try running 'python bank_statement_analyzer.py' manually")
