#!/usr/bin/env python3
"""
Standalone Bank Statement Analyzer Launcher
Simple one-click launcher for the standalone bank statement analyzer
"""

import sys
import subprocess
import os
from pathlib import Path

def print_header():
    """Print application header"""
    print("=" * 70)
    print("🏦 STANDALONE BANK STATEMENT ANALYZER")
    print("=" * 70)
    print("📊 Parse • 🎯 Categorize • 🤖 AI-Powered • 📤 Export")
    print("Complete standalone solution for bank statement analysis")
    print("=" * 70)

def check_python():
    """Check Python version"""
    try:
        version = sys.version_info
        if version.major < 3 or (version.major == 3 and version.minor < 8):
            print("❌ Python 3.8+ required. Current version:", f"{version.major}.{version.minor}")
            return False
        
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} detected")
        return True
    except Exception as e:
        print(f"❌ Python check failed: {e}")
        return False

def install_dependencies():
    """Install required dependencies"""
    print("\n📦 Installing dependencies...")
    
    try:
        # Install from requirements.txt
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ All dependencies installed successfully!")
            return True
        else:
            print(f"❌ Failed to install dependencies: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error installing dependencies: {e}")
        return False

def check_structure():
    """Check if all required files and directories exist"""
    print("\n📁 Checking application structure...")
    
    required_files = [
        "bank_statement_analyzer.py",
        "requirements.txt",
        "README_BANK_ANALYZER.md"
    ]
    
    required_dirs = [
        "bank_analyzer",
        "bank_analyzer_config", 
        "statements",
        "logs",
        "data"
    ]
    
    # Check files
    for file_name in required_files:
        if Path(file_name).exists():
            print(f"   ✅ {file_name}")
        else:
            print(f"   ❌ {file_name} (MISSING)")
            return False
    
    # Check directories
    for dir_name in required_dirs:
        if Path(dir_name).exists():
            print(f"   ✅ {dir_name}/")
        else:
            print(f"   ❌ {dir_name}/ (MISSING)")
            return False
    
    return True

def show_features():
    """Show application features"""
    print("\n🚀 FEATURES")
    print("=" * 20)
    print("📄 Multi-Format Support:")
    print("   • PDF bank statements")
    print("   • CSV files") 
    print("   • Excel files (.xlsx, .xls)")
    print()
    print("🤖 Intelligent Processing:")
    print("   • AI-powered categorization")
    print("   • Machine learning models")
    print("   • Rule-based classification")
    print("   • Smart merchant mapping")
    print()
    print("🎯 Advanced Features:")
    print("   • Hybrid categorization workflows")
    print("   • Training data management")
    print("   • Performance analytics")
    print("   • Cost-optimized AI usage")
    print()
    print("📤 Export Options:")
    print("   • CSV export")
    print("   • Categorized transactions")
    print("   • Ready for any finance app")

def launch_application():
    """Launch the main application"""
    print("\n🚀 LAUNCHING APPLICATION")
    print("=" * 30)
    
    try:
        # Launch the main bank statement analyzer
        subprocess.run([sys.executable, "bank_statement_analyzer.py"])
        print("\n👋 Application closed successfully!")
        
    except KeyboardInterrupt:
        print("\n⏹️  Application interrupted by user")
    except Exception as e:
        print(f"\n❌ Error launching application: {e}")
        print("💡 Try running manually: python bank_statement_analyzer.py")

def main():
    """Main launcher function"""
    print_header()
    
    # Check Python version
    if not check_python():
        input("\nPress Enter to exit...")
        return False
    
    # Check application structure
    if not check_structure():
        print("\n❌ Application structure is incomplete!")
        print("💡 Make sure you're running from the bank_statement_analyzer_standalone directory")
        input("\nPress Enter to exit...")
        return False
    
    # Install dependencies
    print("\n🔍 Checking dependencies...")
    try:
        import pandas, PySide6, PyPDF2, pdfplumber, openpyxl
        print("✅ All dependencies are available!")
    except ImportError:
        if not install_dependencies():
            input("\nPress Enter to exit...")
            return False
    
    # Show features
    show_features()
    
    # Launch application
    print("\n" + "=" * 70)
    input("📱 Press Enter to launch Bank Statement Analyzer...")
    
    launch_application()
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n👋 Launcher interrupted. Goodbye!")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Launcher error: {e}")
        input("Press Enter to exit...")
        sys.exit(1)
