#!/usr/bin/env python3
"""
Test script to verify that the clear data functionality works correctly
and doesn't require two clicks to clear the data.
"""

import sys
import os
import unittest
from unittest.mock import Mock, patch, MagicMock

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


class TestClearDataFunctionality(unittest.TestCase):
    """Test clear data functionality fixes"""
    
    def test_clear_data_code_patterns(self):
        """Test that the clear data fix patterns are correctly implemented"""
        try:
            with open('bank_analyzer/ui/ml_labeling_window.py', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Test 1: Enhanced table should be properly cleared with populate_table([])
            self.assertIn("self.enhanced_table.populate_table([])", content,
                         "Enhanced table should be cleared with populate_table([])")
            
            # Test 2: The problematic populate_transaction_table() call should be removed
            self.assertNotIn("# Force refresh the transaction table display to ensure it's empty\n                    self.populate_transaction_table()", 
                           content, "Problematic populate_transaction_table() call should be removed")
            
            # Test 3: Verification logging should be enhanced
            self.assertIn("Enhanced table transactions:", content,
                         "Enhanced verification logging should be present")
            
            # Test 4: Clear operation verification should be present
            self.assertIn("Clear operation verification passed", content,
                         "Clear operation verification should be present")
            
            # Test 5: Force clear should also use populate_table([])
            force_clear_pattern = "# Clear internal transaction data\n                self.enhanced_table.populate_table([])"
            self.assertIn(force_clear_pattern, content,
                         "Force clear should also properly clear enhanced table internal data")
            
            print("✅ All clear data fix patterns are correctly implemented")
            
        except FileNotFoundError:
            self.fail("Could not find the ML labeling window file")
    
    def test_clear_data_sequence_logic(self):
        """Test that the clear data sequence follows the correct logic"""
        try:
            with open('bank_analyzer/ui/ml_labeling_window.py', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Find the clear_current_data method
            clear_method_start = content.find("def clear_current_data(self):")
            self.assertNotEqual(clear_method_start, -1, "clear_current_data method should exist")
            
            # Extract the method content (approximate)
            clear_method_end = content.find("\n    def ", clear_method_start + 1)
            if clear_method_end == -1:
                clear_method_end = len(content)
            
            clear_method_content = content[clear_method_start:clear_method_end]
            
            # Test the sequence of operations
            operations = [
                "self.unlabeled_transactions = []",
                "self.filtered_transactions = []", 
                "self.enhanced_table.setRowCount(0)",
                "self.enhanced_table.clearContents()",
                "self.enhanced_table.populate_table([])",
                "self.transaction_table.setRowCount(0)",
                "self.transaction_table.clearContents()"
            ]
            
            last_pos = 0
            for operation in operations:
                pos = clear_method_content.find(operation, last_pos)
                self.assertNotEqual(pos, -1, f"Operation '{operation}' should be present in clear_current_data")
                last_pos = pos
            
            print("✅ Clear data sequence logic is correct")
            
        except FileNotFoundError:
            self.fail("Could not find the ML labeling window file")
    
    def test_populate_transaction_table_behavior(self):
        """Test that populate_transaction_table behaves correctly with empty data"""
        try:
            with open('bank_analyzer/ui/ml_labeling_window.py', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Find the populate_transaction_table method
            method_start = content.find("def populate_transaction_table(self):")
            self.assertNotEqual(method_start, -1, "populate_transaction_table method should exist")
            
            # Extract the method content (approximate)
            method_end = content.find("\n    def ", method_start + 1)
            if method_end == -1:
                method_end = content.find("\nclass ", method_start + 1)
            if method_end == -1:
                method_end = len(content)
            
            method_content = content[method_start:method_end]
            
            # Check that it uses the correct transaction sources
            self.assertIn("getattr(self, 'filtered_transactions', None) or getattr(self, 'unlabeled_transactions', [])", 
                         method_content, "populate_transaction_table should use correct transaction sources")
            
            # Check that it handles empty lists correctly
            self.assertIn("self.transaction_table.setRowCount(len(transactions_to_show))", 
                         method_content, "populate_transaction_table should set row count based on transaction list length")
            
            print("✅ populate_transaction_table behavior is correct")
            
        except FileNotFoundError:
            self.fail("Could not find the ML labeling window file")
    
    def test_enhanced_table_populate_table_usage(self):
        """Test that enhanced table populate_table is used correctly"""
        try:
            with open('bank_analyzer/ui/enhanced_transaction_table.py', 'r', encoding='utf-8') as f:
                content = f.read()

            # Find the populate_table method
            method_start = content.find("def populate_table(self, transactions: List[UniqueTransaction]):")
            self.assertNotEqual(method_start, -1, "populate_table method should exist in enhanced table")

            # Extract the method content (approximate)
            method_end = content.find("\n    def ", method_start + 1)
            if method_end == -1:
                method_end = len(content)

            method_content = content[method_start:method_end]

            # Check that it properly handles empty transaction lists
            self.assertIn("self.transactions = valid_transactions",
                         method_content, "populate_table should store transactions internally")

            self.assertIn("self.setRowCount(len(valid_transactions))",
                         method_content, "populate_table should set row count based on valid transactions")

            print("✅ Enhanced table populate_table usage is correct")

        except FileNotFoundError:
            self.fail("Could not find the enhanced transaction table file")

    def test_auto_reload_prevention_fix(self):
        """Test that auto-reload prevention is properly implemented"""
        try:
            with open('bank_analyzer/ui/ml_labeling_window.py', 'r', encoding='utf-8') as f:
                content = f.read()

            # Check that auto-reload prevention is set in clear_current_data
            self.assertIn("self._prevent_auto_reload = True", content,
                         "Auto-reload prevention should be set in clear methods")

            # Check that auto-reload prevention is checked in load_transaction_batch
            self.assertIn("if getattr(self, '_prevent_auto_reload', False):", content,
                         "Auto-reload prevention should be checked in load_transaction_batch")

            # Check that session clearing is enhanced
            self.assertIn("Session successfully cleared from transaction data manager", content,
                         "Enhanced session clearing verification should be present")

            # Check that success message mentions auto-reload prevention
            self.assertIn("Disabled auto-reload to prevent data restoration", content,
                         "Success message should mention auto-reload prevention")

            print("✅ Auto-reload prevention fix is properly implemented")

        except FileNotFoundError:
            self.fail("Could not find the ML labeling window file")

    def test_session_clearing_completeness(self):
        """Test that session clearing is comprehensive"""
        try:
            with open('bank_analyzer/ui/ml_labeling_window.py', 'r', encoding='utf-8') as f:
                content = f.read()

            # Check that current session ID is cleared
            self.assertIn("self.current_session_id = None", content,
                         "Current session ID should be cleared")

            # Check that transaction data manager session is cleared
            self.assertIn("self.transaction_data_manager.current_session = None", content,
                         "Transaction data manager session should be cleared")

            # Check that active session is saved after clearing
            self.assertIn("self.transaction_data_manager._save_active_session()", content,
                         "Active session should be saved after clearing")

            # Check that both regular and force clear have session clearing
            clear_patterns = [
                "Transaction data manager session completely cleared",
                "Auto-reload disabled to prevent data restoration"
            ]

            for pattern in clear_patterns:
                self.assertIn(pattern, content, f"Session clearing pattern '{pattern}' should be present")

            print("✅ Session clearing completeness is correct")

        except FileNotFoundError:
            self.fail("Could not find the ML labeling window file")

    def test_persistent_auto_reload_prevention(self):
        """Test that auto-reload prevention is persistent across app restarts"""
        try:
            with open('bank_analyzer/ui/ml_labeling_window.py', 'r', encoding='utf-8') as f:
                content = f.read()

            # Check that auto-reload state is saved
            self.assertIn("self._save_auto_reload_state()", content,
                         "Auto-reload state should be saved")

            # Check that auto-reload state is loaded during initialization
            self.assertIn("self._prevent_auto_reload = self._load_auto_reload_state()", content,
                         "Auto-reload state should be loaded during initialization")

            # Check that save and load methods exist
            self.assertIn("def _save_auto_reload_state(self):", content,
                         "_save_auto_reload_state method should exist")

            self.assertIn("def _load_auto_reload_state(self):", content,
                         "_load_auto_reload_state method should exist")

            # Check that auto-reload state file is used
            self.assertIn("auto_reload_state.json", content,
                         "Auto-reload state should be persisted to file")

            print("✅ Persistent auto-reload prevention is correctly implemented")

        except FileNotFoundError:
            self.fail("Could not find the ML labeling window file")

    def test_session_filtering_fix(self):
        """Test that cleared sessions are properly excluded from available sessions"""
        try:
            with open('bank_analyzer/core/transaction_data_manager.py', 'r', encoding='utf-8') as f:
                content = f.read()

            # Check that cleared sessions are excluded
            self.assertIn("if session.status == SessionStatus.CLEARED:", content,
                         "Cleared sessions should be filtered out")

            self.assertIn("# Always exclude cleared sessions as they are no longer available", content,
                         "Comment explaining cleared session exclusion should be present")

            # Check that the filtering logic is correct
            self.assertIn("continue", content,
                         "Cleared sessions should be skipped in the loop")

            print("✅ Session filtering fix is correctly implemented")

        except FileNotFoundError:
            self.fail("Could not find the transaction data manager file")


def run_tests():
    """Run the clear data functionality tests"""
    print("Running clear data functionality tests...")
    
    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestClearDataFunctionality)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    if result.wasSuccessful():
        print("\n✅ All clear data functionality tests passed!")
        print("The clear data fix has been successfully implemented.")
        print("\nKey fixes applied:")
        print("1. Enhanced table is properly cleared with populate_table([])")
        print("2. Problematic populate_transaction_table() call removed from clear sequence")
        print("3. Enhanced verification logging added")
        print("4. Force clear method also fixed")
        print("5. Auto-reload prevention implemented to stop data restoration")
        print("6. Session clearing is comprehensive and persistent")
        print("7. Persistent auto-reload prevention across app restarts")
        print("8. Session filtering excludes cleared sessions")
        print("9. Clear data should now work on the first click!")
        print("10. Data should stay cleared even after application restart!")
        return True
    else:
        print(f"\n❌ {len(result.failures)} test(s) failed, {len(result.errors)} error(s)")
        return False


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
