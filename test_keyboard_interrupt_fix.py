#!/usr/bin/env python3
"""
Test script to verify that keyboard interrupt handling works correctly
in the ML labeling window.
"""

import sys
import os
import unittest
from unittest.mock import Mock, patch, MagicMock
from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import Qt

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


class TestKeyboardInterruptHandling(unittest.TestCase):
    """Test keyboard interrupt handling in dialog execution"""

    @classmethod
    def setUpClass(cls):
        """Set up QApplication for testing"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()

    def test_keyboard_interrupt_in_dialog_exec(self):
        """Test that keyboard interrupt in dialog exec is handled gracefully"""
        # Create a mock dialog that raises KeyboardInterrupt when exec() is called
        mock_dialog = Mock()
        mock_dialog.exec.side_effect = KeyboardInterrupt()

        # Test the keyboard interrupt handling pattern used in our fix
        try:
            mock_dialog.exec()
        except KeyboardInterrupt:
            # This simulates our fix - catching the KeyboardInterrupt
            print("KeyboardInterrupt caught and handled gracefully")
            handled = True
        else:
            handled = False

        # Verify that we can handle the interrupt
        self.assertTrue(handled, "KeyboardInterrupt should be caught and handled")

    def test_keyboard_interrupt_handling_pattern(self):
        """Test the specific pattern we implemented for handling keyboard interrupts"""

        # Mock logger
        mock_logger = Mock()

        # Simulate the pattern we implemented
        def simulate_dialog_with_interrupt_handling():
            mock_dialog = Mock()
            mock_dialog.exec.side_effect = KeyboardInterrupt()

            try:
                mock_dialog.exec()
            except KeyboardInterrupt:
                # This is the pattern we implemented
                mock_logger.info("Dialog interrupted by user")
                return "handled"

            return "not_interrupted"

        result = simulate_dialog_with_interrupt_handling()

        # Verify the pattern works
        self.assertEqual(result, "handled")
        mock_logger.info.assert_called_with("Dialog interrupted by user")

    def test_code_pattern_verification(self):
        """Verify that our code changes follow the correct pattern"""
        # Read the actual ML labeling window file to verify our changes
        try:
            with open('bank_analyzer/ui/ml_labeling_window.py', 'r', encoding='utf-8') as f:
                content = f.read()

            # Check that our keyboard interrupt handling patterns are present
            patterns_to_check = [
                "except KeyboardInterrupt:",
                "# User interrupted",
                "dialog interrupted by user",
                "success_msg.exec()",
                "try:",
            ]

            for pattern in patterns_to_check:
                self.assertIn(pattern, content, f"Pattern '{pattern}' not found in the code")

            print("✅ All keyboard interrupt handling patterns found in the code")

        except FileNotFoundError:
            self.fail("Could not find the ML labeling window file to verify changes")

    def test_clear_data_fix_verification(self):
        """Verify that the clear data fix is properly implemented"""
        try:
            with open('bank_analyzer/ui/ml_labeling_window.py', 'r', encoding='utf-8') as f:
                content = f.read()

            # Check that the clear data fix patterns are present
            clear_fix_patterns = [
                "self.enhanced_table.populate_table([])",
                "Clear operation verification passed",
                "Enhanced table transactions:",
                "# Note: No need to call populate_transaction_table()",
            ]

            for pattern in clear_fix_patterns:
                self.assertIn(pattern, content, f"Clear data fix pattern '{pattern}' not found in the code")

            # Check that the problematic populate_transaction_table() call is removed
            # Look for the specific context where it was called
            problematic_pattern = "# Force refresh the transaction table display to ensure it's empty\n                    self.populate_transaction_table()"
            self.assertNotIn(problematic_pattern, content, "Problematic populate_transaction_table() call still present")

            print("✅ All clear data fix patterns found in the code")

        except FileNotFoundError:
            self.fail("Could not find the ML labeling window file to verify clear data fix")


def run_tests():
    """Run the keyboard interrupt tests"""
    print("Running keyboard interrupt handling tests...")

    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestKeyboardInterruptHandling)

    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)

    # Print summary
    if result.wasSuccessful():
        print("\n✅ All keyboard interrupt handling tests passed!")
        print("The KeyboardInterrupt fix has been successfully implemented.")
        return True
    else:
        print(f"\n❌ {len(result.failures)} test(s) failed, {len(result.errors)} error(s)")
        return False


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
