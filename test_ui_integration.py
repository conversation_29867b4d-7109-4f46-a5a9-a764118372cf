#!/usr/bin/env python3
"""
Test UI Integration for Duplicate Detection
"""

import sys
from pathlib import Path
from datetime import date
from decimal import Decimal

# Add the bank_analyzer module to the path
sys.path.insert(0, str(Path(__file__).parent))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt

from bank_analyzer.ui.session_management_dialog import SessionManagementDialog
from bank_analyzer.ui.duplicate_detection_dialog import DuplicateDetectionDialog
from bank_analyzer.core.transaction_data_manager import TransactionDataManager, SessionLoadResult
from bank_analyzer.models.transaction import RawTransaction


def create_test_load_result():
    """Create a test SessionLoadResult with duplicates"""
    # Create some test transactions
    raw_transactions = [
        RawTransaction(
            date=date(2024, 1, 1),
            description="ATM WITHDRAWAL HDFC BANK",
            amount=Decimal("-5000.00"),
            transaction_type="DEBIT"
        ),
        RawTransaction(
            date=date(2024, 1, 2),
            description="SALARY CREDIT COMPANY XYZ",
            amount=Decimal("50000.00"),
            transaction_type="CREDIT"
        ),
        RawTransaction(
            date=date(2024, 1, 3),
            description="UPI PAYMENT TO MERCHANT ABC",
            amount=Decimal("-1200.00"),
            transaction_type="DEBIT"
        )
    ]
    
    # Simulate some duplicates
    new_transactions = [raw_transactions[2]]  # Only UPI payment is new
    duplicate_transactions = raw_transactions[:2]  # ATM and Salary are duplicates
    
    duplicate_details = [
        {
            'transaction': raw_transactions[0],
            'hash_id': 'abc123',
            'normalized_description': 'atm withdrawal hdfc bank',
            'transaction_type': 'debit',
            'reason': 'Exact match with trained data'
        },
        {
            'transaction': raw_transactions[1],
            'hash_id': 'def456',
            'normalized_description': 'salary credit company xyz',
            'transaction_type': 'credit',
            'reason': 'Exact match with trained data'
        }
    ]
    
    return SessionLoadResult(
        session_id="test_session_20241201_1200",
        raw_transactions=raw_transactions,
        processed_transactions=[],
        new_transactions=new_transactions,
        duplicate_transactions=duplicate_transactions,
        duplicate_details=duplicate_details,
        total_new=1,
        total_duplicates=2,
        detection_summary="Processed 3 transactions: 1 new (33.3%), 2 duplicates (66.7%)",
        has_duplicates=True
    )


def test_session_management_dialog():
    """Test the session management dialog"""
    print("Testing Session Management Dialog...")
    
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    try:
        # Create transaction data manager
        data_manager = TransactionDataManager("test_sessions")
        
        # Create dialog
        dialog = SessionManagementDialog(data_manager)
        
        # Check if duplicate detection checkbox exists
        if hasattr(dialog, 'enable_duplicate_detection_cb'):
            print("✅ Duplicate detection checkbox found")
            print(f"   Default state: {'Checked' if dialog.enable_duplicate_detection_cb.isChecked() else 'Unchecked'}")
            print(f"   Tooltip: {dialog.enable_duplicate_detection_cb.toolTip()}")
        else:
            print("❌ Duplicate detection checkbox NOT found")
        
        # Check if load buttons exist
        if hasattr(dialog, 'load_session_btn') and hasattr(dialog, 'load_and_close_btn'):
            print("✅ Load session buttons found")
        else:
            print("❌ Load session buttons NOT found")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing session management dialog: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_duplicate_detection_dialog():
    """Test the duplicate detection dialog"""
    print("\nTesting Duplicate Detection Dialog...")
    
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    try:
        # Create test data
        load_result = create_test_load_result()
        
        # Create dialog
        dialog = DuplicateDetectionDialog(load_result)
        
        # Check if main components exist
        components_found = []
        
        if hasattr(dialog, 'summary_label'):
            components_found.append("Summary label")
        
        if hasattr(dialog, 'tab_widget'):
            components_found.append("Tab widget")
            
        if hasattr(dialog, 'new_table'):
            components_found.append("New transactions table")
            
        if hasattr(dialog, 'duplicate_table'):
            components_found.append("Duplicate transactions table")
            
        if hasattr(dialog, 'proceed_new_btn'):
            components_found.append("Proceed with new button")
            
        if hasattr(dialog, 'proceed_all_btn'):
            components_found.append("Proceed with all button")
        
        print(f"✅ Found components: {', '.join(components_found)}")
        
        # Check if data is populated
        if hasattr(dialog, 'summary_label') and dialog.summary_label.text():
            print("✅ Summary data populated")
        else:
            print("❌ Summary data NOT populated")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing duplicate detection dialog: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main test function"""
    print("Testing UI Integration for Duplicate Detection")
    print("=" * 50)
    
    # Test session management dialog
    session_mgmt_ok = test_session_management_dialog()
    
    # Test duplicate detection dialog
    duplicate_dialog_ok = test_duplicate_detection_dialog()
    
    print("\n" + "=" * 50)
    print("Test Results:")
    print(f"Session Management Dialog: {'✅ PASS' if session_mgmt_ok else '❌ FAIL'}")
    print(f"Duplicate Detection Dialog: {'✅ PASS' if duplicate_dialog_ok else '❌ FAIL'}")
    
    if session_mgmt_ok and duplicate_dialog_ok:
        print("\n🎉 All UI components are properly integrated!")
        print("\nTo access the duplicate detection features:")
        print("1. Run the main application: python bank_statement_analyzer.py")
        print("2. Go to File → Manage Sessions... (Ctrl+M)")
        print("3. Select a session and ensure 'Enable duplicate detection' is checked")
        print("4. Click 'Load Session' or 'Load & Close'")
        print("5. If duplicates are found, the Duplicate Detection Dialog will appear")
    else:
        print("\n❌ Some UI components are missing or not working correctly")
    
    return session_mgmt_ok and duplicate_dialog_ok


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
