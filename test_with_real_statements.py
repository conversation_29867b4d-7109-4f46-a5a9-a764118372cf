#!/usr/bin/env python3
"""
Test the categorization system with real Indian Bank statements
"""

import sys
from pathlib import Path
from datetime import datetime

# Add the bank_analyzer module to the path
sys.path.insert(0, str(Path(__file__).parent))

from bank_analyzer.parsers.indian_bank_parser import IndianBankPDFParser
from bank_analyzer.ml.transaction_categorization_service import TransactionCategorizationService


def test_with_indian_bank_statements():
    """Test categorization with real Indian Bank statements"""
    print("🏦 Testing Categorization with Real Indian Bank Statements")
    print("=" * 70)
    
    # Initialize parser and categorization service
    parser = IndianBankPDFParser()
    categorization_service = TransactionCategorizationService()
    
    # Find Indian Bank statement files
    statements_dir = Path("statements/Indian bank")
    if not statements_dir.exists():
        print("❌ Indian Bank statements directory not found!")
        return False
    
    # Get all PDF files
    pdf_files = list(statements_dir.glob("*.pdf"))
    if not pdf_files:
        print("❌ No PDF statement files found in Indian Bank directory!")
        return False
    
    print(f"📁 Found {len(pdf_files)} statement files:")
    for pdf_file in pdf_files:
        print(f"  - {pdf_file.name}")
    
    # Test with the first statement file
    test_file = pdf_files[0]
    print(f"\n🧪 Testing with: {test_file.name}")
    print("-" * 50)
    
    try:
        # Parse the statement
        print("📖 Parsing statement...")
        raw_transactions = parser.parse(Path(test_file))
        
        if not raw_transactions:
            print("❌ No transactions found in the statement!")
            return False
        
        print(f"✅ Parsed {len(raw_transactions)} transactions")
        
        # Show sample transactions
        print(f"\n📋 Sample transactions:")
        for i, txn in enumerate(raw_transactions[:3]):
            print(f"  {i+1}. {txn.date} | {txn.description[:50]}... | {txn.amount}")
        
        # Test categorization on all transactions
        print(f"\n🔍 Running categorization on all {len(raw_transactions)} transactions...")
        result = categorization_service.categorize_transactions(raw_transactions)
        
        # Display results
        print(f"\n📊 Categorization Results:")
        print(f"  Already Labeled: {result.total_already_labeled}")
        print(f"  Unlabeled: {result.total_unlabeled}")
        print(f"  New: {result.total_new}")
        print(f"  Total: {result.total_already_labeled + result.total_unlabeled + result.total_new}")
        
        # Show percentages
        total = len(raw_transactions)
        if total > 0:
            already_pct = (result.total_already_labeled / total) * 100
            unlabeled_pct = (result.total_unlabeled / total) * 100
            new_pct = (result.total_new / total) * 100
            
            print(f"\n📈 Breakdown:")
            print(f"  Already Labeled: {already_pct:.1f}%")
            print(f"  Unlabeled: {unlabeled_pct:.1f}%")
            print(f"  New: {new_pct:.1f}%")
        
        # Show sample already labeled transactions
        if result.already_labeled:
            print(f"\n✅ Sample Already Labeled Transactions:")
            for i, txn in enumerate(result.already_labeled[:5]):
                category_info = f"{txn.category}/{txn.sub_category}" if txn.category else "No category info"
                print(f"  {i+1}. {txn.description[:40]}... -> {category_info}")
        
        # Show sample unlabeled transactions
        if result.unlabeled:
            print(f"\n⚠️ Sample Unlabeled Transactions:")
            for i, txn in enumerate(result.unlabeled[:5]):
                print(f"  {i+1}. {txn.description[:50]}...")
        
        # Show sample new transactions
        if result.new:
            print(f"\n🆕 Sample New Transactions:")
            for i, txn in enumerate(result.new[:5]):
                print(f"  {i+1}. {txn.description[:50]}...")
        
        # Summary
        print(f"\n" + "=" * 70)
        print(f"🎯 Summary:")
        
        if result.total_already_labeled > 0:
            print(f"✅ Great! {result.total_already_labeled} transactions were recognized as already labeled")
            print(f"   This means the fix is working and your previous labeling is being preserved!")
        else:
            print(f"ℹ️ No transactions were recognized as already labeled")
            print(f"   This could mean:")
            print(f"   - These are genuinely new transactions not seen before")
            print(f"   - The transactions in this statement weren't in your previous training data")
        
        if result.total_unlabeled > 0:
            print(f"⚠️ {result.total_unlabeled} transactions exist in the system but need labeling")
        
        if result.total_new > 0:
            print(f"🆕 {result.total_new} transactions are completely new and ready for labeling")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing with statement: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_multiple_statements():
    """Test with multiple statement files if available"""
    print(f"\n🔍 Testing Multiple Statements")
    print("=" * 70)
    
    statements_dir = Path("statements/Indian bank")
    pdf_files = list(statements_dir.glob("*.pdf"))
    
    if len(pdf_files) <= 1:
        print("ℹ️ Only one statement file available, skipping multiple file test")
        return
    
    parser = IndianBankPDFParser()
    categorization_service = TransactionCategorizationService()
    
    total_already_labeled = 0
    total_unlabeled = 0
    total_new = 0
    total_transactions = 0
    
    print(f"📊 Testing categorization across {len(pdf_files)} statement files:")
    
    for i, pdf_file in enumerate(pdf_files[:3]):  # Test first 3 files to avoid too much output
        try:
            print(f"\n  File {i+1}: {pdf_file.name}")
            raw_transactions = parser.parse(Path(pdf_file))
            
            if raw_transactions:
                result = categorization_service.categorize_transactions(raw_transactions)
                
                print(f"    Transactions: {len(raw_transactions)}")
                print(f"    Already labeled: {result.total_already_labeled}")
                print(f"    Unlabeled: {result.total_unlabeled}")
                print(f"    New: {result.total_new}")
                
                total_already_labeled += result.total_already_labeled
                total_unlabeled += result.total_unlabeled
                total_new += result.total_new
                total_transactions += len(raw_transactions)
            else:
                print(f"    ❌ No transactions found")
                
        except Exception as e:
            print(f"    ❌ Error: {str(e)}")
    
    if total_transactions > 0:
        print(f"\n📈 Overall Results Across All Files:")
        print(f"  Total transactions: {total_transactions}")
        print(f"  Already labeled: {total_already_labeled} ({(total_already_labeled/total_transactions)*100:.1f}%)")
        print(f"  Unlabeled: {total_unlabeled} ({(total_unlabeled/total_transactions)*100:.1f}%)")
        print(f"  New: {total_new} ({(total_new/total_transactions)*100:.1f}%)")


def main():
    """Main test function"""
    try:
        print("🧪 Real Statement Categorization Test")
        print("=" * 70)
        print(f"Testing the enhanced categorization system with your actual Indian Bank statements")
        print(f"This will show how many of your transactions are recognized as already labeled")
        print("")
        
        # Test with single statement
        success = test_with_indian_bank_statements()
        
        if success:
            # Test with multiple statements
            test_multiple_statements()
            
            print(f"\n" + "=" * 70)
            print(f"✅ Testing completed successfully!")
            print(f"\nNow you can load this data in the ML Labeling Window and see the")
            print(f"improved categorization results with your previously labeled transactions")
            print(f"properly recognized!")
        
        return success
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
