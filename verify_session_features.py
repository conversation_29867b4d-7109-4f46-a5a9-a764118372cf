#!/usr/bin/env python3
"""
Verification script to check if all session management features are properly implemented
"""

import sys
from pathlib import Path
from datetime import datetime, date
from decimal import Decimal

# Add the current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def verify_imports():
    """Verify all required imports work"""
    print("🔍 Verifying imports...")
    
    try:
        # Core components
        from bank_analyzer.core.transaction_data_manager import TransactionDataManager, SessionStatus, DataMergeStrategy
        from bank_analyzer.ui.session_management_dialog import SessionManagementDialog
        from bank_analyzer.ui.hybrid_categorization_qt import HybridCategorizationDialog
        from bank_analyzer.models.transaction import RawTransaction
        print("✅ All imports successful")
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def verify_transaction_data_manager():
    """Verify TransactionDataManager functionality"""
    print("\n🔍 Verifying TransactionDataManager...")
    
    try:
        from bank_analyzer.core.transaction_data_manager import TransactionDataManager
        from bank_analyzer.models.transaction import RawTransaction
        
        # Create manager
        manager = TransactionDataManager("test_data/sessions")
        
        # Create sample transaction
        sample_txn = RawTransaction(
            date=date.today(),
            description="Test Transaction",
            amount=Decimal("100.00"),
            transaction_type="DEBIT"
        )
        
        # Test session creation
        session_id = manager.create_session([sample_txn], description="Test session")
        print(f"✅ Session created: {session_id}")
        
        # Test session loading
        raw_txns, processed_txns = manager.get_session_transactions()
        print(f"✅ Session loaded: {len(raw_txns)} raw transactions")
        
        # Test backup
        backup_id = manager.backup_current_session("Test backup")
        print(f"✅ Backup created: {backup_id}")
        
        # Test clearing
        success = manager.clear_current_session()
        print(f"✅ Session cleared: {success}")
        
        return True
        
    except Exception as e:
        print(f"❌ TransactionDataManager error: {e}")
        return False

def verify_ui_components():
    """Verify UI components have required methods"""
    print("\n🔍 Verifying UI components...")
    
    try:
        from bank_analyzer.ui.hybrid_categorization_qt import HybridCategorizationDialog
        
        # Check if required methods exist
        required_methods = [
            'clear_current_transaction_data',
            'open_session_management', 
            'create_transaction_session',
            'update_session_status',
            'load_session_data'
        ]
        
        for method_name in required_methods:
            if hasattr(HybridCategorizationDialog, method_name):
                print(f"✅ Method exists: {method_name}")
            else:
                print(f"❌ Method missing: {method_name}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ UI verification error: {e}")
        return False

def verify_ui_elements():
    """Verify UI elements are properly defined"""
    print("\n🔍 Verifying UI elements...")
    
    try:
        # Read the hybrid categorization file to check for UI elements
        hybrid_file = Path("bank_analyzer/ui/hybrid_categorization_qt.py")
        
        if not hybrid_file.exists():
            print("❌ Hybrid categorization file not found")
            return False
        
        content = hybrid_file.read_text()
        
        # Check for session management UI elements
        ui_elements = [
            'clear_current_data_btn',
            'manage_sessions_btn', 
            'create_session_btn',
            'session_status',
            'Transaction Session Management',
            'Clear Current Data',
            'Manage Sessions',
            'Save as Session'
        ]
        
        for element in ui_elements:
            if element in content:
                print(f"✅ UI element found: {element}")
            else:
                print(f"❌ UI element missing: {element}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ UI elements verification error: {e}")
        return False

def verify_integration():
    """Verify integration between components"""
    print("\n🔍 Verifying component integration...")
    
    try:
        # Check if transaction data manager is initialized in hybrid dialog
        hybrid_file = Path("bank_analyzer/ui/hybrid_categorization_qt.py")
        content = hybrid_file.read_text()
        
        integration_checks = [
            'TransactionDataManager',
            'transaction_data_manager',
            'session_management_dialog',
            'detect_similar_sessions',
            '_handle_similar_sessions_detected'
        ]
        
        for check in integration_checks:
            if check in content:
                print(f"✅ Integration found: {check}")
            else:
                print(f"❌ Integration missing: {check}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Integration verification error: {e}")
        return False

def main():
    """Main verification function"""
    print("🧪 Session Management Features Verification")
    print("=" * 50)
    
    all_passed = True
    
    # Run all verifications
    verifications = [
        verify_imports,
        verify_transaction_data_manager,
        verify_ui_components,
        verify_ui_elements,
        verify_integration
    ]
    
    for verification in verifications:
        if not verification():
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 All verifications passed! Session management features are properly implemented.")
        print("\n📋 To see the features in action:")
        print("1. Run the Bank Statement Analyzer application")
        print("2. Load some bank statements")
        print("3. Open 'ML Processing' → 'Hybrid Categorization System'")
        print("4. Go to the 'Phase 4: Export' tab")
        print("5. Look for 'Transaction Session Management' section")
        print("\n🔧 Available features:")
        print("• 🗑️ Clear Current Data - Clear with automatic backup")
        print("• 📁 Manage Sessions - View and load previous sessions")
        print("• 💾 Save as Session - Save current work as named session")
        print("• Session status display - Shows current session info")
    else:
        print("❌ Some verifications failed. Please check the implementation.")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
